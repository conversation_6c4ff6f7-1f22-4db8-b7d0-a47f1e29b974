"""
数字货币选币系统测试脚本
用于验证系统各个组件的功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import get_config, get_symbols_by_category
from crypto_data_adapter import CryptoDataAdapter
from crypto_stock_picker import CryptoStockPicker
from crypto_alpha_expressions import CRYPTO_ALPHA_EXPRESSIONS


class SystemTester:
    """系统测试类"""
    
    def __init__(self):
        """初始化测试器"""
        self.config = get_config('default')
        print("=" * 60)
        print("数字货币选币系统测试")
        print("=" * 60)
    
    def test_data_adapter(self):
        """测试数据适配器"""
        print("\n1. 测试数据适配器")
        print("-" * 40)
        
        try:
            # 初始化数据适配器
            adapter = CryptoDataAdapter(self.config['data_config'])
            
            # 测试获取单个交易对数据
            print("测试获取BTCUSDT数据...")
            data = adapter.get_kline_data('BTCUSDT', '1d', limit=100)
            
            if data is not None and len(data) > 0:
                print(f"✓ 成功获取数据，共 {len(data)} 条记录")
                print(f"  数据列: {list(data.columns)}")
                print(f"  时间范围: {data.index[0]} 到 {data.index[-1]}")
                print(f"  最新价格: {data['close'].iloc[-1]:.4f}")
                
                # 显示数据样本
                print("\n数据样本 (最近5条):")
                print(data.tail().round(4))
                
            else:
                print("✗ 获取数据失败，使用模拟数据")
            
            # 测试批量获取数据
            print("\n测试批量获取数据...")
            symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
            batch_data = adapter.get_multiple_symbols_data(symbols, '1d')
            
            print(f"✓ 批量获取完成，成功获取 {len(batch_data)} 个交易对的数据")
            for symbol, df in batch_data.items():
                print(f"  {symbol}: {len(df)} 条记录")
            
            return True
            
        except Exception as e:
            print(f"✗ 数据适配器测试失败: {e}")
            return False
    
    def test_technical_indicators(self):
        """测试技术指标计算"""
        print("\n2. 测试技术指标计算")
        print("-" * 40)
        
        try:
            # 创建选股器
            picker = CryptoStockPicker(self.config)
            
            # 生成测试数据
            dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
            np.random.seed(42)
            
            # 模拟价格数据
            prices = [100]
            for _ in range(len(dates) - 1):
                change = np.random.normal(0, 0.02)
                new_price = prices[-1] * (1 + change)
                prices.append(new_price)
            
            test_data = pd.DataFrame({
                'open': [p * (1 + np.random.normal(0, 0.005)) for p in prices],
                'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
                'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
                'close': prices,
                'volume': [np.random.lognormal(10, 1) * 1000 for _ in prices]
            }, index=dates)
            
            print("✓ 生成测试数据完成")
            print(f"  数据长度: {len(test_data)}")
            print(f"  价格范围: {test_data['close'].min():.2f} - {test_data['close'].max():.2f}")
            
            # 计算技术指标
            print("\n计算技术指标...")
            data_with_indicators = picker.calculate_technical_indicators(test_data)
            
            # 检查指标
            indicators = ['rsi', 'macd', 'macd_signal', 'bb_upper', 'bb_lower', 'sma_20']
            for indicator in indicators:
                if indicator in data_with_indicators.columns:
                    latest_value = data_with_indicators[indicator].iloc[-1]
                    print(f"  ✓ {indicator}: {latest_value:.4f}")
                else:
                    print(f"  ✗ {indicator}: 计算失败")
            
            return True
            
        except Exception as e:
            print(f"✗ 技术指标测试失败: {e}")
            return False
    
    def test_pattern_detection(self):
        """测试形态识别"""
        print("\n3. 测试K线形态识别")
        print("-" * 40)
        
        try:
            picker = CryptoStockPicker(self.config)
            
            # 创建包含特定形态的测试数据
            test_data = self.create_pattern_test_data()
            
            # 计算技术指标
            data_with_indicators = picker.calculate_technical_indicators(test_data)
            
            # 测试形态识别
            pattern_signals = picker.evaluate_pattern_signals(data_with_indicators)
            
            print(f"✓ 形态识别完成")
            print(f"  检测到形态信号数量: {(pattern_signals > 0).sum()}")
            print(f"  最高形态得分: {pattern_signals.max():.2f}")
            
            # 显示最近的形态信号
            recent_signals = pattern_signals.tail(10)
            positive_signals = recent_signals[recent_signals > 0]
            
            if len(positive_signals) > 0:
                print(f"\n最近的形态信号:")
                for date, score in positive_signals.items():
                    print(f"  {date.strftime('%Y-%m-%d')}: {score:.2f}")
            else:
                print("  最近10天内未检测到形态信号")
            
            return True
            
        except Exception as e:
            print(f"✗ 形态识别测试失败: {e}")
            return False
    
    def test_stock_selection(self):
        """测试选股功能"""
        print("\n4. 测试选股功能")
        print("-" * 40)
        
        try:
            picker = CryptoStockPicker(self.config)
            
            # 使用少量交易对进行测试
            test_symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
            
            print(f"测试选股，标的: {test_symbols}")
            
            # 执行选股
            results = picker.select_stocks(test_symbols, '1d')
            
            print(f"✓ 选股完成，找到 {len(results)} 个符合条件的标的")
            
            if results:
                print("\n选股结果:")
                print(f"{'排名':<4} {'代码':<12} {'总分':<6} {'当前价格':<12} {'RSI':<6}")
                print("-" * 50)
                
                for i, result in enumerate(results, 1):
                    print(f"{i:<4} {result['symbol']:<12} {result['score']:<6.1f} "
                          f"{result['current_price']:<12.4f} {result['rsi']:<6.1f}")
            else:
                print("  未找到符合条件的标的")
            
            return True
            
        except Exception as e:
            print(f"✗ 选股测试失败: {e}")
            return False
    
    def test_multi_timeframe(self):
        """测试多时间周期分析"""
        print("\n5. 测试多时间周期分析")
        print("-" * 40)
        
        try:
            picker = CryptoStockPicker(self.config)
            
            # 测试单个标的的多时间周期分析
            symbol = 'BTCUSDT'
            print(f"测试 {symbol} 的多时间周期分析...")
            
            result = picker.multi_timeframe_analysis(symbol)
            
            if result:
                print(f"✓ 多时间周期分析完成")
                print(f"  综合得分: {result.get('multi_tf_score', 0):.2f}")
                
                for tf in ['1d', '4h', '1h']:
                    if tf in result:
                        tf_data = result[tf]
                        print(f"  {tf}: 得分 {tf_data.get('score', 0):.1f}")
                
                confirmation = result.get('confirmation', {})
                print(f"  趋势一致: {'✓' if confirmation.get('trend_alignment') else '✗'}")
                print(f"  信号一致: {'✓' if confirmation.get('signal_consistency') else '✗'}")
                print(f"  风险等级: {confirmation.get('risk_level', 'unknown')}")
            else:
                print("✗ 多时间周期分析失败")
                return False
            
            return True
            
        except Exception as e:
            print(f"✗ 多时间周期测试失败: {e}")
            return False
    
    def test_alpha_expressions(self):
        """测试Alpha表达式"""
        print("\n6. 测试Alpha表达式")
        print("-" * 40)
        
        try:
            print("检查Alpha表达式定义...")
            
            expression_count = len(CRYPTO_ALPHA_EXPRESSIONS)
            print(f"✓ 共定义了 {expression_count} 个Alpha表达式")
            
            # 显示部分表达式
            key_expressions = [
                'hammer_pattern',
                'rsi_oversold_recovery', 
                'macd_golden_cross',
                'strong_breakout_stock'
            ]
            
            print("\n关键表达式:")
            for expr_name in key_expressions:
                if expr_name in CRYPTO_ALPHA_EXPRESSIONS:
                    expr = CRYPTO_ALPHA_EXPRESSIONS[expr_name]
                    # 清理表达式以便显示
                    clean_expr = expr.strip().replace('\n', ' ').replace('  ', ' ')
                    if len(clean_expr) > 80:
                        clean_expr = clean_expr[:80] + "..."
                    print(f"  ✓ {expr_name}: {clean_expr}")
                else:
                    print(f"  ✗ {expr_name}: 未找到")
            
            return True
            
        except Exception as e:
            print(f"✗ Alpha表达式测试失败: {e}")
            return False
    
    def create_pattern_test_data(self):
        """创建包含特定形态的测试数据"""
        dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
        
        # 基础价格趋势
        base_prices = np.linspace(100, 120, 100)
        
        data = []
        for i, (date, base_price) in enumerate(zip(dates, base_prices)):
            # 添加一些随机波动
            volatility = 0.02
            
            if i == 50:  # 在第50天创建一个锤子线形态
                open_price = base_price * 0.98
                low = base_price * 0.95  # 长下影线
                high = base_price * 0.99
                close = base_price * 0.985
            elif i == 70:  # 在第70天创建一个突破形态
                open_price = base_price
                high = base_price * 1.05  # 突破
                low = base_price * 0.99
                close = base_price * 1.04
            else:
                # 正常的K线
                change = np.random.normal(0, volatility)
                open_price = base_price * (1 + np.random.normal(0, volatility/2))
                high = max(open_price, base_price) * (1 + abs(np.random.normal(0, volatility/2)))
                low = min(open_price, base_price) * (1 - abs(np.random.normal(0, volatility/2)))
                close = base_price * (1 + change)
            
            volume = np.random.lognormal(10, 0.5) * 1000
            
            data.append({
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume
            })
        
        return pd.DataFrame(data, index=dates)
    
    def run_all_tests(self):
        """运行所有测试"""
        tests = [
            self.test_data_adapter,
            self.test_technical_indicators,
            self.test_pattern_detection,
            self.test_alpha_expressions,
            self.test_stock_selection,
            self.test_multi_timeframe
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
            except Exception as e:
                print(f"测试异常: {e}")
        
        print("\n" + "=" * 60)
        print(f"测试完成: {passed}/{total} 通过")
        print("=" * 60)
        
        if passed == total:
            print("🎉 所有测试通过！系统运行正常。")
        else:
            print("⚠️  部分测试失败，请检查系统配置。")


def main():
    """主函数"""
    tester = SystemTester()
    tester.run_all_tests()


if __name__ == "__main__":
    main()
