#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
逐步测试脚本
"""

import sys
import os
from datetime import datetime

def log(message):
    """日志函数"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def test_step_1_imports():
    """步骤1：测试基本导入"""
    log("步骤1：测试基本导入...")
    
    try:
        import requests
        import sqlite3
        import pandas as pd
        import numpy as np
        import time
        import json
        import hmac
        import hashlib
        import base64
        import urllib.parse
        import os
        import sys
        from datetime import datetime, timedelta
        from typing import Dict, List, Optional, Tuple, Union
        import asyncio
        import aiohttp
        import traceback
        import warnings
        warnings.filterwarnings('ignore')
        
        log("✅ 基本导入成功")
        return True
    except Exception as e:
        log(f"❌ 基本导入失败: {e}")
        return False

def test_step_2_advanced_imports():
    """步骤2：测试高级导入"""
    log("步骤2：测试高级导入...")
    
    try:
        from scipy.signal import find_peaks
        from scipy.stats import linregress
        import yaml
        import subprocess
        import socket
        import socks
        import signal
        import threading
        import schedule
        from collections import defaultdict
        
        log("✅ 高级导入成功")
        return True
    except Exception as e:
        log(f"❌ 高级导入失败: {e}")
        return False

def test_step_3_class_import():
    """步骤3：测试类导入"""
    log("步骤3：测试类导入...")
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from advanced_crypto_scraper import AdvancedCryptoScraper
        log("✅ 类导入成功")
        return True
    except Exception as e:
        log(f"❌ 类导入失败: {e}")
        import traceback
        log(f"错误详情: {traceback.format_exc()}")
        return False

def test_step_4_basic_init():
    """步骤4：测试基本初始化"""
    log("步骤4：测试基本初始化...")
    
    try:
        from advanced_crypto_scraper import AdvancedCryptoScraper
        
        # 创建最简单的实例
        log("创建实例（不使用代理）...")
        scraper = AdvancedCryptoScraper(use_proxy=False)
        log("✅ 基本初始化成功")
        return True
    except Exception as e:
        log(f"❌ 基本初始化失败: {e}")
        import traceback
        log(f"错误详情: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    log("🚀 开始逐步测试...")
    
    # 步骤1：基本导入
    if not test_step_1_imports():
        log("❌ 步骤1失败，停止测试")
        return
    
    # 步骤2：高级导入
    if not test_step_2_advanced_imports():
        log("❌ 步骤2失败，停止测试")
        return
    
    # 步骤3：类导入
    if not test_step_3_class_import():
        log("❌ 步骤3失败，停止测试")
        return
    
    # 步骤4：基本初始化
    if not test_step_4_basic_init():
        log("❌ 步骤4失败，停止测试")
        return
    
    log("✅ 所有测试步骤通过！")

if __name__ == "__main__":
    main()
