{"version": "0.2.0", "configurations": [{"name": "终极版形态分析器", "type": "python", "request": "launch", "program": "${workspaceFolder}/ultimate_crypto_pattern_analyzer.py", "console": "integratedTerminal", "justMyCode": true, "python": "D:\\ProgramData\\miniconda3\\envs\\tqsdk\\python.exe", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}, {"name": "测试分析器", "type": "python", "request": "launch", "program": "${workspaceFolder}/test_ultimate_analyzer.py", "console": "integratedTerminal", "justMyCode": true, "python": "D:\\ProgramData\\miniconda3\\envs\\tqsdk\\python.exe", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}, {"name": "简化版扫描器", "type": "python", "request": "launch", "program": "${workspaceFolder}/simple_crypto_scanner.py", "console": "integratedTerminal", "justMyCode": true, "python": "D:\\ProgramData\\miniconda3\\envs\\tqsdk\\python.exe", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}]}