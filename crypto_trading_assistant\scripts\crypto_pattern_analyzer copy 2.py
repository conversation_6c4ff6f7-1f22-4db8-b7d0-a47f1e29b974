"""
加密货币形态分析模块
专门用于分析新上市币种的放量上影线形态和反转特征
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import sys
import os
import time
from tqdm import tqdm
import requests
import ccxt
import argparse
import json
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich import box
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import asyncio
import aiohttp
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from tenacity import retry, stop_after_attempt, wait_exponential
from io import StringIO

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('crypto_pattern_analyzer.log', mode='w')
    ]
)

logger = logging.getLogger(__name__)

class PatternAnalyzer:
    def __init__(self, 
                 api_keys: Optional[Dict] = None,
                 use_proxy: bool = False,
                 request_delay: float = 1.0):
        """
        初始化模式分析器
        
        Args:
            api_keys: API密钥字典
            use_proxy: 是否使用代理
            request_delay: 请求延迟（秒）
        """
        self.logger = logging.getLogger(__name__)
        self.use_proxy = use_proxy
        self.request_delay = request_delay
        
        # 初始化交易所API
        self.exchange = ccxt.gateio({
            'enableRateLimit': True,
            'timeout': 30000,
            'apiKey': api_keys['apiKey'] if api_keys else None,
            'secret': api_keys['secret'] if api_keys else None,
            'options': {
                'defaultType': 'spot',
                'createMarketBuyOrderRequiresPrice': False,
                'fetchTradesMethod': 'publicGetTradeHistory',
                'recvWindow': 5000,
            }
        })
        
        # 初始化代理设置
        if use_proxy:
            self.exchange.proxies = {
                'http': 'http://127.0.0.1:7890',
                'https': 'http://127.0.0.1:7890'
            }
        
        # 初始化rich控制台
        self.console = Console()
        
        # 创建结果保存目录
        self.results_dir = Path("analysis_results")
        self.results_dir.mkdir(exist_ok=True)

    def _translate_to_chinese(self, text: str) -> str:
        """将英文状态转换为中文显示"""
        translations = {
            'downtrend': '下跌',
            'uptrend': '上涨',
            'sideways': '横盘',
            'low_risk': '低风险',
            'medium_risk': '中等风险',
            'high_risk': '高风险',
            'need_more_observation': '需要更多观察',
            'no_clear_pattern': '无明显形态',
            'doji': '十字星',
            'hammer': '锤子线',
            'shooting_star': '流星线',
            'engulfing': '吞没形态',
            'morning_star': '启明星',
            'evening_star': '黄昏星',
            'three_white_soldiers': '三白兵',
            'three_black_crows': '三乌鸦',
            'unknown': '未知'
        }
        return translations.get(text, text)

    def get_new_listings(self, max_listings: int = 0) -> Dict:
        """
        获取从60天前到今天的新上市币种信息
        
        Args:
            max_listings: 最大获取数量，大于0时获取到指定数量后立即返回，否则获取全部符合条件的币种
        
        Returns:
            新上市币种信息字典
        """
        try:
            self.logger.info("正在获取所有交易对信息...")
            # 获取所有交易对
            markets = self.exchange.load_markets()
            if not markets:
                self.logger.error("未能获取到交易对信息")
                return {}
            
            self.logger.info(f"成功获取{len(markets)}个交易对")
            
            # 获取新币上线时间
            new_listings = {}
            current_date = datetime.now()
            start_date = current_date - timedelta(days=60)  # 60天前的日期
            end_date = current_date
            
            self.logger.info(f"开始日期: {start_date.strftime('%Y-%m-%d')}")
            self.logger.info(f"结束日期: {end_date.strftime('%Y-%m-%d')}")
            
            # 只处理USDT交易对，并转换格式
            usdt_markets = {k: v for k, v in markets.items() if k.endswith('/USDT')}
            self.logger.info(f"找到{len(usdt_markets)}个USDT交易对")
            
            # 批量获取K线数据
            symbols = list(usdt_markets.keys())
            
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TaskProgressColumn(),
                console=self.console
            ) as progress:
                task = progress.add_task("获取K线数据...", total=len(symbols))
                
                for i in range(0, len(symbols), 10):  # 每10个交易对一批
                    batch = symbols[i:i+10]
                    batch_data = self.process_trading_pairs(batch)
                    
                    # 处理每个交易对的数据
                    for symbol, data in batch_data.items():
                        if data and 'analysis' in data:
                            market = usdt_markets[symbol]
                            listing_date = self._get_listing_date(market)
                            
                            if start_date <= listing_date <= end_date:
                                new_listings[symbol] = {
                                    'listing_date': listing_date.strftime('%Y-%m-%d'),
                                    'analysis': data['analysis']
                                }
                                
                                if max_listings > 0 and len(new_listings) >= max_listings:
                                    return new_listings
                    
                    progress.update(task, advance=len(batch))
                    time.sleep(1)  # 每批请求后等待1秒
            
            return new_listings
            
        except Exception as e:
            self.logger.error(f"获取新上市币种时发生错误: {str(e)}")
            import traceback
            self.logger.error(f"错误详情:\n{traceback.format_exc()}")
            return {}

    def generate_csv_report(self, new_listings: Dict) -> str:
        """生成CSV格式的分析报告"""
        try:
            # 准备数据
            data = []
            for symbol, info in new_listings.items():
                analysis = info['analysis']
                row = {
                    '交易对': symbol,
                    '上市日期': info['listing_date'],
                    '当前价格': analysis['current_price'],
                    '24h涨跌幅': f"{analysis['price_change_24h']:.2f}%",
                    '24h成交量变化': f"{analysis['volume_change_24h']:.2f}%",
                    'MA5趋势': self._translate_to_chinese(analysis['ma5_trend']),
                    'MA10趋势': self._translate_to_chinese(analysis['ma10_trend']),
                    '成交量趋势': self._translate_to_chinese(analysis['volume_trend']),
                    '波动率': f"{analysis['volatility']:.2f}%",
                    '上影线比例': f"{analysis['upper_shadow_ratio']:.2f}%",
                    '下影线比例': f"{analysis['lower_shadow_ratio']:.2f}%",
                    '实体大小': f"{analysis['body_size']:.2f}%",
                    'K线形态': self._translate_to_chinese(analysis['pattern']),
                    '风险等级': self._translate_to_chinese(analysis['risk_level']),
                    '交易建议': self._translate_to_chinese(analysis['trading_suggestion'])
                }
                data.append(row)
            
            # 创建DataFrame
            df = pd.DataFrame(data)
            
            # 生成CSV文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            csv_file = Path("analysis_results") / f"crypto_analysis_{timestamp}.csv"
            
            # 保存为CSV文件
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            
            self.logger.info(f"CSV报告已生成: {csv_file}")
            return str(csv_file)
            
        except Exception as e:
            self.logger.error(f"生成CSV报告失败: {str(e)}")
            return ""

    def format_summary_report(self, new_listings: Dict) -> str:
        """格式化汇总报告"""
        try:
            # 生成报告头部
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            report = f"# 加密货币新上市币种分析报告\n"
            report += f"分析时间: {timestamp}\n"
            report += f"分析币种数量: {len(new_listings)}\n\n"
            
            # 生成分析结果表格
            report += "## 分析结果\n\n"
            report += "| 交易对 | 上市日期 | 当前价格 | 24h涨跌幅 | K线形态 | 风险等级 | 交易建议 |\n"
            report += "|---------|----------|-----------|------------|----------|----------|-----------|\n"
            
            for symbol, info in new_listings.items():
                analysis = info['analysis']
                report += f"| {symbol} | {info['listing_date']} | {analysis['current_price']:.6f} | "
                report += f"{analysis['price_change_24h']:.2f}% | {self._translate_to_chinese(analysis['pattern'])} | "
                report += f"{self._translate_to_chinese(analysis['risk_level'])} | {self._translate_to_chinese(analysis['trading_suggestion'])} |\n"
            
            # 添加风险提示
            report += "\n## 风险提示\n"
            report += "1. 新上市币种波动较大，请谨慎投资\n"
            report += "2. 建议设置止损位，控制风险\n"
            report += "3. 关注项目基本面和市场情绪变化\n"
            report += "4. 杠杆代币风险较高，不建议长期持有\n\n"
            
            # 添加免责声明
            report += "## 免责声明\n"
            report += "本分析报告仅供参考，不构成投资建议。投资有风险，入市需谨慎。\n"
            
            return report
            
        except Exception as e:
            self.logger.error(f"格式化汇总报告失败: {str(e)}")
            return "生成报告失败，请检查日志了解详细信息。"

    def _determine_trend(self, data: pd.Series) -> str:
        """
        确定趋势方向
        :param data: 价格或成交量数据序列
        :return: 趋势方向（上涨/下跌/横盘）
        """
        if len(data) < 2:
            return "unknown"
        
        change = (data.iloc[-1] - data.iloc[-2]) / data.iloc[-2] * 100
        
        if change > 1:  # 涨幅超过1%视为上涨
            return "uptrend"
        elif change < -1:  # 跌幅超过1%视为下跌
            return "downtrend"
        else:
            return "sideways"

    def _identify_candlestick_pattern(self, candle: pd.Series) -> str:
        """
        识别K线形态
        :param candle: 包含OHLCV数据的Series
        :return: K线形态名称
        """
        try:
            open_price = float(candle['open'])
            high = float(candle['high'])
            low = float(candle['low'])
            close = float(candle['close'])
            
            body = abs(close - open_price)
            upper_shadow = high - max(open_price, close)
            lower_shadow = min(open_price, close) - low
            
            # 计算实体和影线的比例
            avg_price = (high + low) / 2
            body_ratio = body / avg_price
            upper_shadow_ratio = upper_shadow / avg_price
            lower_shadow_ratio = lower_shadow / avg_price
            
            # 判断K线形态
            if body_ratio < 0.001:  # 十字星
                return "doji"
            elif lower_shadow_ratio > 2 * body_ratio and upper_shadow_ratio < body_ratio:  # 锤子线
                return "hammer"
            elif upper_shadow_ratio > 2 * body_ratio and lower_shadow_ratio < body_ratio:  # 流星线
                return "shooting_star"
            elif body_ratio > 0.02:  # 实体较大
                if close > open_price:
                    return "bullish_body"
                else:
                    return "bearish_body"
            else:
                return "no_clear_pattern"
                
        except Exception as e:
            self.logger.error(f"识别K线形态时发生错误: {str(e)}")
            return "unknown"

    def _evaluate_risk(self, volatility: float, volume_change: float, price_change: float) -> str:
        """
        评估风险等级
        :param volatility: 波动率
        :param volume_change: 成交量变化
        :param price_change: 价格变化
        :return: 风险等级
        """
        try:
            # 根据多个指标综合评估风险
            risk_score = 0
            
            # 波动率风险评分
            if volatility > 20:
                risk_score += 3
            elif volatility > 10:
                risk_score += 2
            elif volatility > 5:
                risk_score += 1
                
            # 成交量变化风险评分
            if abs(volume_change) > 100:
                risk_score += 3
            elif abs(volume_change) > 50:
                risk_score += 2
            elif abs(volume_change) > 20:
                risk_score += 1
                
            # 价格变化风险评分
            if abs(price_change) > 10:
                risk_score += 3
            elif abs(price_change) > 5:
                risk_score += 2
            elif abs(price_change) > 2:
                risk_score += 1
                
            # 根据总分确定风险等级
            if risk_score >= 6:
                return "high_risk"
            elif risk_score >= 3:
                return "medium_risk"
            else:
                return "low_risk"
                
        except Exception as e:
            self.logger.error(f"评估风险等级时发生错误: {str(e)}")
            return "unknown"

    def _get_default_analysis(self) -> Dict:
        """获取默认分析结果"""
        return {
            "current_price": 0.0,
            "price_change_24h": 0.0,
            "volume_change_24h": 0.0,
            "ma5_trend": "unknown",
            "ma10_trend": "unknown",
            "volume_trend": "unknown",
            "volatility": 0.0,
            "upper_shadow_ratio": 0.0,
            "lower_shadow_ratio": 0.0,
            "body_size": 0.0,
            "pattern": "unknown",
            "risk_level": "unknown",
            "trading_suggestion": "need_more_observation"
        }

    def _get_listing_date(self, market: Dict) -> datetime:
        """获取币种上市日期"""
        try:
            # 如果有明确的上市日期信息，使用该信息
            if 'info' in market and 'created_at' in market['info']:
                timestamp = int(market['info']['created_at'])
                return datetime.fromtimestamp(timestamp)
                
            # 否则使用当前日期
            return datetime.now()
            
        except Exception as e:
            self.logger.error(f"获取上市日期时发生错误: {str(e)}")
            return datetime.now()

    def process_trading_pairs(self, symbols: List[str]) -> Dict:
        """
        批量处理交易对数据
        :param symbols: 交易对列表
        :return: 处理结果字典
        """
        try:
            results = {}
            for symbol in symbols:
                try:
                    # 获取K线数据
                    ohlcv = self.exchange.fetch_ohlcv(symbol, '1d', limit=10)
                    if not ohlcv:
                        continue
                        
                    # 转换为DataFrame
                    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    
                    # 分析K线形态
                    analysis = self.analyze_pattern(df)
                    
                    results[symbol] = {
                        'analysis': analysis
                    }
                    
                except Exception as e:
                    self.logger.error(f"处理{symbol}时发生错误: {str(e)}")
                    continue
                    
            return results
            
        except Exception as e:
            self.logger.error(f"批量处理交易对时发生错误: {str(e)}")
            return {}

    def analyze_pattern(self, df: pd.DataFrame) -> Dict:
        """
        分析K线形态
        :param df: 包含OHLCV数据的DataFrame
        :return: 分析结果
        """
        try:
            # 计算基本指标
            current_price = float(df['close'].iloc[-1])
            prev_price = float(df['close'].iloc[-2]) if len(df) > 1 else current_price
            current_volume = float(df['volume'].iloc[-1])
            prev_volume = float(df['volume'].iloc[-2]) if len(df) > 1 else current_volume
            
            # 计算价格和成交量变化
            price_change = ((current_price - prev_price) / prev_price * 100) if prev_price != 0 else 0
            volume_change = ((current_volume - prev_volume) / prev_volume * 100) if prev_volume != 0 else 0
            
            # 计算移动平均线
            if len(df) >= 5:
                df['MA5'] = df['close'].rolling(window=5, min_periods=1).mean()
                ma5_trend = self._determine_trend(df['MA5'].iloc[-2:])
            else:
                ma5_trend = "unknown"
            
            if len(df) >= 10:
                df['MA10'] = df['close'].rolling(window=10, min_periods=1).mean()
                ma10_trend = self._determine_trend(df['MA10'].iloc[-2:])
            else:
                ma10_trend = "unknown"
            
            # 计算成交量趋势
            volume_trend = self._determine_trend(df['volume'].iloc[-2:]) if len(df) > 1 else "unknown"
            
            # 计算波动率
            latest_candle = df.iloc[-1]
            volatility = (latest_candle['high'] - latest_candle['low']) / latest_candle['low'] * 100
            
            # 计算影线比例和实体大小
            body_size = abs(latest_candle['close'] - latest_candle['open'])
            upper_shadow = latest_candle['high'] - max(latest_candle['open'], latest_candle['close'])
            lower_shadow = min(latest_candle['open'], latest_candle['close']) - latest_candle['low']
            
            upper_shadow_ratio = (upper_shadow / latest_candle['close'] * 100) if latest_candle['close'] != 0 else 0
            lower_shadow_ratio = (lower_shadow / latest_candle['close'] * 100) if latest_candle['close'] != 0 else 0
            body_size_ratio = (body_size / latest_candle['close'] * 100) if latest_candle['close'] != 0 else 0
            
            # 识别K线形态
            pattern = self._identify_candlestick_pattern(latest_candle)
            
            # 评估风险等级
            risk_level = self._evaluate_risk(volatility, volume_change, price_change)
            
            # 生成交易建议
            if pattern in ["hammer", "bullish_body"] and price_change > 0 and volume_change > 0:
                trading_suggestion = "consider_buy"
            elif pattern in ["hanging_man", "bearish_body"] or (price_change < -5 and volume_change > 50):
                trading_suggestion = "wait_and_see"
            elif volatility > 15:
                trading_suggestion = "high_volatility_caution"
            else:
                trading_suggestion = "need_more_observation"
            
            # 格式化分析结果
            analysis_result = {
                "current_price": current_price,
                "price_change_24h": price_change,
                "volume_change_24h": volume_change,
                "ma5_trend": ma5_trend,
                "ma10_trend": ma10_trend,
                "volume_trend": volume_trend,
                "volatility": volatility,
                "upper_shadow_ratio": upper_shadow_ratio,
                "lower_shadow_ratio": lower_shadow_ratio,
                "body_size": body_size_ratio,
                "pattern": pattern,
                "risk_level": risk_level,
                "trading_suggestion": trading_suggestion
            }
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"分析K线形态时发生错误: {str(e)}")
            return self._get_default_analysis()

def test_real_data(max_listings: int = 0):
    """使用真实数据进行测试"""
    try:
        # 初始化分析器
        analyzer = PatternAnalyzer(
            api_keys=None,  # 暂时不使用API密钥
            use_proxy=False,  # 暂时不使用代理
            request_delay=1.0  # 请求延迟1秒
        )
        
        try:
            new_listings = analyzer.get_new_listings(max_listings=max_listings)
            
            if not new_listings:
                logger.warning("未找到新上市币种")
                return
            
            # 生成汇总报告
            summary_report = analyzer.format_summary_report(new_listings)
            
            # 打印汇总报告
            console = Console()
            console.print(summary_report)
            
            # 保存汇总结果到JSON
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            summary_file = Path("analysis_results") / f"summary_{timestamp}.json"
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(new_listings, f, ensure_ascii=False, indent=2)
            
            # 生成CSV报告
            csv_file = analyzer.generate_csv_report(new_listings)
            
            logger.info(f"\n分析完成，汇总结果已保存到:")
            logger.info(f"JSON文件: {summary_file}")
            logger.info(f"CSV文件: {csv_file}")
            
        except Exception as e:
            logger.error(f"获取新上市币种时发生错误: {str(e)}")
            import traceback
            logger.error(f"错误详情:\n{traceback.format_exc()}")
            
    except Exception as e:
        logger.error(f"程序运行出错: {str(e)}")
        logger.error(f"错误详情: {type(e).__name__}: {str(e)}")
        import traceback
        logger.error(f"堆栈跟踪:\n{traceback.format_exc()}")

if __name__ == "__main__":
    # 设置Windows控制台编码
    if sys.platform.startswith('win'):
        import subprocess
        subprocess.run(['chcp', '65001'], shell=True)
        os.system('cls')
    
    parser = argparse.ArgumentParser(description='加密货币形态分析')
    parser.add_argument('--max-listings', type=int, default=0, help='最大获取新上市币种数量，0表示获取全部')
    args = parser.parse_args()
    test_real_data(max_listings=args.max_listings) 