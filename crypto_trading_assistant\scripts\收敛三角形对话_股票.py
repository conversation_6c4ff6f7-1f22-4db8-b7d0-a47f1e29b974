# 自动安装依赖库
import sys
import subprocess

def auto_install(package, version=None):
    try:
        module = __import__(package)
        if version:
            current_version = module.__version__
            if current_version < version:
                print(f"Upgrading {package} to version {version}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", f"{package}>={version}"])
    except ImportError:
        print(f"Installing {package}...")
        if version:
            subprocess.check_call([sys.executable, "-m", "pip", "install", f"{package}>={version}"])
        else:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])

# 安装或升级依赖库
auto_install("pandas", "2.2.0")  # 确保pandas版本>=2.2.0
auto_install("akshare")
auto_install("numpy")
auto_install("scipy")
auto_install("matplotlib")
auto_install("requests")  # 添加requests库用于网络请求

import time
import akshare as ak
import pandas as pd
import numpy as np
from scipy.stats import linregress
import matplotlib.pyplot as plt  # 新增可视化导入

def detect_converging_triangle(df, window=250):
    """检测收敛三角形形态，支持250日时间窗口"""
    try:
        # 数据量检查
        if len(df) < window + 10:
            return False, ''
        
        # 计算高低点
        highs = df['high'].rolling(window=5).max()
        lows = df['low'].rolling(window=5).min()
        
        # 获取窗口内数据
        recent_highs = highs[-window:].dropna().values
        recent_lows = lows[-window:].dropna().values
        
        # 数据完整性检查
        if len(recent_highs) < window*0.8 or len(recent_lows) < window*0.8:
            return False, ''
        
        # 计算趋势线斜率
        x = np.arange(len(recent_highs))
        slope_high, _, _, _, _ = linregress(x, recent_highs)
        slope_low, _, _, _, _ = linregress(x, recent_lows)
        
        # 收敛条件判断（优化参数）
        convergence_ratio = (max(recent_highs) - min(recent_lows))/min(recent_lows)
        slope_threshold = 0.12  # 更严格的斜率阈值
        convergence_threshold = 0.12  # 更严格的收敛比例
        
        if (abs(slope_high) < slope_threshold and 
            abs(slope_low) < slope_threshold and 
            convergence_ratio < convergence_threshold):
            
            print(f"检测到潜在收敛形态: "
                 f"斜率(高/低)={slope_high:.3f}/{slope_low:.3f} "
                 f"收敛比例={convergence_ratio:.3f}")
            
            # 检查2周内是否有涨停板突破
            last_2weeks = df[-10:]  # 2周数据(假设5交易日/周)
            upper_trend = recent_highs[-1] + slope_high
            lower_trend = recent_lows[-1] + slope_low
            
            # 检查成交量突破
            avg_volume = df['volume'][-20:].mean()
            last_volume = df['volume'].iloc[-1]
            
            # 上涨突破条件
            if (df['close'].iloc[-1] > upper_trend * 1.01 and 
                any(last_2weeks['pct_chg'] >= 9.8) and  # 涨停板判断
                last_volume >= avg_volume * 1.2):  # 成交量突破
                return True, 'up'
                
            # 下跌突破条件
            elif (df['close'].iloc[-1] < lower_trend * 0.99 and 
                  last_volume >= avg_volume * 1.2):
                return True, 'down'
                
    except Exception as e:
        print(f"检测收敛三角形时出错: {str(e)}")
    return False, ''

def calculate_performance_metrics(equity_series, risk_free_rate=0.03):
    """计算增强版策略绩效指标"""
    returns = equity_series.pct_change().dropna()
    
    # 基础收益指标
    total_return = (equity_series.iloc[-1] / equity_series.iloc[0]) - 1
    annual_return = (1 + total_return) ** (252/len(equity_series)) - 1
    
    # 风险指标
    daily_returns_std = returns.std()
    annualized_vol = daily_returns_std * np.sqrt(252)
    max_drawdown = (equity_series.cummax() - equity_series).max() / equity_series.cummax().max()
    
    # 下行风险
    downside_returns = returns[returns < 0]
    downside_std = downside_returns.std() if len(downside_returns) > 0 else 0
    
    # 风险调整收益指标
    excess_returns = returns - risk_free_rate/252
    sharpe_ratio = np.sqrt(252) * excess_returns.mean() / returns.std() if returns.std() != 0 else 0
    sortino_ratio = np.sqrt(252) * returns.mean() / downside_std if downside_std != 0 else 0
    
    # 交易统计
    winning_trades = len(returns[returns > 0])
    total_trades = len(returns)
    win_rate = winning_trades / total_trades if total_trades > 0 else 0
    
    # 计算最大连续盈利和亏损次数
    profit_streak = 0
    loss_streak = 0
    max_profit_streak = 0
    max_loss_streak = 0
    current_streak = 0
    
    for ret in returns:
        if ret > 0:
            if current_streak > 0:
                current_streak += 1
            else:
                current_streak = 1
            max_profit_streak = max(max_profit_streak, current_streak)
        elif ret < 0:
            if current_streak < 0:
                current_streak -= 1
            else:
                current_streak = -1
            max_loss_streak = min(max_loss_streak, current_streak)
    
    return {
        'total_return': total_return,
        'annual_return': annual_return,
        'annualized_vol': annualized_vol,
        'max_drawdown': max_drawdown,
        'sharpe_ratio': sharpe_ratio,
        'sortino_ratio': sortino_ratio,
        'win_rate': win_rate,
        'max_profit_streak': max_profit_streak,
        'max_loss_streak': abs(max_loss_streak),
        'downside_risk': downside_std * np.sqrt(252),
        'profit_factor': abs(returns[returns > 0].sum() / returns[returns < 0].sum()) if len(returns[returns < 0]) > 0 else float('inf')
    }

def backtest_strategy(df, signals):
    """增强版策略回测"""
    initial_capital = 1000000  # 初始资金
    capital = initial_capital
    position = 0
    trades = []
    equity = [capital]
    stop_loss = 0.05  # 5%止损
    take_profit = 0.15  # 15%止盈
    commission = 0.0005  # 0.05%交易费用
    
    entry_price = 0
    max_profit = 0
    
    for i, row in df.iterrows():
        current_price = row['close']
        
        # 检查持仓状态下的止损止盈
        if position > 0:
            current_profit = (current_price - entry_price) / entry_price
            max_profit = max(max_profit, current_profit)
            
            # 止盈/止损检查
            if current_profit <= -stop_loss or current_profit >= take_profit:
                # 平仓
                capital = position * current_price * (1 - commission)
                position = 0
                trades.append(('sell', i, current_price, 'stop' if current_profit <= 0 else 'profit'))
        
        # 检查新信号
        if i in signals:
            signal = signals[i]
            if signal == 'buy' and position == 0:
                # 开仓
                position = (capital * 0.95) / current_price  # 保留5%现金
                capital -= position * current_price * (1 + commission)
                entry_price = current_price
                max_profit = 0
                trades.append(('buy', i, current_price, 'entry'))
                
            elif signal == 'sell' and position > 0:
                # 平仓
                capital += position * current_price * (1 - commission)
                position = 0
                trades.append(('sell', i, current_price, 'signal'))
        
        # 计算当前权益
        equity.append(capital + (position * current_price if position > 0 else 0))
    
    # 计算年化收益率时考虑交易费用
    equity_series = pd.Series(equity[1:], index=df.index)
    equity_series = equity_series * (1 - commission * len(trades)/2)  # 调整交易费用影响
    
    return equity_series, trades

def visualize_results(df, equity_curve, trades, symbol, signals):
    """专业版可视化结果"""
    plt.style.use('seaborn')
    plt.figure(figsize=(16, 12))
    
    # 创建子图布局
    ax1 = plt.subplot2grid((5, 1), (0, 0), rowspan=3)
    ax2 = plt.subplot2grid((5, 1), (3, 0), rowspan=1, sharex=ax1)
    ax3 = plt.subplot2grid((5, 1), (4, 0), rowspan=1, sharex=ax1)
    
    # 绘制价格和趋势线
    ax1.plot(df['close'], label='Price', color='royalblue', linewidth=1.5)
    
    # 标记收敛三角形区域
    for date, signal in signals.items():
        if signal == 'buy':
            ax1.axvspan(date - pd.Timedelta(days=30), date, 
                       facecolor='lightgreen', alpha=0.3, label='Bullish Zone')
        elif signal == 'sell':
            ax1.axvspan(date - pd.Timedelta(days=30), date,
                       facecolor='lightcoral', alpha=0.3, label='Bearish Zone')
    
    # 绘制资金曲线
    ax2.plot(equity_curve, label='Equity Curve', color='darkviolet', linewidth=2)
    
    # 标记交易点
    trade_colors = {
        'entry': 'lime',
        'signal': 'red',
        'stop': 'black',
        'profit': 'gold'
    }
    
    for trade in trades:
        action, date, price, trade_type = trade
        color = trade_colors.get(trade_type, 'blue')
        marker = '^' if action == 'buy' else 'v'
        
        ax1.scatter(date, price, color=color, marker=marker, 
                   s=120, label=f'{action.capitalize()} ({trade_type})')
        ax2.scatter(date, equity_curve.loc[date], color=color, 
                   marker=marker, s=120)
    
    # 绘制成交量
    ax3.bar(df.index, df['volume'], color='gray', alpha=0.7, width=0.8)
    
    # 设置图表属性
    ax1.set_title(f'Professional Analysis: {symbol}', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Price (¥)', fontsize=10)
    ax1.legend(loc='upper left', fontsize=8)
    ax1.grid(True, alpha=0.3)
    
    ax2.set_ylabel('Equity (¥)', fontsize=10)
    ax2.legend(loc='upper left', fontsize=8)
    ax2.grid(True, alpha=0.3)
    
    ax3.set_ylabel('Volume', fontsize=10)
    ax3.grid(True, alpha=0.3)
    
    # 添加交易统计信息
    plt.figtext(0.15, 0.92, 
               f"Total Trades: {len(trades)} | Final Equity: ¥{equity_curve[-1]:,.2f}",
               fontsize=10, bbox=dict(facecolor='white', alpha=0.5))
    
    plt.tight_layout()
    plt.savefig(f'professional_backtest_{symbol}.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 绘制成交量
    ax2.bar(df.index, df['volume'], color='gray', alpha=0.5)
    ax2.axhline(y=df['volume'][-20:].mean()*1.2, 
               color='red', linestyle='--', linewidth=1)
    
    # 设置图表属性
    ax1.set_title(f'Enhanced Analysis for {symbol}', fontsize=14)
    ax1.set_ylabel('Price/Equity')
    ax1.legend(loc='upper left')
    ax1.grid(True, alpha=0.3)
    
    ax2.set_ylabel('Volume')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'enhanced_backtest_{symbol}.png', dpi=300)
    plt.close()
    
def get_stock_data(symbol, max_retries=3, retry_delay=5):
    """增强版股票数据获取函数"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    # 设置akshare的全局参数
    import akshare as ak
    ak.set_us_proxy(enable_proxy=False)  # 禁用代理
    
    for attempt in range(max_retries):
        try:
            # 首先尝试使用新浪数据源
            try:
                df = ak.stock_zh_a_daily(symbol=symbol, adjust="hfq")
            except Exception as e1:
                print(f"新浪数据源获取失败，尝试其他数据源: {str(e1)}")
                # 尝试使用腾讯数据源
                df = ak.stock_zh_a_hist(symbol=symbol, period="daily", adjust="hfq")
            
            if df is None or len(df) < 250:
                print(f"数据量不足: {symbol}")
                return None
            
            # 统一列名处理
            column_mappings = {
                '日期': 'date', '时间': 'date', 'date': 'date',
                '开盘': 'open', '开盘价': 'open', 'open': 'open',
                '最高': 'high', '最高价': 'high', 'high': 'high',
                '最低': 'low', '最低价': 'low', 'low': 'low',
                '收盘': 'close', '收盘价': 'close', 'close': 'close',
                '成交量': 'volume', '成交额': 'volume', 'volume': 'volume'
            }
            
            # 重命名列
            df.columns = [column_mappings.get(col, col) for col in df.columns]
            
            # 确保必需列存在
            required_cols = ['date', 'open', 'high', 'low', 'close', 'volume']
            if not all(col in df.columns for col in required_cols):
                print(f"缺少必需列: {symbol}")
                return None
            
            # 数据清洗和格式转换
            try:
                df['date'] = pd.to_datetime(df['date'])
                df = df.set_index('date')
                
                # 确保数值列为数值类型
                numeric_cols = ['open', 'high', 'low', 'close', 'volume']
                for col in numeric_cols:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                
                # 删除包含NaN的行
                df = df.dropna()
                
                # 按日期排序
                df = df.sort_index()
                
                # 添加涨跌幅
                df['pct_chg'] = df['close'].pct_change() * 100
                
                return df
                
            except Exception as e:
                print(f"数据处理失败: {symbol}, 错误: {str(e)}")
                return None
                
        except Exception as e:
            print(f"获取{symbol}数据失败(尝试{attempt+1}/{max_retries}): {str(e)}")
            if attempt == max_retries - 1:
                return None
            time.sleep(retry_delay)  # 增加重试等待时间
            retry_delay *= 2  # 指数退避

def main():
    print("=== 收敛三角形选股策略分析系统 ===")
    print("正在初始化...")
    
    # 创建日志文件
    with open('strategy_log.txt', 'w', encoding='utf-8') as log_file:
        log_file.write(f"策略执行开始时间: {pd.Timestamp.now()}\n")
    
    # 获取所有A股代码（带重试）
    max_attempts = 3
    stock_list = []
    for attempt in range(max_attempts):
        try:
            print("正在获取股票列表...")
            stock_list = ak.stock_zh_a_spot_em()['代码'].tolist()
            print(f"成功获取{len(stock_list)}只股票的代码")
            break
        except Exception as e:
            print(f"获取股票列表失败(尝试{attempt+1}/{max_attempts}): {str(e)}")
            if attempt == max_attempts - 1:
                print("无法获取股票列表，程序退出")
                return
            time.sleep(5)
    
    selected_stocks = []
    total_stocks = len(stock_list)
    
    print("\n开始分析股票...")
    print("=" * 50)
    
    # 初始化统计数据
    processed = 0
    errors = 0
    data_insufficient = 0
    start_time = time.time()
    
    for code in stock_list:
        processed += 1
        
        # 显示进度
        if processed % 10 == 0 or processed == total_stocks:
            elapsed_time = time.time() - start_time
            avg_time_per_stock = elapsed_time / processed
            remaining_stocks = total_stocks - processed
            estimated_remaining_time = remaining_stocks * avg_time_per_stock
            
            print(f"\r进度: {processed}/{total_stocks} ({processed/total_stocks*100:.1f}%) "
                  f"预计剩余时间: {estimated_remaining_time/60:.1f}分钟", end="")
        
        try:
            market = 'sh' if code.startswith('6') else 'sz'
            symbol = f"{market}{code}"
            
            # 获取数据
            df = get_stock_data(symbol)
            if df is None:
                data_insufficient += 1
                with open('strategy_log.txt', 'a', encoding='utf-8') as log_file:
                    log_file.write(f"数据不足: {code}\n")
                continue
            
            # 检测收敛三角形
            is_converging, direction = detect_converging_triangle(df, window=20)
            if is_converging and direction in ['up', 'down']:
                selected_stocks.append((code, direction))
                print(f"\n检测到信号: {code} {direction}")
                with open('strategy_log.txt', 'a', encoding='utf-8') as log_file:
                    log_file.write(f"发现信号: {code} {direction}\n")
        
        except Exception as e:
            errors += 1
            with open('strategy_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"处理{code}时出错: {str(e)}\n")
        
        # 定期清理内存
        if processed % 100 == 0:
            import gc
            gc.collect()
            
    # 计算并显示详细统计信息
    total_time = time.time() - start_time
    success_rate = (processed - errors - data_insufficient) / processed * 100
    
    print("\n\n=== 执行统计 ===")
    print(f"总耗时: {total_time/60:.1f}分钟")
    print(f"处理股票数: {processed}")
    print(f"发现信号数: {len(selected_stocks)}")
    print(f"数据不足数: {data_insufficient}")
    print(f"错误数量: {errors}")
    print(f"成功率: {success_rate:.1f}%")
    print("=" * 50)
    
    # 记录统计信息到日志
    with open('strategy_log.txt', 'a', encoding='utf-8') as log_file:
        log_file.write("\n=== 执行统计 ===\n")
        log_file.write(f"执行结束时间: {pd.Timestamp.now()}\n")
        log_file.write(f"总耗时: {total_time/60:.1f}分钟\n")
        log_file.write(f"处理股票数: {processed}\n")
        log_file.write(f"发现信号数: {len(selected_stocks)}\n")
        log_file.write(f"数据不足数: {data_insufficient}\n")
        log_file.write(f"错误数量: {errors}\n")
        log_file.write(f"成功率: {success_rate:.1f}%\n")
        log_file.write("=" * 50 + "\n")
    
    # 保存选股结果
    with open('selected_stocks.txt', 'w', encoding='utf-8') as f:
        for stock, direction in selected_stocks:
            f.write(f"{stock}, {direction}\n")
    
    # 对选中的股票进行回测
    backtest_results = []
    for code, direction in selected_stocks:
        try:
            market = 'sh' if code.startswith('6') else 'sz'
            symbol = f"{market}{code}"
            
            # 获取完整历史数据用于回测
            df = ak.stock_zh_a_daily(symbol=symbol, adjust="")
            df.columns = [col.replace('最高', 'high').replace('最低', 'low').replace('收盘', 'close') 
                         for col in df.columns]
            
            # 生成交易信号 (增强版)
            signals = {}
            last_signal = None
            for i in range(len(df)-250, len(df)):
                sub_df = df.iloc[:i+1]
                is_converging, direction = detect_converging_triangle(sub_df)
                if is_converging:
                    signal_type = 'buy' if direction == 'up' else 'sell'
                    # 避免重复信号
                    if last_signal != signal_type:
                        signals[sub_df.index[-1]] = signal_type
                        last_signal = signal_type
            
            # 执行回测
            equity_curve, trades = backtest_strategy(df, signals)
            metrics = calculate_performance_metrics(equity_curve)
            
            # 分析交易类型
            profit_trades = sum(1 for t in trades if t[3] == 'profit')
            stop_trades = sum(1 for t in trades if t[3] == 'stop')
            signal_trades = sum(1 for t in trades if t[3] == 'signal')
            
            # 保存详细回测结果
            backtest_results.append({
                'symbol': code,
                'metrics': metrics,
                'trades': len(trades),
                'profit_trades': profit_trades,
                'stop_trades': stop_trades,
                'signal_trades': signal_trades,
                'entry_price': trades[0][2] if trades else None,
                'exit_price': trades[-1][2] if trades else None
            })
            
            # 生成增强版可视化图表
            visualize_results(df, equity_curve, trades, code, signals)
            
            # 打印调试信息
            print(f"完成{code}的回测分析")
            print(f"年化收益率: {metrics['annual_return']:.2%}")
            print(f"最大回撤: {metrics['max_drawdown']:.2%}")
            print(f"夏普比率: {metrics['sharpe_ratio']:.2f}")
            print(f"交易次数: {len(trades)}")
            
        except Exception as e:
            print(f"回测{code}时出错: {str(e)}")
    
    # 生成增强版专业商业报告
    with open('professional_backtest_report.html', 'w', encoding='utf-8') as f:
        # 计算平均指标
        avg_metrics = {
            'total_return': np.mean([r['metrics']['total_return'] for r in backtest_results]),
            'annual_return': np.mean([r['metrics']['annual_return'] for r in backtest_results]),
            'annualized_vol': np.mean([r['metrics']['annualized_vol'] for r in backtest_results]),
            'max_drawdown': np.mean([r['metrics']['max_drawdown'] for r in backtest_results]),
            'sharpe_ratio': np.mean([r['metrics']['sharpe_ratio'] for r in backtest_results]),
            'sortino_ratio': np.mean([r['metrics']['sortino_ratio'] for r in backtest_results]),
            'win_rate': np.mean([r['metrics']['win_rate'] for r in backtest_results]),
            'profit_factor': np.mean([r['metrics']['profit_factor'] for r in backtest_results]),
            'downside_risk': np.mean([r['metrics']['downside_risk'] for r in backtest_results])
        }

        # 评级函数
        def get_rating(value, metric_type):
            if metric_type == 'return':
                return ('excellent', 'A+') if value > 0.3 else \
                       ('very-good', 'A') if value > 0.2 else \
                       ('good', 'B+') if value > 0.1 else \
                       ('normal', 'B') if value > 0 else \
                       ('bad', 'C')
            elif metric_type == 'risk':
                return ('excellent', 'A+') if value < 0.1 else \
                       ('very-good', 'A') if value < 0.15 else \
                       ('good', 'B+') if value < 0.2 else \
                       ('normal', 'B') if value < 0.25 else \
                       ('bad', 'C')
            elif metric_type == 'ratio':
                return ('excellent', 'A+') if value > 2 else \
                       ('very-good', 'A') if value > 1.5 else \
                       ('good', 'B+') if value > 1 else \
                       ('normal', 'B') if value > 0.5 else \
                       ('bad', 'C')
            return ('normal', '-')

        f.write(f"""
        <html>
        <head>
            <title>收敛三角形策略专业分析报告</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
                .container { max-width: 1200px; margin: 0 auto; background-color: white; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
                h1, h2 { color: #333366; border-bottom: 2px solid #eee; padding-bottom: 10px; }
                table { border-collapse: collapse; width: 100%; margin: 20px 0; background-color: white; }
                th, td { border: 1px solid #ddd; padding: 12px; text-align: center; }
                th { background-color: #f8f9fa; color: #333; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                .good { color: #28a745; font-weight: bold; }
                .bad { color: #dc3545; font-weight: bold; }
                .warning { color: #ffc107; font-weight: bold; }
                .stats-card { 
                    background-color: #fff; 
                    border-radius: 5px; 
                    padding: 15px; 
                    margin: 10px 0; 
                    box-shadow: 0 2px 4px rgba(0,0,0,0.05); 
                }
                
                .metrics-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 20px;
                    margin: 20px 0;
                }
                
                .metrics-section {
                    background: white;
                    border-radius: 8px;
                    padding: 15px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                }
                
                .metrics-section h3 {
                    color: #333366;
                    margin-top: 0;
                    padding-bottom: 10px;
                    border-bottom: 2px solid #eee;
                }
                
                .metrics-section table {
                    margin: 10px 0;
                    width: 100%;
                }
                
                .metrics-section th {
                    background-color: #f8f9fa;
                    font-weight: bold;
                    padding: 8px;
                }
                
                .metrics-section td {
                    padding: 8px;
                }
                
                .chart-container { 
                    margin: 20px 0;
                    background: white;
                    padding: 15px;
                    border-radius: 8px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                }
                
                .summary { 
                    background-color: #e9ecef; 
                    padding: 20px;
                    border-radius: 8px;
                    margin: 20px 0;
                }
                
                @media (max-width: 768px) {
                    .metrics-grid {
                        grid-template-columns: 1fr;
                    }
                    
                    .container {
                        padding: 10px;
                    }
                    
                    table {
                        font-size: 14px;
                    }
                }
                
                /* 评级样式 */
                .excellent { color: #28a745; font-weight: bold; }
                .very-good { color: #5cb85c; font-weight: bold; }
                .normal { color: #777; }
                .risky { color: #f0ad4e; }
                .high-risk { color: #d9534f; }
                
                /* 风险等级样式 */
                .低 { color: #28a745; }
                .中 { color: #ffc107; }
                .高 { color: #dc3545; }
                
                /* 说明部分样式 */
                .rating-guide {
                    background-color: #f8f9fa;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 20px 0;
                }
                .risk-guide {
                    background-color: #f8f9fa;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 20px 0;
                }
                .footnote {
                    font-size: 0.9em;
                    color: #666;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #eee;
                }
            </style>
        </head>
        <body>
        <div class="container">
        <div class="container">
            <h1>收敛三角形策略专业分析报告</h1>
            
            <div class="summary">
                <h2>策略概览</h2>
                <p>分析时间: {}</p>
                <p>分析股票数量: {}</p>
                <p>策略参数:</p>
                <ul>
                    <li>时间窗口: 250日</li>
                    <li>止损设置: 5%</li>
                    <li>止盈设置: 15%</li>
                    <li>交易费用: 0.05%</li>
                </ul>
            </div>

            <div class="stats-card">
                <h2>策略整体表现</h2>
            <div class="performance-metrics">
                <h2>策略整体表现分析</h2>
                <div class="metrics-grid">
                    <div class="metrics-section">
                        <h3>收益指标</h3>
                        <table>
                            <tr>
                                <th>指标</th>
                                <th>数值</th>
                                <th>评级</th>
                            </tr>
                            <tr>
                                <td>总收益率</td>
                                <td class="{}">{:.2%}</td>
                                <td>{}</td>
                            </tr>
                            <tr>
                                <td>年化收益率</td>
                                <td class="{}">{:.2%}</td>
                                <td>{}</td>
                            </tr>
                            <tr>
                                <td>年化波动率</td>
                                <td>{:.2%}</td>
                                <td>-</td>
                            </tr>
                        </table>
                    </div>

                    <div class="metrics-section">
                        <h3>风险指标</h3>
                        <table>
                            <tr>
                                <th>指标</th>
                                <th>数值</th>
                                <th>评级</th>
                            </tr>
                            <tr>
                                <td>最大回撤</td>
                                <td class="{}">{:.2%}</td>
                                <td>{}</td>
                            </tr>
                            <tr>
                                <td>下行风险</td>
                                <td>{:.2%}</td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>收益回撤比</td>
                                <td>{:.2f}</td>
                                <td>-</td>
                            </tr>
                        </table>
                    </div>

                    <div class="metrics-section">
                        <h3>风险调整收益</h3>
                        <table>
                            <tr>
                                <th>指标</th>
                                <th>数值</th>
                                <th>评级</th>
                            </tr>
                            <tr>
                                <td>夏普比率</td>
                                <td class="{}">{:.2f}</td>
                                <td>{}</td>
                            </tr>
                            <tr>
                                <td>索提诺比率</td>
                                <td>{:.2f}</td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>盈亏比</td>
                                <td>{:.2f}</td>
                                <td>-</td>
                            </tr>
                        </table>
                    </div>

                    <div class="metrics-section">
                        <h3>交易统计</h3>
                        <table>
                            <tr>
                                <th>指标</th>
                                <th>数值</th>
                                <th>说明</th>
                            </tr>
                            <tr>
                                <td>胜率</td>
                                <td class="{}">{:.2%}</td>
                                <td>盈利交易占比</td>
                            </tr>
                            <tr>
                                <td>最大连续盈利</td>
                                <td>{}</td>
                                <td>连续盈利次数</td>
                            </tr>
                            <tr>
                                <td>最大连续亏损</td>
                                <td>{}</td>
                                <td>连续亏损次数</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <div class="risk-metrics">
                <h2>风险分析</h2>
                <table>
                    <tr>
                        <th>风险指标</th>
                        <th>数值</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>波动率</td>
                        <td>{:.2%}</td>
                        <td>收益率的标准差</td>
                    </tr>
                    <tr>
                        <td>信息比率</td>
                        <td>{:.2f}</td>
                        <td>超额收益与跟踪误差的比值</td>
                    </tr>
                    <tr>
                        <td>索提诺比率</td>
                        <td>{:.2f}</td>
                        <td>考虑下行风险的收益指标</td>
                    </tr>
                </table>
            </div>
            <div class="stock-analysis">
                <h2>个股分析明细</h2>
                <table>
                    <tr>
                        <th>股票代码</th>
                        <th>综合评级</th>
                        <th>年化收益率</th>
                        <th>最大回撤</th>
                        <th>夏普比率</th>
                        <th>胜率</th>
                        <th>交易统计</th>
                        <th>风险评估</th>
                        <th>建议操作</th>
                    </tr>
        """.format(
            pd.Timestamp.now().strftime('%Y-%m-%d %H:%M'),
            len(backtest_results),
            np.mean([r['metrics']['annual_return'] for r in backtest_results]),
            np.mean([r['metrics']['max_drawdown'] for r in backtest_results]),
            np.mean([r['metrics']['sharpe_ratio'] for r in backtest_results]),
            np.mean([r['metrics']['win_rate'] for r in backtest_results]),
            sum([r['trades'] for r in backtest_results])
        ))
        
        for result in backtest_results:
            m = result['metrics']
            # 根据表现设置颜色
            ret_class = "good" if m['annual_return'] > 0 else "bad"
            dd_class = "good" if m['max_drawdown'] < 0.1 else "bad"
            
            # 计算综合评级
            rating_score = (
                (1 if m['annual_return'] > 0.2 else 0.5 if m['annual_return'] > 0 else 0) +
                (1 if m['max_drawdown'] < 0.1 else 0.5 if m['max_drawdown'] < 0.2 else 0) +
                (1 if m['sharpe_ratio'] > 2 else 0.5 if m['sharpe_ratio'] > 1 else 0) +
                (1 if m['win_rate'] > 0.6 else 0.5 if m['win_rate'] > 0.5 else 0)
            ) / 4 * 5  # 转换为5分制

            # 生成评级标签
            rating_label = {
                4.5: ('A+', 'excellent'),
                4.0: ('A', 'very-good'),
                3.5: ('B+', 'good'),
                3.0: ('B', 'normal'),
                2.5: ('C+', 'warning'),
                2.0: ('C', 'risky'),
                0.0: ('D', 'high-risk')
            }
            rating = next((v for k, v in rating_label.items() if rating_score >= k), ('D', 'high-risk'))

            # 生成交易统计
            trade_stats = f"""
                总交易: {result['trades']}<br>
                止盈: {result.get('profit_trades', 0)}<br>
                止损: {result.get('stop_trades', 0)}<br>
                信号: {result.get('signal_trades', 0)}
            """

            # 生成风险评估
            risk_level = '低风险' if m['max_drawdown'] < 0.1 and m['sharpe_ratio'] > 2 else \
                        '中风险' if m['max_drawdown'] < 0.2 and m['sharpe_ratio'] > 1 else '高风险'
            
            # 生成操作建议
            if rating_score >= 4.0:
                advice = '建议关注'
                advice_class = 'good'
            elif rating_score >= 3.0:
                advice = '谨慎参与'
                advice_class = 'warning'
            else:
                advice = '暂不参与'
                advice_class = 'bad'

            f.write(f"""
                <tr>
                    <td>{result['symbol']}</td>
                    <td class="{rating[1]}">{rating[0]}</td>
                    <td class="{ret_class}">{m['annual_return']:.2%}</td>
                    <td class="{dd_class}">{m['max_drawdown']:.2%}</td>
                    <td>{m['sharpe_ratio']:.2f}</td>
                    <td>{m['win_rate']:.2%}</td>
                    <td>{trade_stats}</td>
                    <td class="{risk_level.replace('风险', '')}">{risk_level}</td>
                    <td class="{advice_class}">{advice}</td>
                </tr>
            """)
        
        f.write("""
            </table>
            
            <div class="rating-guide">
                <h2>评级说明</h2>
                <table>
                    <tr>
                        <th>评级</th>
                        <th>说明</th>
                        <th>建议</th>
                    </tr>
                    <tr>
                        <td class="excellent">A+</td>
                        <td>优秀表现，各项指标均达到优秀水平</td>
                        <td>重点关注</td>
                    </tr>
                    <tr>
                        <td class="very-good">A</td>
                        <td>表现良好，主要指标表现优秀</td>
                        <td>建议关注</td>
                    </tr>
                    <tr>
                        <td class="good">B+</td>
                        <td>表现稳定，具有一定投资价值</td>
                        <td>可以关注</td>
                    </tr>
                    <tr>
                        <td class="normal">B</td>
                        <td>表现一般，需要进一步观察</td>
                        <td>谨慎参与</td>
                    </tr>
                    <tr>
                        <td class="risky">C</td>
                        <td>表现欠佳，存在较大风险</td>
                        <td>暂不参与</td>
                    </tr>
                    <tr>
                        <td class="high-risk">D</td>
                        <td>表现不佳，风险较高</td>
                        <td>不建议参与</td>
                    </tr>
                </table>
            </div>

            <div class="risk-guide">
                <h2>风险等级说明</h2>
                <table>
                    <tr>
                        <th>风险等级</th>
                        <th>特征</th>
                        <th>建议</th>
                    </tr>
                    <tr>
                        <td class="低">低风险</td>
                        <td>最大回撤较小，夏普比率较高</td>
                        <td>适合稳健型投资者</td>
                    </tr>
                    <tr>
                        <td class="中">中风险</td>
                        <td>最大回撤和夏普比率处于中等水平</td>
                        <td>适合平衡型投资者</td>
                    </tr>
                    <tr>
                        <td class="高">高风险</td>
                        <td>最大回撤较大或夏普比率较低</td>
                        <td>适合进取型投资者</td>
                    </tr>
                </table>
            </div>

            <div class="footnote">
                <h2>重要说明</h2>
                <p>1. 本报告基于历史数据分析，不构成投资建议。过往表现不代表未来收益。</p>
                <p>2. 投资者应根据自身风险承受能力和投资目标做出投资决策。</p>
                <p>3. 策略参数可能需要根据市场情况进行调整。</p>
                <p>4. 建议在实盘交易前进行充分的模拟交易测试。</p>
                <p>报告生成时间：{}</p>
            </div>
            </div>
            </body>
            </html>
        """)

if __name__ == '__main__':
    main()