#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import unittest
from datetime import datetime, timedelta

# 添加项目根目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.join(current_dir, '..'))

# 导入要测试的脚本
from get_new_crypto import NewCryptocurrencyFinder


class TestNewCryptocurrencyFinder(unittest.TestCase):
    """
    测试新币列表获取功能
    """
    
    def setUp(self):
        """
        测试前的准备工作
        """
        self.finder = NewCryptocurrencyFinder(days=7)  # 使用7天进行测试以减少API调用
    
    def test_get_all_coins(self):
        """
        测试获取所有币种信息功能
        """
        try:
            all_coins = self.finder.get_all_coins()
            self.assertIsNotNone(all_coins)
            self.assertGreater(len(all_coins), 0)
            
            # 检查必要的字段是否存在
            required_fields = ['id', 'symbol', 'name', 'current_price']
            for field in required_fields:
                self.assertIn(field, all_coins.columns)
                
            print(f"成功获取到 {len(all_coins)} 个币种的信息")
        except Exception as e:
            self.fail(f"获取所有币种信息失败: {e}")
    
    def test_get_new_listings(self):
        """
        测试获取新上市币种功能
        """
        try:
            new_coins = self.finder.get_new_listings()
            
            # 新币列表可能为空，这是正常的，所以我们不进行非空断言
            print(f"找到 {len(new_coins) if new_coins is not None and not new_coins.empty else 0} 个最近上市的新币")
            
            # 如果有新币，检查日期是否在预期范围内
            if new_coins is not None and not new_coins.empty and 'genesis_date' in new_coins.columns:
                non_null_dates = new_coins['genesis_date'].dropna()
                if len(non_null_dates) > 0:
                    latest_date = non_null_dates.max()
                    earliest_date = non_null_dates.min()
                    current_date = datetime.now()
                    days_ago = current_date - timedelta(days=self.finder.days)
                    
                    # 检查最新日期是否不超过当前日期
                    self.assertLessEqual(latest_date, current_date)
                    
                    # 日期应该在指定范围内，但由于有些币种可能是通过其他方式判断为新币的，所以这不是必须的
                    print(f"最早创建日期: {earliest_date}, 最新创建日期: {latest_date}")
                    print(f"查询范围: {days_ago} 到 {current_date}")
        except Exception as e:
            self.fail(f"获取新上市币种失败: {e}")


if __name__ == "__main__":
    unittest.main() 