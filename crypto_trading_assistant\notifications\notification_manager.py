"""
通知管理器
统一管理各种消息推送渠道
"""

import asyncio
from typing import Dict, List, Optional
from loguru import logger
import pandas as pd

from .message_sender import WeChatBot, DingTalkBot, ChartGenerator, MessageFormatter
from ..analysis.signal_generator import TradingSignal


class NotificationManager:
    """通知管理器"""
    
    def __init__(self, config: Dict):
        """
        初始化通知管理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.notification_config = config.get('notifications', {})
        
        # 初始化各种推送渠道
        self.wechat_bot = None
        self.dingtalk_bot = None
        
        self._init_bots()
        
    def _init_bots(self):
        """初始化机器人"""
        try:
            # 初始化企业微信机器人
            wechat_config = self.notification_config.get('enterprise_wechat', {})
            if wechat_config.get('enabled', False) and wechat_config.get('webhook_url'):
                self.wechat_bot = WeChatBot(
                    webhook_url=wechat_config['webhook_url'],
                    secret=wechat_config.get('secret', '')
                )
                logger.info("企业微信机器人初始化成功")
            
            # 初始化钉钉机器人
            dingtalk_config = self.notification_config.get('dingtalk', {})
            if dingtalk_config.get('enabled', False) and dingtalk_config.get('webhook_url'):
                self.dingtalk_bot = DingTalkBot(
                    webhook_url=dingtalk_config['webhook_url'],
                    secret=dingtalk_config.get('secret', '')
                )
                logger.info("钉钉机器人初始化成功")
                
        except Exception as e:
            logger.error(f"初始化机器人时出错: {e}")
    
    async def send_signal_notification(self, signal: TradingSignal, market_data: pd.DataFrame = None):
        """
        发送交易信号通知
        
        Args:
            signal: 交易信号
            market_data: 市场数据（用于生成图表）
        """
        try:
            # 生成图表
            chart_data = None
            if (self.notification_config.get('message_template', {}).get('include_chart', False) 
                and market_data is not None):
                chart_data = ChartGenerator.generate_candlestick_chart(
                    market_data, 
                    signal.symbol, 
                    signal.timeframe, 
                    signal
                )
            
            # 格式化消息
            message = MessageFormatter.format_signal_message(
                signal, 
                include_chart=chart_data is not None,
                chart_data=chart_data
            )
            
            # 并发发送到各个渠道
            tasks = []
            
            if self.wechat_bot:
                tasks.append(self._send_wechat_notification(message, signal))
            
            if self.dingtalk_bot:
                tasks.append(self._send_dingtalk_notification(message, signal))
            
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)
                success_count = sum(1 for result in results if result is True)
                logger.info(f"信号通知发送完成: {success_count}/{len(tasks)} 个渠道成功")
            else:
                logger.warning("没有可用的通知渠道")
                
        except Exception as e:
            logger.error(f"发送信号通知时出错: {e}")
    
    async def _send_wechat_notification(self, message: str, signal: TradingSignal) -> bool:
        """发送企业微信通知"""
        try:
            wechat_config = self.notification_config.get('enterprise_wechat', {})
            mentioned_list = wechat_config.get('mentioned_list', [])
            mentioned_mobile_list = wechat_config.get('mentioned_mobile_list', [])
            
            # 根据信号强度决定是否@所有人
            if signal.strength.value in ['strong', 'very_strong']:
                if '@all' not in mentioned_list:
                    mentioned_list.append('@all')
            
            return self.wechat_bot.send_markdown(message)
            
        except Exception as e:
            logger.error(f"发送企业微信通知时出错: {e}")
            return False
    
    async def _send_dingtalk_notification(self, message: str, signal: TradingSignal) -> bool:
        """发送钉钉通知"""
        try:
            dingtalk_config = self.notification_config.get('dingtalk', {})
            at_mobiles = dingtalk_config.get('at_mobiles', [])
            at_all = dingtalk_config.get('at_all', False)
            
            # 根据信号强度决定是否@所有人
            if signal.strength.value in ['strong', 'very_strong']:
                at_all = True
            
            title = f"🚀 {signal.symbol} 交易信号"
            return self.dingtalk_bot.send_markdown(title, message, at_mobiles, at_all)
            
        except Exception as e:
            logger.error(f"发送钉钉通知时出错: {e}")
            return False
    
    async def send_market_summary(self, summary_data: Dict):
        """
        发送市场概况
        
        Args:
            summary_data: 市场概况数据
        """
        try:
            message = self._format_market_summary(summary_data)
            
            tasks = []
            if self.wechat_bot:
                tasks.append(asyncio.create_task(
                    asyncio.to_thread(self.wechat_bot.send_markdown, message)
                ))
            
            if self.dingtalk_bot:
                tasks.append(asyncio.create_task(
                    asyncio.to_thread(self.dingtalk_bot.send_markdown, "📊 市场概况", message)
                ))
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
                
        except Exception as e:
            logger.error(f"发送市场概况时出错: {e}")
    
    def _format_market_summary(self, summary_data: Dict) -> str:
        """格式化市场概况消息"""
        try:
            message = "📊 **数字货币市场概况**\n\n"
            
            # 总体统计
            if 'total_signals' in summary_data:
                message += f"🔍 **今日信号统计**:\n"
                message += f"- 总信号数: {summary_data['total_signals']}\n"
                message += f"- 买入信号: {summary_data.get('buy_signals', 0)}\n"
                message += f"- 强信号数: {summary_data.get('strong_signals', 0)}\n\n"
            
            # 热门币种
            if 'hot_symbols' in summary_data:
                message += f"🔥 **热门币种**:\n"
                for symbol, data in summary_data['hot_symbols'].items():
                    change = data.get('change_24h', 0)
                    emoji = "📈" if change > 0 else "📉"
                    message += f"{emoji} {symbol}: {change:+.2%}\n"
                message += "\n"
            
            # 市场情绪
            if 'market_sentiment' in summary_data:
                sentiment = summary_data['market_sentiment']
                emoji = "😊" if sentiment > 0.6 else "😐" if sentiment > 0.4 else "😟"
                message += f"{emoji} **市场情绪**: {sentiment:.1%}\n\n"
            
            message += f"⏰ **更新时间**: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            return message
            
        except Exception as e:
            logger.error(f"格式化市场概况消息时出错: {e}")
            return "市场概况数据处理出错"
    
    async def send_error_notification(self, error_message: str, error_type: str = "系统错误"):
        """
        发送错误通知
        
        Args:
            error_message: 错误消息
            error_type: 错误类型
        """
        try:
            message = f"""
⚠️ **{error_type}**

**错误信息**: {error_message}

**时间**: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}

请及时检查系统状态！
"""
            
            # 只发送给管理员
            if self.wechat_bot:
                await asyncio.to_thread(self.wechat_bot.send_message, message, ["@all"])
            
            if self.dingtalk_bot:
                await asyncio.to_thread(self.dingtalk_bot.send_message, message, at_all=True)
                
        except Exception as e:
            logger.error(f"发送错误通知时出错: {e}")
    
    async def send_system_status(self, status_data: Dict):
        """
        发送系统状态报告
        
        Args:
            status_data: 系统状态数据
        """
        try:
            message = f"""
🖥️ **系统状态报告**

**运行状态**: {status_data.get('status', 'unknown')}
**运行时间**: {status_data.get('uptime', 'unknown')}
**处理的交易对数**: {status_data.get('symbols_count', 0)}
**今日信号数**: {status_data.get('signals_today', 0)}
**内存使用**: {status_data.get('memory_usage', 'unknown')}
**CPU使用**: {status_data.get('cpu_usage', 'unknown')}

**最后更新**: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            if self.wechat_bot:
                await asyncio.to_thread(self.wechat_bot.send_markdown, message)
                
        except Exception as e:
            logger.error(f"发送系统状态时出错: {e}")
    
    def test_notifications(self) -> Dict[str, bool]:
        """
        测试所有通知渠道
        
        Returns:
            各渠道测试结果
        """
        results = {}
        test_message = f"🧪 通知系统测试 - {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        try:
            if self.wechat_bot:
                results['wechat'] = self.wechat_bot.send_message(test_message)
            
            if self.dingtalk_bot:
                results['dingtalk'] = self.dingtalk_bot.send_message(test_message)
            
            logger.info(f"通知测试完成: {results}")
            
        except Exception as e:
            logger.error(f"测试通知时出错: {e}")
        
        return results


class NotificationScheduler:
    """通知调度器"""
    
    def __init__(self, notification_manager: NotificationManager):
        self.notification_manager = notification_manager
        self.scheduled_tasks = []
    
    async def schedule_daily_summary(self, hour: int = 9, minute: int = 0):
        """安排每日市场概况推送"""
        # 这里可以实现定时任务逻辑
        pass
    
    async def schedule_system_health_check(self, interval_minutes: int = 60):
        """安排系统健康检查"""
        # 这里可以实现定期健康检查逻辑
        pass
