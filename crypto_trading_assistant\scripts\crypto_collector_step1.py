#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
加密货币收集第一步：获取最近60天的新币基本信息

此脚本只收集基本信息并保存到文件，不进行详细分析
"""

import os
import time
import json
import argparse
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>
from loguru import logger
from pycoingecko import CoinGeckoAPI
import requests
import pytz

# 配置日志
logger.remove()
logger.add(lambda msg: print(msg), level="INFO")
logger.add("crypto_collector.log", rotation="500 MB", level="DEBUG")

class NewCoinCollector:
    """
    新币收集器
    只收集基本信息，不进行详细分析
    """
    def __init__(self, days=60, vs_currency="usd", use_proxy=False, request_delay=5.0):
        """
        初始化币种收集器
        
        Parameters
        ----------
        days : int
            收集过去多少天内上市的币种，默认60天
        vs_currency : str
            计价货币，默认为美元(usd)
        use_proxy : bool
            是否使用代理，默认为False
        request_delay : float
            API请求间隔时间（秒），默认5.0秒
        """
        self.days = days
        self.vs_currency = vs_currency
        self.request_delay = request_delay
        self.max_retries = 3
        
        # 设置代理
        self.proxies = None
        if use_proxy:
            self.proxies = {
                'http': 'http://127.0.0.1:7890',
                'https': 'http://127.0.0.1:7890'
            }
            session = requests.Session()
            session.proxies = self.proxies
            self.cg = CoinGeckoAPI(session=session)
        else:
            self.cg = CoinGeckoAPI()
    
    def collect_new_coins(self):
        """
        获取过去指定天数内上市的新币
        
        Returns
        -------
        pd.DataFrame
            新上市币种的基础信息
        """
        try:
            logger.info("获取所有币种信息...")
            # 获取所有币种的市场数据，包括价格变化百分比
            price_change_params = ','.join([f'price_change_percentage_{p}' for p in ['7d', '14d', '30d']])
            
            all_coins = []
            # 分页获取，每页250个币种
            for page in range(1, 5):  # 最多获取4页，共1000个币种
                logger.info(f"获取第{page}页数据...")
                # 延迟请求，避免触发API限制
                if page > 1:
                    time.sleep(self.request_delay)
                
                for retry in range(self.max_retries):
                    try:
                        # 获取市场数据，按初次添加到CoinGecko的时间倒序排序
                        params = {
                            'vs_currency': self.vs_currency,
                            'per_page': 250,
                            'page': page,
                            'sparkline': False,
                            'price_change_percentage': price_change_params,
                            'order': 'id_asc'  # 按ID升序排序，确保获取最新添加的币种
                        }
                        
                        coins = self.cg.get_coins_markets(**params)
                        
                        if not coins:
                            break
                        
                        logger.info(f"获取到{len(coins)}个币种数据")
                        all_coins.extend(coins)
                        break  # 成功获取数据，跳出重试循环
                    except Exception as e:
                        logger.warning(f"获取第{page}页数据失败(尝试 {retry+1}/{self.max_retries}): {e}")
                        if retry == self.max_retries - 1:  # 最后一次重试
                            logger.error(f"获取第{page}页数据失败，已达到最大重试次数")
                            break
                        # 指数退避策略
                        time.sleep(self.request_delay * (2 ** retry))
                        continue
            
            # 将列表转换为DataFrame
            coins_df = pd.DataFrame(all_coins)
            if coins_df.empty:
                logger.warning("未获取到任何币种数据")
                return pd.DataFrame()
            
            # 计算日期范围
            cutoff_date = datetime.now() - timedelta(days=self.days)
            cutoff_str = cutoff_date.strftime('%Y-%m-%d')
            logger.info(f"查找{cutoff_str}之后上市的新币")
            
            # 通过ATH日期查找新币
            new_coins = self._find_new_coins_by_ath_date(coins_df, cutoff_date)
            
            if not new_coins.empty:
                logger.info(f"通过ATH日期找到{len(new_coins)}个新币")
                return new_coins
            
            # 如果通过ATH日期没找到新币，尝试通过特征分析
            logger.warning("通过ATH日期未找到新币，尝试使用其他方法...")
            
            # 这里可以添加其他查找新币的方法
            
            logger.warning("未找到任何新上市的币种")
            return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取新币列表失败: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
            
    def _find_new_coins_by_ath_date(self, coins_df, cutoff_date):
        """
        通过ATH（历史最高价）日期查找新币
        
        Parameters
        ----------
        coins_df : pd.DataFrame
            所有币种数据
        cutoff_date : datetime
            截止日期
            
        Returns
        -------
        pd.DataFrame
            新上市的币种数据
        """
        # 确保ath_date列存在
        if 'ath_date' not in coins_df.columns:
            logger.error("币种数据中缺少ath_date列，无法通过ATH日期查找新币")
            return pd.DataFrame()
            
        # 转换ATH日期列为datetime
        coins_df['ath_date'] = pd.to_datetime(coins_df['ath_date'], errors='coerce')
        
        # 将cutoff_date转换为UTC时区的datetime
        cutoff_date_utc = pd.Timestamp(cutoff_date).tz_localize('UTC')
        
        # 过滤出ATH日期在cutoff_date之后的币种
        new_coins = coins_df[coins_df['ath_date'] >= cutoff_date_utc].copy()
        
        # 按ATH日期降序排序
        new_coins.sort_values('ath_date', ascending=False, inplace=True)
        
        return new_coins
    
    def save_to_file(self, new_coins, file_path='new_coins.csv', json_path='new_coins.json'):
        """
        保存新币数据到文件
        
        Parameters
        ----------
        new_coins : pd.DataFrame
            新上市的币种数据
        file_path : str
            CSV文件保存路径
        json_path : str
            JSON文件保存路径
        """
        try:
            if new_coins.empty:
                logger.warning("没有数据可保存")
                return
                
            # 保存为CSV
            new_coins.to_csv(file_path, index=False)
            logger.info(f"新币数据已保存至 {file_path}")
            
            # 处理datetime列，将其转换为字符串格式
            json_df = new_coins.copy()
            for col in json_df.columns:
                if pd.api.types.is_datetime64_any_dtype(json_df[col]):
                    json_df[col] = json_df[col].astype(str)
            
            # 保存为JSON
            json_df_dict = json_df.to_dict(orient='records')
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(json_df_dict, f, ensure_ascii=False, indent=4)
            logger.info(f"新币数据已保存至 {json_path}")
            
        except Exception as e:
            logger.error(f"保存数据失败: {e}")
            import traceback
            traceback.print_exc()

def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description='加密货币新币收集工具 - 第一步')
    parser.add_argument('-d', '--days', type=int, default=60, help='收集过去多少天内上市的币种，默认60天')
    parser.add_argument('-c', '--currency', type=str, default='usd', help='计价货币，默认为美元(usd)')
    parser.add_argument('-p', '--proxy', action='store_true', help='是否使用代理')
    parser.add_argument('-o', '--output', type=str, default='new_coins.csv', help='输出文件路径')
    parser.add_argument('-j', '--json', type=str, default='new_coins.json', help='JSON输出文件路径')
    return parser.parse_args()

def main():
    """
    主函数
    """
    args = parse_args()
    
    logger.info("=" * 80)
    logger.info("             加密货币新币收集工具 - 第一步")
    logger.info("=" * 80)
    
    collector = NewCoinCollector(
        days=args.days,
        vs_currency=args.currency,
        use_proxy=args.proxy
    )
    
    # 收集新币
    new_coins = collector.collect_new_coins()
    
    # 保存结果
    collector.save_to_file(new_coins, args.output, args.json)
    
    logger.info("数据收集完成")
    
if __name__ == "__main__":
    main() 