@echo off
echo ==============================================================
echo        加密货币新币分析工具 - 全流程自动执行脚本
echo ==============================================================
echo.

REM 设置参数
set DAYS=30
set MAX_COINS=10
set OUTPUT_FOLDER=crypto_reports

REM 解析命令行参数
if not "%1"=="" set DAYS=%1
if not "%2"=="" set MAX_COINS=%2
if not "%3"=="" set OUTPUT_FOLDER=%3

echo 即将分析最近 %DAYS% 天的 %MAX_COINS% 个新币，结果将保存到 %OUTPUT_FOLDER% 文件夹
echo.
echo 按任意键开始分析，或按Ctrl+C取消...
pause > nul

echo.
echo 步骤1: 收集新币基本信息...
python crypto_collector_step1.py -d %DAYS%
if %ERRORLEVEL% NEQ 0 (
    echo 步骤1执行失败，按任意键退出...
    pause > nul
    exit /b 1
)

echo.
echo 步骤2: 分析新币详细数据并生成报告...
python crypto_collector_step2.py -m %MAX_COINS% -o %OUTPUT_FOLDER%
if %ERRORLEVEL% NEQ 0 (
    echo 步骤2执行失败，按任意键退出...
    pause > nul
    exit /b 1
)

echo.
echo 分析完成！报告已生成在 %OUTPUT_FOLDER% 文件夹
echo 按任意键查看HTML报告...
pause > nul

REM 打开最新生成的HTML报告
for /f "delims=" %%a in ('dir /b /od %OUTPUT_FOLDER%\*.html') do set LATEST_REPORT=%%a
start %OUTPUT_FOLDER%\%LATEST_REPORT%

echo.
echo 报告已在浏览器中打开，按任意键退出...
pause > nul 