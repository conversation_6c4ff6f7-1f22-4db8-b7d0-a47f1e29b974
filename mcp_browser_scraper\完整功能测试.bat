@echo off
chcp 65001 >nul
title 完整功能测试 - 数据下载 + 形态分析
echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █                    🧪 完整功能测试                          █
echo █              数据下载 + 形态分析 + 微信推送                 █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.

cd /d "%~dp0"

echo 📍 当前目录: %CD%
echo 🐍 Python环境: D:\envs\tqsdk\python.exe
echo.

echo 🔧 检查环境...
if not exist "D:\envs\tqsdk\python.exe" (
    echo ❌ Python环境不存在
    pause
    exit /b 1
)

if not exist "advanced_crypto_scraper tuxing.py" (
    echo ❌ 主脚本文件不存在
    pause
    exit /b 1
)

if not exist "完整功能测试.py" (
    echo ❌ 测试脚本不存在
    pause
    exit /b 1
)

echo ✅ 环境检查通过
echo.

echo 💡 测试内容:
echo ┌─────────────────────────────────────────────────────────────┐
echo │ 🧪 完整功能测试流程                                        │
echo │                                                             │
echo │ 📊 1. 数据下载测试                                         │
echo │   • 测试OKX、Gate.io、CoinGecko数据源                      │
echo │   • 验证实时市场数据获取                                   │
echo │   • 检查数据格式和完整性                                   │
echo │                                                             │
echo │ 📈 2. 历史数据获取测试                                     │
echo │   • 获取K线历史数据                                        │
echo │   • 验证数据时间序列                                       │
echo │   • 检查OHLCV数据完整性                                    │
echo │                                                             │
echo │ 🎯 3. 形态分析测试                                         │
echo │   • 双长上影线形态识别                                     │
echo │   • 技术指标计算                                           │
echo │   • 综合评分系统                                           │
echo │                                                             │
echo │ 📱 4. 微信推送测试                                         │
echo │   • 分析结果推送                                           │
echo │   • 消息格式验证                                           │
echo │   • 推送状态检查                                           │
echo │                                                             │
echo │ 🎨 5. 图表生成测试                                         │
echo │   • K线图表生成                                            │
echo │   • 技术指标显示                                           │
echo │   • 形态标注功能                                           │
echo └─────────────────────────────────────────────────────────────┘
echo.

echo 🚀 开始完整功能测试...
echo ================================================================
echo.

D:\envs\tqsdk\python.exe "完整功能测试.py"

echo.
echo ================================================================
echo 📝 测试完成
echo.
pause
