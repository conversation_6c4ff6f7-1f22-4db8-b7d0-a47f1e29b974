#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
加密货币交易所数据收集模块

从币安等交易所获取额外数据，包括：
- 多空比例
- 资金费率
- 未平仓合约数量
- 大额持仓分布
"""

import time
import hmac
import hashlib
import base64
import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from loguru import logger
import json
import os
import sys
from dataclasses import dataclass
from typing import Dict, List, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

@dataclass
class ExchangeConfig:
    """交易所配置"""
    name: str
    api_base_url: str
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    use_proxy: bool = False
    request_delay: float = 1.0

class ExchangeDataCollector:
    """
    交易所数据收集器
    从各大交易所API获取数据
    """
    def __init__(self, 
                 api_keys: Optional[Dict] = None,
                 use_proxy: bool = False,
                 request_delay: float = 1.0):
        """
        初始化数据收集器
        
        Args:
            api_keys: API密钥字典
            use_proxy: 是否使用代理
            request_delay: 请求延迟（秒）
        """
        self.logger = logger
        self.use_proxy = use_proxy
        self.request_delay = request_delay
        
        # 初始化交易所配置
        self.exchanges = {
            'huobi': ExchangeConfig(
                name='火币',
                api_base_url='https://api.huobi.pro',
                api_key=api_keys.get('huobi_api_key') if api_keys else None,
                api_secret=api_keys.get('huobi_api_secret') if api_keys else None,
                use_proxy=use_proxy,
                request_delay=request_delay
            ),
            'binance': ExchangeConfig(
                name='币安',
                api_base_url='https://api.binance.com',
                api_key=api_keys.get('binance_api_key') if api_keys else None,
                api_secret=api_keys.get('binance_api_secret') if api_keys else None,
                use_proxy=use_proxy,
                request_delay=request_delay
            )
        }
        
        # 初始化代理设置
        self.proxies = {
            'http': 'http://127.0.0.1:7890',
            'https': 'http://127.0.0.1:7890'
        } if use_proxy else None
        
    def get_exchange_data(self, df: pd.DataFrame) -> Dict:
        """
        获取交易所数据
        
        Args:
            df: 包含交易对信息的DataFrame
            
        Returns:
            交易所数据字典
        """
        self.logger.info("开始使用火币API获取交易所数据...")
        
        try:
            # 获取所有交易对
            markets = self._get_huobi_data()
            if not markets:
                return {}
            # 获取新上市币种信息
            new_listings = self._get_new_listings()
            # 合并数据
            for symbol, data in markets.items():
                if symbol in new_listings:
                    data.update(new_listings[symbol])
            return markets
        except Exception as e:
            self.logger.error(f"获取交易所数据时出错: {str(e)}")
            return {}
        
    def _get_huobi_data(self) -> Dict:
        """
        获取火币数据
        
        Returns:
            火币数据字典
        """
        try:
            self.logger.info("使用火币API获取数据...")
            # 获取所有交易对
            response = requests.get(
                f"{self.exchanges['huobi'].api_base_url}/v1/common/symbols",
                proxies=self.proxies
            )
            response.raise_for_status()
            symbols_data = response.json()['data']
            # 获取K线数据
            markets = {}
            for symbol_data in symbols_data:
                symbol = symbol_data['symbol'].upper()
                # 获取K线数据
                kline_response = requests.get(
                    f"{self.exchanges['huobi'].api_base_url}/market/history/kline",
                    params={
                        'symbol': symbol_data['symbol'],
                        'period': '1day',
                        'size': 200
                    },
                    proxies=self.proxies
                )
                kline_response.raise_for_status()
                kline_data = kline_response.json()['data']
                if kline_data:
                    # 转换为DataFrame格式
                    df = pd.DataFrame(kline_data)
                    df.columns = ['timestamp', 'open', 'close', 'high', 'low', 'volume', 'amount']
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    # 存储数据
                    markets[symbol] = {
                        'data': df,
                        'base_currency': symbol_data['base-currency'],
                        'quote_currency': symbol_data['quote-currency'],
                        'price_precision': symbol_data['price-precision'],
                        'amount_precision': symbol_data['amount-precision']
                    }
                # 避免请求过于频繁
                time.sleep(self.request_delay)
            return markets
        except Exception as e:
            self.logger.error(f"获取火币数据时出错: {str(e)}")
            return {}
            
    def _get_new_listings(self) -> Dict:
        """
        获取新上市币种信息
        
        Returns:
            新上市币种信息字典
        """
        try:
            # 获取新币上线公告
            response = requests.get(
                f"{self.exchanges['huobi'].api_base_url}/v1/common/currencies",
                proxies=self.proxies
            )
            response.raise_for_status()
            currencies_data = response.json()['data']
            
            # 获取新币上线时间
            new_listings = {}
            for currency in currencies_data:
                symbol = currency['currency'].upper()
                
                # 获取币种详细信息
                detail_response = requests.get(
                    f"{self.exchanges['huobi'].api_base_url}/v1/common/currency/{currency['currency']}",
                    proxies=self.proxies
                )
                detail_response.raise_for_status()
                detail_data = detail_response.json()['data']
                
                if detail_data and 'listing-date' in detail_data:
                    new_listings[symbol] = {
                        'listing_date': detail_data['listing-date']
                    }
                    
                # 避免请求过于频繁
                time.sleep(self.request_delay)
                
            return new_listings
                    
        except Exception as e:
            self.logger.error(f"获取新上市币种信息时出错: {str(e)}")
            return {}
            
    def _get_binance_data(self) -> Dict:
        """
        获取币安数据
        
        Returns:
            币安数据字典
        """
        try:
            self.logger.info("使用币安API获取数据...")
            
            # 获取所有交易对
            response = requests.get(
                f"{self.exchanges['binance'].api_base_url}/api/v3/exchangeInfo",
                proxies=self.proxies
            )
            response.raise_for_status()
            symbols_data = response.json()['symbols']
            
            # 获取K线数据
            markets = {}
            for symbol_data in symbols_data:
                symbol = symbol_data['symbol']
                
                # 获取K线数据
                kline_response = requests.get(
                    f"{self.exchanges['binance'].api_base_url}/api/v3/klines",
                    params={
                        'symbol': symbol,
                        'interval': '1d',
                        'limit': 200
                    },
                    proxies=self.proxies
                )
                kline_response.raise_for_status()
                kline_data = kline_response.json()
                
                if kline_data:
                    # 转换为DataFrame格式
                    df = pd.DataFrame(kline_data)
                    df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'close_time', 'quote_volume', 'trades', 'taker_buy_base', 'taker_buy_quote', 'ignore']
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    
                    # 存储数据
                    markets[symbol] = {
                        'data': df,
                        'base_currency': symbol_data['baseAsset'],
                        'quote_currency': symbol_data['quoteAsset'],
                        'price_precision': symbol_data['pricePrecision'],
                        'amount_precision': symbol_data['quantityPrecision']
                    }
                    
                # 避免请求过于频繁
                time.sleep(self.request_delay)
                
            return markets
                
        except Exception as e:
            self.logger.error(f"获取币安数据时出错: {str(e)}")
            return {}

def test_data_collector():
    """测试数据收集器"""
    # 加载API密钥
    try:
        with open('api_keys.json', 'r') as f:
            api_keys = json.load(f)
    except Exception:
        api_keys = {}
        
    # 创建数据收集器实例
    collector = ExchangeDataCollector(
        api_keys=api_keys,
        use_proxy=True,
        request_delay=1.0
    )
    
    # 获取交易所数据
    print("获取交易所数据...")
    markets = collector.get_exchange_data(pd.DataFrame())
    
    if markets:
        print(f"成功获取{len(markets)}个交易对的数据")
        
        # 显示部分数据
        for symbol, data in list(markets.items())[:3]:
            print(f"\n{symbol}:")
            print(f"基础货币: {data['base_currency']}")
            print(f"计价货币: {data['quote_currency']}")
            print(f"价格精度: {data['price_precision']}")
            print(f"数量精度: {data['amount_precision']}")
            if 'listing_date' in data:
                print(f"上市日期: {data['listing_date']}")
            print("\n最近5天K线数据:")
            print(data['data'].tail())
    else:
        print("获取数据失败")

if __name__ == "__main__":
    # 设置日志
    logger.basicConfig(
        level=logger.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行测试
    test_data_collector()