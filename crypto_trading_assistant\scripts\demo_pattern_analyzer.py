#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
演示版形态分析器
使用模拟数据演示完整功能，包括企业微信推送
"""

import pandas as pd
import numpy as np
import requests
import time
from datetime import datetime

class DemoPatternAnalyzer:
    """演示版形态分析器"""
    
    def __init__(self):
        """初始化"""
        self.wechat_webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=52d1b8a8-a88a-4d41-9664-f1268da20985"
        
        # 模拟市场数据
        self.mock_data = {
            'BTCUSDT': {'name': 'Bitcoin', 'current_price': 43250.50, 'price_change_24h': 2.45},
            'ETHUSDT': {'name': 'Ethereum', 'current_price': 2650.80, 'price_change_24h': 1.85},
            'BNBUSDT': {'name': 'BNB', 'current_price': 315.20, 'price_change_24h': -0.75},
            'ADAUSDT': {'name': 'Cardano', 'current_price': 0.485, 'price_change_24h': 3.20},
            'SOLUSDT': {'name': 'Solana', 'current_price': 98.75, 'price_change_24h': 4.15},
            'LINKUSDT': {'name': 'Chainlink', 'current_price': 14.85, 'price_change_24h': 1.95},
            'DOGEUSDT': {'name': 'Dogecoin', 'current_price': 0.0875, 'price_change_24h': -1.25}
        }
        
        print("演示版形态分析器初始化完成")
        print(f"模拟数据包含 {len(self.mock_data)} 个交易对")
    
    def generate_kline_data(self, symbol):
        """生成K线数据"""
        if symbol not in self.mock_data:
            return None
        
        real_data = self.mock_data[symbol]
        current_price = real_data['current_price']
        price_change = real_data['price_change_24h'] / 100
        
        # 生成50个周期的数据
        periods = 50
        dates = pd.date_range(end=datetime.now(), periods=periods, freq='D')
        
        # 设置随机种子确保结果可重复
        np.random.seed(hash(symbol) % 10000)
        
        # 生成价格序列
        prices = []
        base_volatility = 0.02  # 2%日波动率
        
        for i in range(periods):
            if i == periods - 1:
                price = current_price
            else:
                # 基于趋势和随机波动生成历史价格
                days_back = periods - 1 - i
                trend_factor = price_change / periods * days_back
                noise = np.random.normal(0, base_volatility)
                
                # 添加一些技术形态特征
                if days_back < 10:  # 最近10天
                    if symbol in ['BTCUSDT', 'ADAUSDT', 'SOLUSDT']:  # 模拟上涨趋势
                        trend_factor += 0.01
                    elif symbol in ['BNBUSDT', 'DOGEUSDT']:  # 模拟下跌趋势
                        trend_factor -= 0.005
                
                price = current_price / (1 + trend_factor + noise)
                price = max(price, current_price * 0.5)  # 防止价格过低
            
            prices.append(price)
        
        # 生成OHLCV数据
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            # 生成开盘价
            if i == 0:
                open_price = close * (1 + np.random.normal(0, 0.002))
            else:
                open_price = prices[i-1] * (1 + np.random.normal(0, 0.003))
            
            # 生成高低价
            price_range = abs(close - open_price) * (1 + abs(np.random.normal(0, 0.5)))
            high = max(open_price, close) + price_range * np.random.uniform(0, 0.8)
            low = min(open_price, close) - price_range * np.random.uniform(0, 0.8)
            
            # 确保高低价合理
            high = max(high, open_price, close)
            low = min(low, open_price, close)
            low = max(low, close * 0.95)  # 防止价格差异过大
            
            # 生成成交量
            base_volume = 1000000 + hash(symbol + str(i)) % 5000000
            volume_factor = 1 + abs(close - open_price) / close  # 价格波动大时成交量大
            volume = base_volume * volume_factor
            
            data.append({
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume
            })
        
        return pd.DataFrame(data, index=dates)
    
    def identify_patterns(self, df):
        """识别K线形态 - 增强版"""
        if len(df) < 3:
            return {'patterns': [], 'score': 0.0, 'details': {}}
        
        latest = df.iloc[-1]
        prev = df.iloc[-2]
        prev2 = df.iloc[-3]
        
        patterns = []
        score = 0.0
        
        # 计算K线基本参数
        body = abs(latest['close'] - latest['open'])
        upper_shadow = latest['high'] - max(latest['open'], latest['close'])
        lower_shadow = min(latest['open'], latest['close']) - latest['low']
        total_range = latest['high'] - latest['low']
        
        prev_body = abs(prev['close'] - prev['open'])
        
        if total_range > 0:
            body_ratio = body / total_range
            upper_ratio = upper_shadow / total_range
            lower_ratio = lower_shadow / total_range
            
            # 单K线形态识别
            if body_ratio < 0.1:
                patterns.append('十字星')
                score += 1.8
            elif upper_ratio > 0.4 and lower_ratio < 0.2:
                patterns.append('长上影线')
                score += 1.2
            elif lower_ratio > 0.4 and upper_ratio < 0.2:
                patterns.append('锤子线')
                score += 2.2
            elif body_ratio > 0.7:
                if latest['close'] > latest['open']:
                    patterns.append('大阳线')
                    score += 1.8
                else:
                    patterns.append('大阴线')
                    score -= 0.8
            
            # 组合形态识别
            if (latest['close'] > latest['open'] and prev['close'] < prev['open'] and
                latest['close'] > prev['open'] and latest['open'] < prev['close'] and
                body > prev_body * 1.2):
                patterns.append('看涨吞没')
                score += 2.8
            elif (latest['close'] < latest['open'] and prev['close'] > prev['open'] and
                  latest['close'] < prev['open'] and latest['open'] > prev['close'] and
                  body > prev_body * 1.2):
                patterns.append('看跌吞没')
                score -= 2.2
            
            # 三K线形态
            if len(df) >= 3:
                # 启明星形态
                if (prev2['close'] < prev2['open'] and  # 第一根阴线
                    abs(prev['close'] - prev['open']) < prev_body * 0.3 and  # 第二根十字星
                    latest['close'] > latest['open'] and  # 第三根阳线
                    latest['close'] > (prev2['open'] + prev2['close']) / 2):
                    patterns.append('启明星')
                    score += 3.0
        
        return {
            'patterns': patterns, 
            'score': max(min(score, 5.0), 0.0),
            'details': {
                'body_ratio': body_ratio if total_range > 0 else 0,
                'upper_ratio': upper_ratio if total_range > 0 else 0,
                'lower_ratio': lower_ratio if total_range > 0 else 0
            }
        }
    
    def calculate_indicators(self, df):
        """计算技术指标"""
        data = df.copy()
        
        # RSI计算
        delta = data['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = (-delta.where(delta < 0, 0))
        
        # 使用指数移动平均
        alpha = 1.0 / 14
        avg_gain = gain.ewm(alpha=alpha, adjust=False).mean()
        avg_loss = loss.ewm(alpha=alpha, adjust=False).mean()
        
        rs = avg_gain / avg_loss
        data['rsi'] = 100 - (100 / (1 + rs))
        
        # 移动平均线
        data['sma_5'] = data['close'].rolling(5, min_periods=1).mean()
        data['sma_10'] = data['close'].rolling(10, min_periods=1).mean()
        data['sma_20'] = data['close'].rolling(20, min_periods=1).mean()
        
        # MACD
        ema12 = data['close'].ewm(span=12, adjust=False).mean()
        ema26 = data['close'].ewm(span=26, adjust=False).mean()
        data['macd'] = ema12 - ema26
        data['macd_signal'] = data['macd'].ewm(span=9, adjust=False).mean()
        data['macd_hist'] = data['macd'] - data['macd_signal']
        
        # 布林带
        data['bb_middle'] = data['close'].rolling(20, min_periods=1).mean()
        bb_std = data['close'].rolling(20, min_periods=1).std()
        data['bb_upper'] = data['bb_middle'] + (bb_std * 2)
        data['bb_lower'] = data['bb_middle'] - (bb_std * 2)
        data['bb_position'] = (data['close'] - data['bb_lower']) / (data['bb_upper'] - data['bb_lower'])
        
        # 成交量指标
        data['volume_sma'] = data['volume'].rolling(10, min_periods=1).mean()
        data['volume_ratio'] = data['volume'] / data['volume_sma']
        
        return data
    
    def evaluate_indicators(self, data):
        """评估技术指标"""
        if len(data) < 20:
            return {'score': 0.0, 'details': {}}
        
        latest = data.iloc[-1]
        prev = data.iloc[-2]
        score = 0.0
        
        # RSI评分
        rsi = latest.get('rsi', 50)
        if not pd.isna(rsi):
            if rsi < 30:
                score += 2.5  # 超卖，买入信号
            elif rsi < 40:
                score += 1.5
            elif 40 <= rsi <= 60:
                score += 0.8  # 中性
            elif rsi > 70:
                score -= 1.2  # 超买
        
        # 移动平均线评分
        sma5 = latest.get('sma_5', 0)
        sma10 = latest.get('sma_10', 0)
        sma20 = latest.get('sma_20', 0)
        
        if not pd.isna(sma5) and not pd.isna(sma20):
            if latest['close'] > sma5 > sma10 > sma20:
                score += 2.5  # 完美多头排列
            elif latest['close'] > sma5 > sma20:
                score += 2.0  # 多头排列
            elif latest['close'] > sma20:
                score += 1.0
        
        # MACD评分
        macd = latest.get('macd', 0)
        macd_signal = latest.get('macd_signal', 0)
        macd_hist = latest.get('macd_hist', 0)
        
        if not pd.isna(macd) and not pd.isna(macd_signal):
            if macd > macd_signal and macd_hist > 0:
                score += 1.5
            # 金叉检测
            if (macd > macd_signal and 
                prev.get('macd', 0) <= prev.get('macd_signal', 0)):
                score += 2.0  # MACD金叉
        
        # 布林带评分
        bb_pos = latest.get('bb_position', 0.5)
        if not pd.isna(bb_pos):
            if bb_pos < 0.2:
                score += 2.0  # 接近下轨，超卖
            elif bb_pos > 0.8:
                score -= 1.0  # 接近上轨，超买
            else:
                score += 0.5
        
        # 成交量评分
        vol_ratio = latest.get('volume_ratio', 1.0)
        if not pd.isna(vol_ratio):
            if vol_ratio > 1.5:
                score += 1.2  # 放量
            elif vol_ratio > 1.2:
                score += 0.8
        
        return {
            'score': max(min(score, 5.0), 0.0),
            'details': {
                'rsi': rsi if not pd.isna(rsi) else 50,
                'macd': macd if not pd.isna(macd) else 0,
                'macd_signal': macd_signal if not pd.isna(macd_signal) else 0,
                'bb_position': bb_pos if not pd.isna(bb_pos) else 0.5,
                'volume_ratio': vol_ratio if not pd.isna(vol_ratio) else 1.0,
                'price': latest['close']
            }
        }

    def analyze_symbol(self, symbol):
        """分析单个交易对"""
        try:
            print(f"  正在分析 {symbol}...")

            # 生成K线数据
            df = self.generate_kline_data(symbol)
            if df is None:
                return None

            # 计算技术指标
            df = self.calculate_indicators(df)

            # 形态分析
            pattern_result = self.identify_patterns(df)

            # 指标分析
            indicator_result = self.evaluate_indicators(df)

            # 综合评分 (形态40% + 指标60%)
            total_score = (pattern_result['score'] * 0.4 +
                          indicator_result['score'] * 0.6)

            # 风险评估
            risk_level = 'low' if total_score > 3.5 else 'medium' if total_score > 2.5 else 'high'

            # 趋势判断
            latest = df.iloc[-1]
            trend = "上升" if latest['close'] > latest['sma_20'] else "下降"

            result = {
                'symbol': symbol,
                'name': self.mock_data[symbol]['name'],
                'total_score': total_score,
                'pattern_score': pattern_result['score'],
                'indicator_score': indicator_result['score'],
                'patterns': pattern_result['patterns'],
                'risk_level': risk_level,
                'trend': trend,
                'current_price': indicator_result['details']['price'],
                'price_change_24h': self.mock_data[symbol]['price_change_24h'],
                'rsi': indicator_result['details']['rsi'],
                'macd': indicator_result['details']['macd'],
                'bb_position': indicator_result['details']['bb_position'],
                'volume_ratio': indicator_result['details']['volume_ratio'],
                'analysis_time': datetime.now()
            }

            print(f"    {symbol} 分析完成，得分: {total_score:.2f}")
            return result

        except Exception as e:
            print(f"    分析 {symbol} 失败: {e}")
            return None

    def send_wechat_message(self, message):
        """发送企业微信消息"""
        try:
            data = {
                "msgtype": "markdown",
                "markdown": {
                    "content": message
                }
            }

            response = requests.post(
                self.wechat_webhook,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )

            result = response.json()
            success = result.get('errcode') == 0

            if success:
                print("  企业微信推送成功")
            else:
                print(f"  企业微信推送失败: {result}")

            return success

        except Exception as e:
            print(f"  发送微信消息失败: {e}")
            return False

    def format_message(self, results):
        """格式化分析结果消息"""
        if not results:
            return "# 演示版形态分析结果\n\n暂无符合条件的交易信号"

        message = f"""# 演示版形态分析结果
分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
分析方法: K线形态识别 + 技术指标分析
发现 {len(results)} 个潜力标的

"""

        for i, result in enumerate(results, 1):
            symbol = result['symbol']
            name = result['name']
            score = result['total_score']
            risk = result['risk_level']
            trend = result['trend']

            patterns_str = ', '.join(result['patterns']) if result['patterns'] else '无明显形态'

            risk_emoji = {'low': '🟢 低风险', 'medium': '🟡 中风险', 'high': '🔴 高风险'}[risk]
            trend_emoji = '📈' if trend == '上升' else '📉'

            message += f"""{i}. {symbol} ({name}) {risk_emoji}
   💯 综合得分: {score:.2f}/5.0
   💰 当前价格: ${result['current_price']:.6f}
   📊 24h涨跌: {result['price_change_24h']:+.2f}%
   {trend_emoji} 趋势方向: {trend}趋势
   📈 RSI指标: {result['rsi']:.1f}
   📊 K线形态: {patterns_str}
   🎯 布林带位置: {result['bb_position']:.2f}
   📈 成交量比: {result['volume_ratio']:.2f}

"""

        message += """💡 投资建议:
🟢 低风险: 建议重点关注，适合稳健投资
🟡 中风险: 可适量配置，注意风险控制
🔴 高风险: 谨慎观察，等待更好时机

📊 技术分析说明:
- 综合得分 = 形态分析(40%) + 指标分析(60%)
- RSI < 30 超卖，> 70 超买
- 布林带位置 < 0.2 接近下轨，> 0.8 接近上轨
- 成交量比 > 1.5 表示放量

⚠️ 风险提示:
本分析仅供参考，不构成投资建议。
数字货币投资有风险，请谨慎决策。"""

        return message

    def run_analysis(self):
        """运行完整分析"""
        print("="*60)
        print("启动演示版形态分析器")
        print("="*60)

        print(f"\n开始分析 {len(self.mock_data)} 个交易对...")

        results = []

        for symbol in self.mock_data.keys():
            result = self.analyze_symbol(symbol)
            if result and result['total_score'] > 1.5:  # 只保留得分较高的
                results.append(result)

            # 模拟分析延迟
            time.sleep(0.2)

        # 按得分排序
        results.sort(key=lambda x: x['total_score'], reverse=True)

        print(f"\n分析完成，发现 {len(results)} 个潜力标的")

        # 显示详细结果
        if results:
            print("\n" + "="*60)
            print("详细分析结果")
            print("="*60)

            for i, result in enumerate(results, 1):
                symbol = result['symbol']
                name = result['name']
                score = result['total_score']
                risk = result['risk_level']
                patterns = result['patterns']

                print(f"\n{i}. {symbol} ({name})")
                print(f"   综合得分: {score:.2f}/5.0")
                print(f"   风险等级: {risk}")
                print(f"   当前价格: ${result['current_price']:.6f}")
                print(f"   24h涨跌: {result['price_change_24h']:+.2f}%")
                print(f"   RSI: {result['rsi']:.1f}")
                print(f"   K线形态: {', '.join(patterns) if patterns else '无明显形态'}")
                print(f"   趋势方向: {result['trend']}趋势")

            # 发送企业微信推送
            print(f"\n准备发送企业微信推送...")
            message = self.format_message(results)

            if self.send_wechat_message(message):
                print("企业微信推送成功！")
            else:
                print("企业微信推送失败")
        else:
            print("\n未发现符合条件的交易信号")

        print(f"\n演示版形态分析完成！")
        return results


def main():
    """主程序"""
    try:
        analyzer = DemoPatternAnalyzer()
        results = analyzer.run_analysis()

        print(f"\n分析总结:")
        print(f"   总计分析: {len(analyzer.mock_data)} 个交易对")
        print(f"   发现机会: {len(results)} 个")
        print(f"   成功率: {len(results)/len(analyzer.mock_data)*100:.1f}%")

        return 0

    except Exception as e:
        print(f"程序运行失败: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
