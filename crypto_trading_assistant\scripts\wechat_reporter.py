import json
import csv
import logging
from typing import Dict, List
import requests
from pathlib import Path
from analyze_shadow_crypto import WeChatNotifier

logger = logging.getLogger(__name__)

class WechatReporter:
    def __init__(self, config_path: str = 'config.json'):
        self.config = self._load_config(config_path)
        self.notifier = WeChatNotifier(self.config['wechat']['send_keys'])

    def _load_config(self, config_path: str) -> Dict:
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            if 'wechat' not in config:
                raise ValueError("微信配置缺失")
            return config
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            raise

    def generate_report(self, input_path: str) -> str:
        file_ext = Path(input_path).suffix.lower()
        
        try:
            if file_ext == '.csv':
                data = self._read_csv(input_path)
            elif file_ext == '.json':
                data = self._read_json(input_path)
            else:
                raise ValueError("不支持的文件格式")

            markdown = "## 数字货币上影线分析报告\n\n"
            markdown += "| 币种名称 | 当前价格 | 上影线比例 | 分析时间 |\n"
            markdown += "|---|---|---|---|\n"
            
            for item in data:
                markdown += f"| {item['币种名称']} | {item['当前价格']} | {item['上影线比例']}% | {item['分析时间']} |\n"
            
            return markdown
            
        except Exception as e:
            logger.error(f"报告生成失败: {e}")
            raise

    def _read_csv(self, file_path: str) -> List[Dict]:
        with open(file_path, 'r', encoding='utf-8') as f:
            return list(csv.DictReader(f))

    def _read_json(self, file_path: str) -> List[Dict]:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    def send_report(self, report_content: str):
        try:
            self.notifier.send_message(
                title="数字货币分析报告",
                content=report_content
            )
            logger.info("微信推送已发送")
        except Exception as e:
            logger.error(f"推送发送失败: {e}")
            raise

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='微信分析报告推送工具')
    parser.add_argument('--input', type=str, required=True, help='输入文件路径（CSV/JSON）')
    parser.add_argument('--config', type=str, default='config.json', help='配置文件路径')
    
    args = parser.parse_args()
    
    try:
        reporter = WechatReporter(args.config)
        report = reporter.generate_report(args.input)
        reporter.send_report(report)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        exit(1)