@echo off
REM Set code page to ANSI (e.g., 437 for US/English, 936 for Simplified Chinese)
chcp 437 > nul

set "CONDA_BASE_PATH=D:\ProgramData\miniconda3"
set "ENV_NAME=tqsdk"
set "PYTHON_EXE=%CONDA_BASE_PATH%\envs\%ENV_NAME%\python.exe"
set "SCRIPT_DIR=%~dp0"
set "UPDATE_SCRIPT=get_new_coins_list.py"
set "ANALYZE_SCRIPT=analyze_from_list.py"
set "LOG_FILE=%SCRIPT_DIR%analyze_from_list.log"

echo Activating Conda environment: %ENV_NAME% ...

REM Try activating using conda command if it's in PATH
REM 检测执行环境类型
@echo off
REM 兼容PowerShell和CMD的执行环境检测
if "%COMSPEC%"=="%windir%\system32\cmd.exe" (
    call "%CONDA_BASE_PATH%\Scripts\activate.bat" "%CONDA_BASE_PATH%\envs\%ENV_NAME%"
) else (
    conda activate %ENV_NAME%
)

REM 统一Python脚本执行方式
"%PYTHON_EXE%" "%SCRIPT_DIR%%UPDATE_SCRIPT%" >> "%LOG_FILE%" 2>&1
if errorlevel 1 (
    echo ERROR: Failed to activate Conda environment '%ENV_NAME%'. Check Conda installation and environment name. >> "%LOG_FILE%"
    echo ERROR: Failed to activate Conda environment '%ENV_NAME%'. Check Conda installation and environment name.
    goto End
)

echo Changing directory to: %SCRIPT_DIR%
cd /d "%SCRIPT_DIR%"

echo.
echo Menu Options:
echo 1. Update coin list (last 60 days)
echo 2. Analyze coins and push to WeChat
echo 3. Update, then Analyze and Push
echo.

echo 注意：在PowerShell中请直接输入数字后按回车
choice /c 123 /n /m "请输入选项(1, 2, 或 3):"

if "%choice%"=="1" (
    echo Running Update Script: %UPDATE_SCRIPT_PATH%
    echo Script started at %date% %time% >> "%LOG_FILE%"
    "%PYTHON_EXE%" "%SCRIPT_DIR%%UPDATE_SCRIPT%" >> "%LOG_FILE%" 2>&1
    if errorlevel 1 (
        echo ERROR: Update script execution failed. Check log file for details. >> "%LOG_FILE%"
        echo ERROR: Update script execution failed. Check log file for details.
    ) else (
        echo Update script executed successfully.
    )
    echo Script finished at %date% %time% >> "%LOG_FILE%"
) else if "%choice%"=="2" (
    echo Running Analyze Script: %ANALYZE_SCRIPT_PATH%
    echo Script started at %date% %time% >> "%LOG_FILE%"
    "%PYTHON_EXE%" %ANALYZE_SCRIPT_PATH% >> "%LOG_FILE%" 2>>&1
    if errorlevel 1 (
        echo ERROR: Analyze script execution failed. Check log file for details. >> "%LOG_FILE%"
        echo ERROR: Analyze script execution failed. Check log file for details.
    ) else (
        echo Analyze script executed successfully.
    )
    echo Script finished at %date% %time% >> "%LOG_FILE%"
) else if "%choice%"=="3" (
    echo Running Update Script: %UPDATE_SCRIPT_PATH%
    echo Script started at %date% %time% >> "%LOG_FILE%"
    "%PYTHON_EXE%" "%SCRIPT_DIR%%UPDATE_SCRIPT%" >> "%LOG_FILE%" 2>&1
    if errorlevel 1 (
        echo ERROR: Update script execution failed. Check log file for details. >> "%LOG_FILE%"
        echo ERROR: Update script execution failed. Check log file for details.
        goto Deactivate
    ) else (
        echo Update script executed successfully.
    )
    echo Script finished at %date% %time% >> "%LOG_FILE%"

    echo Running Analyze Script: %ANALYZE_SCRIPT_PATH%
    echo Script started at %date% %time% >> "%LOG_FILE%"
    "%PYTHON_EXE%" %ANALYZE_SCRIPT_PATH% >> "%LOG_FILE%" 2>>&1
    if errorlevel 1 (
        echo ERROR: Analyze script execution failed. Check log file for details. >> "%LOG_FILE%"
        echo ERROR: Analyze script execution failed. Check log file for details.
    ) else (
        echo Analyze script executed successfully.
    )
    echo Script finished at %date% %time% >> "%LOG_FILE%"
) else (
    echo Invalid choice. Exiting.
    goto Deactivate
)

:Deactivate
echo --- >> "%LOG_FILE%"
echo. >> "%LOG_FILE%"

echo Deactivating Conda environment...
if "%COMSPEC%"=="%windir%\system32\cmd.exe" (
    call conda deactivate >> "%LOG_FILE%" 2>>&1
) else (
    conda deactivate >> "%LOG_FILE%" 2>>&1
)

echo Script execution finished. Check "%LOG_FILE%" for details.

:End
pause