"""
数字货币数据提供者模块
基于Qlib框架的加密货币数据获取和处理
"""

import ccxt
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta
import asyncio
import aiohttp
from loguru import logger
import time
from qlib.data.base import BaseProvider
from qlib.utils import get_module_logger


class CryptoDataProvider(BaseProvider):
    """加密货币数据提供者，继承Qlib的BaseProvider"""
    
    def __init__(self, config: Dict):
        """
        初始化数据提供者
        
        Args:
            config: 配置字典，包含交易所API配置
        """
        super().__init__()
        self.config = config
        self.exchanges = {}
        self.logger = get_module_logger(self.__class__.__name__)
        self._init_exchanges()
    
    def _init_exchanges(self):
        """初始化交易所连接"""
        try:
            # 初始化Binance
            if 'binance' in self.config['data_sources']:
                binance_config = self.config['data_sources']['binance']
                self.exchanges['binance'] = ccxt.binance({
                    'apiKey': binance_config.get('api_key', ''),
                    'secret': binance_config.get('api_secret', ''),
                    'sandbox': binance_config.get('testnet', False),
                    'enableRateLimit': True,
                })
            
            # 初始化Coinbase Pro
            if 'coinbase' in self.config['data_sources']:
                coinbase_config = self.config['data_sources']['coinbase']
                self.exchanges['coinbase'] = ccxt.coinbasepro({
                    'apiKey': coinbase_config.get('api_key', ''),
                    'secret': coinbase_config.get('api_secret', ''),
                    'passphrase': coinbase_config.get('passphrase', ''),
                    'enableRateLimit': True,
                })
            
            self.logger.info(f"初始化了 {len(self.exchanges)} 个交易所连接")
            
        except Exception as e:
            self.logger.error(f"初始化交易所连接失败: {e}")
            raise
    
    def get_ohlcv_data(self, 
                       symbol: str, 
                       timeframe: str = '1h',
                       start_time: Optional[datetime] = None,
                       end_time: Optional[datetime] = None,
                       exchange: str = 'binance') -> pd.DataFrame:
        """
        获取OHLCV数据
        
        Args:
            symbol: 交易对符号，如'BTC/USDT'
            timeframe: 时间周期
            start_time: 开始时间
            end_time: 结束时间
            exchange: 交易所名称
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        try:
            if exchange not in self.exchanges:
                raise ValueError(f"不支持的交易所: {exchange}")
            
            exchange_obj = self.exchanges[exchange]
            
            # 设置默认时间范围
            if end_time is None:
                end_time = datetime.now()
            if start_time is None:
                start_time = end_time - timedelta(days=30)
            
            # 转换为毫秒时间戳
            since = int(start_time.timestamp() * 1000)
            until = int(end_time.timestamp() * 1000)
            
            # 获取数据
            ohlcv = exchange_obj.fetch_ohlcv(
                symbol, timeframe, since=since, limit=1000
            )
            
            # 转换为DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('datetime', inplace=True)
            df.drop('timestamp', axis=1, inplace=True)
            
            # 过滤时间范围
            df = df[(df.index >= start_time) & (df.index <= end_time)]
            
            self.logger.info(f"获取 {symbol} {timeframe} 数据成功，共 {len(df)} 条记录")
            return df
            
        except Exception as e:
            self.logger.error(f"获取 {symbol} 数据失败: {e}")
            raise
    
    def get_multiple_symbols_data(self, 
                                  symbols: List[str],
                                  timeframe: str = '1h',
                                  start_time: Optional[datetime] = None,
                                  end_time: Optional[datetime] = None,
                                  exchange: str = 'binance') -> Dict[str, pd.DataFrame]:
        """
        批量获取多个交易对的数据
        
        Args:
            symbols: 交易对列表
            timeframe: 时间周期
            start_time: 开始时间
            end_time: 结束时间
            exchange: 交易所名称
            
        Returns:
            字典，键为交易对，值为对应的DataFrame
        """
        data_dict = {}
        
        for symbol in symbols:
            try:
                data_dict[symbol] = self.get_ohlcv_data(
                    symbol, timeframe, start_time, end_time, exchange
                )
                # 添加延迟避免API限制
                time.sleep(0.1)
                
            except Exception as e:
                self.logger.warning(f"获取 {symbol} 数据失败: {e}")
                continue
        
        self.logger.info(f"成功获取 {len(data_dict)}/{len(symbols)} 个交易对的数据")
        return data_dict
    
    def get_realtime_price(self, symbol: str, exchange: str = 'binance') -> Dict:
        """
        获取实时价格数据
        
        Args:
            symbol: 交易对符号
            exchange: 交易所名称
            
        Returns:
            包含实时价格信息的字典
        """
        try:
            if exchange not in self.exchanges:
                raise ValueError(f"不支持的交易所: {exchange}")
            
            exchange_obj = self.exchanges[exchange]
            ticker = exchange_obj.fetch_ticker(symbol)
            
            return {
                'symbol': symbol,
                'price': ticker['last'],
                'bid': ticker['bid'],
                'ask': ticker['ask'],
                'volume': ticker['baseVolume'],
                'change': ticker['change'],
                'percentage': ticker['percentage'],
                'timestamp': datetime.fromtimestamp(ticker['timestamp'] / 1000)
            }
            
        except Exception as e:
            self.logger.error(f"获取 {symbol} 实时价格失败: {e}")
            raise
    
    def get_market_data_for_qlib(self, 
                                 symbols: List[str],
                                 timeframe: str = '1d',
                                 start_time: Optional[datetime] = None,
                                 end_time: Optional[datetime] = None) -> pd.DataFrame:
        """
        获取符合Qlib格式的市场数据
        
        Args:
            symbols: 交易对列表
            timeframe: 时间周期
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            符合Qlib格式的多级索引DataFrame
        """
        all_data = []
        
        for symbol in symbols:
            try:
                df = self.get_ohlcv_data(symbol, timeframe, start_time, end_time)
                
                # 添加instrument列
                df['instrument'] = symbol.replace('/', '')  # 移除斜杠
                
                # 重置索引，将datetime作为列
                df.reset_index(inplace=True)
                
                # 重命名列以符合Qlib格式
                df.rename(columns={
                    'datetime': 'datetime',
                    'open': '$open',
                    'high': '$high', 
                    'low': '$low',
                    'close': '$close',
                    'volume': '$volume'
                }, inplace=True)
                
                all_data.append(df)
                
            except Exception as e:
                self.logger.warning(f"处理 {symbol} 数据失败: {e}")
                continue
        
        if not all_data:
            raise ValueError("没有成功获取任何数据")
        
        # 合并所有数据
        combined_df = pd.concat(all_data, ignore_index=True)
        
        # 设置多级索引
        combined_df.set_index(['instrument', 'datetime'], inplace=True)
        combined_df.sort_index(inplace=True)
        
        self.logger.info(f"为Qlib准备了 {len(symbols)} 个交易对的数据")
        return combined_df


class CryptoDataCollector:
    """加密货币数据收集器，用于定期收集和更新数据"""
    
    def __init__(self, provider: CryptoDataProvider, config: Dict):
        self.provider = provider
        self.config = config
        self.logger = get_module_logger(self.__class__.__name__)
    
    async def collect_data_continuously(self):
        """持续收集数据"""
        while True:
            try:
                await self._collect_batch_data()
                await asyncio.sleep(self.config['system']['data_update_interval'])
                
            except Exception as e:
                self.logger.error(f"数据收集过程中出错: {e}")
                await asyncio.sleep(60)  # 出错时等待1分钟再重试
    
    async def _collect_batch_data(self):
        """批量收集数据"""
        all_symbols = (
            self.config['trading_pairs']['major_pairs'] + 
            self.config['trading_pairs']['defi_pairs']
        )
        
        for timeframe in self.config['timeframes']['available']:
            try:
                data = self.provider.get_multiple_symbols_data(
                    symbols=all_symbols,
                    timeframe=timeframe,
                    start_time=datetime.now() - timedelta(hours=24),
                    end_time=datetime.now()
                )
                
                # 这里可以添加数据存储逻辑
                self.logger.info(f"收集了 {timeframe} 周期的 {len(data)} 个交易对数据")
                
            except Exception as e:
                self.logger.error(f"收集 {timeframe} 数据失败: {e}")
