import ccxt
import logging
import time
import requests
import socket
import json
from datetime import datetime
import os
import traceback

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('exchange_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ExchangeConnectionTester:
    def __init__(self):
        # 配置主要交易所
        self.exchanges = {
            'gateio': ccxt.gateio({
                'timeout': 15000,
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'spot',
                    'adjustForTimeDifference': True
                }
            })
        }
    
    def test_internet_connection(self):
        """测试基本的互联网连接"""
        logger.info("测试基本互联网连接...")
        try:
            response = requests.get('https://www.google.com', timeout=10)
            logger.info(f"Google连接测试成功: {response.status_code}")
            return True
        except Exception as e:
            logger.error(f"互联网连接测试失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def get_public_ip(self):
        """获取当前公网IP"""
        logger.info("获取公网IP...")
        try:
            response = requests.get('https://api.ipify.org?format=json')
            ip_info = response.json()
            logger.info(f"当前公网IP: {ip_info['ip']}")
            return ip_info['ip']
        except Exception as e:
            logger.error(f"获取公网IP失败: {str(e)}")
            logger.error(traceback.format_exc())
            return None
    
    def test_dns_resolution(self, domain):
        """测试DNS解析"""
        logger.info(f"测试DNS解析: {domain}")
        
        # 备用域名映射
        backup_domains = {
            'www.gateio.com': [
                'api.gateio.ws',
                'api.gateio.io',
                'api.gateio.com'
            ]
        }
        
        domains_to_try = [domain]
        if domain in backup_domains:
            domains_to_try.extend(backup_domains[domain])
            
        max_retries = 2
        for d in domains_to_try:
            for attempt in range(max_retries):
                try:
                    if attempt > 0:
                        logger.info(f"第 {attempt + 1} 次尝试解析域名: {d}")
                        time.sleep(1)  # 重试前等待1秒
                        
                    ip = socket.gethostbyname(d)
                    logger.info(f"DNS解析成功: {d} -> {ip}")
                    return True
                except Exception as e:
                    if attempt == max_retries - 1:
                        logger.warning(f"域名 {d} DNS解析失败: {str(e)}")
                    else:
                        logger.info(f"域名 {d} 第 {attempt + 1} 次解析失败，准备重试")
                    continue
                
        logger.error(f"所有域名DNS解析均失败: {domain}")
        return False
    
    def test_exchange_connection(self, exchange_id: str, exchange: ccxt.Exchange):
        """测试交易所API连接"""
        logger.info(f"测试交易所连接: {exchange_id}")
        max_retries = 3
        retry_delay = 2  # 秒
        
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    logger.info(f"第 {attempt + 1} 次重试连接 {exchange_id}")
                    time.sleep(retry_delay)
                
                # 测试服务器时间（最基础的API调用）
                server_time = exchange.fetch_time()
                time_diff = abs(server_time - int(time.time() * 1000))
                logger.info(f"服务器时间差: {time_diff}ms")
                
                # 如果时间差太大，可能有问题
                if time_diff > 5000:  # 5秒
                    raise Exception(f"服务器时间差过大: {time_diff}ms")
                
                # 测试市场数据加载
                markets = exchange.load_markets()
                logger.info(f"成功加载市场数据: {len(markets)} 个交易对")
                
                # 测试获取行情
                symbol = 'BTC/USDT'
                ticker = exchange.fetch_ticker(symbol)
                logger.info(f"成功获取行情数据: {symbol}")
                
                # 测试获取K线数据
                ohlcv = exchange.fetch_ohlcv(symbol, timeframe='1d', limit=1)
                logger.info(f"成功获取K线数据: {symbol}")
                
                return True
            except ccxt.NetworkError as e:
                logger.error(f"第 {attempt + 1} 次连接测试失败 (网络错误): {str(e)}")
                if attempt < max_retries - 1:
                    logger.info(f"等待 {retry_delay} 秒后重试...")
                else:
                    logger.error("达到最大重试次数，测试失败")
            except ccxt.ExchangeError as e:
                logger.error(f"第 {attempt + 1} 次连接测试失败 (交易所错误): {str(e)}")
                if attempt < max_retries - 1:
                    logger.info(f"等待 {retry_delay} 秒后重试...")
                else:
                    logger.error("达到最大重试次数，测试失败")
            except Exception as e:
                logger.error(f"第 {attempt + 1} 次连接测试失败 (其他错误): {str(e)}")
                logger.error(traceback.format_exc())
                if attempt < max_retries - 1:
                    logger.info(f"等待 {retry_delay} 秒后重试...")
                else:
                    logger.error("达到最大重试次数，测试失败")
        
        return False
    
    def test_api_endpoints(self, exchange_id: str, exchange: ccxt.Exchange):
        """测试各个API端点的延迟"""
        logger.info(f"测试API端点延迟: {exchange_id}")
        endpoints = {
            'markets': exchange.fetch_markets,
            'ticker': lambda: exchange.fetch_ticker('BTC/USDT'),
            'ohlcv': lambda: exchange.fetch_ohlcv('BTC/USDT', '1d', limit=1)
        }
        
        results = {}
        timeout = 10  # 秒
        max_retries = 2  # 每个端点最多重试2次
        
        for name, func in endpoints.items():
            for attempt in range(max_retries):
                try:
                    if attempt > 0:
                        logger.info(f"第 {attempt + 1} 次重试端点 {name}")
                        time.sleep(2)  # 重试前等待2秒
                    
                    start_time = time.time()
                    # 设置超时
                    exchange.timeout = timeout * 1000  # 转换为毫秒
                    func()
                    latency = (time.time() - start_time) * 1000
                    
                    if latency > timeout * 1000:
                        raise TimeoutError(f"请求超时: {latency:.2f}ms > {timeout * 1000}ms")
                    
                    results[name] = {
                        'status': 'success',
                        'latency_ms': round(latency, 2),
                        'attempts': attempt + 1
                    }
                    logger.info(f"端点 {name} 测试成功，延迟: {latency:.2f}ms (尝试 {attempt + 1} 次)")
                    break  # 成功后跳出重试循环
                    
                except Exception as e:
                    if attempt == max_retries - 1:  # 最后一次尝试
                        results[name] = {
                            'status': 'error',
                            'error': str(e),
                            'attempts': attempt + 1
                        }
                        logger.error(f"端点 {name} 测试失败: {str(e)}")
                        logger.error(traceback.format_exc())
                    else:
                        logger.warning(f"端点 {name} 第 {attempt + 1} 次尝试失败: {str(e)}")
        
        return results
    
    def run_all_tests(self):
        """运行所有测试"""
        results = {
            'timestamp': datetime.now().isoformat(),
            'internet_connection': self.test_internet_connection(),
            'public_ip': self.get_public_ip(),
            'dns_resolution': {},
            'exchange_connection': {},
            'api_endpoints': {}
        }
        
        # 测试主要交易所
        logger.info("开始测试主要交易所...")
        for exchange_id, exchange in self.exchanges.items():
            domain = f'www.{exchange_id}.com'
            results['dns_resolution'][domain] = self.test_dns_resolution(domain)
            results['exchange_connection'][exchange_id] = self.test_exchange_connection(exchange_id, exchange)
            results['api_endpoints'][exchange_id] = self.test_api_endpoints(exchange_id, exchange)
        
        # 保存测试结果
        with open('connection_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        return results

def main():
    try:
        tester = ExchangeConnectionTester()
        logger.info("开始连接测试...")
        results = tester.run_all_tests()
        logger.info("测试完成，结果已保存到 connection_test_results.json")
        
        # 打印摘要
        print("\n=== 测试结果摘要 ===")
        print(f"互联网连接: {'成功' if results['internet_connection'] else '失败'}")
        print(f"公网IP: {results['public_ip']}")
        
        print("\nDNS解析:")
        for domain, success in results['dns_resolution'].items():
            print(f"  {domain}: {'成功' if success else '失败'}")
        
        print("\n交易所连接:")
        for exchange, success in results['exchange_connection'].items():
            print(f"  {exchange}: {'成功' if success else '失败'}")
        
        print("\nAPI端点延迟:")
        for exchange, endpoints in results['api_endpoints'].items():
            print(f"\n  {exchange}:")
            for endpoint, result in endpoints.items():
                status = result.get('status', 'error')
                if status == 'success':
                    print(f"    {endpoint}: {result['latency_ms']}ms")
                else:
                    print(f"    {endpoint}: 失败 - {result.get('error', 'unknown error')}")
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main() 