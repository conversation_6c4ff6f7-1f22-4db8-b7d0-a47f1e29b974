"""
基于Qlib的数字货币Alpha表达式选币策略
实现K线形态、技术指标、多时间周期确认的选币条件
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from qlib.data import D
from qlib.utils import get_module_logger
import qlib

# Qlib Alpha表达式定义
CRYPTO_ALPHA_EXPRESSIONS = {
    
    # ==================== K线形态识别 ====================
    
    # 锤子线识别 (Hammer Pattern)
    "hammer_pattern": """
        # 锤子线：下影线长度 >= 实体2倍，上影线很短，出现在下跌趋势中
        (
            # 下影线长度 >= 实体长度的2倍
            (($low - Min($open, $close)) >= 2 * Abs($open - $close)) &
            # 上影线很短 (< 实体长度的0.1倍)
            (($high - Max($open, $close)) <= 0.1 * Abs($open - $close)) &
            # 实体不能太小 (避免十字星)
            (Abs($open - $close) >= 0.001 * $close) &
            # 前期处于下跌趋势 (5日均线下降)
            (Mean($close, 5) < Ref(Mean($close, 5), 1))
        )
    """,
    
    # 十字星识别 (Doji Pattern)
    "doji_pattern": """
        # 十字星：开盘价接近收盘价，上下影线较长
        (
            # 实体很小 (< 0.1%的价格波动)
            (Abs($open - $close) <= 0.001 * $close) &
            # 有一定的上下影线
            (($high - $low) >= 0.01 * $close) &
            # 上下影线相对均衡
            (Abs(($high - Max($open, $close)) - ($low - Min($open, $close))) <= 0.005 * $close)
        )
    """,
    
    # 吞没形态识别 (Engulfing Pattern)
    "bullish_engulfing": """
        # 看涨吞没：今日阳线完全包含昨日阴线
        (
            # 昨日为阴线
            (Ref($close, 1) < Ref($open, 1)) &
            # 今日为阳线
            ($close > $open) &
            # 今日开盘价低于昨日收盘价
            ($open < Ref($close, 1)) &
            # 今日收盘价高于昨日开盘价
            ($close > Ref($open, 1)) &
            # 今日实体大于昨日实体
            (($close - $open) > (Ref($open, 1) - Ref($close, 1)))
        )
    """,
    
    # 价格突破识别
    "breakout_resistance": """
        # 突破阻力位：价格突破近20日高点
        (
            # 今日收盘价突破20日内最高价
            ($close > Ref(Max($high, 20), 1)) &
            # 成交量放大确认
            ($volume > 1.5 * Mean($volume, 10)) &
            # 突破幅度 > 1%
            (($close / Ref(Max($high, 20), 1) - 1) > 0.01)
        )
    """,
    
    "breakout_support": """
        # 跌破支撑位后的反弹
        (
            # 前期跌破20日最低点
            (Ref($low, 1) < Ref(Min($low, 20), 2)) &
            # 今日收盘价回到支撑位上方
            ($close > Ref(Min($low, 20), 2)) &
            # 今日为阳线
            ($close > $open) &
            # 成交量放大
            ($volume > 1.2 * Mean($volume, 10))
        )
    """,
    
    # ==================== 技术指标条件 ====================
    
    # RSI超卖后回升
    "rsi_oversold_recovery": """
        # RSI从超卖区域回升
        (
            # 前期RSI < 30 (超卖)
            (Ref(RSI($close, 14), 1) < 30) &
            # 当前RSI > 35 (开始回升)
            (RSI($close, 14) > 35) &
            # RSI呈上升趋势
            (RSI($close, 14) > Ref(RSI($close, 14), 1))
        )
    """,
    
    # MACD金叉确认
    "macd_golden_cross": """
        # MACD金叉信号
        (
            # MACD线上穿信号线
            (EMA($close, 12) - EMA($close, 26) > EMA(EMA($close, 12) - EMA($close, 26), 9)) &
            # 前一日MACD线在信号线下方
            (Ref(EMA($close, 12) - EMA($close, 26), 1) <= Ref(EMA(EMA($close, 12) - EMA($close, 26), 9), 1)) &
            # MACD柱状图转正
            ((EMA($close, 12) - EMA($close, 26)) - EMA(EMA($close, 12) - EMA($close, 26), 9) > 0)
        )
    """,
    
    # 布林带下轨支撑反弹
    "bollinger_support_bounce": """
        # 布林带下轨支撑反弹
        (
            # 前期价格触及或跌破布林带下轨
            (Ref($low, 1) <= Ref(Mean($close, 20) - 2 * Std($close, 20), 1)) &
            # 今日收盘价回到布林带下轨上方
            ($close > Mean($close, 20) - 2 * Std($close, 20)) &
            # 今日为阳线
            ($close > $open) &
            # 布林带宽度不能太窄 (避免震荡市)
            ((Mean($close, 20) + 2 * Std($close, 20)) / (Mean($close, 20) - 2 * Std($close, 20)) > 1.04)
        )
    """,
    
    # 成交量放大确认
    "volume_breakout": """
        # 成交量突破确认
        (
            # 成交量大于20日均量的1.5倍
            ($volume > 1.5 * Mean($volume, 20)) &
            # 价格上涨
            ($close > $open) &
            # 价格创近10日新高
            ($close >= Max($close, 10))
        )
    """,
    
    # ==================== 趋势确认指标 ====================
    
    # 多头排列确认
    "bullish_alignment": """
        # 均线多头排列
        (
            # 短期均线在长期均线上方
            (Mean($close, 5) > Mean($close, 10)) &
            (Mean($close, 10) > Mean($close, 20)) &
            (Mean($close, 20) > Mean($close, 50)) &
            # 均线呈上升趋势
            (Mean($close, 5) > Ref(Mean($close, 5), 1)) &
            (Mean($close, 20) > Ref(Mean($close, 20), 1))
        )
    """,
    
    # 趋势反转信号
    "trend_reversal": """
        # 趋势反转信号
        (
            # 前期下跌趋势 (20日均线下降)
            (Ref(Mean($close, 20), 5) > Mean($close, 20)) &
            # 当前出现反转迹象
            (Mean($close, 5) > Mean($close, 20)) &
            # RSI从超卖区域回升
            (RSI($close, 14) > 50) &
            # 成交量配合
            ($volume > Mean($volume, 10))
        )
    """,
    
    # ==================== 综合选币条件 ====================
    
    # 强势突破选币
    "strong_breakout_stock": """
        # 强势突破选币条件
        (
            # 价格突破
            ($close > Ref(Max($high, 20), 1)) &
            # 成交量放大
            ($volume > 1.5 * Mean($volume, 20)) &
            # RSI不超买
            (RSI($close, 14) < 70) &
            # 均线支撑
            ($close > Mean($close, 20)) &
            # 突破幅度适中
            (($close / Ref($close, 1) - 1) > 0.02) &
            (($close / Ref($close, 1) - 1) < 0.15)
        )
    """,
    
    # 超跌反弹选币
    "oversold_rebound_stock": """
        # 超跌反弹选币条件
        (
            # RSI超卖后回升
            (Ref(RSI($close, 14), 2) < 30) &
            (RSI($close, 14) > 35) &
            # 布林带下轨支撑
            ($close > Mean($close, 20) - 2 * Std($close, 20)) &
            # 出现反弹K线形态
            ($close > $open) &
            # 下跌幅度已经较大
            (($close / Max($close, 20) - 1) < -0.1) &
            # 成交量配合
            ($volume > Mean($volume, 10))
        )
    """,
    
    # 趋势跟随选币
    "trend_following_stock": """
        # 趋势跟随选币条件
        (
            # 均线多头排列
            (Mean($close, 5) > Mean($close, 10)) &
            (Mean($close, 10) > Mean($close, 20)) &
            # MACD金叉
            (EMA($close, 12) - EMA($close, 26) > EMA(EMA($close, 12) - EMA($close, 26), 9)) &
            # 价格在布林带中上轨
            ($close > Mean($close, 20)) &
            ($close < Mean($close, 20) + 2 * Std($close, 20)) &
            # 成交量正常
            ($volume > 0.8 * Mean($volume, 20)) &
            # 涨幅不过大
            (($close / Ref($close, 5) - 1) < 0.2)
        )
    """
}

# 多时间周期表达式
MULTI_TIMEFRAME_EXPRESSIONS = {
    
    # 日线趋势向上，4小时回调买点
    "daily_uptrend_4h_pullback": """
        # 需要在策略中分别获取日线和4小时数据进行组合判断
        # 这里提供单一时间周期的表达式，多周期需要在策略层面实现
        (
            # 当前时间周期的回调条件 (假设为4小时)
            ($close < Mean($close, 10)) &
            ($close > Mean($close, 20)) &
            (RSI($close, 14) < 50) &
            ($volume > Mean($volume, 10))
        )
    """,
    
    # 多周期RSI确认
    "multi_timeframe_rsi": """
        # 当前周期RSI超卖回升
        (
            (Ref(RSI($close, 14), 1) < 35) &
            (RSI($close, 14) > 40) &
            (RSI($close, 14) < 60)
        )
    """
}

# 辅助函数表达式
HELPER_EXPRESSIONS = {
    
    # 计算ATR (Average True Range)
    "atr": """
        Mean(Max(Max($high - $low, Abs($high - Ref($close, 1))), Abs($low - Ref($close, 1))), 14)
    """,
    
    # 计算波动率
    "volatility": """
        Std($close / Ref($close, 1) - 1, 20)
    """,
    
    # 计算相对强度
    "relative_strength": """
        $close / Mean($close, 20) - 1
    """,
    
    # 计算成交量相对强度
    "volume_strength": """
        $volume / Mean($volume, 20) - 1
    """
}
