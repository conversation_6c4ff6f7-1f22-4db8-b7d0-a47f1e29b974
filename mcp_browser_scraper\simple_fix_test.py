#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单修复测试脚本
"""

import os
import json
from datetime import datetime

def log(message):
    """日志函数"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def main():
    """主函数"""
    log("开始修复验证...")
    
    # 测试1: 检查SSR配置
    log("1. 检查SSR配置文件...")
    if os.path.exists('ssr_config.json'):
        try:
            with open('ssr_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            log(f"   ✅ SSR配置文件存在，包含 {len(config)} 个配置项")
            
            if 'wechat_webhooks' in config:
                wechat_config = config['wechat_webhooks']
                robots = wechat_config.get('robots', [])
                log(f"   ✅ 微信配置存在，{len(robots)} 个机器人")
            else:
                log("   ⚠️ 微信配置不在SSR文件中")
        except Exception as e:
            log(f"   ❌ SSR配置文件读取失败: {e}")
    else:
        log("   ❌ SSR配置文件不存在")
    
    # 测试2: 检查微信配置
    log("2. 检查微信配置文件...")
    if os.path.exists('wechat_bots_config.json'):
        try:
            with open('wechat_bots_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            bots = config.get('bots', [])
            log(f"   ✅ 微信配置文件存在，{len(bots)} 个机器人")
        except Exception as e:
            log(f"   ❌ 微信配置文件读取失败: {e}")
    else:
        log("   ⚠️ 独立微信配置文件不存在")
    
    # 测试3: 检查脚本导入
    log("3. 检查脚本导入...")
    try:
        from advanced_crypto_scraper import AdvancedCryptoScraper
        log("   ✅ 脚本导入成功")
        
        # 创建实例测试
        scraper = AdvancedCryptoScraper(use_proxy=False)
        log("   ✅ 实例创建成功")
        
        # 检查数据源
        data_sources = scraper.data_sources
        real_sources = [name for name, config in data_sources.items() if config.get('real_data', False)]
        log(f"   ✅ 数据源配置: {len(data_sources)} 个总数，{len(real_sources)} 个真实数据源")
        log(f"   真实数据源: {', '.join(real_sources)}")
        
    except Exception as e:
        log(f"   ❌ 脚本测试失败: {e}")
    
    log("修复验证完成！")

if __name__ == "__main__":
    main()
