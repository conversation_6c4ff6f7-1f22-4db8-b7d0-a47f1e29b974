#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版形态分析脚本
"""

import requests
import sqlite3
import pandas as pd
import time
import json
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union
import numpy as np
from scipy.signal import find_peaks
from scipy.stats import linregress
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import seaborn as sns

def log(message):
    """增强的日志函数"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

class SimplifiedAdvancedCryptoScraper:
    """简化版高级加密货币数据抓取器"""

    def __init__(self, use_proxy: bool = False):
        """初始化抓取器"""
        print("🔧 开始初始化简化版抓取器...")
        self.use_proxy = use_proxy
        self.db_path = "advanced_crypto_data.db"
        
        print(f"✅ 基本参数设置完成，代理模式: {'启用' if use_proxy else '禁用'}")
        
        # 初始化请求会话
        print("🔧 初始化网络会话...")
        self._init_session()
        print("✅ 网络会话初始化完成")
        
        print("✅ 简化版抓取器初始化完成")

    def _init_session(self):
        """初始化请求会话"""
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
        })
        
        # 设置超时
        self.session.timeout = 30

    def _identify_patterns(self, historical_data: List[Dict]) -> List[str]:
        """识别K线形态 - 专门识别双长上影线形态"""
        patterns = []

        try:
            if len(historical_data) < 2:
                log("⚠️ 历史数据不足，无法进行双长上影线形态识别")
                return ["数据不足"]

            # 只分析最近的2根K线数据
            recent_candles = historical_data[-2:]
            
            log(f"🔍 开始双长上影线形态识别，分析最近2根K线...")
            
            # 分析每根K线的详细信息
            candle_info = []
            for i, candle in enumerate(recent_candles):
                try:
                    open_price = float(candle.get('open_price', 0))
                    high_price = float(candle.get('high_price', 0))
                    low_price = float(candle.get('low_price', 0))
                    close_price = float(candle.get('close_price', 0))
                    date = candle.get('date', 'N/A')
                    
                    if high_price <= 0 or low_price <= 0:
                        log(f"❌ 第{i+1}根K线数据异常: 最高价={high_price}, 最低价={low_price}")
                        return ["数据异常"]
                    
                    # 计算K线各部分长度
                    upper_shadow = high_price - max(open_price, close_price)  # 上影线长度
                    lower_shadow = min(open_price, close_price) - low_price   # 下影线长度
                    body_length = abs(close_price - open_price)               # 实体长度
                    total_length = high_price - low_price                     # 整根K线长度
                    
                    candle_info.append({
                        'index': i + 1,
                        'date': date,
                        'open': open_price,
                        'high': high_price,
                        'low': low_price,
                        'close': close_price,
                        'upper_shadow': upper_shadow,
                        'lower_shadow': lower_shadow,
                        'body_length': body_length,
                        'total_length': total_length
                    })
                    
                    log(f"📊 第{i+1}根K线 ({date}):")
                    log(f"   开盘: {open_price:.6f}, 最高: {high_price:.6f}, 最低: {low_price:.6f}, 收盘: {close_price:.6f}")
                    log(f"   上影线: {upper_shadow:.6f}, 实体: {body_length:.6f}, 整根: {total_length:.6f}")
                    log(f"   上影线比例: {(upper_shadow/total_length*100):.1f}%, 实体比例: {(body_length/total_length*100):.1f}%")
                    
                except (ValueError, TypeError, KeyError) as e:
                    log(f"❌ 处理第{i+1}根K线数据时出错: {e}")
                    return ["数据处理错误"]
            
            # 检查双长上影线形态
            if len(candle_info) == 2:
                first_candle = candle_info[0]
                second_candle = candle_info[1]
                
                log(f"🔍 开始双长上影线形态检查...")
                
                # 第一根K线条件检查
                first_upper_ratio = first_candle['upper_shadow'] / first_candle['total_length']
                first_body_ratio = first_candle['body_length'] / first_candle['total_length']
                first_is_long_upper = (first_upper_ratio >= 1/3) and (first_body_ratio <= 2/3)
                
                log(f"📈 第一根K线检查:")
                log(f"   上影线比例: {first_upper_ratio:.3f} (需要 ≥ 0.333)")
                log(f"   实体比例: {first_body_ratio:.3f} (需要 ≤ 0.667)")
                log(f"   是否长上影线: {'✅' if first_is_long_upper else '❌'}")
                
                # 第二根K线条件检查
                second_upper_ratio = second_candle['upper_shadow'] / second_candle['total_length']
                second_body_ratio = second_candle['body_length'] / second_candle['total_length']
                second_is_long_upper = (second_upper_ratio >= 1/3) and (second_body_ratio <= 2/3)
                
                log(f"📈 第二根K线检查:")
                log(f"   上影线比例: {second_upper_ratio:.3f} (需要 ≥ 0.333)")
                log(f"   实体比例: {second_body_ratio:.3f} (需要 ≤ 0.667)")
                log(f"   是否长上影线: {'✅' if second_is_long_upper else '❌'}")
                
                # 高点递减检查
                high_decreasing = second_candle['high'] < first_candle['high']
                log(f"📉 高点递减检查:")
                log(f"   第一根最高价: {first_candle['high']:.6f}")
                log(f"   第二根最高价: {second_candle['high']:.6f}")
                log(f"   高点递减: {'✅' if high_decreasing else '❌'}")
                
                # 综合判断
                if first_is_long_upper and second_is_long_upper and high_decreasing:
                    patterns.append("双长上影线")
                    log("🎯 ✅ 识别到双长上影线形态！")
                    log(f"   形态特征: 连续两根长上影线，高点递减")
                    log(f"   技术含义: 上涨乏力，可能出现回调")
                else:
                    log("❌ 未满足双长上影线形态条件")
                    if not first_is_long_upper:
                        log("   原因: 第一根K线不是长上影线")
                    if not second_is_long_upper:
                        log("   原因: 第二根K线不是长上影线")
                    if not high_decreasing:
                        log("   原因: 高点未递减")
                    patterns.append("非双长上影线")
            
            return patterns if patterns else ["无明显形态"]

        except Exception as e:
            log(f"❌ 形态识别过程中出错: {e}")
            import traceback
            log(f"错误详情: {traceback.format_exc()}")
            return ["识别错误"]

    def test_pattern_analysis(self):
        """测试形态分析功能"""
        log("🧪 开始测试双长上影线形态识别...")
        
        # 构造测试数据 - 模拟双长上影线形态
        test_historical_data = [
            # 第一根K线 - 长上影线
            {
                'date': '2025-06-20',
                'open_price': 100.0,
                'high_price': 120.0,  # 高点
                'low_price': 95.0,
                'close_price': 105.0,  # 实体较小
                'volume': 1000000
            },
            # 第二根K线 - 长上影线，高点递减
            {
                'date': '2025-06-21',
                'open_price': 105.0,
                'high_price': 115.0,  # 比第一根低
                'low_price': 100.0,
                'close_price': 108.0,  # 实体较小
                'volume': 1200000
            }
        ]
        
        log("📊 测试数据:")
        for i, candle in enumerate(test_historical_data, 1):
            log(f"   第{i}根K线: 开盘={candle['open_price']}, 最高={candle['high_price']}, "
                f"最低={candle['low_price']}, 收盘={candle['close_price']}")
        
        # 测试形态识别
        patterns = self._identify_patterns(test_historical_data)
        
        log(f"🎯 识别结果: {patterns}")
        
        if "双长上影线" in patterns:
            log("✅ 双长上影线形态识别测试成功！")
            return True
        else:
            log("❌ 双长上影线形态识别测试失败")
            return False

    def show_menu(self):
        """显示简化菜单"""
        log("\n" + "="*80)
        log("🎯 简化版形态分析脚本")
        log("="*80)
        log("1. 测试双长上影线形态识别")
        log("2. 退出")
        log("="*80)
        
        while True:
            try:
                choice = input("请选择功能 (1-2): ").strip()
                
                if choice == "1":
                    self.test_pattern_analysis()
                elif choice == "2":
                    log("👋 退出程序")
                    break
                else:
                    log("❌ 无效选择，请重新输入")
                    
            except KeyboardInterrupt:
                log("\n👋 用户中断程序")
                break
            except Exception as e:
                log(f"❌ 程序出错: {e}")

def main():
    """主程序入口"""
    try:
        print("🚀 启动简化版形态分析脚本...")
        print("="*80)
        
        # 询问用户选择代理模式
        print("🌐 网络连接模式选择:")
        print("1. 使用代理")
        print("2. 直连模式 (推荐)")
        
        choice = input("请选择 (1/2, 默认2): ").strip() or "2"
        
        if choice == "1":
            print("🔗 启用代理模式...")
            scraper = SimplifiedAdvancedCryptoScraper(use_proxy=True)
        else:
            print("🔗 启用直连模式...")
            scraper = SimplifiedAdvancedCryptoScraper(use_proxy=False)
        
        print("✅ 初始化完成！")
        print("="*80)
        
        # 显示主菜单
        scraper.show_menu()
        
    except KeyboardInterrupt:
        print("\n👋 用户中断程序，正在退出...")
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
