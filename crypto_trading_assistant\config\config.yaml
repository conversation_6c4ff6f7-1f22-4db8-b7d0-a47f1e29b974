# 数字货币交易辅助系统配置文件

# Qlib配置
qlib_config:
  provider_uri: "~/.qlib/qlib_data/crypto_data"
  region: "crypto"
  logging_level: "INFO"

# 数据源配置
data_sources:
  binance:
    api_key: ""
    api_secret: ""
    base_url: "https://api.binance.com"
    testnet: false
  
  coinbase:
    api_key: ""
    api_secret: ""
    passphrase: ""
    base_url: "https://api.pro.coinbase.com"

# 交易对配置
trading_pairs:
  major_pairs:
    - "BTCUSDT"
    - "ETHUSDT"
    - "BNBUSDT"
    - "ADAUSDT"
    - "DOTUSDT"
    - "LINKUSDT"
    - "LTCUSDT"
    - "BCHUSDT"
    - "XLMUSDT"
    - "EOSUSDT"
  
  defi_pairs:
    - "UNIUSDT"
    - "AAVEUSDT"
    - "SUSHIUSDT"
    - "COMPUSDT"
    - "MKRUSDT"

# 时间周期配置
timeframes:
  primary: "4h"      # 主要分析周期
  secondary: "1d"    # 趋势确认周期
  entry: "1h"        # 入场精确周期
  available: ["1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w"]

# 技术形态识别配置
pattern_recognition:
  head_and_shoulders:
    enabled: true
    min_periods: 20
    tolerance: 0.02
    volume_confirmation: true
  
  triangle:
    enabled: true
    min_periods: 15
    convergence_threshold: 0.01
    breakout_volume_ratio: 1.5
  
  flag_pennant:
    enabled: true
    min_periods: 10
    max_periods: 30
    slope_tolerance: 0.005
    volume_decline_ratio: 0.7

# 买点信号配置
entry_signals:
  pattern_breakout:
    enabled: true
    volume_confirmation: true
    min_volume_ratio: 1.2
  
  indicator_confluence:
    enabled: true
    required_indicators: 2
    indicators:
      - "rsi_oversold"
      - "macd_bullish_cross"
      - "bollinger_squeeze"
      - "support_bounce"
  
  multi_timeframe:
    enabled: true
    primary_signal_required: true
    secondary_confirmation: true

# 风险控制配置
risk_management:
  stop_loss:
    default_percentage: 0.05  # 5%
    pattern_based: true
    atr_multiplier: 2.0
  
  position_sizing:
    max_risk_per_trade: 0.02  # 2%
    kelly_criterion: false
    fixed_amount: 1000  # USDT
  
  max_positions: 5
  correlation_limit: 0.7

# 消息推送配置
notifications:
  enterprise_wechat:
    enabled: true
    webhook_url: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=69db19ba-d1af-422a-b0cf-19f21cd5b5fc"
    secret: ""
    mentioned_list: ["@all"]
    mentioned_mobile_list: []

  dingtalk:
    enabled: false
    webhook_url: ""
    secret: ""
    at_mobiles: []
    at_all: false
  
  message_template:
    include_chart: true
    chart_timeframe: "4h"
    chart_periods: 100

# 系统运行配置
system:
  scan_interval: 300  # 5分钟扫描一次
  data_update_interval: 60  # 1分钟更新数据
  max_concurrent_requests: 10
  retry_attempts: 3
  retry_delay: 5

# 日志配置
logging:
  level: "INFO"
  file: "logs/crypto_trading.log"
  max_size: "10MB"
  backup_count: 5
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
