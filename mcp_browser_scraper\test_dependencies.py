#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
依赖测试脚本
"""

print("开始测试依赖...")

try:
    import requests
    print("✅ requests 导入成功")
except Exception as e:
    print(f"❌ requests 导入失败: {e}")

try:
    import sqlite3
    print("✅ sqlite3 导入成功")
except Exception as e:
    print(f"❌ sqlite3 导入失败: {e}")

try:
    import pandas as pd
    print("✅ pandas 导入成功")
except Exception as e:
    print(f"❌ pandas 导入失败: {e}")

try:
    import numpy as np
    print("✅ numpy 导入成功")
except Exception as e:
    print(f"❌ numpy 导入失败: {e}")

try:
    import schedule
    print("✅ schedule 导入成功")
except Exception as e:
    print(f"❌ schedule 导入失败: {e}")

try:
    import socks
    print("✅ socks 导入成功")
except Exception as e:
    print(f"❌ socks 导入失败: {e}")

try:
    from scipy.signal import find_peaks
    from scipy.stats import linregress
    print("✅ scipy 导入成功")
except Exception as e:
    print(f"❌ scipy 导入失败: {e}")

try:
    import yaml
    print("✅ yaml 导入成功")
except Exception as e:
    print(f"❌ yaml 导入失败: {e}")

print("依赖测试完成！")
