"""
快速演示数字货币选币系统核心功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def main():
    print("🚀 基于Qlib的数字货币选币系统")
    print("=" * 60)
    print("技术分析选币策略演示")
    print("=" * 60)
    
    # 模拟选币结果
    mock_results = [
        {
            'symbol': 'BTCUSDT',
            'score': 4.2,
            'pattern_score': 2.0,
            'indicator_score': 1.8,
            'trend_score': 0.3,
            'volume_score': 0.1,
            'current_price': 45234.56,
            'rsi': 45.2,
            'signals': ['MACD金叉', '成交量放大', '均线支撑']
        },
        {
            'symbol': 'ETHUSDT',
            'score': 3.8,
            'pattern_score': 1.5,
            'indicator_score': 2.0,
            'trend_score': 0.2,
            'volume_score': 0.1,
            'current_price': 3156.78,
            'rsi': 42.1,
            'signals': ['RSI回升', '突破阻力', '布林带支撑']
        },
        {
            'symbol': 'BNBUSDT',
            'score': 3.5,
            'pattern_score': 2.0,
            'indicator_score': 1.2,
            'trend_score': 0.2,
            'volume_score': 0.1,
            'current_price': 412.34,
            'rsi': 38.9,
            'signals': ['锤子线', '布林带支撑']
        },
        {
            'symbol': 'ADAUSDT',
            'score': 3.2,
            'pattern_score': 1.0,
            'indicator_score': 1.8,
            'trend_score': 0.3,
            'volume_score': 0.1,
            'current_price': 1.2345,
            'rsi': 35.6,
            'signals': ['RSI超卖回升', 'MACD金叉']
        },
        {
            'symbol': 'SOLUSDT',
            'score': 2.9,
            'pattern_score': 0.5,
            'indicator_score': 1.5,
            'trend_score': 0.8,
            'volume_score': 0.1,
            'current_price': 98.76,
            'rsi': 52.3,
            'signals': ['多头排列', '成交量确认']
        }
    ]
    
    print("\n📊 选币结果")
    print("-" * 80)
    print(f"{'排名':<4} {'代码':<12} {'总分':<6} {'形态':<6} {'指标':<6} {'趋势':<6} {'成交量':<8} {'当前价格':<12} {'RSI':<6} {'主要信号'}")
    print("-" * 80)
    
    for i, result in enumerate(mock_results, 1):
        signals_str = ', '.join(result['signals'][:2])
        if len(result['signals']) > 2:
            signals_str += '...'
        
        print(f"{i:<4} {result['symbol']:<12} {result['score']:<6.1f} "
              f"{result['pattern_score']:<6.1f} {result['indicator_score']:<6.1f} "
              f"{result['trend_score']:<6.1f} {result['volume_score']:<8.1f} "
              f"{result['current_price']:<12.4f} {result['rsi']:<6.1f} {signals_str}")
    
    print("\n" + "=" * 60)
    print("📈 Qlib Alpha表达式示例")
    print("=" * 60)
    
    expressions = {
        "锤子线形态": """
        # 锤子线识别
        (
            # 下影线长度 >= 实体长度的2倍
            (($low - Min($open, $close)) >= 2 * Abs($open - $close)) &
            # 上影线很短
            (($high - Max($open, $close)) <= 0.1 * Abs($open - $close)) &
            # 前期下跌趋势
            (Mean($close, 5) < Ref(Mean($close, 5), 1))
        )
        """,
        
        "RSI超卖回升": """
        # RSI从超卖区域回升
        (
            (Ref(RSI($close, 14), 1) < 30) &  # 前期超卖
            (RSI($close, 14) > 35) &          # 当前回升
            (RSI($close, 14) > Ref(RSI($close, 14), 1))  # 上升趋势
        )
        """,
        
        "MACD金叉": """
        # MACD金叉信号
        (
            (EMA($close, 12) - EMA($close, 26) > 
             EMA(EMA($close, 12) - EMA($close, 26), 9)) &
            (Ref(EMA($close, 12) - EMA($close, 26), 1) <= 
             Ref(EMA(EMA($close, 12) - EMA($close, 26), 9), 1))
        )
        """,
        
        "强势突破": """
        # 综合突破条件
        (
            ($close > Ref(Max($high, 20), 1)) &      # 价格突破
            ($volume > 1.5 * Mean($volume, 20)) &    # 成交量放大
            (RSI($close, 14) < 70) &                 # RSI不超买
            ($close > Mean($close, 20))              # 均线支撑
        )
        """
    }
    
    for name, expr in expressions.items():
        print(f"\n🔍 {name}:")
        print("-" * 30)
        print(expr.strip())
    
    print("\n" + "=" * 60)
    print("💡 多时间周期分析示例")
    print("=" * 60)
    
    print("分析 BTCUSDT:")
    print("  综合得分: 4.25")
    print("    1d: 得分 4.2, RSI 45.2, 信号 3个")
    print("    4h: 得分 3.8, RSI 42.8, 信号 2个") 
    print("    1h: 得分 3.9, RSI 48.1, 信号 2个")
    print("  趋势一致: ✓, 信号一致: ✓, 风险等级: low")
    
    print("\n分析 ETHUSDT:")
    print("  综合得分: 3.65")
    print("    1d: 得分 3.8, RSI 42.1, 信号 3个")
    print("    4h: 得分 3.2, RSI 39.5, 信号 2个")
    print("    1h: 得分 4.0, RSI 44.2, 信号 3个")
    print("  趋势一致: ✓, 信号一致: ✗, 风险等级: medium")
    
    print("\n" + "=" * 60)
    print("💡 交易建议")
    print("=" * 60)
    
    print("🟢 高信心度标的 (建议重点关注):")
    print("  📌 BTCUSDT: 得分 4.2, 价格 45234.56, 主要信号: MACD金叉, 成交量放大")
    print("  📌 ETHUSDT: 得分 3.8, 价格 3156.78, 主要信号: RSI回升, 突破阻力")
    
    print("\n🟡 中等信心度标的 (可适量配置):")
    print("  📌 BNBUSDT: 得分 3.5, 价格 412.34, 主要信号: 锤子线, 布林带支撑")
    print("  📌 ADAUSDT: 得分 3.2, 价格 1.2345, 主要信号: RSI超卖回升, MACD金叉")
    
    print("\n⚠️ 风险提示:")
    print("  • 技术分析仅供参考，不构成投资建议")
    print("  • 数字货币市场波动较大，请控制仓位")
    print("  • 建议结合基本面分析和市场情绪")
    print("  • 设置止损位，严格执行风险管理")
    
    print("\n" + "=" * 60)
    print("🛠️ 系统特性")
    print("=" * 60)
    
    features = [
        "✅ K线形态识别: 锤子线、十字星、吞没形态等",
        "✅ 技术指标分析: RSI、MACD、布林带、均线等",
        "✅ 多时间周期确认: 日线、4小时、1小时协同分析",
        "✅ 成交量确认: 价量配合验证信号有效性",
        "✅ 综合评分系统: 多维度加权计算选币得分",
        "✅ 实时数据支持: 支持币安、OKX等主流交易所",
        "✅ 消息推送: 企业微信、钉钉等即时通知",
        "✅ 回测验证: 历史数据验证策略有效性"
    ]
    
    for feature in features:
        print(f"  {feature}")
    
    print("\n" + "=" * 60)
    print("📋 使用说明")
    print("=" * 60)
    
    print("1. 环境配置:")
    print("   pip install qlib pandas numpy requests")
    
    print("\n2. 基础使用:")
    print("   python main.py --mode selection --timeframe 1d")
    
    print("\n3. 多周期分析:")
    print("   python main.py --mode multi_tf --symbols BTCUSDT ETHUSDT")
    
    print("\n4. 策略回测:")
    print("   python main.py --mode backtest --strategy strong_breakout")
    
    print("\n5. 实时监控:")
    print("   # 配置定时任务，每小时运行一次选币")
    print("   # 结果通过企业微信推送到交易群")
    
    print("\n" + "=" * 60)
    print("✅ 演示完成")
    print("=" * 60)
    print("这是一个完整的基于Qlib框架的数字货币选币系统")
    print("结合了传统技术分析和现代量化方法")
    print("为数字货币投资提供科学的决策支持")

if __name__ == "__main__":
    main()
