
import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def is_converging_triangle(df):
    """
    判断是否形成收敛三角形
    :param df: 包含OHLC数据的DataFrame，时间跨度为6个月
    :return: 是否形成收敛三角形 (bool)
    """
    highs = df['high']
    lows = df['low']
    
    # 计算高点和低点的趋势
    high_trend = np.polyfit(range(len(highs)), highs, 1)[0]  # 高点趋势斜率
    low_trend = np.polyfit(range(len(lows)), lows, 1)[0]     # 低点趋势斜率
    
    # 收敛三角形条件：高点趋势下降，低点趋势上升
    return high_trend < 0 and low_trend > 0

def is_breakout(df, recent_period=20):
    """
    判断是否在最近周期内突破三角形上线
    :param df: 包含OHLC数据的DataFrame
    :param recent_period: 最近周期天数，默认20天（1个月）
    :return: 是否突破 (bool), 突破日期 (str), 是否涨停突破 (bool)
    """
    # 获取最近周期的数据
    recent_df = df.tail(recent_period)
    
    # 拟合高点趋势线作为三角形上线
    highs = recent_df['high']
    x = range(len(highs))
    trend_line = np.poly1d(np.polyfit(x, highs, 1))(x)
    
    # 判断是否突破
    for i in range(len(recent_df)):
        if recent_df.iloc[i]['close'] > trend_line[i]:
            # 判断是否涨停
            prev_close = recent_df.iloc[i - 1]['close'] if i > 0 else recent_df.iloc[0]['close']
            is_limit_up = (recent_df.iloc[i]['close'] / prev_close - 1) >= 0.099
            return True, recent_df.index[i].strftime('%Y-%m-%d'), is_limit_up
    
    return False, None, False

def triangle_breakout_strategy(data):
    """
    收敛三角形选股策略主函数
    :param data: 包含OHLC数据的DataFrame，时间跨度至少6个月
    :return: 符合条件的股票列表
    """
    results = []
    
    # 按股票分组处理
    grouped = data.groupby('stock_code')
    for stock_code, group in grouped:
        # 确保数据时间跨度足够
        if len(group) < 120:  # 至少6个月数据
            continue
        
        # 判断是否形成收敛三角形
        if not is_converging_triangle(group):
            continue
        
        # 判断是否突破三角形上线
        breakout, breakout_date, is_limit_up = is_breakout(group)
        if breakout:
            results.append({
                'stock_code': stock_code,
                'breakout_date': breakout_date,
                'is_limit_up': is_limit_up
            })
    
    return pd.DataFrame(results)

# 示例调用
if __name__ == "__main__":
    # 示例数据加载（需替换为实际数据源）
    # 数据格式：包含'stock_code', 'date', 'open', 'high', 'low', 'close'列
    data = pd.read_csv("stock_data.csv", parse_dates=['date'])
    data.set_index('date', inplace=True)
    
    # 执行选股策略
    result = triangle_breakout_strategy(data)
    print(result)