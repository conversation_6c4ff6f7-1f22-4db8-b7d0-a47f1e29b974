"""
基于真实数据的数字货币选币系统
使用CoinGecko API获取真实市场数据
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import requests
import time
from typing import Dict, List, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

from real_data_provider import RealDataProvider


class RealDataCryptoSelector:
    """基于真实数据的数字货币选币器"""
    
    def __init__(self):
        """初始化选币器"""
        self.data_provider = RealDataProvider()
        
        # 企业微信webhook
        self.wechat_webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=52d1b8a8-a88a-4d41-9664-f1268da20985"
        
        # 评分权重
        self.weights = {
            'price_momentum': 0.3,    # 价格动量
            'volume_activity': 0.2,   # 成交量活跃度
            'market_position': 0.2,   # 市场地位
            'volatility': 0.15,       # 波动率
            'trend_strength': 0.15    # 趋势强度
        }
        
        # 筛选条件
        self.filter_conditions = {
            'min_market_cap': 100000000,      # 最小市值1亿美元
            'min_volume': 10000000,           # 最小24h交易量1000万美元
            'max_volatility': 0.2,            # 最大日波动率20%
            'min_price': 0.0001               # 最小价格
        }
    
    def get_real_market_data(self) -> Dict:
        """获取真实市场数据"""
        print("📊 正在获取真实市场数据...")
        
        market_data = self.data_provider.get_market_data()
        
        if market_data:
            print(f"✅ 成功获取 {len(market_data)} 个交易对的市场数据")
            return market_data
        else:
            print("❌ 获取市场数据失败")
            return {}
    
    def analyze_market_data(self, market_data: Dict) -> Dict:
        """分析市场数据"""
        analysis_results = {}
        
        print("\n🔍 开始分析市场数据...")
        
        for symbol, data in market_data.items():
            try:
                # 基础数据提取
                current_price = data['current_price']
                market_cap = data['market_cap']
                volume_24h = data['total_volume']
                price_change_24h = data['price_change_24h']
                price_change_7d = data['price_change_7d']
                market_cap_rank = data['market_cap_rank']
                
                # 数据有效性检查
                if not all([current_price, market_cap, volume_24h]):
                    continue
                
                # 应用筛选条件
                if not self.apply_filters(data):
                    continue
                
                # 计算各项评分
                scores = self.calculate_scores(data)
                
                # 计算综合得分
                total_score = sum(scores[key] * self.weights[key] for key in self.weights.keys())
                
                # 风险评估
                risk_level = self.assess_risk_level(data, scores)
                
                # 生成交易信号
                signals = self.generate_trading_signals(data, scores)
                
                analysis_results[symbol] = {
                    'symbol': symbol,
                    'name': data['name'],
                    'current_price': current_price,
                    'market_cap': market_cap,
                    'market_cap_rank': market_cap_rank,
                    'volume_24h': volume_24h,
                    'price_change_24h': price_change_24h,
                    'price_change_7d': price_change_7d,
                    'scores': scores,
                    'total_score': total_score,
                    'risk_level': risk_level,
                    'signals': signals,
                    'last_updated': data['last_updated']
                }
                
            except Exception as e:
                print(f"分析 {symbol} 时出错: {e}")
                continue
        
        print(f"✅ 分析完成，共 {len(analysis_results)} 个标的通过筛选")
        return analysis_results
    
    def apply_filters(self, data: Dict) -> bool:
        """应用筛选条件"""
        try:
            # 市值筛选
            if data['market_cap'] < self.filter_conditions['min_market_cap']:
                return False
            
            # 交易量筛选
            if data['total_volume'] < self.filter_conditions['min_volume']:
                return False
            
            # 价格筛选
            if data['current_price'] < self.filter_conditions['min_price']:
                return False
            
            # 波动率筛选 (基于24h价格变化)
            if abs(data['price_change_24h']) > self.filter_conditions['max_volatility'] * 100:
                return False
            
            return True
            
        except:
            return False
    
    def calculate_scores(self, data: Dict) -> Dict:
        """计算各项评分"""
        scores = {}
        
        try:
            # 价格动量评分 (基于24h和7d价格变化)
            price_24h = data['price_change_24h']
            price_7d = data['price_change_7d']
            
            momentum_score = 0
            if price_24h > 5:
                momentum_score += 3
            elif price_24h > 2:
                momentum_score += 2
            elif price_24h > 0:
                momentum_score += 1
            
            if price_7d > 10:
                momentum_score += 2
            elif price_7d > 5:
                momentum_score += 1
            
            scores['price_momentum'] = min(momentum_score, 5)
            
            # 成交量活跃度评分
            volume = data['total_volume']
            if volume > 1000000000:  # >10亿
                scores['volume_activity'] = 5
            elif volume > 500000000:  # >5亿
                scores['volume_activity'] = 4
            elif volume > 100000000:  # >1亿
                scores['volume_activity'] = 3
            elif volume > 50000000:   # >5000万
                scores['volume_activity'] = 2
            else:
                scores['volume_activity'] = 1
            
            # 市场地位评分 (基于市值排名)
            rank = data['market_cap_rank']
            if rank <= 10:
                scores['market_position'] = 5
            elif rank <= 20:
                scores['market_position'] = 4
            elif rank <= 50:
                scores['market_position'] = 3
            elif rank <= 100:
                scores['market_position'] = 2
            else:
                scores['market_position'] = 1
            
            # 波动率评分 (适度波动为佳)
            volatility = abs(data['price_change_24h'])
            if 2 <= volatility <= 8:
                scores['volatility'] = 5
            elif 1 <= volatility <= 12:
                scores['volatility'] = 4
            elif volatility <= 15:
                scores['volatility'] = 3
            elif volatility <= 20:
                scores['volatility'] = 2
            else:
                scores['volatility'] = 1
            
            # 趋势强度评分
            if price_24h > 0 and price_7d > 0:
                scores['trend_strength'] = 5
            elif price_24h > 0 or price_7d > 0:
                scores['trend_strength'] = 3
            elif price_24h < -5 and price_7d < -10:
                scores['trend_strength'] = 1  # 超跌可能反弹
            else:
                scores['trend_strength'] = 2
            
        except Exception as e:
            print(f"计算评分时出错: {e}")
            # 默认评分
            scores = {key: 2.5 for key in self.weights.keys()}
        
        return scores
    
    def assess_risk_level(self, data: Dict, scores: Dict) -> str:
        """评估风险等级"""
        try:
            risk_factors = 0
            
            # 市值风险
            if data['market_cap'] < 1000000000:  # <10亿
                risk_factors += 1
            
            # 波动率风险
            if abs(data['price_change_24h']) > 15:
                risk_factors += 1
            
            # 交易量风险
            if data['total_volume'] < 50000000:  # <5000万
                risk_factors += 1
            
            # 市场排名风险
            if data['market_cap_rank'] > 100:
                risk_factors += 1
            
            # 价格趋势风险
            if data['price_change_7d'] < -20:
                risk_factors += 1
            
            if risk_factors <= 1:
                return 'low'
            elif risk_factors <= 3:
                return 'medium'
            else:
                return 'high'
                
        except:
            return 'medium'
    
    def generate_trading_signals(self, data: Dict, scores: Dict) -> List[str]:
        """生成交易信号"""
        signals = []
        
        try:
            price_24h = data['price_change_24h']
            price_7d = data['price_change_7d']
            volume = data['total_volume']
            rank = data['market_cap_rank']
            
            # 动量信号
            if price_24h > 5 and price_7d > 10:
                signals.append("强势上涨动量")
            elif price_24h > 2:
                signals.append("短期上涨趋势")
            
            # 超跌反弹信号
            if price_24h < -10 and price_7d < -20:
                signals.append("超跌反弹机会")
            
            # 成交量信号
            if volume > 500000000:
                signals.append("高成交量活跃")
            
            # 市场地位信号
            if rank <= 20:
                signals.append("主流币种稳定")
            
            # 波动率信号
            volatility = abs(price_24h)
            if 3 <= volatility <= 8:
                signals.append("适度波动健康")
            
            # 综合信号
            total_score = sum(scores[key] * self.weights[key] for key in self.weights.keys())
            if total_score >= 4:
                signals.append("综合评分优秀")
            elif total_score >= 3:
                signals.append("综合评分良好")
            
            if not signals:
                signals.append("技术面中性")
                
        except:
            signals = ["数据分析中"]
        
        return signals[:4]  # 最多返回4个信号
    
    def send_wechat_notification(self, results: Dict):
        """发送企业微信通知"""
        try:
            if not results:
                return
            
            message = self.build_notification_message(results)
            
            data = {
                "msgtype": "text",
                "text": {
                    "content": message
                }
            }
            
            response = requests.post(
                self.wechat_webhook,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    print("✅ 企业微信通知发送成功")
                else:
                    print(f"❌ 企业微信通知发送失败: {result}")
            else:
                print(f"❌ 企业微信通知请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 发送企业微信通知失败: {e}")
    
    def build_notification_message(self, results: Dict) -> str:
        """构建通知消息"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 按总分排序
            sorted_results = sorted(
                results.items(),
                key=lambda x: x[1]['total_score'],
                reverse=True
            )
            
            # 筛选高分标的
            high_score_results = [(k, v) for k, v in sorted_results if v['total_score'] >= 3.0]
            
            message = f"🚀 真实数据数字货币选币提醒\n"
            message += f"⏰ 时间: {timestamp}\n"
            message += f"📊 数据源: CoinGecko实时数据\n"
            message += f"🎯 发现 {len(high_score_results)} 个优质标的\n"
            message += f"📈 总分析标的: {len(results)} 个\n\n"
            
            # 显示前5个结果
            for i, (symbol, data) in enumerate(high_score_results[:5], 1):
                risk_emoji = {'low': '🟢', 'medium': '🟡', 'high': '🔴'}
                risk_icon = risk_emoji.get(data['risk_level'], '🟡')
                
                message += f"{i}. {symbol} {risk_icon}\n"
                message += f"   💯 综合得分: {data['total_score']:.2f}\n"
                message += f"   🏷️ 名称: {data['name']}\n"
                message += f"   💰 当前价格: ${data['current_price']:,.2f}\n"
                message += f"   📊 市值排名: #{data['market_cap_rank']}\n"
                message += f"   📈 24h涨跌: {data['price_change_24h']:+.2f}%\n"
                message += f"   📅 7d涨跌: {data['price_change_7d']:+.2f}%\n"
                message += f"   💹 24h成交量: ${data['volume_24h']/1000000:.0f}M\n"
                message += f"   🔔 主要信号: {', '.join(data['signals'][:2])}\n\n"
            
            # 风险分布统计
            risk_stats = {'low': 0, 'medium': 0, 'high': 0}
            for _, data in high_score_results:
                risk_stats[data['risk_level']] += 1
            
            message += f"📋 风险分布:\n"
            message += f"🟢 低风险: {risk_stats['low']} 个\n"
            message += f"🟡 中风险: {risk_stats['medium']} 个\n"
            message += f"🔴 高风险: {risk_stats['high']} 个\n\n"
            
            message += f"💡 投资建议:\n"
            message += f"🟢 低风险: 建议重点关注\n"
            message += f"🟡 中风险: 可适量配置\n"
            message += f"🔴 高风险: 谨慎观察\n\n"
            message += f"📊 数据来源: CoinGecko API\n"
            message += f"⚠️ 风险提示: 基于真实市场数据分析，仅供参考"
            
            return message
            
        except Exception as e:
            print(f"构建通知消息失败: {e}")
            return f"真实数据数字货币选币系统运行完成\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    
    def run_real_data_analysis(self):
        """运行基于真实数据的分析"""
        print("🚀 基于真实数据的数字货币选币系统")
        print("=" * 60)
        print("📊 数据源: CoinGecko API (真实市场数据)")
        print("🔍 分析维度: 价格动量、成交量、市场地位、波动率、趋势强度")
        print("=" * 60)
        
        # 获取真实市场数据
        market_data = self.get_real_market_data()
        
        if not market_data:
            print("❌ 无法获取市场数据，请检查网络连接")
            return None
        
        # 分析市场数据
        analysis_results = self.analyze_market_data(market_data)
        
        if not analysis_results:
            print("❌ 没有标的通过筛选条件")
            return None
        
        # 显示分析结果
        self.display_analysis_results(analysis_results)
        
        # 发送企业微信通知
        print("\n📱 发送企业微信通知...")
        self.send_wechat_notification(analysis_results)
        
        return analysis_results
    
    def display_analysis_results(self, results: Dict):
        """显示分析结果"""
        print(f"\n📊 真实数据分析结果")
        print("=" * 100)
        
        # 按总分排序
        sorted_results = sorted(
            results.items(),
            key=lambda x: x[1]['total_score'],
            reverse=True
        )
        
        print(f"{'排名':<4} {'代码':<12} {'名称':<15} {'价格':<12} {'24h%':<8} {'7d%':<8} {'排名':<6} {'得分':<6} {'风险':<6}")
        print("-" * 100)
        
        for i, (symbol, data) in enumerate(sorted_results, 1):
            price_str = f"${data['current_price']:,.2f}"
            change_24h = f"{data['price_change_24h']:+.1f}%"
            change_7d = f"{data['price_change_7d']:+.1f}%"
            
            print(f"{i:<4} {symbol:<12} {data['name'][:14]:<15} {price_str:<12} "
                  f"{change_24h:<8} {change_7d:<8} #{data['market_cap_rank']:<5} "
                  f"{data['total_score']:<6.2f} {data['risk_level']:<6}")


def main():
    """主函数"""
    selector = RealDataCryptoSelector()
    
    # 运行真实数据分析
    results = selector.run_real_data_analysis()
    
    if results:
        print(f"\n" + "=" * 60)
        print("✅ 真实数据分析完成")
        print("=" * 60)
        print("🌟 系统特色:")
        print("• 基于CoinGecko真实市场数据")
        print("• 多维度综合评分系统")
        print("• 智能风险等级评估")
        print("• 实时交易信号生成")
        print("• 企业微信自动推送")
        print("\n📊 数据可靠性: 100%真实市场数据")


if __name__ == "__main__":
    main()
