@echo off
chcp 65001 >nul
title 快速测试 - 双长上影线识别
echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █                    🧪 快速功能测试                          █
echo █                  双长上影线形态识别                         █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.

cd /d "%~dp0"

echo 📍 当前目录: %CD%
echo 🐍 Python环境: D:\envs\tqsdk\python.exe
echo.

echo 🔧 检查环境...
if not exist "D:\envs\tqsdk\python.exe" (
    echo ❌ Python环境不存在
    pause
    exit /b 1
)

if not exist "advanced_crypto_scraper tuxing.py" (
    echo ❌ 主脚本文件不存在
    pause
    exit /b 1
)

if not exist "快速测试.py" (
    echo ❌ 测试脚本不存在
    pause
    exit /b 1
)

echo ✅ 环境检查通过
echo.

echo 🧪 开始快速测试...
echo ================================================================
echo.

D:\envs\tqsdk\python.exe "快速测试.py"

echo.
echo ================================================================
echo 📝 测试完成
echo.
pause
