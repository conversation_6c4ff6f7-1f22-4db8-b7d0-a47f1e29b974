{"ssr_servers": [{"name": "主服务器", "host": "77.gdpp.com", "port": 11807, "password": "q12345678q", "method": "aes-256-ctr", "protocol": "auth_aes128_sha1", "obfs": "tls1.2_ticket_auth", "local_port": 1080, "timeout": 300, "priority": 1, "enabled": true}, {"name": "备用服务器", "host": "*************", "port": 11807, "password": "q12345678q", "method": "aes-256-ctr", "protocol": "auth_aes128_sha1", "obfs": "tls1.2_ticket_auth", "local_port": 1081, "timeout": 300, "priority": 2, "enabled": true}], "proxy_settings": {"enable_proxy": true, "proxy_type": "socks5", "proxy_host": "127.0.0.1", "proxy_port": 1080, "connection_timeout": 30, "read_timeout": 60, "max_retries": 3, "retry_delay": 2, "test_urls": ["https://api.coingecko.com/api/v3/ping", "https://pro-api.coinmarketcap.com/v1/cryptocurrency/listings/latest?limit=1", "https://httpbin.org/ip"]}, "fallback_settings": {"enable_fallback": true, "fallback_to_direct": false, "auto_switch_servers": true, "health_check_interval": 300, "failure_threshold": 3}, "wechat_webhooks": {"enabled": true, "robots": [{"name": "户部尚赢量化平台", "webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=69db19ba-d1af-422a-b0cf-19f21cd5b5fc", "enabled": true, "description": "户部尚赢量化平台企业微信机器人"}, {"name": "BTC机器人", "webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=52d1b8a8-a88a-4d41-9664-f1268da20985", "enabled": true, "description": "企业微信BTC机器人"}], "send_settings": {"timeout": 10, "retry_count": 3, "retry_delay": 2, "concurrent_send": true}}}