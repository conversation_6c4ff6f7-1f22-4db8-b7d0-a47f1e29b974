"""
基于Qlib的数字货币选币策略实现
支持K线形态、技术指标、多时间周期确认
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
import qlib
from qlib.data import D
from qlib.utils import get_module_logger
from qlib.strategy.base import BaseStrategy
from qlib.backtest.decision import BaseTradeDecision, TradeRange, TradeRangeByTime
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

from .crypto_alpha_expressions import CRYPTO_ALPHA_EXPRESSIONS, MULTI_TIMEFRAME_EXPRESSIONS, HELPER_EXPRESSIONS
from .crypto_data_adapter import CryptoDataAdapter


class CryptoStockPicker:
    """数字货币选币器"""
    
    def __init__(self, config: Dict):
        """
        初始化选币器

        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = get_module_logger(self.__class__.__name__)

        # 选币参数
        self.min_score = config.get('min_score', 3)
        self.max_selections = config.get('max_selections', 10)
        self.timeframes = config.get('timeframes', ['1d', '4h', '1h'])

        # 技术指标权重
        self.weights = config.get('weights', {
            'pattern': 0.3,
            'indicator': 0.4,
            'trend': 0.2,
            'volume': 0.1
        })

        # 初始化数据适配器
        data_config = config.get('data_config', {
            'data_source': 'binance',
            'cache_timeout': 300
        })
        self.data_adapter = CryptoDataAdapter(data_config)
        
    def calculate_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算技术指标
        
        Args:
            data: OHLCV数据
            
        Returns:
            包含技术指标的DataFrame
        """
        try:
            df = data.copy()
            
            # 基础技术指标
            df['rsi'] = self._calculate_rsi(df['close'], 14)
            df['macd'], df['macd_signal'], df['macd_hist'] = self._calculate_macd(df['close'])
            df['bb_upper'], df['bb_middle'], df['bb_lower'] = self._calculate_bollinger_bands(df['close'])
            df['sma_5'] = df['close'].rolling(5).mean()
            df['sma_10'] = df['close'].rolling(10).mean()
            df['sma_20'] = df['close'].rolling(20).mean()
            df['sma_50'] = df['close'].rolling(50).mean()
            df['ema_12'] = df['close'].ewm(span=12).mean()
            df['ema_26'] = df['close'].ewm(span=26).mean()
            df['volume_sma'] = df['volume'].rolling(20).mean()
            df['atr'] = self._calculate_atr(df)
            
            return df
            
        except Exception as e:
            self.logger.error(f"计算技术指标失败: {e}")
            return data
    
    def evaluate_pattern_signals(self, data: pd.DataFrame) -> pd.Series:
        """
        评估K线形态信号
        
        Args:
            data: 包含技术指标的数据
            
        Returns:
            形态信号得分
        """
        try:
            df = data.copy()
            pattern_score = pd.Series(0.0, index=df.index)
            
            # 锤子线形态
            hammer = self._detect_hammer_pattern(df)
            pattern_score += hammer * 2
            
            # 十字星形态
            doji = self._detect_doji_pattern(df)
            pattern_score += doji * 1
            
            # 看涨吞没形态
            engulfing = self._detect_bullish_engulfing(df)
            pattern_score += engulfing * 3
            
            # 突破形态
            breakout = self._detect_breakout_pattern(df)
            pattern_score += breakout * 2
            
            # 支撑反弹形态
            support_bounce = self._detect_support_bounce(df)
            pattern_score += support_bounce * 2
            
            return pattern_score
            
        except Exception as e:
            self.logger.error(f"评估形态信号失败: {e}")
            return pd.Series(0.0, index=data.index)
    
    def evaluate_indicator_signals(self, data: pd.DataFrame) -> pd.Series:
        """
        评估技术指标信号
        
        Args:
            data: 包含技术指标的数据
            
        Returns:
            指标信号得分
        """
        try:
            df = data.copy()
            indicator_score = pd.Series(0.0, index=df.index)
            
            # RSI超卖后回升
            rsi_signal = self._evaluate_rsi_signal(df)
            indicator_score += rsi_signal * 2
            
            # MACD金叉
            macd_signal = self._evaluate_macd_signal(df)
            indicator_score += macd_signal * 2
            
            # 布林带支撑反弹
            bb_signal = self._evaluate_bollinger_signal(df)
            indicator_score += bb_signal * 1.5
            
            # 均线支撑
            ma_signal = self._evaluate_ma_signal(df)
            indicator_score += ma_signal * 1
            
            return indicator_score
            
        except Exception as e:
            self.logger.error(f"评估指标信号失败: {e}")
            return pd.Series(0.0, index=data.index)
    
    def evaluate_trend_signals(self, data: pd.DataFrame) -> pd.Series:
        """
        评估趋势信号
        
        Args:
            data: 包含技术指标的数据
            
        Returns:
            趋势信号得分
        """
        try:
            df = data.copy()
            trend_score = pd.Series(0.0, index=df.index)
            
            # 均线多头排列
            bullish_alignment = (
                (df['sma_5'] > df['sma_10']) &
                (df['sma_10'] > df['sma_20']) &
                (df['sma_20'] > df['sma_50'])
            )
            trend_score += bullish_alignment * 2
            
            # 趋势反转信号
            trend_reversal = self._detect_trend_reversal(df)
            trend_score += trend_reversal * 3
            
            # 价格相对位置
            price_position = (df['close'] - df['sma_20']) / df['sma_20']
            trend_score += np.where(
                (price_position > -0.05) & (price_position < 0.1), 1, 0
            )
            
            return trend_score
            
        except Exception as e:
            self.logger.error(f"评估趋势信号失败: {e}")
            return pd.Series(0.0, index=data.index)
    
    def evaluate_volume_signals(self, data: pd.DataFrame) -> pd.Series:
        """
        评估成交量信号
        
        Args:
            data: 包含技术指标的数据
            
        Returns:
            成交量信号得分
        """
        try:
            df = data.copy()
            volume_score = pd.Series(0.0, index=df.index)
            
            # 成交量放大
            volume_breakout = df['volume'] > (df['volume_sma'] * 1.5)
            volume_score += volume_breakout * 2
            
            # 价量配合
            price_up = df['close'] > df['open']
            volume_confirm = volume_breakout & price_up
            volume_score += volume_confirm * 1
            
            return volume_score
            
        except Exception as e:
            self.logger.error(f"评估成交量信号失败: {e}")
            return pd.Series(0.0, index=data.index)
    
    def select_stocks(self, symbols: List[str], timeframe: str = '1d', 
                     start_time: Optional[datetime] = None,
                     end_time: Optional[datetime] = None) -> List[Dict]:
        """
        选股主函数
        
        Args:
            symbols: 股票代码列表
            timeframe: 时间周期
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            选股结果列表
        """
        try:
            if end_time is None:
                end_time = datetime.now()
            if start_time is None:
                start_time = end_time - timedelta(days=100)
            
            results = []
            
            for symbol in symbols:
                try:
                    # 获取数据 (这里需要根据实际数据源调整)
                    data = self._get_symbol_data(symbol, timeframe, start_time, end_time)
                    
                    if data is None or len(data) < 50:
                        continue
                    
                    # 计算技术指标
                    data_with_indicators = self.calculate_technical_indicators(data)
                    
                    # 评估各类信号
                    pattern_signals = self.evaluate_pattern_signals(data_with_indicators)
                    indicator_signals = self.evaluate_indicator_signals(data_with_indicators)
                    trend_signals = self.evaluate_trend_signals(data_with_indicators)
                    volume_signals = self.evaluate_volume_signals(data_with_indicators)
                    
                    # 计算综合得分
                    total_score = (
                        pattern_signals * self.weights['pattern'] +
                        indicator_signals * self.weights['indicator'] +
                        trend_signals * self.weights['trend'] +
                        volume_signals * self.weights['volume']
                    )
                    
                    # 获取最新得分
                    latest_score = total_score.iloc[-1] if len(total_score) > 0 else 0
                    
                    if latest_score >= self.min_score:
                        # 获取详细信息
                        latest_data = data_with_indicators.iloc[-1]
                        
                        result = {
                            'symbol': symbol,
                            'score': float(latest_score),
                            'pattern_score': float(pattern_signals.iloc[-1]),
                            'indicator_score': float(indicator_signals.iloc[-1]),
                            'trend_score': float(trend_signals.iloc[-1]),
                            'volume_score': float(volume_signals.iloc[-1]),
                            'current_price': float(latest_data['close']),
                            'rsi': float(latest_data['rsi']),
                            'macd': float(latest_data['macd']),
                            'volume_ratio': float(latest_data['volume'] / latest_data['volume_sma']),
                            'timestamp': latest_data.name,
                            'signals': self._get_signal_details(data_with_indicators.iloc[-5:])
                        }
                        
                        results.append(result)
                        
                except Exception as e:
                    self.logger.warning(f"处理 {symbol} 时出错: {e}")
                    continue
            
            # 按得分排序并限制数量
            results.sort(key=lambda x: x['score'], reverse=True)
            return results[:self.max_selections]
            
        except Exception as e:
            self.logger.error(f"选股过程出错: {e}")
            return []
    
    def multi_timeframe_analysis(self, symbol: str, 
                                timeframes: List[str] = None) -> Dict:
        """
        多时间周期分析
        
        Args:
            symbol: 股票代码
            timeframes: 时间周期列表
            
        Returns:
            多周期分析结果
        """
        try:
            if timeframes is None:
                timeframes = self.timeframes
            
            results = {}
            
            for tf in timeframes:
                # 获取该时间周期的选股结果
                tf_results = self.select_stocks([symbol], tf)
                
                if tf_results:
                    results[tf] = tf_results[0]
                else:
                    results[tf] = {'score': 0, 'signals': []}
            
            # 计算多周期综合得分
            total_score = 0
            weight_sum = 0
            
            # 不同时间周期的权重
            tf_weights = {'1d': 0.4, '4h': 0.25, '1h': 0.15, '30m': 0.12, '15m': 0.08}
            
            for tf, result in results.items():
                weight = tf_weights.get(tf, 0.2)
                total_score += result['score'] * weight
                weight_sum += weight
            
            if weight_sum > 0:
                results['multi_tf_score'] = total_score / weight_sum
            else:
                results['multi_tf_score'] = 0
            
            # 多周期确认逻辑
            results['confirmation'] = self._check_multi_timeframe_confirmation(results)
            
            return results
            
        except Exception as e:
            self.logger.error(f"多时间周期分析失败: {e}")
            return {}
    
    # ==================== 辅助方法 ====================
    
    def _get_symbol_data(self, symbol: str, timeframe: str,
                        start_time: datetime, end_time: datetime) -> Optional[pd.DataFrame]:
        """获取股票数据"""
        try:
            return self.data_adapter.get_kline_data(symbol, timeframe, start_time, end_time)
        except Exception as e:
            self.logger.error(f"获取 {symbol} 数据失败: {e}")
            return None
    
    def _calculate_rsi(self, close: pd.Series, window: int = 14) -> pd.Series:
        """计算RSI"""
        delta = close.diff()
        gain = delta.where(delta > 0, 0).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_macd(self, close: pd.Series) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """计算MACD"""
        ema12 = close.ewm(span=12).mean()
        ema26 = close.ewm(span=26).mean()
        macd_line = ema12 - ema26
        signal_line = macd_line.ewm(span=9).mean()
        histogram = macd_line - signal_line
        return macd_line, signal_line, histogram
    
    def _calculate_bollinger_bands(self, close: pd.Series, window: int = 20) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """计算布林带"""
        sma = close.rolling(window=window).mean()
        std = close.rolling(window=window).std()
        upper = sma + (std * 2)
        lower = sma - (std * 2)
        return upper, sma, lower
    
    def _calculate_atr(self, data: pd.DataFrame, window: int = 14) -> pd.Series:
        """计算ATR"""
        high_low = data['high'] - data['low']
        high_close = np.abs(data['high'] - data['close'].shift())
        low_close = np.abs(data['low'] - data['close'].shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        return true_range.rolling(window).mean()

    # ==================== K线形态检测方法 ====================

    def _detect_hammer_pattern(self, data: pd.DataFrame) -> pd.Series:
        """检测锤子线形态"""
        df = data.copy()

        # 计算实体、上影线、下影线
        body = np.abs(df['close'] - df['open'])
        upper_shadow = df['high'] - np.maximum(df['close'], df['open'])
        lower_shadow = np.minimum(df['close'], df['open']) - df['low']

        # 锤子线条件
        hammer = (
            (lower_shadow >= 2 * body) &  # 下影线 >= 实体2倍
            (upper_shadow <= 0.1 * body) &  # 上影线很短
            (body >= 0.001 * df['close']) &  # 实体不能太小
            (df['sma_5'] < df['sma_5'].shift(1))  # 前期下跌趋势
        )

        return hammer.astype(int)

    def _detect_doji_pattern(self, data: pd.DataFrame) -> pd.Series:
        """检测十字星形态"""
        df = data.copy()

        body = np.abs(df['close'] - df['open'])
        total_range = df['high'] - df['low']

        # 十字星条件
        doji = (
            (body <= 0.001 * df['close']) &  # 实体很小
            (total_range >= 0.01 * df['close']) &  # 有一定波动
            (np.abs((df['high'] - np.maximum(df['close'], df['open'])) -
                   (np.minimum(df['close'], df['open']) - df['low'])) <= 0.005 * df['close'])  # 上下影线均衡
        )

        return doji.astype(int)

    def _detect_bullish_engulfing(self, data: pd.DataFrame) -> pd.Series:
        """检测看涨吞没形态"""
        df = data.copy()

        # 昨日阴线，今日阳线
        prev_bearish = df['close'].shift(1) < df['open'].shift(1)
        curr_bullish = df['close'] > df['open']

        # 吞没条件
        engulfing = (
            prev_bearish &
            curr_bullish &
            (df['open'] < df['close'].shift(1)) &  # 今日开盘低于昨日收盘
            (df['close'] > df['open'].shift(1)) &  # 今日收盘高于昨日开盘
            ((df['close'] - df['open']) > (df['open'].shift(1) - df['close'].shift(1)))  # 今日实体大于昨日
        )

        return engulfing.astype(int)

    def _detect_breakout_pattern(self, data: pd.DataFrame) -> pd.Series:
        """检测突破形态"""
        df = data.copy()

        # 20日高点突破
        high_20 = df['high'].rolling(20).max().shift(1)

        breakout = (
            (df['close'] > high_20) &  # 突破20日高点
            (df['volume'] > df['volume_sma'] * 1.5) &  # 成交量放大
            ((df['close'] / high_20 - 1) > 0.01)  # 突破幅度 > 1%
        )

        return breakout.astype(int)

    def _detect_support_bounce(self, data: pd.DataFrame) -> pd.Series:
        """检测支撑反弹形态"""
        df = data.copy()

        # 20日低点
        low_20 = df['low'].rolling(20).min()

        support_bounce = (
            (df['low'].shift(1) <= low_20.shift(2)) &  # 前期触及支撑
            (df['close'] > low_20.shift(2)) &  # 今日回到支撑上方
            (df['close'] > df['open']) &  # 今日阳线
            (df['volume'] > df['volume_sma'] * 1.2)  # 成交量放大
        )

        return support_bounce.astype(int)

    # ==================== 技术指标信号评估方法 ====================

    def _evaluate_rsi_signal(self, data: pd.DataFrame) -> pd.Series:
        """评估RSI信号"""
        df = data.copy()

        rsi_signal = (
            (df['rsi'].shift(1) < 30) &  # 前期超卖
            (df['rsi'] > 35) &  # 当前回升
            (df['rsi'] > df['rsi'].shift(1))  # RSI上升
        )

        return rsi_signal.astype(int)

    def _evaluate_macd_signal(self, data: pd.DataFrame) -> pd.Series:
        """评估MACD信号"""
        df = data.copy()

        macd_signal = (
            (df['macd'] > df['macd_signal']) &  # MACD线上穿信号线
            (df['macd'].shift(1) <= df['macd_signal'].shift(1)) &  # 前一日在下方
            (df['macd_hist'] > 0)  # 柱状图转正
        )

        return macd_signal.astype(int)

    def _evaluate_bollinger_signal(self, data: pd.DataFrame) -> pd.Series:
        """评估布林带信号"""
        df = data.copy()

        bb_signal = (
            (df['low'].shift(1) <= df['bb_lower'].shift(1)) &  # 前期触及下轨
            (df['close'] > df['bb_lower']) &  # 今日回到下轨上方
            (df['close'] > df['open']) &  # 今日阳线
            ((df['bb_upper'] / df['bb_lower']) > 1.04)  # 布林带宽度足够
        )

        return bb_signal.astype(int)

    def _evaluate_ma_signal(self, data: pd.DataFrame) -> pd.Series:
        """评估均线信号"""
        df = data.copy()

        ma_signal = (
            (df['close'] > df['sma_20']) &  # 价格在20日均线上方
            (df['sma_20'] > df['sma_20'].shift(1))  # 20日均线上升
        )

        return ma_signal.astype(int)

    def _detect_trend_reversal(self, data: pd.DataFrame) -> pd.Series:
        """检测趋势反转"""
        df = data.copy()

        trend_reversal = (
            (df['sma_20'].shift(5) > df['sma_20']) &  # 前期下跌趋势
            (df['sma_5'] > df['sma_20']) &  # 当前短期均线上穿长期均线
            (df['rsi'] > 50) &  # RSI回到中性区域上方
            (df['volume'] > df['volume_sma'])  # 成交量配合
        )

        return trend_reversal.astype(int)

    def _get_signal_details(self, recent_data: pd.DataFrame) -> List[str]:
        """获取信号详情"""
        signals = []

        if len(recent_data) == 0:
            return signals

        latest = recent_data.iloc[-1]

        # 检查各种信号
        if latest['rsi'] < 30:
            signals.append("RSI超卖")
        elif latest['rsi'] > 70:
            signals.append("RSI超买")

        if latest['macd'] > latest['macd_signal']:
            signals.append("MACD金叉")

        if latest['close'] > latest['bb_upper']:
            signals.append("突破布林带上轨")
        elif latest['close'] < latest['bb_lower']:
            signals.append("跌破布林带下轨")

        if latest['volume'] > latest['volume_sma'] * 1.5:
            signals.append("成交量放大")

        if (latest['sma_5'] > latest['sma_10'] > latest['sma_20']):
            signals.append("均线多头排列")

        return signals

    def _check_multi_timeframe_confirmation(self, results: Dict) -> Dict:
        """检查多时间周期确认"""
        confirmation = {
            'trend_alignment': False,
            'signal_consistency': False,
            'risk_level': 'medium'
        }

        # 检查趋势一致性
        daily_score = results.get('1d', {}).get('score', 0)
        h4_score = results.get('4h', {}).get('score', 0)
        h1_score = results.get('1h', {}).get('score', 0)

        if daily_score > 3 and h4_score > 2:
            confirmation['trend_alignment'] = True

        if daily_score > 2 and h4_score > 2 and h1_score > 1:
            confirmation['signal_consistency'] = True

        # 风险评估
        if confirmation['trend_alignment'] and confirmation['signal_consistency']:
            confirmation['risk_level'] = 'low'
        elif confirmation['trend_alignment'] or confirmation['signal_consistency']:
            confirmation['risk_level'] = 'medium'
        else:
            confirmation['risk_level'] = 'high'

        return confirmation
