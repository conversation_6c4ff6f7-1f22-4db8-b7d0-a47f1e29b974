@echo off
chcp 65001 >nul
title 形态分析 - 双长上影线识别
echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █                    🎯 形态分析功能                          █
echo █                  双长上影线形态识别                         █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.

cd /d "%~dp0"

echo 📍 当前目录: %CD%
echo 🐍 Python环境: D:\envs\tqsdk\python.exe
echo.

echo 🔧 检查环境...
if not exist "D:\envs\tqsdk\python.exe" (
    echo ❌ Python环境不存在: D:\envs\tqsdk\python.exe
    echo 💡 请检查Python虚拟环境路径
    pause
    exit /b 1
)

if not exist "advanced_crypto_scraper tuxing.py" (
    echo ❌ 主脚本文件不存在: advanced_crypto_scraper tuxing.py
    pause
    exit /b 1
)

if not exist "直接启动形态分析.py" (
    echo ❌ 启动脚本不存在: 直接启动形态分析.py
    pause
    exit /b 1
)

echo ✅ 环境检查通过
echo.

echo 💡 功能说明:
echo ┌─────────────────────────────────────────────────────────────┐
echo │ 🎯 双长上影线形态识别                                      │
echo │                                                             │
echo │ 📊 识别条件:                                               │
echo │   • 分析最近2根K线数据                                     │
echo │   • 上影线长度 ≥ 整根K线长度的1/3                          │
echo │   • 实体部分长度 ≤ 整根K线长度的2/3                        │
echo │   • 第二根K线最高价 < 第一根K线最高价                      │
echo │                                                             │
echo │ 📈 技术含义:                                               │
echo │   • 上涨乏力，多头力量衰竭                                 │
echo │   • 可能出现价格回调                                       │
echo │   • 重要的反转信号                                         │
echo │                                                             │
echo │ 🎨 图表验证:                                               │
echo │   • 分析完成后可选择生成K线图表                            │
echo │   • 直观验证形态识别准确性                                 │
echo │   • 图表保存在 pattern_charts 目录                        │
echo └─────────────────────────────────────────────────────────────┘
echo.

echo 🚀 启动形态分析...
echo ================================================================
echo.

D:\envs\tqsdk\python.exe "直接启动形态分析.py"

echo.
echo ================================================================
echo 📝 任务完成
echo.
pause
