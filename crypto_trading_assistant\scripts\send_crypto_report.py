import json
import requests

# 读取crypto_reports文件
with open('scripts/crypto_reports/crypto_analysis_20250418_083417.json', 'r', encoding='utf-8') as file:
    crypto_data = json.load(file)

# 从config.json中获取server酱API的密钥
with open('scripts/config.json', 'r', encoding='utf-8') as config_file:
    config = json.load(config_file)
    send_keys = config['wechat']['send_keys']

# 将数据转换为字符串
crypto_data_str = json.dumps(crypto_data, ensure_ascii=False, indent=4)

# 使用server酱API将内容推送到微信
for send_key in send_keys:
    url = f"https://sctapi.ftqq.com/{send_key}.send"
    data = {
        "title": "加密货币分析报告",
        "desp": crypto_data_str
    }
    response = requests.post(url, data=data)
    if response.status_code == 200:
        print(f"成功推送至微信，密钥: {send_key}")
    else:
        print(f"推送失败，状态码: {response.status_code}, 密钥: {send_key}")