#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自动启动脚本 - 避免交互式输入
"""

import sys
import os
from datetime import datetime

def log(message):
    """日志函数"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def main():
    """主函数"""
    log("🚀 自动启动加密货币数据抓取器...")
    
    try:
        # 导入主类
        from advanced_crypto_scraper import AdvancedCryptoScraper
        
        # 创建实例（不使用代理，避免阻塞）
        log("📋 正在初始化系统（直连模式）...")
        scraper = AdvancedCryptoScraper(use_proxy=False)
        
        log("✅ 系统初始化完成")
        log("🎯 主数据源: Gate.io")
        log("🔄 备用数据源: CoinGecko, 火币, Binance, CoinMarketCap")
        log("✅ 微信推送功能已启用")
        
        # 显示主要功能
        log("\n📊 主要功能:")
        log("1. 数据获取策略: Gate.io主数据源优先")
        log("2. 真实交易数据: 5个交易所数据源")
        log("3. 微信推送通知: 2个企业微信机器人")
        log("4. 技术分析功能: 15种K线形态 + 10+技术指标")
        log("5. 自动化调度: 支持单次、循环、定时运行")
        
        # 测试数据获取
        log("\n🔧 测试数据获取功能...")
        try:
            # 获取少量数据进行测试
            test_data = scraper.get_all_cryptocurrencies_enhanced(max_coins=10)
            if test_data:
                log(f"✅ 数据获取测试成功: 获取 {len(test_data)} 个币种")
                log("📈 前5个币种:")
                for i, crypto in enumerate(test_data[:5], 1):
                    symbol = crypto.get('symbol', 'N/A')
                    price = crypto.get('current_price', 0)
                    source = crypto.get('data_source', 'N/A')
                    log(f"   {i}. {symbol}: ${price:.6f} [{source}]")
            else:
                log("⚠️ 数据获取测试失败，可能需要启用代理")
        except Exception as e:
            log(f"⚠️ 数据获取测试异常: {e}")
        
        # 显示使用说明
        log("\n" + "="*60)
        log("🎉 系统启动成功！")
        log("💡 使用说明:")
        log("   - 如需访问国外数据源，请启用SSR代理")
        log("   - 当前使用直连模式，可访问火币等国内数据源")
        log("   - 主数据源Gate.io需要代理访问")
        log("   - 微信推送功能已就绪")
        log("="*60)
        
        # 询问是否进入主菜单
        try:
            choice = input("\n是否进入主菜单？(y/n, 默认n): ").strip().lower()
            if choice == 'y':
                log("🚀 进入主菜单...")
                scraper.run_main_menu()
            else:
                log("👋 程序结束")
        except (EOFError, KeyboardInterrupt):
            log("👋 程序结束")
        
    except Exception as e:
        log(f"❌ 启动失败: {e}")
        import traceback
        log(f"错误详情: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
