"""
真实数据与模拟数据对比分析
验证系统在真实数据下的表现
"""

import pandas as pd
import numpy as np
from datetime import datetime
import requests
from typing import Dict, List
import warnings
warnings.filterwarnings('ignore')

from real_data_selector import RealDataCryptoSelector
from optimized_demo import OptimizedCryptoAnalyzer


class DataComparisonAnalyzer:
    """数据对比分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.real_data_selector = RealDataCryptoSelector()
        self.mock_data_selector = OptimizedCryptoAnalyzer()
        self.wechat_webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=52d1b8a8-a88a-4d41-9664-f1268da20985"
    
    def run_comparison_analysis(self):
        """运行对比分析"""
        print("🔬 真实数据 vs 模拟数据对比分析")
        print("=" * 80)
        
        # 运行真实数据分析
        print("1️⃣ 运行真实数据分析...")
        real_results = self.real_data_selector.run_real_data_analysis()
        
        print("\n" + "="*50)
        
        # 运行模拟数据分析
        print("2️⃣ 运行模拟数据分析...")
        mock_results = self.run_mock_analysis()
        
        # 对比分析
        if real_results and mock_results:
            print("\n" + "="*50)
            print("3️⃣ 对比分析结果...")
            comparison_report = self.compare_results(real_results, mock_results)
            self.display_comparison_report(comparison_report)
            
            # 发送对比报告
            print("\n📱 发送对比分析报告...")
            self.send_comparison_notification(comparison_report, real_results, mock_results)
        
        return real_results, mock_results
    
    def run_mock_analysis(self) -> Dict:
        """运行模拟数据分析"""
        try:
            # 使用优化版演示的前8个币种
            symbols = self.mock_data_selector.symbols[:8]
            
            results = {}
            for symbol in symbols:
                symbol_results = {}
                
                # 分析各个时间周期
                for timeframe in ['1d', '4h', '1h']:
                    tf_result = self.mock_data_selector.analyze_symbol_timeframe(symbol, timeframe)
                    if tf_result:
                        symbol_results[timeframe] = tf_result
                
                if symbol_results:
                    # 计算多周期综合得分
                    total_score = 0
                    weight_sum = 0
                    
                    for tf, result in symbol_results.items():
                        weight = self.mock_data_selector.timeframe_weights.get(tf, 0.1)
                        total_score += result['score'] * weight
                        weight_sum += weight
                    
                    multi_tf_score = total_score / weight_sum if weight_sum > 0 else 0
                    
                    # 多周期确认
                    confirmation = self.mock_data_selector.check_multi_timeframe_confirmation(symbol_results)
                    
                    symbol_results['multi_tf_score'] = multi_tf_score
                    symbol_results['confirmation'] = confirmation
                    
                    results[symbol] = symbol_results
            
            print(f"✅ 模拟数据分析完成，共分析 {len(results)} 个标的")
            return results
            
        except Exception as e:
            print(f"❌ 模拟数据分析失败: {e}")
            return {}
    
    def compare_results(self, real_results: Dict, mock_results: Dict) -> Dict:
        """对比分析结果"""
        comparison = {
            'data_source_comparison': {},
            'scoring_comparison': {},
            'risk_assessment_comparison': {},
            'signal_quality_comparison': {},
            'reliability_analysis': {}
        }
        
        # 数据源对比
        comparison['data_source_comparison'] = {
            'real_data': {
                'source': 'CoinGecko API',
                'data_type': '真实市场数据',
                'symbols_count': len(real_results),
                'data_freshness': '实时数据',
                'reliability': '100%真实'
            },
            'mock_data': {
                'source': '算法生成',
                'data_type': '模拟数据',
                'symbols_count': len(mock_results),
                'data_freshness': '模拟生成',
                'reliability': '仅供演示'
            }
        }
        
        # 评分对比
        real_scores = []
        mock_scores = []
        
        # 获取共同的交易对
        common_symbols = set(real_results.keys()) & set(mock_results.keys())
        
        for symbol in common_symbols:
            if symbol in real_results:
                real_scores.append(real_results[symbol]['total_score'])
            if symbol in mock_results:
                mock_scores.append(mock_results[symbol]['multi_tf_score'])
        
        comparison['scoring_comparison'] = {
            'common_symbols': len(common_symbols),
            'real_data_scores': {
                'avg_score': np.mean(real_scores) if real_scores else 0,
                'max_score': max(real_scores) if real_scores else 0,
                'min_score': min(real_scores) if real_scores else 0,
                'score_range': max(real_scores) - min(real_scores) if real_scores else 0
            },
            'mock_data_scores': {
                'avg_score': np.mean(mock_scores) if mock_scores else 0,
                'max_score': max(mock_scores) if mock_scores else 0,
                'min_score': min(mock_scores) if mock_scores else 0,
                'score_range': max(mock_scores) - min(mock_scores) if mock_scores else 0
            }
        }
        
        # 风险评估对比
        real_risk_dist = {'low': 0, 'medium': 0, 'high': 0}
        mock_risk_dist = {'low': 0, 'medium': 0, 'high': 0}
        
        for symbol, data in real_results.items():
            risk = data.get('risk_level', 'medium')
            real_risk_dist[risk] += 1
        
        for symbol, data in mock_results.items():
            risk = data.get('confirmation', {}).get('risk_level', 'medium')
            mock_risk_dist[risk] += 1
        
        comparison['risk_assessment_comparison'] = {
            'real_data_risk': real_risk_dist,
            'mock_data_risk': mock_risk_dist
        }
        
        # 信号质量对比
        real_signals_count = sum(len(data.get('signals', [])) for data in real_results.values())
        mock_signals_count = sum(len(data.get('1d', {}).get('signals', [])) for data in mock_results.values())
        
        comparison['signal_quality_comparison'] = {
            'real_data_signals': real_signals_count,
            'mock_data_signals': mock_signals_count,
            'real_avg_signals_per_symbol': real_signals_count / len(real_results) if real_results else 0,
            'mock_avg_signals_per_symbol': mock_signals_count / len(mock_results) if mock_results else 0
        }
        
        # 可靠性分析
        comparison['reliability_analysis'] = {
            'real_data_advantages': [
                '反映真实市场情况',
                '价格数据准确可靠',
                '成交量数据真实',
                '市场排名实时更新',
                '适合实盘交易决策'
            ],
            'mock_data_limitations': [
                '无法反映真实市场波动',
                '技术指标可能失真',
                '风险评估不够准确',
                '仅适合系统功能演示',
                '不建议用于实盘交易'
            ],
            'recommendation': '强烈建议使用真实数据进行投资决策'
        }
        
        return comparison
    
    def display_comparison_report(self, comparison: Dict):
        """显示对比报告"""
        print("\n📊 数据源对比分析报告")
        print("=" * 80)
        
        # 数据源对比
        real_data = comparison['data_source_comparison']['real_data']
        mock_data = comparison['data_source_comparison']['mock_data']
        
        print(f"📈 真实数据:")
        print(f"   数据源: {real_data['source']}")
        print(f"   数据类型: {real_data['data_type']}")
        print(f"   标的数量: {real_data['symbols_count']}")
        print(f"   数据新鲜度: {real_data['data_freshness']}")
        print(f"   可靠性: {real_data['reliability']}")
        
        print(f"\n🎭 模拟数据:")
        print(f"   数据源: {mock_data['source']}")
        print(f"   数据类型: {mock_data['data_type']}")
        print(f"   标的数量: {mock_data['symbols_count']}")
        print(f"   数据新鲜度: {mock_data['data_freshness']}")
        print(f"   可靠性: {mock_data['reliability']}")
        
        # 评分对比
        scoring = comparison['scoring_comparison']
        print(f"\n📊 评分系统对比:")
        print(f"   共同标的: {scoring['common_symbols']} 个")
        print(f"   真实数据平均分: {scoring['real_data_scores']['avg_score']:.2f}")
        print(f"   模拟数据平均分: {scoring['mock_data_scores']['avg_score']:.2f}")
        print(f"   真实数据得分范围: {scoring['real_data_scores']['score_range']:.2f}")
        print(f"   模拟数据得分范围: {scoring['mock_data_scores']['score_range']:.2f}")
        
        # 风险评估对比
        risk_real = comparison['risk_assessment_comparison']['real_data_risk']
        risk_mock = comparison['risk_assessment_comparison']['mock_data_risk']
        
        print(f"\n⚠️ 风险评估对比:")
        print(f"   真实数据风险分布: 🟢{risk_real['low']} 🟡{risk_real['medium']} 🔴{risk_real['high']}")
        print(f"   模拟数据风险分布: 🟢{risk_mock['low']} 🟡{risk_mock['medium']} 🔴{risk_mock['high']}")
        
        # 可靠性分析
        reliability = comparison['reliability_analysis']
        print(f"\n✅ 真实数据优势:")
        for advantage in reliability['real_data_advantages']:
            print(f"   • {advantage}")
        
        print(f"\n❌ 模拟数据局限:")
        for limitation in reliability['mock_data_limitations']:
            print(f"   • {limitation}")
        
        print(f"\n💡 建议: {reliability['recommendation']}")
    
    def send_comparison_notification(self, comparison: Dict, real_results: Dict, mock_results: Dict):
        """发送对比分析通知"""
        try:
            message = self.build_comparison_message(comparison, real_results, mock_results)
            
            data = {
                "msgtype": "text",
                "text": {
                    "content": message
                }
            }
            
            response = requests.post(
                self.wechat_webhook,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    print("✅ 对比分析报告发送成功")
                else:
                    print(f"❌ 对比分析报告发送失败: {result}")
            else:
                print(f"❌ 对比分析报告请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 发送对比分析报告失败: {e}")
    
    def build_comparison_message(self, comparison: Dict, real_results: Dict, mock_results: Dict) -> str:
        """构建对比分析消息"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            message = f"🔬 数据源对比分析报告\n"
            message += f"⏰ 分析时间: {timestamp}\n\n"
            
            # 数据源对比
            real_data = comparison['data_source_comparison']['real_data']
            mock_data = comparison['data_source_comparison']['mock_data']
            
            message += f"📊 数据源对比:\n"
            message += f"📈 真实数据: {real_data['source']} ({real_data['symbols_count']}个标的)\n"
            message += f"🎭 模拟数据: {mock_data['source']} ({mock_data['symbols_count']}个标的)\n\n"
            
            # 评分对比
            scoring = comparison['scoring_comparison']
            message += f"💯 评分对比:\n"
            message += f"真实数据平均分: {scoring['real_data_scores']['avg_score']:.2f}\n"
            message += f"模拟数据平均分: {scoring['mock_data_scores']['avg_score']:.2f}\n\n"
            
            # 风险分布
            risk_real = comparison['risk_assessment_comparison']['real_data_risk']
            risk_mock = comparison['risk_assessment_comparison']['mock_data_risk']
            
            message += f"⚠️ 风险分布对比:\n"
            message += f"真实数据: 🟢{risk_real['low']} 🟡{risk_real['medium']} 🔴{risk_real['high']}\n"
            message += f"模拟数据: 🟢{risk_mock['low']} 🟡{risk_mock['medium']} 🔴{risk_mock['high']}\n\n"
            
            # 真实数据前3名
            real_sorted = sorted(real_results.items(), key=lambda x: x[1]['total_score'], reverse=True)
            message += f"🏆 真实数据TOP3:\n"
            for i, (symbol, data) in enumerate(real_sorted[:3], 1):
                message += f"{i}. {symbol}: {data['total_score']:.2f}分 ({data['risk_level']}风险)\n"
            
            message += f"\n💡 结论:\n"
            message += f"✅ 真实数据更准确可靠\n"
            message += f"✅ 适合实盘交易决策\n"
            message += f"❌ 模拟数据仅供演示\n"
            message += f"❌ 不建议用于实际投资\n\n"
            
            message += f"🔗 数据源: CoinGecko API\n"
            message += f"⚠️ 投资有风险，决策需谨慎"
            
            return message
            
        except Exception as e:
            print(f"构建对比消息失败: {e}")
            return f"数据源对比分析完成\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"


def main():
    """主函数"""
    analyzer = DataComparisonAnalyzer()
    
    print("🔬 开始数据源对比分析...")
    real_results, mock_results = analyzer.run_comparison_analysis()
    
    print(f"\n" + "=" * 80)
    print("🎯 对比分析总结")
    print("=" * 80)
    print("📈 真实数据系统:")
    print("   • 使用CoinGecko API获取真实市场数据")
    print("   • 价格、成交量、市值等数据100%真实")
    print("   • 适合实盘交易决策")
    print("   • 风险评估更加准确")
    
    print("\n🎭 模拟数据系统:")
    print("   • 使用算法生成模拟数据")
    print("   • 仅用于系统功能演示")
    print("   • 不反映真实市场情况")
    print("   • 不建议用于实际投资")
    
    print(f"\n💡 强烈建议:")
    print("   🚀 实盘交易请使用真实数据系统")
    print("   📊 模拟数据仅供学习和测试")


if __name__ == "__main__":
    main()
