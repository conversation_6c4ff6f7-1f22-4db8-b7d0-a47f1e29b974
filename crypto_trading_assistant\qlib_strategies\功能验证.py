"""
数字货币选币系统功能验证脚本
验证所有核心功能是否正常工作
"""

from market_filter import CryptoMarketFilter
from advanced_selection_demo import AdvancedCryptoSelector
import requests


def test_market_filter():
    """测试市场筛选功能"""
    print("🔍 测试市场筛选功能...")
    
    filter_obj = CryptoMarketFilter()
    
    # 测试预定义列表
    predefined_lists = filter_obj.get_predefined_lists()
    
    print(f"✅ 全市场币种数量: {len(predefined_lists['all_market'])}")
    print(f"✅ 主流币种数量: {len(predefined_lists['mainstream'])}")
    print(f"✅ 小市值币种数量: {len(predefined_lists['small_caps'])}")
    print(f"✅ 新上市币种数量: {len(predefined_lists['new_listings_30d'])}")
    print(f"✅ DeFi代币数量: {len(predefined_lists['defi_tokens'])}")
    
    # 测试自定义筛选
    custom_result = filter_obj.custom_filter(
        min_market_cap=1000000000,  # 10亿美元
        max_market_cap=50000000000,  # 500亿美元
        categories=['Layer1', 'DeFi']
    )
    print(f"✅ 自定义筛选结果: {len(custom_result)} 个币种")
    
    return True


def test_technical_analysis():
    """测试技术分析功能"""
    print("\n📊 测试技术分析功能...")
    
    selector = AdvancedCryptoSelector()
    
    # 测试单个币种分析
    test_symbol = 'BTCUSDT'
    result = selector.analyze_symbol_timeframe(test_symbol, '1d')
    
    if result:
        print(f"✅ {test_symbol} 分析成功:")
        print(f"   综合得分: {result['score']:.2f}")
        print(f"   RSI: {result['rsi']:.1f}")
        print(f"   K线形态: {', '.join(result['kline_patterns'][:2])}")
        print(f"   技术信号: {', '.join(result['signals'][:2])}")
        return True
    else:
        print(f"❌ {test_symbol} 分析失败")
        return False


def test_multi_timeframe():
    """测试多时间周期分析"""
    print("\n⏰ 测试多时间周期分析...")
    
    selector = AdvancedCryptoSelector()
    
    # 测试多周期分析
    test_symbols = ['BTCUSDT', 'ETHUSDT']
    results = selector.multi_timeframe_analysis(test_symbols, "测试")
    
    if results:
        print(f"✅ 多周期分析成功，分析了 {len(results)} 个币种")
        for symbol, data in results.items():
            multi_tf_score = data.get('multi_tf_score', 0)
            confirmation = data.get('confirmation', {})
            print(f"   {symbol}: 综合得分 {multi_tf_score:.2f}, 风险等级 {confirmation.get('risk_level', 'unknown')}")
        return True
    else:
        print("❌ 多周期分析失败")
        return False


def test_kline_patterns():
    """测试K线形态识别"""
    print("\n📊 测试K线形态识别...")
    
    selector = AdvancedCryptoSelector()
    
    # 生成测试数据
    test_data = selector.generate_mock_data('BTCUSDT', '1d')
    
    # 测试形态识别
    patterns = selector.detect_kline_patterns(test_data)
    
    if patterns:
        print(f"✅ K线形态识别成功: {', '.join(patterns[:3])}")
        return True
    else:
        print("❌ K线形态识别失败")
        return False


def test_wechat_notification():
    """测试企业微信推送功能"""
    print("\n📱 测试企业微信推送功能...")
    
    webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=52d1b8a8-a88a-4d41-9664-f1268da20985"
    
    test_message = {
        "msgtype": "text",
        "text": {
            "content": "🧪 数字货币选币系统功能验证测试\n⏰ 测试时间: 2024-12-19 20:45\n✅ 所有功能模块测试通过\n📊 系统运行正常"
        }
    }
    
    try:
        response = requests.post(
            webhook,
            json=test_message,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('errcode') == 0:
                print("✅ 企业微信推送测试成功")
                return True
            else:
                print(f"❌ 企业微信推送失败: {result}")
                return False
        else:
            print(f"❌ 企业微信推送请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 企业微信推送异常: {e}")
        return False


def test_filter_options():
    """测试筛选选项"""
    print("\n🔧 测试筛选选项...")
    
    filter_obj = CryptoMarketFilter()
    options = filter_obj.get_filter_options()
    
    print(f"✅ 市值范围选项: {len(options['market_cap_ranges'])} 个")
    print(f"✅ 上市时间选项: {len(options['listing_periods'])} 个")
    print(f"✅ 类别选项: {len(options['categories'])} 个")
    print(f"✅ 交易量选项: {len(options['volume_ranges'])} 个")
    
    return True


def main():
    """主测试函数"""
    print("🚀 数字货币选币系统功能验证")
    print("=" * 60)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("市场筛选功能", test_market_filter()))
    test_results.append(("技术分析功能", test_technical_analysis()))
    test_results.append(("多时间周期分析", test_multi_timeframe()))
    test_results.append(("K线形态识别", test_kline_patterns()))
    test_results.append(("筛选选项", test_filter_options()))
    test_results.append(("企业微信推送", test_wechat_notification()))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📋 功能验证结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15}: {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有功能验证通过！系统运行正常")
    else:
        print("⚠️ 部分功能存在问题，请检查相关模块")
    
    print("\n📚 详细使用说明请查看:")
    print("- crypto_trading_assistant/qlib_strategies/详细使用说明.md")
    print("- crypto_trading_assistant/qlib_strategies/使用说明.md")
    
    print("\n🚀 快速开始:")
    print("python advanced_selection_demo.py  # 高级选币系统")
    print("python optimized_demo.py          # 优化版演示")
    print("python kline_pattern_demo.py      # K线形态演示")


if __name__ == "__main__":
    main()
