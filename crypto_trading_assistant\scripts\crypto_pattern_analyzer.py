# -*- coding: utf-8 -*-
"""
加密货币形态分析模块
专门用于分析新上市币种的放量上影线形态和反转特征
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import sys
import os
import time
from tqdm import tqdm
import requests
import ccxt
import argparse
import json
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich import box
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import asyncio
import aiohttp
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from tenacity import retry, stop_after_attempt, wait_exponential
from io import StringIO

# 设置系统默认编码
import sys
sys.stdout.reconfigure(encoding='utf-8')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('crypto_pattern_analyzer.log', mode='w', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

# Roo: 移除重复的控制台处理器添加，basicConfig已处理StreamHandler
# # 添加控制台处理器
# console_handler = logging.StreamHandler(sys.stdout)
# console_handler.setLevel(logging.INFO)
# console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
# logger.addHandler(console_handler)

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))) # Roo: 恢复sys.path修改以支持直接运行脚本
from scripts.crypto_technical_analyzer import TechnicalAnalyzer # Roo: 改回绝对导入
from scripts.crypto_exchange_data import ExchangeDataCollector # Roo: 改回绝对导入

def _serialize_data(obj):
    """Custom JSON serializer for objects not serializable by default json code"""
    if isinstance(obj, datetime):  # Handles datetime.datetime and pd.Timestamp
        return obj.isoformat()
    if obj is pd.NaT:
        return None  # Represent NaT as null in JSON
    if isinstance(obj, np.integer):
        return int(obj)
    if isinstance(obj, np.floating):
        return float(obj)
    if isinstance(obj, np.bool_):
        return bool(obj)
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    raise TypeError(f"Object of type {type(obj).__name__} is not JSON serializable")

@dataclass
class ExchangeConfig:
    """交易所配置"""
    name: str
    api_base_url: str
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    use_proxy: bool = False
    request_delay: float = 1.0

@dataclass
class PatternParameters:
    """形态识别参数"""
    volume_ma_period: int = 10  # 成交量均线周期
    volume_threshold: float = 1.2  # 放量倍数阈值
    shadow_ratio_threshold: float = 1.0  # 上影线比例阈值
    body_ratio_threshold: float = 0.1  # 实体比例阈值
    reversal_days: int = 10  # 反转观察天数
    reversal_threshold: float = 0.05  # 反转幅度阈值
    listing_days: int = 60  # 新上市天数阈值

class PatternAnalyzer:
    def __init__(self, 
                 api_keys: Optional[Dict] = None,
                 use_proxy: bool = False,
                 request_delay: float = 1.0):
        """
        初始化模式分析器
        
        Args:
            api_keys: API密钥字典
            use_proxy: 是否使用代理
            request_delay: 请求延迟（秒）
        """
        self.logger = logging.getLogger(__name__)
        self.use_proxy = use_proxy
        self.request_delay = request_delay
        
        # 初始化交易所API
        self.exchange = ccxt.gateio({
            'enableRateLimit': True,
            'timeout': 30000,
            'apiKey': api_keys['apiKey'] if api_keys else None,
            'secret': api_keys['secret'] if api_keys else None,
            'options': {
                'defaultType': 'spot',
                'createMarketBuyOrderRequiresPrice': False,
                'fetchTradesMethod': 'publicGetTradeHistory',
                'recvWindow': 5000,
            }
        })
        
        # 初始化代理设置
        if use_proxy:
            self.exchange.proxies = {
                'http': 'http://127.0.0.1:7890',
                'https': 'http://127.0.0.1:7890'
            }

        # 加载SERVER酱配置 和 SMTP 配置
        try:
            # Roo: 确保logger在config加载前已初始化，此处的self.logger是在这之前定义的
            with open('scripts/config.json', 'r', encoding='utf-8') as f: # Roo: 假设config.json在scripts目录下
                self.config = json.load(f)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {str(e)}")
            self.config = {"wechat": {"send_keys": []}, "smtp": {}} # Roo: 提供默认smtp配置

        # Roo: 获取SMTP配置，如果不存在则使用TechnicalAnalyzer的默认值
        smtp_config = self.config.get("smtp", {})
        smtp_server = smtp_config.get('server', 'smtp.your.email.com')
        smtp_port = smtp_config.get('port', 587)
        smtp_user = smtp_config.get('user', '<EMAIL>')
        smtp_password = smtp_config.get('password', 'your_password')

        if smtp_server == 'smtp.your.email.com' or not smtp_user or not smtp_password:
            self.logger.warning("SMTP配置未在config.json中正确配置或使用的是默认占位符，邮件功能可能无法正常工作。请在scripts/config.json中配置SMTP服务器信息 (server, port, user, password)。")
        
        # 初始化技术分析器
        self.technical_analyzer = TechnicalAnalyzer(
            smtp_server=smtp_server,
            smtp_port=smtp_port,
            smtp_user=smtp_user,
            smtp_password=smtp_password
        )
        
        # 设置重试次数和延迟
        self.max_retries = 3
        self.retry_delay = 5  # 秒

        # 初始化rich控制台
        self.console = Console(force_terminal=True, color_system="auto")
        
        # 创建结果保存目录
        self.results_dir = Path("analysis_results")
        self.results_dir.mkdir(exist_ok=True)
        
        # 初始化缓存
        self.ohlcv_cache = {}
        self.analysis_cache = {}
        self.cache_ttl = 300  # 缓存有效期5分钟

    def get_cached_ohlcv(self, symbol: str) -> Optional[List]:
        """获取缓存的K线数据"""
        if symbol in self.ohlcv_cache:
            cache_time, data = self.ohlcv_cache[symbol]
            if time.time() - cache_time < self.cache_ttl:
                return data
        return None

    def set_cached_ohlcv(self, symbol: str, data: List):
        """设置K线数据缓存"""
        self.ohlcv_cache[symbol] = (time.time(), data)

    def fetch_ohlcv(self, symbol: str) -> Optional[pd.DataFrame]:
        """获取K线数据"""
        try:
            # 使用ccxt获取K线数据，增加 limit 以获取更多历史数据
            ohlcv = self.exchange.fetch_ohlcv(symbol, '1d', limit=250) # Roo: 将 limit 从 30 增加到 250
            if ohlcv:
                # 转换为DataFrame
                df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                return df
            else:
                self.logger.error(f"获取{symbol}的K线数据失败: 未获取到数据")
                return None
        except Exception as e:
            self.logger.error(f"获取{symbol}的K线数据失败: {str(e)}")
            return None

    def process_trading_pairs(self, trading_pairs: List[str]) -> Dict:
        """处理交易对列表"""
        results = {}
        
        # 创建进度条
        with Progress() as progress:
            task = progress.add_task("[cyan]获取K线数据...", total=len(trading_pairs))
            
            # 处理每个交易对
            for symbol in trading_pairs:
                result = self.process_single_pair(symbol, {'symbol': symbol})
                if result:
                    results[symbol] = result
                progress.update(task, advance=1)
        
        return results

    def process_single_pair(self, symbol: str, pair_info: Dict) -> Optional[Dict]:
        """处理单个交易对"""
        try:
            # 获取K线数据
            df = self.fetch_ohlcv(symbol)
            if df is None: # Roo: 先检查None，避免对None使用len()
                self.logger.warning(f"{symbol}: fetch_ohlcv 返回 None，跳过处理。")
                return None
            
            self.logger.info(f"{symbol}: fetch_ohlcv 获取到 {len(df)} 条K线数据。") # Roo: 添加日志记录获取的数据条数

            if len(df) < 5: # Roo: 原有的检查，确保至少有5条数据进行后续处理
                self.logger.warning(f"{symbol}: K线数据少于5条 ({len(df)})，不进行分析。")
                return None
            
            # 计算技术指标
            df = self.preprocess_ohlcv(df)
            
            # 分析形态 - 将 symbol 传递给 analyze_pattern
            analysis = self.analyze_pattern(df, symbol)
            
            # 添加基本信息
            result = {
                'symbol': symbol,
                'analysis': analysis
            }
            
            return result
        except Exception as e:
            self.logger.error(f"处理{symbol}时出错: {str(e)}")
            return None

    def preprocess_ohlcv(self, df: pd.DataFrame) -> pd.DataFrame:
        """预处理K线数据，计算常用指标"""
        # 确保数据类型正确
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns: # Roo: 确保所有列都存在
            if col not in df.columns:
                self.logger.error(f"DataFrame缺少必需的列: {col}")
                # 可以选择返回原始df或引发错误
                return df.copy() # Roo: 返回副本以避免修改原始传入的df
        df[numeric_columns] = df[numeric_columns].astype(float)
        
        # Roo: 使用TechnicalAnalyzer计算所有指标
        try:
            # Roo: 确保传递的是DataFrame的副本，以避免calculate_indicators修改原始df（如果它有副作用）
            df_with_indicators = self.technical_analyzer.calculate_indicators(df.copy())
            return df_with_indicators
        except Exception as e:
            self.logger.error(f"调用TechnicalAnalyzer计算指标时出错: {str(e)}")
            # Roo: 如果TechnicalAnalyzer失败，返回仅包含基本类型转换的DataFrame副本
            return df.copy()

    def get_new_listings(self, max_listings: int = 0) -> Dict:
        """
        获取从60天前到今天的新上市币种信息
        
        Args:
            max_listings: 最大获取数量，大于0时获取到指定数量后立即返回，否则获取全部符合条件的币种
        
        Returns:
            新上市币种信息字典
        """
        try:
            self.logger.info("正在获取所有交易对信息...")
            # 获取所有交易对
            markets = self.exchange.load_markets()
            if not markets:
                self.logger.error("未能获取到交易对信息")
                return {}
            
            self.logger.info(f"成功获取{len(markets)}个交易对")
            
            # 获取新币上线时间
            new_listings = {}
            current_date = datetime.now()
            start_date = current_date - timedelta(days=60)  # 60天前的日期
            end_date = current_date
            
            self.logger.info(f"开始日期: {start_date.strftime('%Y-%m-%d')}")
            self.logger.info(f"结束日期: {end_date.strftime('%Y-%m-%d')}")
            
            # 只处理USDT交易对，并转换格式
            usdt_markets = {k: v for k, v in markets.items() if k.endswith('/USDT')}
            self.logger.info(f"找到{len(usdt_markets)}个USDT交易对")
            
            # 批量获取K线数据
            symbols = list(usdt_markets.keys())
            
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TaskProgressColumn(),
                console=self.console
            ) as progress:
                task = progress.add_task("获取K线数据...", total=len(symbols))
                
                for i in range(0, len(symbols), 10):  # 每10个交易对一批
                    batch = symbols[i:i+10]
                    batch_data = self.process_trading_pairs(batch)
                    
                    # 处理每个交易对的数据
                    for symbol, data in batch_data.items():
                        if data and 'analysis' in data:
                            market = usdt_markets[symbol]
                            listing_date = self._get_listing_date(market)
                            
                            if start_date <= listing_date <= end_date:
                                new_listings[symbol] = {
                                        'listing_date': listing_date.strftime('%Y-%m-%d'),
                                        'analysis': data['analysis']
                                    }
                                    
                                if max_listings > 0 and len(new_listings) >= max_listings:
                                    return new_listings
                    
                    progress.update(task, advance=len(batch))
                    time.sleep(1)  # 每批请求后等待1秒
            
                return new_listings
                
        except Exception as e:
            self.logger.error(f"获取新上市币种时发生错误: {str(e)}")
            import traceback
            self.logger.error(f"错误详情:\n{traceback.format_exc()}")
            return {}

    def _get_listing_date(self, market, current_date=None):
        """
        获取币种的上市日期
        
        Args:
            market: 交易对信息
            current_date: 当前日期，默认为None
            
        Returns:
            上市日期
        """
        if current_date is None:
            current_date = datetime.now()
            
        try:
            # 使用ccxt获取K线数据
            ohlcv = self.exchange.fetch_ohlcv(market['symbol'], '1d', limit=1)
            if ohlcv:
                return datetime.fromtimestamp(ohlcv[0][0] / 1000)
        except Exception as e:
            self.logger.error(f"获取{market['symbol']}的上市日期失败: {str(e)}")
        return current_date

    def backtest(self, symbol: str, start_date: str, end_date: str) -> Dict:
        """
        回测指定币种的交易策略
        
        Args:
            symbol: 交易对符号
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            回测结果字典
        """
        try:
            # 获取历史数据
            retries = 0
            while retries < self.max_retries:
                try:
                    # 获取K线数据
                    ohlcv = self.exchange.fetch_ohlcv(symbol, '1d', limit=200)
                    if not ohlcv:
                        raise Exception("未获取到K线数据")
                        
                    # 转换为DataFrame
                    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    
                    # 计算技术指标
                    df = self.technical_analyzer.calculate_indicators(df)
                    
                    # 识别上影线模式
                    signals = self.technical_analyzer.identify_upper_shadow(df)
                    
                    # 计算回测结果
                    results = self._calculate_backtest_results(df, signals)
                    
                    return results
                    
                except Exception as e:
                    retries += 1
                    self.logger.error(f"回测失败 (尝试 {retries}/{self.max_retries}): {str(e)}")
                    if retries < self.max_retries:
                        time.sleep(self.retry_delay)
                    else:
                        raise Exception("回测失败，已达到最大重试次数")
                        
        except Exception as e:
            self.logger.error(f"回测{symbol}失败: {str(e)}")
            raise

    def test_exchange_connection(self):
        """测试交易所API连接"""
        try:
            self.logger.info("正在测试交易所API连接...")
            
            # 测试获取交易对
            self.logger.info("1. 测试获取交易对...")
            markets = self.exchange.load_markets()
            self.logger.info(f"成功获取交易对，总数: {len(markets)}")
            
            # 测试获取K线数据
            self.logger.info("\n2. 测试获取K线数据...")
            symbol = "BTC/USDT"
            self.logger.info(f"使用测试交易对: {symbol}")
            ohlcv = self.exchange.fetch_ohlcv(symbol, '1d', limit=1)
            self.logger.info(f"成功获取K线数据: {ohlcv}")
            
            self.logger.info("\n交易所API连接测试成功!")
            return True
            
        except Exception as e:
            self.logger.error(f"交易所API连接测试失败: {str(e)}")
            self.logger.error(f"错误详情: {type(e).__name__}: {str(e)}")
            return False

    def save_analysis_results(self, results: Dict, symbol: str):
        """保存分析结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = self.results_dir / f"{symbol}_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=_serialize_data)
            self.logger.info(f"分析结果已保存到: {filename}")
        except Exception as e:
            self.logger.error(f"保存分析结果失败: {str(e)}")

    def send_wechat_notification(self, title: str, content: str):
        """发送微信通知"""
        if not self.config.get("wechat", {}).get("send_keys"):
            self.logger.warning("未配置SERVER酱API密钥")
            return
            
        for send_key in self.config["wechat"]["send_keys"]:
            try:
                url = f"https://sctapi.ftqq.com/{send_key}.send"
                data = {
                    "title": title,
                    "desp": content
                }
                response = requests.post(url, data=data)
                if response.status_code == 200:
                    self.logger.info("微信通知发送成功")
                else:
                    self.logger.error(f"微信通知发送失败: {response.text}")
            except Exception as e:
                self.logger.error(f"发送微信通知时出错: {str(e)}")

    def format_analysis_report(self, symbol: str, analysis: Dict) -> str:
        """格式化分析报告"""
        # 创建rich表格
        table = Table(title=f"\n{symbol} 技术分析报告", box=box.ROUNDED)
        
        # 添加列
        table.add_column("指标", style="cyan")
        table.add_column("数值", style="green")
        
        # 添加基本信息
        table.add_row("当前价格", f"{analysis['当前价格']:.4f} USDT")
        table.add_row("24h涨跌幅", f"{analysis['24h涨跌幅']:.2f}%")
        table.add_row("24h成交量变化", f"{analysis['24h成交量变化']:.2f}%")
        
        # 添加趋势信息
        table.add_row("MA5趋势", analysis['MA5趋势'])
        table.add_row("MA10趋势", analysis['MA10趋势'])
        table.add_row("成交量趋势", analysis['成交量趋势'])
        
        # 添加技术指标
        table.add_row("波动率", f"{analysis['波动率']:.2f}%")
        table.add_row("上影线比例", f"{analysis['上影线比例']:.2f}%")
        table.add_row("下影线比例", f"{analysis['下影线比例']:.2f}%")
        table.add_row("实体大小", f"{analysis['实体大小']:.2f}%")
        
        # 添加形态和风险信息
        table.add_row("K线形态", analysis['K线形态'])
        table.add_row("风险等级", analysis['风险等级'])
        table.add_row("交易建议", analysis['交易建议'])
        
        # 使用rich渲染表格
        console = Console()
        report = console.render(table)
        
        # 添加时间戳
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        report = f"分析时间: {timestamp}\n{report}"
        
        return report

    def analyze_pattern(self, df: pd.DataFrame, symbol: str = "未知币种") -> Dict: # Roo: 添加 symbol 参数
        """
        分析K线形态，并结合TechnicalAnalyzer生成综合分析结果。
        :param df: 包含OHLCV及TechnicalAnalyzer计算的技术指标的DataFrame
        :param symbol: 当前分析的交易对符号，用于日志记录
        :return: 分析结果字典
        """
        try:
            if df is None or df.empty or len(df) < 2: # 需要至少两行数据来计算变化
                self.logger.warning("DataFrame数据不足，无法进行分析。")
                return self._get_default_analysis()

            # 确保 'timestamp' 列存在且为 datetime 类型，用于信号产生时间
            if 'timestamp' not in df.columns or not pd.api.types.is_datetime64_any_dtype(df['timestamp']):
                 # 如果 fetch_ohlcv 返回的 df['timestamp'] 是 datetime64[ms]
                 # 而 preprocess_ohlcv 中 self.technical_analyzer.calculate_indicators(df.copy())
                 # 如果 technical_analyzer 内部 set_index('timestamp') 后又 reset_index()，需要确认 timestamp 列的状态
                 # 假设 preprocess_ohlcv 返回的 df 仍然有 'timestamp' 列
                self.logger.error("DataFrame中缺少有效的 'timestamp' 列。")
                # 尝试从索引获取时间戳（如果它是DatetimeIndex）
                if isinstance(df.index, pd.DatetimeIndex) and not df.index.empty:
                    signal_generation_time = df.index[-1].isoformat()
                else: # 否则无法确定信号时间
                    signal_generation_time = None
            else:
                signal_generation_time = df['timestamp'].iloc[-1].isoformat()


            # 1. 从TechnicalAnalyzer获取主要交易信号
            # 确保传递给generate_signals的df包含所有必要的指标
            trade_signal_details = self.technical_analyzer.generate_signals(df.copy()) # 使用副本

            # 2. 提取当前价格
            current_price = float(df['close'].iloc[-1])

            # 3. 计算24小时价格变动 (基于传入的df的最后两根K线)
            prev_close_price = float(df['close'].iloc[-2])
            price_change_24h = ((current_price - prev_close_price) / prev_close_price * 100) if prev_close_price != 0 else 0

            # 4. 计算24小时成交量变动 (基于传入的df的最后两根K线)
            current_volume = float(df['volume'].iloc[-1])
            prev_volume = float(df['volume'].iloc[-2])
            volume_change_24h = ((current_volume - prev_volume) / prev_volume * 100) if prev_volume != 0 else 0
            
            # 5. 信号产生时间 (已在开头获取)

            # 6. 生成建议交易时间周期 和 7. 提取关键指标
            # 首先检查 trade_signal_details 是否表明数据不足
            is_data_insufficient = (
                trade_signal_details.get('reasons') == ['数据不足'] and
                trade_signal_details.get('risk_level') == 'unknown' # 与TechnicalAnalyzer的设定一致
            )

            if is_data_insufficient:
                self.logger.info(f"检测到数据不足信号 ({symbol})，将设置默认的建议周期和指标。") # Roo: 使用传入的 symbol
                suggested_timeframe = "N/A (数据不足)"
                key_indicators = {
                    "rsi_14": "N/A", "macd": "N/A", "macd_signal": "N/A", "macd_hist": "N/A",
                    "bollinger_upper": "N/A", "bollinger_middle": "N/A", "bollinger_lower": "N/A",
                    "atr": "N/A", "adx": "N/A", "overall_trend": "N/A", "candlestick_pattern": "N/A"
                }
            else:
                suggested_timeframe = "1d" # 默认日线
                atr_value = df['atr'].iloc[-1] if 'atr' in df.columns and not pd.isna(df['atr'].iloc[-1]) else 0.05 * current_price # 默认ATR为价格的5%
                
                # 确保 trade_signal_details 和 risk_level 存在
                sig_strength = trade_signal_details.get('strength', 'neutral')
                # sig_risk 可能来自 TechnicalAnalyzer._evaluate_risk (中文) 或 TechnicalAnalyzer.generate_signals 中数据不足时的 'unknown'
                sig_risk = trade_signal_details.get('risk_level', 'medium')

                if sig_strength == 'strong' and sig_risk in ['low', '中', '低']: # 'medium' 对应中文 '中', 'low' 对应 '低'
                    suggested_timeframe = "1d/4h (趋势较强)"
                elif sig_risk == '高' or str(sig_risk).lower() == 'high': # 兼容中文“高”和可能的英文 "high"
                    suggested_timeframe = "1h/15m (高风险, 短线)"
                elif atr_value > 0.1 * current_price : # ATR 大于当前价格10%，波动剧烈
                    suggested_timeframe = "谨慎操作或关注1h及更短周期 (波动剧烈)"
                elif sig_strength == 'weak':
                    suggested_timeframe = "观望或关注1h突破 (信号较弱)"
                # 如果 risk_level 是 'unknown' (例如从 TechnicalAnalyzer 传过来但非数据不足的情况)，则 suggested_timeframe 会保持 "1d"

                key_indicators = {
                    "rsi_14": df['rsi_14'].iloc[-1] if 'rsi_14' in df.columns else None,
                "macd": df['macd'].iloc[-1] if 'macd' in df.columns else None,
                "macd_signal": df['macd_signal'].iloc[-1] if 'macd_signal' in df.columns else None,
                "macd_hist": df['macd_hist'].iloc[-1] if 'macd_hist' in df.columns else None,
                "bollinger_upper": df['bollinger_upper'].iloc[-1] if 'bollinger_upper' in df.columns else None,
                "bollinger_middle": df['bollinger_middle'].iloc[-1] if 'bollinger_middle' in df.columns else None,
                "bollinger_lower": df['bollinger_lower'].iloc[-1] if 'bollinger_lower' in df.columns else None,
                "atr": atr_value, # 使用已获取的atr_value
                "adx": df['adx'].iloc[-1] if 'adx' in df.columns else None,
                # 从TechnicalAnalyzer的_determine_overall_trend获取趋势，或者直接用EMA判断
                "overall_trend": self.technical_analyzer._determine_overall_trend(df) if hasattr(self.technical_analyzer, '_determine_overall_trend') else "未知",
                "candlestick_pattern": self.technical_analyzer._identify_candlestick_pattern(df) if hasattr(self.technical_analyzer, '_identify_candlestick_pattern') else "未知"
            }
            # 清理None值，转换为字符串"N/A"以便报告
            for k, v in key_indicators.items():
                if v is None or pd.isna(v):
                    key_indicators[k] = "N/A"
                elif isinstance(v, float):
                    key_indicators[k] = f"{v:.4f}" # 格式化浮点数


            analysis_result = {
                "current_price": current_price,
                "price_change_24h": price_change_24h,
                "volume_change_24h": volume_change_24h,
                "signal_details": trade_signal_details, # 包含action, strength, reasons, confidence, stop_loss, take_profit, risk_level
                "signal_generation_time": signal_generation_time,
                "suggested_timeframe": suggested_timeframe,
                "key_indicators": key_indicators
            }
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"分析K线形态时发生严重错误: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return self._get_default_analysis()
            
    def _get_default_analysis(self):
        """
        返回默认的分析结果 (新的结构)
        """
        return {
            "current_price": 0.0,
            "price_change_24h": 0.0,
            "volume_change_24h": 0.0,
            "signal_details": {
                'action': 'hold',
                'strength': 'neutral',
                'reasons': ['数据不足或分析错误'],
                'confidence': 0.0,
                'stop_loss': None,
                'take_profit': None,
                'risk_level': 'unknown' # 确保与TechnicalAnalyzer数据不足时一致
            },
            "signal_generation_time": None,
            "suggested_timeframe": "N/A",
            "key_indicators": {
                "rsi_14": "N/A",
                "macd": "N/A",
                "macd_signal": "N/A",
                "macd_hist": "N/A",
                "bollinger_upper": "N/A",
                "bollinger_middle": "N/A",
                "bollinger_lower": "N/A",
                "atr": "N/A",
                "adx": "N/A",
                "overall_trend": "N/A",
                "candlestick_pattern": "N/A"
            }
        }

    def _translate_to_chinese(self, text: str) -> str:
        """将分析结果转换为中文"""
        # Roo: 确保翻译字典的键与TechnicalAnalyzer可能返回的英文状态一致
        translations = {
            # 信号方向
            "buy": "买入",
            "sell": "卖出",
            "hold": "观望",
            # 信号强度
            "strong": "强",
            "moderate": "中等",
            "weak": "弱",
            "neutral": "中性",
            # 风险等级 (来自TechnicalAnalyzer._evaluate_risk)
            "高": "高风险", # TechnicalAnalyzer._evaluate_risk 返回中文
            "中": "中等风险",
            "低": "低风险",
            "未知": "未知风险", # TechnicalAnalyzer._evaluate_risk 返回中文
            "high_risk": "高风险", # PatternAnalyzer旧版可能返回
            "medium_risk": "中等风险",
            "low_risk": "低风险",
            # 趋势 (来自TechnicalAnalyzer._determine_overall_trend)
            "强势上涨": "强势上涨",
            "上涨": "上涨",
            "盘整": "盘整",
            "下跌": "下跌",
            "强势下跌": "强势下跌",
            # K线形态 (来自TechnicalAnalyzer._identify_candlestick_pattern)
            "十字星": "十字星",
            "长上影线": "长上影线",
            "锤子线": "锤子线",
            "看涨吞没": "看涨吞没",
            "看跌吞没": "看跌吞没",
            "看涨实体": "看涨实体",
            "看跌实体": "看跌实体",
            # 旧版 PatternAnalyzer 形态
            "doji": "十字星",
            "hammer": "锤子线",
            "hanging_man": "上吊线",
            "bullish_body": "看涨实体",
            "bearish_body": "看跌实体",
            "no_clear_pattern": "无明显形态",
            # 旧版 PatternAnalyzer 交易建议
            "consider_buy": "可以考虑买入",
            "wait_and_see": "建议观望",
            "high_volatility_caution": "波动较大，需谨慎",
            "need_more_observation": "需要更多观察",
            # 确保未知情况有默认处理
            "unknown": "未知",
            # 趋势
            "uptrend": "上涨",
            "downtrend": "下跌",
            "sideways": "横盘",
            "unknown": "未知",
            
            # K线形态
            "doji": "十字星",
            "hammer": "锤子线",
            "hanging_man": "上吊线",
            "bullish_body": "看涨实体",
            "bearish_body": "看跌实体",
            "no_clear_pattern": "无明显形态",
            
            # 风险等级
            "high_risk": "高风险",
            "medium_risk": "中等风险",
            "low_risk": "低风险",
            
            # 交易建议
            "consider_buy": "可以考虑买入",
            "wait_and_see": "建议观望",
            "high_volatility_caution": "波动较大，需谨慎",
            "need_more_observation": "需要更多观察"
        }
        return translations.get(text, text)

    def format_summary_report(self, new_listings: Dict) -> str:
        """格式化汇总报告"""
        # 添加时间戳和统计信息
        report_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        total_pairs = len(new_listings)
        
        header = f"""# 加密货币新上市币种分析报告
分析时间: {report_time}
分析币种数量: {total_pairs}

"""
        # 定义表头，为了适应更多信息，可能需要调整列宽或选择性显示
        # 交易对 | 上市日期 | 信号时间 | 价格 | 24h % | 信号 | 强度 | 置信度 | 风险 | 建议周期 | SL | TP | RSI | 趋势 | 形态
        # 为了终端显示友好，选择核心信息
        table_header = "{:<12} {:<10} {:<19} {:<10} {:<8} {:<6} {:<6} {:<6} {:<8} {:<20} {:<10} {:<10}".format(
            "交易对", "上市日期", "信号时间", "价格", "24h %", "信号", "强度", "置信度", "风险", "建议周期", "止损", "止盈"
        )
        separator = "-" * len(table_header) # 根据表头长度调整分隔线
        
        rows_data = [header, table_header, separator]

        for symbol, info in new_listings.items():
            analysis = info.get('analysis', self._get_default_analysis()) # 获取分析，如果不存在则用默认值
            
            current_price = analysis.get('current_price', 0.0)
            price_change_24h = analysis.get('price_change_24h', 0.0)
            price_text = f"{price_change_24h:+.2f}%"
            
            signal_details = analysis.get('signal_details', {})
            action = self._translate_to_chinese(signal_details.get('action', 'N/A'))
            strength = self._translate_to_chinese(signal_details.get('strength', 'N/A'))
            confidence = f"{signal_details.get('confidence', 0.0):.2f}"
            risk_level = self._translate_to_chinese(signal_details.get('risk_level', 'N/A'))
            
            stop_loss = signal_details.get('stop_loss')
            sl_text = f"{stop_loss:.4f}" if stop_loss is not None else "N/A"
            take_profit = signal_details.get('take_profit')
            tp_text = f"{take_profit:.4f}" if take_profit is not None else "N/A"

            signal_time_raw = analysis.get('signal_generation_time', 'N/A')
            signal_time_formatted = "N/A"
            if signal_time_raw and signal_time_raw != "N/A":
                try:
                    # 解析ISO格式时间并重新格式化
                    dt_obj = datetime.fromisoformat(signal_time_raw)
                    signal_time_formatted = dt_obj.strftime('%Y-%m-%d %H:%M:%S')
                except ValueError:
                    signal_time_formatted = signal_time_raw # 如果解析失败，显示原始值

            suggested_timeframe = analysis.get('suggested_timeframe', "N/A")
            
            # key_indicators = analysis.get('key_indicators', {})
            # rsi = key_indicators.get('rsi_14', "N/A")
            # overall_trend = self._translate_to_chinese(key_indicators.get('overall_trend', "N/A"))
            # candlestick_pattern = self._translate_to_chinese(key_indicators.get('candlestick_pattern', "N/A"))

            row = "{:<12} {:<10} {:<19} {:<10.4f} {:<8} {:<6} {:<6} {:<6} {:<8} {:<20} {:<10} {:<10}".format(
                symbol,
                info.get('listing_date', 'N/A'),
                signal_time_formatted,
                current_price,
                price_text,
                action,
                strength,
                confidence,
                risk_level,
                suggested_timeframe,
                sl_text,
                tp_text
                # rsi,
                # overall_trend,
                # candlestick_pattern
            )
            rows_data.append(row)
        
        report_body = "\n".join(rows_data)
        
        footer = """

## 重点指标解释:
- **信号**: 根据技术分析得出的买入/卖出/观望建议。
- **强度**: 信号的强弱程度 (强/中等/弱)。
- **置信度**: 信号的可靠性评分 (0.0-1.0)。
- **风险**: 综合风险评估 (高/中/低)。
- **建议周期**: 建议关注的交易时间周期。
- **止损(SL)/止盈(TP)**: 建议的止损和止盈价格点位。

## 风险提示
1. 新上市币种波动较大，请谨慎投资。
2. 建议设置止损位，控制风险。
3. 关注项目基本面和市场情绪变化。
4. 杠杆代币风险较高，不建议长期持有。

## 免责声明
本分析报告仅供参考，不构成投资建议。投资有风险，入市需谨慎。"""
        
        return report_body + footer

    def save_csv_report(self, new_listings: Dict, filename: str):
        """保存CSV格式的分析报告"""
        try:
            rows = []
            for symbol, info in new_listings.items():
                analysis = info.get('analysis', self._get_default_analysis())
                signal_details = analysis.get('signal_details', {})
                key_indicators = analysis.get('key_indicators', {})

                # 格式化信号时间
                signal_time_raw = analysis.get('signal_generation_time', 'N/A')
                signal_time_formatted = "N/A"
                if signal_time_raw and signal_time_raw != "N/A":
                    try:
                        dt_obj = datetime.fromisoformat(signal_time_raw)
                        signal_time_formatted = dt_obj.strftime('%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        signal_time_formatted = signal_time_raw
                
                reasons_text = "; ".join(signal_details.get('reasons', [])) if signal_details.get('reasons') else "N/A"

                row = {
                    '交易对': symbol,
                    '上市日期': info.get('listing_date', 'N/A'),
                    '信号产生时间': signal_time_formatted,
                    '当前价格': f"{analysis.get('current_price', 0.0):.6f}",
                    '24h价格变动(%)': f"{analysis.get('price_change_24h', 0.0):.2f}",
                    '24h成交量变动(%)': f"{analysis.get('volume_change_24h', 0.0):.2f}",
                    '建议时间周期': analysis.get('suggested_timeframe', 'N/A'),
                    
                    '信号-操作': self._translate_to_chinese(signal_details.get('action', 'N/A')),
                    '信号-强度': self._translate_to_chinese(signal_details.get('strength', 'N/A')),
                    '信号-置信度': f"{signal_details.get('confidence', 0.0):.2f}",
                    '信号-风险等级': self._translate_to_chinese(signal_details.get('risk_level', 'N/A')),
                    '信号-止损': f"{signal_details.get('stop_loss'):.6f}" if signal_details.get('stop_loss') is not None else "N/A",
                    '信号-止盈': f"{signal_details.get('take_profit'):.6f}" if signal_details.get('take_profit') is not None else "N/A",
                    '信号-原因': reasons_text,
                    
                    '指标-RSI14': key_indicators.get('rsi_14', "N/A"),
                    '指标-MACD': key_indicators.get('macd', "N/A"),
                    '指标-MACD信号线': key_indicators.get('macd_signal', "N/A"),
                    '指标-MACD柱': key_indicators.get('macd_hist', "N/A"),
                    '指标-ATR': key_indicators.get('atr', "N/A"),
                    '指标-ADX': key_indicators.get('adx', "N/A"),
                    '指标-整体趋势': self._translate_to_chinese(key_indicators.get('overall_trend', "N/A")),
                    '指标-K线形态': self._translate_to_chinese(key_indicators.get('candlestick_pattern', "N/A")),
                }
                rows.append(row)
            
            if rows:
                df = pd.DataFrame(rows)
                df.to_csv(filename, index=False, encoding='utf-8-sig')
                self.logger.info(f"CSV报告已保存到: {filename}")
            else:
                self.logger.info("没有数据可保存到CSV报告。")
            
        except Exception as e:
            self.logger.error(f"保存CSV报告时出错: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())

def test_real_data(max_listings: int = 0):
    """使用真实数据进行测试"""
    try:
        # 初始化分析器
        analyzer = PatternAnalyzer(
            api_keys=None,  # 暂时不使用API密钥
            use_proxy=False,  # 暂时不使用代理
            request_delay=1.0  # 请求延迟1秒
        )
        
        try:
            new_listings = analyzer.get_new_listings(max_listings=max_listings)
            
            if not new_listings:
                logger.warning("未找到新上市币种")
                return
            
            # 生成汇总报告
            summary_report = analyzer.format_summary_report(new_listings)
            
            # 打印汇总报告
            console = Console(force_terminal=True, color_system="auto")
            console.print(summary_report)
            
            # 保存汇总结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            summary_file = Path("analysis_results") / f"summary_{timestamp}.json"
            csv_file = Path("analysis_results") / f"summary_{timestamp}.csv"
            
            # 保存JSON格式
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(new_listings, f, ensure_ascii=False, indent=2, default=_serialize_data)
            
            # 保存CSV格式
            analyzer.save_csv_report(new_listings, csv_file)
            
            # 发送微信通知 (如果配置了)
            if analyzer.config.get("wechat", {}).get("send_keys"):
                analyzer.send_wechat_notification(
                    title=f"加密货币新上市币种分析报告 - {timestamp}",
                    content=summary_report
                )
            
            # # Roo: 添加邮件发送功能 (旧的邮件逻辑，将被替换)
            # email_recipients = analyzer.config.get("email_recipients", ['<EMAIL>', '<EMAIL>'])
            # # Roo: 确保 technical_analyzer 实例存在且有 send_analysis_report_email 方法
            # if hasattr(analyzer, 'technical_analyzer') and hasattr(analyzer.technical_analyzer, 'send_analysis_report_email'):
            #     email_subject = f"加密货币新上市币种分析报告 - {timestamp}"
            #     try:
            #         analyzer.technical_analyzer.send_analysis_report_email(
            #             report_content=summary_report,
            #             recipients=email_recipients,
            #             subject=email_subject
            #         )
            #         logger.info(f"分析报告已通过邮件发送至: {', '.join(email_recipients)}")
            #     except Exception as mail_e:
            #         logger.error(f"发送邮件报告失败: {str(mail_e)}")
            # else:
            #     logger.error("TechnicalAnalyzer 或其邮件发送功能未正确初始化。")

            logger.info(f"\n分析完成，结果已保存到:")
            logger.info(f"JSON文件: {summary_file}")
            logger.info(f"CSV文件: {csv_file}")

            # 调用新的 Outlook 邮件发送脚本
            # 将导入移到函数内部，在调用前执行，以避免循环导入
            send_outlook_report_module = None
            try:
                from scripts import send_outlook_report as send_outlook_report_module_scripts
                send_outlook_report_module = send_outlook_report_module_scripts
                logger.info("成功从 'scripts' 导入 send_outlook_report 模块。")
            except ImportError:
                try:
                    import send_outlook_report as send_outlook_report_module_direct
                    send_outlook_report_module = send_outlook_report_module_direct
                    logger.info("成功直接导入 send_outlook_report 模块 (fallback)。")
                except ImportError as import_err:
                    logger.error(f"无法导入 send_outlook_report 模块: {import_err}. 邮件将不会自动发送。")

            if send_outlook_report_module and hasattr(send_outlook_report_module, 'main'):
                logger.info("准备调用 send_outlook_report.main() 发送邮件...")
                try:
                    send_outlook_report_module.main()
                    logger.info("send_outlook_report.main() 执行完毕。")
                except Exception as e_outlook:
                    logger.error(f"调用 send_outlook_report.main() 时发生错误: {e_outlook}")
                    import traceback
                    logger.error(f"send_outlook_report 错误详情:\n{traceback.format_exc()}")
            elif not send_outlook_report_module:
                logger.warning("send_outlook_report 模块未能成功导入，无法自动发送邮件。")
            
        except Exception as e:
            logger.error(f"获取新上市币种时发生错误: {str(e)}")
            import traceback
            logger.error(f"错误详情:\n{traceback.format_exc()}")
            
    except Exception as e:
        logger.error(f"程序运行出错: {str(e)}")
        logger.error(f"错误详情: {type(e).__name__}: {str(e)}")
        import traceback
        logger.error(f"堆栈跟踪:\n{traceback.format_exc()}")

def print_results(results: Dict):
    """打印回测结果"""
    if results:
        print(f"总信号数: {results['total_signals']}")
        print(f"成功信号数: {results['successful_signals']}")
        print(f"胜率: {results['win_rate']:.2%}")
        print(f"平均收益: {results['avg_gain']:.2%}")
        print(f"平均亏损: {results['avg_loss']:.2%}")
        print(f"盈亏比: {results['profit_factor']:.2f}")
        print(f"平均风险收益比: {results['avg_risk_reward']:.2f}")
        
        # 显示最近的信号
        if results['signals']:
            print("\n最近的信号:")
            for signal in results['signals'][-3:]:  # 显示最后3个信号
                print(f"\n日期: {signal['date']}")
                print(f"价格: {signal['price']:.2f}")
                print(f"成交量比例: {signal['volume_ratio']:.2f}")
                print(f"上影线比例: {signal['shadow_ratio']:.2f}")
                print(f"信号强度: {signal['signal_strength']:.2f}")
                print(f"趋势: {signal['trend']:.2%}")
                print(f"波动率: {signal['volatility']:.2%}")
                if 'reversal_occurred' in signal:
                    print(f"是否发生反转: {'是' if signal['reversal_occurred'] else '否'}")
                    print(f"最大收益: {signal['max_gain']:.2%}")
                    print(f"最大亏损: {signal['max_loss']:.2%}")
                    print(f"风险收益比: {signal['risk_reward_ratio']:.2f}")
    else:
        print("回测失败")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='加密货币形态分析')
    parser.add_argument('--max-listings', type=int, default=0, help='最大获取新上市币种数量，0表示获取全部')
    args = parser.parse_args()
    test_real_data(max_listings=args.max_listings)