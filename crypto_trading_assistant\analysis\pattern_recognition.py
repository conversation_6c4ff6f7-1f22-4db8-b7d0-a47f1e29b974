"""
技术形态识别模块
实现头肩顶/底、三角形整理、旗形等技术形态的识别算法
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
from scipy.signal import find_peaks
from scipy.stats import linregress
from loguru import logger
import talib
from dataclasses import dataclass
from enum import Enum


class PatternType(Enum):
    """形态类型枚举"""
    HEAD_AND_SHOULDERS = "head_and_shoulders"
    INVERSE_HEAD_AND_SHOULDERS = "inverse_head_and_shoulders"
    ASCENDING_TRIANGLE = "ascending_triangle"
    DESCENDING_TRIANGLE = "descending_triangle"
    SYMMETRICAL_TRIANGLE = "symmetrical_triangle"
    FLAG_BULLISH = "flag_bullish"
    FLAG_BEARISH = "flag_bearish"
    PENNANT_BULLISH = "pennant_bullish"
    PENNANT_BEARISH = "pennant_bearish"
    DOUBLE_TOP = "double_top"
    DOUBLE_BOTTOM = "double_bottom"


@dataclass
class PatternResult:
    """形态识别结果"""
    pattern_type: PatternType
    confidence: float  # 置信度 0-1
    start_index: int
    end_index: int
    key_points: List[Tuple[int, float]]  # 关键点位置和价格
    support_level: Optional[float] = None
    resistance_level: Optional[float] = None
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None
    volume_confirmation: bool = False
    breakout_direction: Optional[str] = None  # 'up' or 'down'


class PatternRecognizer:
    """技术形态识别器"""
    
    def __init__(self, config: Dict):
        """
        初始化形态识别器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.pattern_config = config.get('pattern_recognition', {})
        
    def analyze_patterns(self, df: pd.DataFrame, symbol: str) -> List[PatternResult]:
        """
        分析所有形态
        
        Args:
            df: OHLCV数据
            symbol: 交易对符号
            
        Returns:
            识别到的形态列表
        """
        patterns = []
        
        try:
            # 头肩顶/底形态
            if self.pattern_config.get('head_and_shoulders', {}).get('enabled', True):
                hs_patterns = self._detect_head_and_shoulders(df)
                patterns.extend(hs_patterns)
            
            # 三角形形态
            if self.pattern_config.get('triangle', {}).get('enabled', True):
                triangle_patterns = self._detect_triangles(df)
                patterns.extend(triangle_patterns)
            
            # 旗形和三角旗形态
            if self.pattern_config.get('flag_pennant', {}).get('enabled', True):
                flag_patterns = self._detect_flags_and_pennants(df)
                patterns.extend(flag_patterns)
            
            # 双顶/双底形态
            double_patterns = self._detect_double_tops_bottoms(df)
            patterns.extend(double_patterns)
            
            logger.info(f"{symbol} 识别到 {len(patterns)} 个形态")
            
        except Exception as e:
            logger.error(f"分析 {symbol} 形态时出错: {e}")
        
        return patterns
    
    def _detect_head_and_shoulders(self, df: pd.DataFrame) -> List[PatternResult]:
        """检测头肩顶/底形态"""
        patterns = []
        config = self.pattern_config.get('head_and_shoulders', {})
        min_periods = config.get('min_periods', 20)
        tolerance = config.get('tolerance', 0.02)
        
        try:
            highs = np.array(df['high'].values, dtype=float)
            lows = np.array(df['low'].values, dtype=float)
            volumes = np.array(df['volume'].values, dtype=float)
            
            # 寻找峰值和谷值
            peaks, _ = find_peaks(highs, distance=min_periods//4)
            troughs, _ = find_peaks(-lows, distance=min_periods//4)
            
            # 头肩顶检测
            for i in range(len(peaks) - 2):
                if i + 2 < len(peaks):
                    left_shoulder = peaks[i]
                    head = peaks[i + 1]
                    right_shoulder = peaks[i + 2]
                    
                    # 检查头肩顶条件
                    if (highs[head] > highs[left_shoulder] and 
                        highs[head] > highs[right_shoulder] and
                        abs(highs[left_shoulder] - highs[right_shoulder]) / highs[head] < tolerance):
                        
                        # 寻找颈线
                        neckline_points = self._find_neckline_points(df, left_shoulder, right_shoulder, troughs)
                        
                        if neckline_points:
                            confidence = self._calculate_hs_confidence(df, left_shoulder, head, right_shoulder, neckline_points)
                            
                            pattern = PatternResult(
                                pattern_type=PatternType.HEAD_AND_SHOULDERS,
                                confidence=confidence,
                                start_index=left_shoulder,
                                end_index=right_shoulder,
                                key_points=[(left_shoulder, float(highs[left_shoulder])),
                                           (head, float(highs[head])),
                                           (right_shoulder, float(highs[right_shoulder]))],
                                resistance_level=float(highs[head]),
                                support_level=min(neckline_points, key=lambda x: x[1])[1],
                                volume_confirmation=self._check_volume_confirmation(volumes, left_shoulder, right_shoulder),
                                breakout_direction='down'
                            )

                            # 计算目标价位
                            if pattern.support_level is not None:
                                head_to_neckline = float(highs[head]) - pattern.support_level
                                pattern.target_price = pattern.support_level - head_to_neckline
                            pattern.stop_loss = float(highs[head]) * 1.02
                            
                            patterns.append(pattern)
            
            # 头肩底检测（类似逻辑，但使用低点）
            for i in range(len(troughs) - 2):
                if i + 2 < len(troughs):
                    left_shoulder = troughs[i]
                    head = troughs[i + 1]
                    right_shoulder = troughs[i + 2]
                    
                    if (lows[head] < lows[left_shoulder] and 
                        lows[head] < lows[right_shoulder] and
                        abs(lows[left_shoulder] - lows[right_shoulder]) / lows[head] < tolerance):
                        
                        # 寻找颈线
                        neckline_points = self._find_neckline_points(df, left_shoulder, right_shoulder, peaks, use_highs=True)
                        
                        if neckline_points:
                            confidence = self._calculate_hs_confidence(df, left_shoulder, head, right_shoulder, neckline_points, inverse=True)
                            
                            pattern = PatternResult(
                                pattern_type=PatternType.INVERSE_HEAD_AND_SHOULDERS,
                                confidence=confidence,
                                start_index=left_shoulder,
                                end_index=right_shoulder,
                                key_points=[(left_shoulder, lows[left_shoulder]), 
                                           (head, lows[head]), 
                                           (right_shoulder, lows[right_shoulder])],
                                support_level=lows[head],
                                resistance_level=max(neckline_points, key=lambda x: x[1])[1],
                                volume_confirmation=self._check_volume_confirmation(volumes, left_shoulder, right_shoulder),
                                breakout_direction='up'
                            )
                            
                            # 计算目标价位
                            neckline_to_head = pattern.resistance_level - lows[head]
                            pattern.target_price = pattern.resistance_level + neckline_to_head
                            pattern.stop_loss = lows[head] * 0.98
                            
                            patterns.append(pattern)
                            
        except Exception as e:
            logger.error(f"检测头肩形态时出错: {e}")
        
        return patterns
    
    def _detect_triangles(self, df: pd.DataFrame) -> List[PatternResult]:
        """检测三角形形态"""
        patterns = []
        config = self.pattern_config.get('triangle', {})
        min_periods = config.get('min_periods', 15)
        convergence_threshold = config.get('convergence_threshold', 0.01)
        
        try:
            highs = df['high'].values
            lows = df['low'].values
            
            # 寻找趋势线
            for start_idx in range(len(df) - min_periods):
                end_idx = start_idx + min_periods
                
                if end_idx >= len(df):
                    break
                
                # 获取区间数据
                period_highs = highs[start_idx:end_idx]
                period_lows = lows[start_idx:end_idx]
                
                # 计算上升趋势线和下降趋势线
                upper_trendline = self._calculate_trendline(period_highs, 'resistance')
                lower_trendline = self._calculate_trendline(period_lows, 'support')
                
                if upper_trendline and lower_trendline:
                    # 检查收敛性
                    convergence = abs(upper_trendline['slope'] - lower_trendline['slope'])
                    
                    if convergence < convergence_threshold:
                        # 判断三角形类型
                        pattern_type = self._classify_triangle(upper_trendline, lower_trendline)
                        
                        if pattern_type:
                            confidence = self._calculate_triangle_confidence(
                                df.iloc[start_idx:end_idx], upper_trendline, lower_trendline
                            )
                            
                            # 计算突破点
                            breakout_price = self._calculate_triangle_breakout_price(
                                upper_trendline, lower_trendline, end_idx
                            )
                            
                            pattern = PatternResult(
                                pattern_type=pattern_type,
                                confidence=confidence,
                                start_index=start_idx,
                                end_index=end_idx,
                                key_points=[(start_idx, period_highs[0]), (end_idx-1, period_highs[-1])],
                                resistance_level=upper_trendline['value_at_end'],
                                support_level=lower_trendline['value_at_end'],
                                target_price=breakout_price,
                                breakout_direction=self._get_triangle_breakout_direction(pattern_type)
                            )
                            
                            patterns.append(pattern)
                            
        except Exception as e:
            logger.error(f"检测三角形形态时出错: {e}")
        
        return patterns
    
    def _detect_flags_and_pennants(self, df: pd.DataFrame) -> List[PatternResult]:
        """检测旗形和三角旗形态"""
        patterns = []
        config = self.pattern_config.get('flag_pennant', {})
        min_periods = config.get('min_periods', 10)
        max_periods = config.get('max_periods', 30)
        
        try:
            # 寻找强势趋势后的整理形态
            for i in range(min_periods, len(df) - max_periods):
                # 检查前期趋势
                trend_strength = self._calculate_trend_strength(df.iloc[i-min_periods:i])
                
                if abs(trend_strength) > 0.1:  # 强势趋势
                    # 检查整理区间
                    consolidation_period = df.iloc[i:i+max_periods]
                    
                    if self._is_flag_pattern(consolidation_period, trend_strength):
                        pattern_type = PatternType.FLAG_BULLISH if trend_strength > 0 else PatternType.FLAG_BEARISH
                        
                        confidence = self._calculate_flag_confidence(consolidation_period, trend_strength)
                        
                        pattern = PatternResult(
                            pattern_type=pattern_type,
                            confidence=confidence,
                            start_index=i,
                            end_index=i+max_periods,
                            key_points=[(i, consolidation_period['high'].iloc[0])],
                            breakout_direction='up' if trend_strength > 0 else 'down'
                        )
                        
                        patterns.append(pattern)
                        
        except Exception as e:
            logger.error(f"检测旗形形态时出错: {e}")
        
        return patterns

    # 辅助方法
    def _find_neckline_points(self, df: pd.DataFrame, left_idx: int, right_idx: int,
                             reference_points: np.ndarray, use_highs: bool = False) -> List[Tuple[int, float]]:
        """寻找颈线点位"""
        try:
            price_col = 'high' if use_highs else 'low'
            points_in_range = [p for p in reference_points if left_idx < p < right_idx]

            if len(points_in_range) >= 2:
                return [(p, df[price_col].iloc[p]) for p in points_in_range[:2]]
            return []
        except:
            return []

    def _calculate_hs_confidence(self, df: pd.DataFrame, left: int, head: int, right: int,
                                neckline_points: List, inverse: bool = False) -> float:
        """计算头肩形态置信度"""
        try:
            confidence = 0.5

            # 对称性检查
            left_to_head = head - left
            head_to_right = right - head
            symmetry = 1 - abs(left_to_head - head_to_right) / max(left_to_head, head_to_right)
            confidence += symmetry * 0.3

            # 成交量确认
            if self._check_volume_confirmation(df['volume'].values, left, right):
                confidence += 0.2

            return min(confidence, 1.0)
        except:
            return 0.5

    def _check_volume_confirmation(self, volumes: np.ndarray, start_idx: int, end_idx: int) -> bool:
        """检查成交量确认"""
        try:
            pattern_volume = np.mean(volumes[start_idx:end_idx])
            before_volume = np.mean(volumes[max(0, start_idx-20):start_idx])
            return pattern_volume > before_volume * 1.2
        except:
            return False

    def _calculate_trendline(self, prices: np.ndarray, line_type: str) -> Optional[Dict]:
        """计算趋势线"""
        try:
            x = np.arange(len(prices))

            if line_type == 'resistance':
                # 寻找高点
                peaks, _ = find_peaks(prices, distance=3)
                if len(peaks) < 2:
                    return None
                slope, intercept, r_value, _, _ = linregress(peaks, prices[peaks])
            else:  # support
                # 寻找低点
                troughs, _ = find_peaks(-prices, distance=3)
                if len(troughs) < 2:
                    return None
                slope, intercept, r_value, _, _ = linregress(troughs, prices[troughs])

            return {
                'slope': slope,
                'intercept': intercept,
                'r_value': r_value,
                'value_at_end': slope * (len(prices) - 1) + intercept
            }
        except:
            return None

    def _classify_triangle(self, upper_line: Dict, lower_line: Dict) -> Optional[PatternType]:
        """分类三角形类型"""
        try:
            upper_slope = upper_line['slope']
            lower_slope = lower_line['slope']

            if abs(upper_slope) < 0.001 and lower_slope > 0.001:
                return PatternType.ASCENDING_TRIANGLE
            elif upper_slope < -0.001 and abs(lower_slope) < 0.001:
                return PatternType.DESCENDING_TRIANGLE
            elif upper_slope < 0 and lower_slope > 0:
                return PatternType.SYMMETRICAL_TRIANGLE

            return None
        except:
            return None

    def _calculate_triangle_confidence(self, df: pd.DataFrame, upper_line: Dict, lower_line: Dict) -> float:
        """计算三角形置信度"""
        try:
            confidence = 0.5

            # R值检查
            avg_r_value = (abs(upper_line['r_value']) + abs(lower_line['r_value'])) / 2
            confidence += avg_r_value * 0.3

            # 成交量递减检查
            volumes = df['volume'].values
            if len(volumes) > 5:
                early_vol = np.mean(volumes[:len(volumes)//2])
                late_vol = np.mean(volumes[len(volumes)//2:])
                if late_vol < early_vol:
                    confidence += 0.2

            return min(confidence, 1.0)
        except:
            return 0.5

    def _calculate_triangle_breakout_price(self, upper_line: Dict, lower_line: Dict, end_idx: int) -> float:
        """计算三角形突破价位"""
        try:
            upper_price = upper_line['slope'] * end_idx + upper_line['intercept']
            lower_price = lower_line['slope'] * end_idx + lower_line['intercept']
            return (upper_price + lower_price) / 2
        except:
            return 0.0

    def _get_triangle_breakout_direction(self, pattern_type: PatternType) -> str:
        """获取三角形突破方向"""
        if pattern_type == PatternType.ASCENDING_TRIANGLE:
            return 'up'
        elif pattern_type == PatternType.DESCENDING_TRIANGLE:
            return 'down'
        else:
            return 'either'

    def _calculate_trend_strength(self, df: pd.DataFrame) -> float:
        """计算趋势强度"""
        try:
            close_prices = df['close'].values
            if len(close_prices) < 2:
                return 0.0

            return (close_prices[-1] - close_prices[0]) / close_prices[0]
        except:
            return 0.0

    def _is_flag_pattern(self, df: pd.DataFrame, trend_strength: float) -> bool:
        """判断是否为旗形形态"""
        try:
            # 检查价格区间是否相对稳定
            price_range = (df['high'].max() - df['low'].min()) / df['close'].mean()

            # 旗形应该是相对窄幅整理
            return price_range < 0.1
        except:
            return False

    def _calculate_flag_confidence(self, df: pd.DataFrame, trend_strength: float) -> float:
        """计算旗形置信度"""
        try:
            confidence = 0.6

            # 趋势强度越大，置信度越高
            confidence += min(abs(trend_strength), 0.3)

            # 成交量萎缩确认
            volumes = df['volume'].values
            if len(volumes) > 3:
                if volumes[-1] < volumes[0] * 0.7:
                    confidence += 0.1

            return min(confidence, 1.0)
        except:
            return 0.6

    def _calculate_double_pattern_confidence(self, price1: float, price2: float, middle_price: float) -> float:
        """计算双顶/双底置信度"""
        try:
            # 两个峰值/谷值的相似度
            similarity = 1 - abs(price1 - price2) / max(price1, price2)

            # 中间回调/反弹幅度
            if price1 > middle_price:  # 双顶
                retracement = (price1 - middle_price) / price1
            else:  # 双底
                retracement = (middle_price - price1) / middle_price

            confidence = similarity * 0.7 + min(retracement, 0.3)
            return min(confidence, 1.0)
        except:
            return 0.5
    
    def _detect_double_tops_bottoms(self, df: pd.DataFrame) -> List[PatternResult]:
        """检测双顶/双底形态"""
        patterns = []
        
        try:
            highs = df['high'].values
            lows = df['low'].values
            
            # 寻找峰值
            peaks, _ = find_peaks(highs, distance=10)
            troughs, _ = find_peaks(-lows, distance=10)
            
            # 双顶检测
            for i in range(len(peaks) - 1):
                peak1 = peaks[i]
                peak2 = peaks[i + 1]
                
                # 检查两个峰值是否相近
                if abs(highs[peak1] - highs[peak2]) / max(highs[peak1], highs[peak2]) < 0.03:
                    # 寻找中间的谷值
                    middle_troughs = [t for t in troughs if peak1 < t < peak2]
                    
                    if middle_troughs:
                        valley = min(middle_troughs, key=lambda x: lows[x])
                        
                        confidence = self._calculate_double_pattern_confidence(
                            highs[peak1], highs[peak2], lows[valley]
                        )
                        
                        pattern = PatternResult(
                            pattern_type=PatternType.DOUBLE_TOP,
                            confidence=confidence,
                            start_index=peak1,
                            end_index=peak2,
                            key_points=[(peak1, highs[peak1]), (peak2, highs[peak2])],
                            resistance_level=max(highs[peak1], highs[peak2]),
                            support_level=lows[valley],
                            breakout_direction='down'
                        )
                        
                        # 计算目标价位
                        height = pattern.resistance_level - pattern.support_level
                        pattern.target_price = pattern.support_level - height
                        
                        patterns.append(pattern)
            
            # 双底检测（类似逻辑）
            for i in range(len(troughs) - 1):
                trough1 = troughs[i]
                trough2 = troughs[i + 1]
                
                if abs(lows[trough1] - lows[trough2]) / max(lows[trough1], lows[trough2]) < 0.03:
                    middle_peaks = [p for p in peaks if trough1 < p < trough2]
                    
                    if middle_peaks:
                        peak = max(middle_peaks, key=lambda x: highs[x])
                        
                        confidence = self._calculate_double_pattern_confidence(
                            lows[trough1], lows[trough2], highs[peak]
                        )
                        
                        pattern = PatternResult(
                            pattern_type=PatternType.DOUBLE_BOTTOM,
                            confidence=confidence,
                            start_index=trough1,
                            end_index=trough2,
                            key_points=[(trough1, lows[trough1]), (trough2, lows[trough2])],
                            support_level=min(lows[trough1], lows[trough2]),
                            resistance_level=highs[peak],
                            breakout_direction='up'
                        )
                        
                        # 计算目标价位
                        height = pattern.resistance_level - pattern.support_level
                        pattern.target_price = pattern.resistance_level + height
                        
                        patterns.append(pattern)
                        
        except Exception as e:
            logger.error(f"检测双顶/双底形态时出错: {e}")
        
        return patterns
