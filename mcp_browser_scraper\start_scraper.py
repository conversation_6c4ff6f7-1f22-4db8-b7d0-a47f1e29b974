#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
加密货币数据抓取器启动脚本
自动检查依赖并启动主程序
"""

import sys
import os
import subprocess
from datetime import datetime

def log(message):
    """日志函数"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_dependencies():
    """检查依赖"""
    log("🔍 检查依赖包...")
    
    required_packages = {
        "requests": "requests",
        "pandas": "pandas", 
        "numpy": "numpy",
        "scipy": "scipy",
        "socks": "pysocks",
        "yaml": "pyyaml",
        "schedule": "schedule",
        "aiohttp": "aiohttp"
    }
    
    missing_packages = []
    
    for import_name, package_name in required_packages.items():
        try:
            __import__(import_name)
            log(f"✅ {package_name}")
        except ImportError:
            log(f"❌ {package_name} 缺失")
            missing_packages.append(package_name)
    
    return missing_packages

def install_missing_packages(packages):
    """安装缺失的包"""
    if not packages:
        return True
    
    log(f"📦 需要安装 {len(packages)} 个包: {', '.join(packages)}")
    
    try:
        # 尝试批量安装
        cmd = [sys.executable, "-m", "pip", "install"] + packages
        log(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, timeout=300)
        return result.returncode == 0
    except Exception as e:
        log(f"❌ 安装失败: {e}")
        return False

def start_main_program():
    """启动主程序"""
    log("🚀 启动加密货币数据抓取器...")
    
    try:
        # 导入并运行主程序
        from advanced_crypto_scraper import main
        main()
    except Exception as e:
        log(f"❌ 启动失败: {e}")
        import traceback
        log(f"错误详情: {traceback.format_exc()}")

def main():
    """主函数"""
    log("="*60)
    log("🚀 加密货币数据抓取器启动器")
    log("="*60)
    
    # 检查依赖
    missing = check_dependencies()
    
    if missing:
        log(f"⚠️ 发现 {len(missing)} 个缺失的依赖包")
        
        # 询问是否自动安装
        try:
            choice = input("\n是否自动安装缺失的依赖包？(y/n): ").strip().lower()
            if choice == 'y':
                if install_missing_packages(missing):
                    log("✅ 依赖包安装完成")
                else:
                    log("❌ 依赖包安装失败，请手动安装")
                    log("💡 手动安装命令:")
                    log(f"   pip install {' '.join(missing)}")
                    return
            else:
                log("⚠️ 跳过依赖安装，程序可能无法正常运行")
        except (EOFError, KeyboardInterrupt):
            log("⚠️ 用户取消操作")
            return
    else:
        log("✅ 所有依赖包已安装")
    
    # 启动主程序
    log("\n" + "="*60)
    start_main_program()

if __name__ == "__main__":
    main()
