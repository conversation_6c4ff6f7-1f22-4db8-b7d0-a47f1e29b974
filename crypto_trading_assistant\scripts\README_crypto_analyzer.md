# 加密货币新币全面分析工具

这是一个强大的加密货币新币分析工具，可以帮助您快速了解最近上市的数字货币的各种指标和投资潜力。本工具整合了多个数据源，提供全面的分析报告，包括市场数据、技术分析、交易所数据和可视化图表。

## 功能特点

- **自动识别新币**：自动识别并分析最近（默认30天内）上市的数字货币
- **多维度数据**：收集并分析市场数据、技术指标和交易所数据
- **技术分析**：计算包括RSI、MACD、EMA、布林带等多种技术指标
- **资金流向分析**：提供多空比例、资金费率等资金流向指标
- **中文支持**：完全支持中文显示，让分析结果更直观易懂
- **可视化图表**：自动生成多种分析图表，帮助您更好地理解数据
- **完整报告**：生成HTML和CSV格式的分析报告，方便查看和分享

## 安装步骤

1. 确保已安装Python 3.8或更高版本
2. 克隆或下载本项目代码
3. 安装所需依赖:

```bash
pip install -r scripts/requirements.txt
```

## 使用方法

### 基本用法

```bash
python scripts/crypto_analyzer.py
```

这将使用默认参数分析过去30天内上市的新币，并生成报告。

### 高级选项

```bash
python scripts/crypto_analyzer.py -d 7 -o my_reports -c usd -l zh -p
```

参数说明:
- `-d, --days`：分析过去多少天内上市的币种，默认30天
- `-o, --output`：指定输出文件夹路径，默认为"crypto_reports"
- `-c, --currency`：计价货币，默认为美元(usd)
- `-l, --lang`：语言，默认为中文(zh)
- `-p, --proxy`：是否使用代理（通过http://127.0.0.1:7890）

### 配置交易所API

如需获取更全面的交易所数据（如多空比例、资金费率等），可以配置交易所API密钥:

1. 打开`api_keys.json`文件
2. 填入您的交易所API密钥信息
3. 保存文件

示例:
```json
{
    "binance": {
        "api_key": "您的币安API密钥",
        "api_secret": "您的币安API密钥"
    },
    "okx": {
        "api_key": "您的OKX API密钥",
        "api_secret": "您的OKX API密钥",
        "passphrase": "您的OKX API密钥密码"
    }
}
```

## 分析报告

执行程序后，将在指定的输出文件夹中生成以下文件:

1. HTML报告：包含新币概览、数据可视化图表和详细数据
2. CSV报告：包含所有分析数据，可导入Excel或其他工具进行进一步分析
3. 可视化图表：自动生成的多种分析图表，保存在charts子文件夹中

## 关键指标说明

报告中包含以下关键指标:

### 市场数据
- **上市日期**：币种首次上市的日期
- **上市以来涨幅**：从上市至今的价格变化百分比
- **市值**：币种当前市值
- **24h交易量**：24小时内的交易量
- **换手率**：衡量交易活跃度的指标

### 资金流向
- **多空比**：多头持仓与空头持仓的比例，>1表示多头占优
- **资金费率**：期货合约的资金费率，反映市场情绪
- **大户持仓**：大户多空持仓比例

### 技术指标
- **RSI(14)**：相对强弱指标，超过70为超买，低于30为超卖
- **MACD**：移动平均线汇聚/发散指标
- **布林带**：价格波动的上下轨道，反映波动性

### 趋势分析
- **趋势**：当前价格趋势（上升、下降、中性）
- **强度**：趋势强度（强、中等、弱）
- **波动性**：价格波动程度（高、中等、低）
- **交易建议**：基于技术分析的交易建议

## 注意事项

- 本工具仅提供数据分析，不构成投资建议
- 加密货币市场风险较高，投资需谨慎
- 免费API可能有访问限制，建议合理控制使用频率
- 如需长期稳定使用，建议申请自己的API密钥

## 技术架构

本工具由以下模块组成:

- `crypto_analyzer.py`：主程序，整合各个模块
- `crypto_data_collector.py`：数据收集模块
- `crypto_technical_analyzer.py`：技术分析模块
- `crypto_exchange_data.py`：交易所数据收集模块
- `crypto_report_generator.py`：报告生成模块

## 贡献与反馈

如果您遇到任何问题或有改进建议，欢迎提出。 