#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
启动诊断测试脚本
用于检查 advanced_crypto_scraper.py 的启动问题
"""

import sys
import os
import traceback
from datetime import datetime

def log(message):
    """日志函数"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def test_imports():
    """测试导入"""
    log("🔍 开始测试导入...")
    
    try:
        import requests
        log("✅ requests 导入成功")
    except ImportError as e:
        log(f"❌ requests 导入失败: {e}")
        return False
    
    try:
        import sqlite3
        log("✅ sqlite3 导入成功")
    except ImportError as e:
        log(f"❌ sqlite3 导入失败: {e}")
        return False
    
    try:
        import pandas as pd
        log("✅ pandas 导入成功")
    except ImportError as e:
        log(f"❌ pandas 导入失败: {e}")
        return False
    
    try:
        import numpy as np
        log("✅ numpy 导入成功")
    except ImportError as e:
        log(f"❌ numpy 导入失败: {e}")
        return False
    
    try:
        from scipy.signal import find_peaks
        from scipy.stats import linregress
        log("✅ scipy 导入成功")
    except ImportError as e:
        log(f"❌ scipy 导入失败: {e}")
        return False
    
    try:
        import yaml
        log("✅ yaml 导入成功")
    except ImportError as e:
        log(f"❌ yaml 导入失败: {e}")
        return False
    
    try:
        import socks
        log("✅ socks 导入成功")
    except ImportError as e:
        log(f"❌ socks 导入失败: {e}")
        return False
    
    try:
        import schedule
        log("✅ schedule 导入成功")
    except ImportError as e:
        log(f"❌ schedule 导入失败: {e}")
        return False
    
    try:
        from real_kline_data_provider import RealKlineDataProvider
        log("✅ real_kline_data_provider 导入成功")
    except ImportError as e:
        log(f"❌ real_kline_data_provider 导入失败: {e}")
        log("💡 这是可选模块，不影响主要功能")
    
    return True

def test_basic_initialization():
    """测试基本初始化"""
    log("🔍 开始测试基本初始化...")
    
    try:
        # 尝试导入主类
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from advanced_crypto_scraper import AdvancedCryptoScraper
        log("✅ AdvancedCryptoScraper 类导入成功")
        
        # 尝试创建实例（不使用代理，避免阻塞）
        log("🔄 尝试创建实例...")
        scraper = AdvancedCryptoScraper(use_proxy=False)
        log("✅ AdvancedCryptoScraper 实例创建成功")
        
        return True
        
    except Exception as e:
        log(f"❌ 基本初始化失败: {e}")
        log(f"📍 错误详情: {traceback.format_exc()}")
        return False

def test_configuration_files():
    """测试配置文件"""
    log("🔍 开始测试配置文件...")
    
    config_files = [
        'ssr_config.json',
        'wechat_bots_config.json'
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            log(f"✅ 配置文件存在: {config_file}")
            try:
                import json
                with open(config_file, 'r', encoding='utf-8') as f:
                    json.load(f)
                log(f"✅ 配置文件格式正确: {config_file}")
            except Exception as e:
                log(f"❌ 配置文件格式错误: {config_file} - {e}")
        else:
            log(f"⚠️ 配置文件不存在: {config_file} (将自动创建)")

def main():
    """主函数"""
    log("🚀 启动诊断测试开始...")
    log("="*60)
    
    # 测试导入
    if not test_imports():
        log("❌ 导入测试失败，请检查依赖包安装")
        return
    
    log("="*60)
    
    # 测试配置文件
    test_configuration_files()
    
    log("="*60)
    
    # 测试基本初始化
    if not test_basic_initialization():
        log("❌ 基本初始化测试失败")
        return
    
    log("="*60)
    log("✅ 所有诊断测试通过！")
    log("💡 advanced_crypto_scraper.py 应该可以正常启动")

if __name__ == "__main__":
    main()
