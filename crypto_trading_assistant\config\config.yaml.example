# 数字货币交易辅助系统配置文件模板
# 请复制此文件为 config.yaml 并填入实际配置信息

# Qlib配置
qlib_config:
  provider_uri: "~/.qlib/qlib_data/crypto_data"
  region: "crypto"
  logging_level: "INFO"

# 数据源配置
data_sources:
  binance:
    api_key: "YOUR_BINANCE_API_KEY"           # 请填入您的Binance API Key
    api_secret: "YOUR_BINANCE_API_SECRET"     # 请填入您的Binance API Secret
    base_url: "https://api.binance.com"
    testnet: true                             # 建议先使用测试网，正式使用时改为false
  
  coinbase:
    api_key: "YOUR_COINBASE_API_KEY"          # 可选：Coinbase Pro API Key
    api_secret: "YOUR_COINBASE_API_SECRET"    # 可选：Coinbase Pro API Secret
    passphrase: "YOUR_COINBASE_PASSPHRASE"    # 可选：Coinbase Pro Passphrase
    base_url: "https://api.pro.coinbase.com"

# 交易对配置
trading_pairs:
  major_pairs:
    - "BTC/USDT"
    - "ETH/USDT"
    - "BNB/USDT"
    - "ADA/USDT"
    - "DOT/USDT"
    - "LINK/USDT"
    - "LTC/USDT"
    - "BCH/USDT"
    - "XLM/USDT"
    - "EOS/USDT"
  
  defi_pairs:
    - "UNI/USDT"
    - "AAVE/USDT"
    - "SUSHI/USDT"
    - "COMP/USDT"
    - "MKR/USDT"

# 时间周期配置
timeframes:
  primary: "4h"      # 主要分析周期
  secondary: "1d"    # 趋势确认周期
  entry: "1h"        # 入场精确周期
  available: ["1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w"]

# 技术形态识别配置
pattern_recognition:
  head_and_shoulders:
    enabled: true
    min_periods: 20
    tolerance: 0.02
    volume_confirmation: true
  
  triangle:
    enabled: true
    min_periods: 15
    convergence_threshold: 0.01
    breakout_volume_ratio: 1.5
  
  flag_pennant:
    enabled: true
    min_periods: 10
    max_periods: 30
    slope_tolerance: 0.005
    volume_decline_ratio: 0.7

# 买点信号配置
entry_signals:
  pattern_breakout:
    enabled: true
    volume_confirmation: true
    min_volume_ratio: 1.2
  
  indicator_confluence:
    enabled: true
    required_indicators: 2
    indicators:
      - "rsi_oversold"
      - "macd_bullish_cross"
      - "bollinger_squeeze"
      - "support_bounce"
  
  multi_timeframe:
    enabled: true
    primary_signal_required: true
    secondary_confirmation: true

# 风险控制配置
risk_management:
  stop_loss:
    default_percentage: 0.05  # 5%
    pattern_based: true
    atr_multiplier: 2.0
  
  position_sizing:
    max_risk_per_trade: 0.02  # 2%
    kelly_criterion: false
    fixed_amount: 1000  # USDT
    initial_capital: 100000  # 初始资金
  
  max_positions: 5
  correlation_limit: 0.7

# 消息推送配置
notifications:
  enterprise_wechat:
    enabled: true                                     # 设置为true启用企业微信推送
    webhook_url: "YOUR_WECHAT_WEBHOOK_URL"           # 请填入企业微信机器人Webhook URL
    # 示例: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY"
    secret: ""                                       # 可选：企业微信机器人密钥（一般不需要）
    mentioned_list: ["@all"]                         # @用户列表
    mentioned_mobile_list: []                        # @手机号列表

  dingtalk:
    enabled: false                                   # 钉钉推送暂时取消，设置为false
    webhook_url: ""                                  # 钉钉机器人Webhook URL（暂不使用）
    secret: ""                                       # 钉钉机器人加签密钥（暂不使用）
    at_mobiles: []                                   # @手机号列表
    at_all: false                                    # 是否@所有人
  
  message_template:
    include_chart: true                              # 是否包含图表
    chart_timeframe: "4h"                           # 图表时间周期
    chart_periods: 100                              # 图表显示周期数

# 系统运行配置
system:
  scan_interval: 300        # 5分钟扫描一次
  data_update_interval: 60  # 1分钟更新数据
  max_concurrent_requests: 10
  retry_attempts: 3
  retry_delay: 5

# 日志配置
logging:
  level: "INFO"
  file: "logs/crypto_trading.log"
  max_size: "10MB"
  backup_count: 5
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 配置说明：
# 1. 请将所有 "YOUR_XXX" 替换为实际的配置值
# 2. API密钥获取方法请参考README.md
# 3. 建议先在测试环境运行，确认无误后再切换到正式环境
# 4. 消息推送功能需要先创建对应的机器人并获取Webhook URL
