"""
数字货币选币系统主程序
基于Qlib框架的技术分析选币系统
"""

import sys
import os
import argparse
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import get_config, get_symbols_by_category
from crypto_stock_picker import CryptoStockPicker
from crypto_selection_example import CryptoSelectionExample
import requests
import json


class CryptoSelectionSystem:
    """数字货币选币系统主类"""
    
    def __init__(self, strategy_name: str = 'default'):
        """
        初始化选币系统
        
        Args:
            strategy_name: 策略名称
        """
        self.strategy_name = strategy_name
        self.config = get_config(strategy_name)
        self.setup_logging()
        
        # 初始化选币器
        self.picker = CryptoStockPicker(self.config)
        
        # 获取交易对列表
        self.symbols = self.config['symbols']
        
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info(f"初始化选币系统，策略: {strategy_name}")
    
    def setup_logging(self):
        """设置日志"""
        log_config = self.config['logging_config']
        
        # 创建日志目录
        log_dir = os.path.dirname(log_config['file_path'])
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 配置日志
        logging.basicConfig(
            level=getattr(logging, log_config['level']),
            format=log_config['format'],
            handlers=[
                logging.FileHandler(log_config['file_path'], encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
    
    def run_daily_selection(self, timeframe: str = '1d', 
                           category: str = 'all') -> List[Dict]:
        """
        运行日常选币
        
        Args:
            timeframe: 时间周期
            category: 交易对类别
            
        Returns:
            选币结果列表
        """
        try:
            self.logger.info(f"开始运行选币，时间周期: {timeframe}, 类别: {category}")
            
            # 获取指定类别的交易对
            symbols = get_symbols_by_category(category)
            
            # 执行选币
            results = self.picker.select_stocks(
                symbols=symbols,
                timeframe=timeframe
            )
            
            self.logger.info(f"选币完成，找到 {len(results)} 个符合条件的标的")
            
            # 保存结果
            self.save_results(results, timeframe, category)
            
            # 发送通知
            if results:
                self.send_notification(results, timeframe)
            
            return results
            
        except Exception as e:
            self.logger.error(f"选币过程出错: {e}")
            return []
    
    def run_multi_timeframe_analysis(self, symbols: List[str] = None) -> Dict:
        """
        运行多时间周期分析
        
        Args:
            symbols: 要分析的交易对列表，如果为None则使用日线选币结果
            
        Returns:
            多时间周期分析结果
        """
        try:
            if symbols is None:
                # 先运行日线选币
                daily_results = self.run_daily_selection('1d')
                symbols = [result['symbol'] for result in daily_results[:5]]
            
            if not symbols:
                self.logger.warning("没有找到需要分析的交易对")
                return {}
            
            self.logger.info(f"开始多时间周期分析，标的数量: {len(symbols)}")
            
            multi_tf_results = {}
            
            for symbol in symbols:
                try:
                    result = self.picker.multi_timeframe_analysis(symbol)
                    if result:
                        multi_tf_results[symbol] = result
                        self.logger.info(f"{symbol} 多周期分析完成，综合得分: {result.get('multi_tf_score', 0):.2f}")
                    
                except Exception as e:
                    self.logger.warning(f"分析 {symbol} 时出错: {e}")
                    continue
            
            # 保存多时间周期结果
            self.save_multi_timeframe_results(multi_tf_results)
            
            return multi_tf_results
            
        except Exception as e:
            self.logger.error(f"多时间周期分析出错: {e}")
            return {}
    
    def save_results(self, results: List[Dict], timeframe: str, category: str):
        """保存选币结果"""
        try:
            if not results:
                return
            
            # 创建结果目录
            results_dir = 'results'
            if not os.path.exists(results_dir):
                os.makedirs(results_dir)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{results_dir}/selection_{self.strategy_name}_{timeframe}_{category}_{timestamp}.csv"
            
            # 转换为DataFrame并保存
            df = pd.DataFrame(results)
            df.to_csv(filename, index=False, encoding='utf-8')
            
            self.logger.info(f"选币结果已保存到: {filename}")
            
        except Exception as e:
            self.logger.error(f"保存结果失败: {e}")
    
    def save_multi_timeframe_results(self, results: Dict):
        """保存多时间周期分析结果"""
        try:
            if not results:
                return
            
            results_dir = 'results'
            if not os.path.exists(results_dir):
                os.makedirs(results_dir)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{results_dir}/multi_timeframe_{self.strategy_name}_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info(f"多时间周期结果已保存到: {filename}")
            
        except Exception as e:
            self.logger.error(f"保存多时间周期结果失败: {e}")
    
    def send_notification(self, results: List[Dict], timeframe: str):
        """发送选币结果通知"""
        try:
            notification_config = self.config['notification_config']
            
            if not notification_config.get('enable_wechat', False):
                return
            
            # 构建消息内容
            message = self.build_notification_message(results, timeframe)
            
            # 发送企业微信通知
            webhook_url = notification_config.get('wechat_webhook')
            if webhook_url:
                self.send_wechat_notification(webhook_url, message)
            
        except Exception as e:
            self.logger.error(f"发送通知失败: {e}")
    
    def build_notification_message(self, results: List[Dict], timeframe: str) -> str:
        """构建通知消息"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            message = f"🚀 数字货币选币提醒 ({timeframe})\n"
            message += f"⏰ 时间: {timestamp}\n"
            message += f"📊 策略: {self.strategy_name}\n"
            message += f"🎯 发现 {len(results)} 个优质标的\n\n"
            
            # 添加前5个结果
            for i, result in enumerate(results[:5], 1):
                symbol = result['symbol']
                score = result['score']
                price = result['current_price']
                rsi = result['rsi']
                signals = ', '.join(result['signals'][:2])
                
                message += f"{i}. {symbol}\n"
                message += f"   💯 得分: {score:.1f}\n"
                message += f"   💰 价格: {price:.4f}\n"
                message += f"   📈 RSI: {rsi:.1f}\n"
                message += f"   🔔 信号: {signals}\n\n"
            
            message += "⚠️ 风险提示: 仅供参考，请谨慎投资"
            
            return message
            
        except Exception as e:
            self.logger.error(f"构建通知消息失败: {e}")
            return "选币系统运行完成"
    
    def send_wechat_notification(self, webhook_url: str, message: str):
        """发送企业微信通知"""
        try:
            data = {
                "msgtype": "text",
                "text": {
                    "content": message
                }
            }
            
            response = requests.post(
                webhook_url,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    self.logger.info("企业微信通知发送成功")
                else:
                    self.logger.error(f"企业微信通知发送失败: {result}")
            else:
                self.logger.error(f"企业微信通知请求失败: {response.status_code}")
                
        except Exception as e:
            self.logger.error(f"发送企业微信通知失败: {e}")
    
    def run_backtest(self, start_date: str = None, end_date: str = None):
        """运行回测"""
        try:
            self.logger.info("开始运行回测...")
            
            backtest_config = self.config['backtest_config']
            
            if start_date is None:
                start_date = backtest_config['start_date']
            if end_date is None:
                end_date = backtest_config['end_date']
            
            # 这里可以实现具体的回测逻辑
            # 由于篇幅限制，这里只是一个框架
            
            self.logger.info(f"回测期间: {start_date} 到 {end_date}")
            self.logger.info("回测功能待实现...")
            
        except Exception as e:
            self.logger.error(f"回测失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数字货币选币系统')
    parser.add_argument('--strategy', default='default', help='策略名称')
    parser.add_argument('--timeframe', default='1d', help='时间周期')
    parser.add_argument('--category', default='all', help='交易对类别')
    parser.add_argument('--mode', default='selection', 
                       choices=['selection', 'multi_tf', 'backtest', 'demo'],
                       help='运行模式')
    parser.add_argument('--symbols', nargs='+', help='指定分析的交易对')
    
    args = parser.parse_args()
    
    try:
        if args.mode == 'demo':
            # 运行演示
            example = CryptoSelectionExample()
            example.run_daily_selection()
            example.demonstrate_alpha_expressions()
            example.backtest_strategy_example()
        else:
            # 运行实际系统
            system = CryptoSelectionSystem(args.strategy)
            
            if args.mode == 'selection':
                results = system.run_daily_selection(args.timeframe, args.category)
                print(f"\n选币完成，找到 {len(results)} 个符合条件的标的")
                
            elif args.mode == 'multi_tf':
                results = system.run_multi_timeframe_analysis(args.symbols)
                print(f"\n多时间周期分析完成，分析了 {len(results)} 个标的")
                
            elif args.mode == 'backtest':
                system.run_backtest()
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")


if __name__ == "__main__":
    main()
