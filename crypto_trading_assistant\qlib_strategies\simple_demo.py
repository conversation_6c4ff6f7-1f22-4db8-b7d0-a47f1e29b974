"""
简化版数字货币选币系统演示
不依赖qlib，展示核心算法逻辑
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')


class SimpleCryptoAnalyzer:
    """简化版数字货币分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.symbols = [
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'XRPUSDT',
            'SOLUSDT', 'DOTUSDT', 'DOGEUSDT', 'AVAXUSDT', 'MATICUSDT'
        ]
        
        # 技术指标权重
        self.weights = {
            'pattern': 0.3,
            'indicator': 0.4,
            'trend': 0.2,
            'volume': 0.1
        }
    
    def generate_mock_data(self, symbol: str, days: int = 100) -> pd.DataFrame:
        """生成模拟数据"""
        # 基础价格设置
        base_prices = {
            'BTCUSDT': 45000, 'ETHUSDT': 3000, 'BNBUSDT': 400,
            'ADAUSDT': 1.2, 'XRPUSDT': 0.6, 'SOLUSDT': 100,
            'DOTUSDT': 25, 'DOGEUSDT': 0.08, 'AVAXUSDT': 35, 'MATICUSDT': 1.5
        }
        
        base_price = base_prices.get(symbol, 100)
        
        # 生成日期序列
        dates = pd.date_range(start=datetime.now() - timedelta(days=days), 
                             periods=days, freq='D')
        
        # 生成价格走势
        np.random.seed(hash(symbol) % 1000)  # 为每个symbol设置不同的随机种子
        returns = np.random.normal(0, 0.02, days)  # 2%的日波动率
        
        prices = [base_price]
        for ret in returns[1:]:
            new_price = prices[-1] * (1 + ret)
            prices.append(new_price)
        
        # 生成OHLCV数据
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            volatility = abs(np.random.normal(0, 0.01))
            
            open_price = close * (1 + np.random.normal(0, 0.005))
            high = max(open_price, close) * (1 + volatility)
            low = min(open_price, close) * (1 - volatility)
            volume = np.random.lognormal(10, 1) * 1000
            
            data.append({
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume
            })
        
        df = pd.DataFrame(data, index=dates)
        return df
    
    def calculate_rsi(self, close: pd.Series, window: int = 14) -> pd.Series:
        """计算RSI"""
        delta = close.diff()
        gain = delta.where(delta > 0, 0).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_macd(self, close: pd.Series) -> tuple:
        """计算MACD"""
        ema12 = close.ewm(span=12).mean()
        ema26 = close.ewm(span=26).mean()
        macd_line = ema12 - ema26
        signal_line = macd_line.ewm(span=9).mean()
        histogram = macd_line - signal_line
        return macd_line, signal_line, histogram
    
    def calculate_bollinger_bands(self, close: pd.Series, window: int = 20) -> tuple:
        """计算布林带"""
        sma = close.rolling(window=window).mean()
        std = close.rolling(window=window).std()
        upper = sma + (std * 2)
        lower = sma - (std * 2)
        return upper, sma, lower
    
    def detect_hammer_pattern(self, data: pd.DataFrame) -> pd.Series:
        """检测锤子线形态"""
        body = abs(data['close'] - data['open'])
        upper_shadow = data['high'] - data[['close', 'open']].max(axis=1)
        lower_shadow = data[['close', 'open']].min(axis=1) - data['low']
        
        # 锤子线条件
        hammer = (
            (lower_shadow >= 2 * body) &  # 下影线 >= 实体2倍
            (upper_shadow <= 0.1 * body) &  # 上影线很短
            (body >= 0.001 * data['close'])  # 实体不能太小
        )
        
        return hammer.astype(int)
    
    def detect_doji_pattern(self, data: pd.DataFrame) -> pd.Series:
        """检测十字星形态"""
        body = abs(data['close'] - data['open'])
        total_range = data['high'] - data['low']
        
        doji = (
            (body <= 0.001 * data['close']) &  # 实体很小
            (total_range >= 0.01 * data['close'])  # 有一定波动
        )
        
        return doji.astype(int)
    
    def analyze_symbol(self, symbol: str) -> dict:
        """分析单个交易对"""
        try:
            # 获取数据
            data = self.generate_mock_data(symbol)
            
            # 计算技术指标
            data['rsi'] = self.calculate_rsi(data['close'])
            data['macd'], data['macd_signal'], data['macd_hist'] = self.calculate_macd(data['close'])
            data['bb_upper'], data['bb_middle'], data['bb_lower'] = self.calculate_bollinger_bands(data['close'])
            data['sma_5'] = data['close'].rolling(5).mean()
            data['sma_20'] = data['close'].rolling(20).mean()
            data['volume_sma'] = data['volume'].rolling(20).mean()
            
            # 形态识别
            hammer_signals = self.detect_hammer_pattern(data)
            doji_signals = self.detect_doji_pattern(data)
            
            # 技术指标信号
            rsi_oversold = (data['rsi'].shift(1) < 30) & (data['rsi'] > 35)
            macd_golden_cross = (data['macd'] > data['macd_signal']) & (data['macd'].shift(1) <= data['macd_signal'].shift(1))
            bb_support = (data['low'].shift(1) <= data['bb_lower'].shift(1)) & (data['close'] > data['bb_lower'])
            volume_breakout = data['volume'] > (data['volume_sma'] * 1.5)
            
            # 趋势信号
            bullish_trend = (data['sma_5'] > data['sma_20']) & (data['close'] > data['sma_20'])
            
            # 计算综合得分
            latest_idx = -1
            
            pattern_score = (
                hammer_signals.iloc[latest_idx] * 2 +
                doji_signals.iloc[latest_idx] * 1
            )
            
            indicator_score = (
                rsi_oversold.iloc[latest_idx] * 2 +
                macd_golden_cross.iloc[latest_idx] * 2 +
                bb_support.iloc[latest_idx] * 1.5
            )
            
            trend_score = bullish_trend.iloc[latest_idx] * 2
            volume_score = volume_breakout.iloc[latest_idx] * 2
            
            total_score = (
                pattern_score * self.weights['pattern'] +
                indicator_score * self.weights['indicator'] +
                trend_score * self.weights['trend'] +
                volume_score * self.weights['volume']
            )
            
            # 收集信号
            signals = []
            if rsi_oversold.iloc[latest_idx]:
                signals.append("RSI超卖回升")
            if macd_golden_cross.iloc[latest_idx]:
                signals.append("MACD金叉")
            if bb_support.iloc[latest_idx]:
                signals.append("布林带支撑")
            if hammer_signals.iloc[latest_idx]:
                signals.append("锤子线形态")
            if doji_signals.iloc[latest_idx]:
                signals.append("十字星形态")
            if volume_breakout.iloc[latest_idx]:
                signals.append("成交量放大")
            if bullish_trend.iloc[latest_idx]:
                signals.append("多头趋势")
            
            return {
                'symbol': symbol,
                'score': float(total_score),
                'pattern_score': float(pattern_score),
                'indicator_score': float(indicator_score),
                'trend_score': float(trend_score),
                'volume_score': float(volume_score),
                'current_price': float(data['close'].iloc[latest_idx]),
                'rsi': float(data['rsi'].iloc[latest_idx]),
                'macd': float(data['macd'].iloc[latest_idx]),
                'volume_ratio': float(data['volume'].iloc[latest_idx] / data['volume_sma'].iloc[latest_idx]),
                'signals': signals
            }
            
        except Exception as e:
            print(f"分析 {symbol} 时出错: {e}")
            return None
    
    def run_selection(self, min_score: float = 2.0) -> list:
        """运行选币"""
        print("🚀 数字货币技术分析选币系统")
        print("=" * 60)
        print("基于Qlib框架的Alpha表达式选币策略演示")
        print("=" * 60)
        
        results = []
        
        print("\n正在分析交易对...")
        for symbol in self.symbols:
            result = self.analyze_symbol(symbol)
            if result and result['score'] >= min_score:
                results.append(result)
            print(f"  ✓ {symbol}: 得分 {result['score']:.2f}" if result else f"  ✗ {symbol}: 分析失败")
        
        # 按得分排序
        results.sort(key=lambda x: x['score'], reverse=True)
        
        print(f"\n📊 选币结果 (最低得分: {min_score})")
        print("-" * 80)
        
        if results:
            print(f"{'排名':<4} {'代码':<12} {'总分':<6} {'形态':<6} {'指标':<6} {'趋势':<6} {'成交量':<8} {'当前价格':<12} {'RSI':<6} {'信号'}")
            print("-" * 80)
            
            for i, result in enumerate(results, 1):
                signals_str = ', '.join(result['signals'][:2])
                if len(result['signals']) > 2:
                    signals_str += '...'
                
                print(f"{i:<4} {result['symbol']:<12} {result['score']:<6.1f} "
                      f"{result['pattern_score']:<6.1f} {result['indicator_score']:<6.1f} "
                      f"{result['trend_score']:<6.1f} {result['volume_score']:<8.1f} "
                      f"{result['current_price']:<12.4f} {result['rsi']:<6.1f} {signals_str}")
        else:
            print("未找到符合条件的标的")
        
        return results
    
    def demonstrate_alpha_expressions(self):
        """演示Alpha表达式概念"""
        print("\n" + "=" * 60)
        print("📈 Qlib Alpha表达式技术分析策略")
        print("=" * 60)
        
        expressions = {
            "锤子线形态": """
            # 锤子线识别逻辑
            (
                # 下影线长度 >= 实体长度的2倍
                (($low - Min($open, $close)) >= 2 * Abs($open - $close)) &
                # 上影线很短 (< 实体长度的0.1倍)  
                (($high - Max($open, $close)) <= 0.1 * Abs($open - $close)) &
                # 实体不能太小
                (Abs($open - $close) >= 0.001 * $close)
            )
            """,
            
            "RSI超卖回升": """
            # RSI从超卖区域回升
            (
                # 前期RSI < 30 (超卖)
                (Ref(RSI($close, 14), 1) < 30) &
                # 当前RSI > 35 (开始回升)
                (RSI($close, 14) > 35) &
                # RSI呈上升趋势
                (RSI($close, 14) > Ref(RSI($close, 14), 1))
            )
            """,
            
            "MACD金叉确认": """
            # MACD金叉信号
            (
                # MACD线上穿信号线
                (EMA($close, 12) - EMA($close, 26) > EMA(EMA($close, 12) - EMA($close, 26), 9)) &
                # 前一日MACD线在信号线下方
                (Ref(EMA($close, 12) - EMA($close, 26), 1) <= Ref(EMA(EMA($close, 12) - EMA($close, 26), 9), 1))
            )
            """,
            
            "强势突破选币": """
            # 强势突破综合条件
            (
                # 价格突破20日高点
                ($close > Ref(Max($high, 20), 1)) &
                # 成交量放大确认
                ($volume > 1.5 * Mean($volume, 20)) &
                # RSI不超买
                (RSI($close, 14) < 70) &
                # 均线支撑
                ($close > Mean($close, 20))
            )
            """
        }
        
        for name, expression in expressions.items():
            print(f"\n🔍 {name}:")
            print("-" * 40)
            print(expression.strip())
    
    def show_trading_suggestions(self, results: list):
        """显示交易建议"""
        if not results:
            return
        
        print("\n" + "=" * 60)
        print("💡 交易建议")
        print("=" * 60)
        
        # 按风险等级分类
        high_confidence = [r for r in results if r['score'] >= 4.0 and 30 <= r['rsi'] <= 70]
        medium_confidence = [r for r in results if 3.0 <= r['score'] < 4.0]
        
        if high_confidence:
            print("\n🟢 高信心度标的 (建议重点关注):")
            for result in high_confidence[:3]:
                print(f"  📌 {result['symbol']}: 得分 {result['score']:.1f}, "
                      f"价格 {result['current_price']:.4f}, "
                      f"主要信号: {', '.join(result['signals'][:2])}")
        
        if medium_confidence:
            print("\n🟡 中等信心度标的 (可适量配置):")
            for result in medium_confidence[:3]:
                print(f"  📌 {result['symbol']}: 得分 {result['score']:.1f}, "
                      f"价格 {result['current_price']:.4f}")
        
        print("\n⚠️ 风险提示:")
        print("  • 技术分析仅供参考，不构成投资建议")
        print("  • 数字货币市场波动较大，请控制仓位")
        print("  • 建议结合基本面分析和市场情绪")
        print("  • 设置止损位，严格执行风险管理")


def main():
    """主函数"""
    analyzer = SimpleCryptoAnalyzer()
    
    # 运行选币 (降低最低得分以显示更多结果)
    results = analyzer.run_selection(min_score=0.3)
    
    # 演示Alpha表达式
    analyzer.demonstrate_alpha_expressions()
    
    # 显示交易建议
    analyzer.show_trading_suggestions(results)
    
    print("\n" + "=" * 60)
    print("✅ 演示完成")
    print("=" * 60)
    print("这是一个基于Qlib框架的数字货币选币系统演示")
    print("实际使用时需要:")
    print("1. 安装Qlib框架: pip install qlib")
    print("2. 配置真实数据源 (币安、OKX等)")
    print("3. 设置消息推送 (企业微信、钉钉等)")
    print("4. 进行充分的回测验证")


if __name__ == "__main__":
    main()
