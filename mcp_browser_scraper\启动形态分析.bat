@echo off
chcp 65001 >nul
echo 🚀 启动形态分析脚本...
echo =====================================

cd /d "%~dp0"

echo 📍 当前目录: %CD%
echo 🐍 Python路径: D:\envs\tqsdk\python.exe

echo.
echo 🔧 检查Python环境...
D:\envs\tqsdk\python.exe --version
if errorlevel 1 (
    echo ❌ Python环境异常
    pause
    exit /b 1
)

echo.
echo 🔧 检查脚本文件...
if not exist "advanced_crypto_scraper tuxing.py" (
    echo ❌ 找不到脚本文件: advanced_crypto_scraper tuxing.py
    pause
    exit /b 1
)
echo ✅ 脚本文件存在

echo.
echo 🚀 启动形态分析脚本...
echo =====================================
echo.

D:\envs\tqsdk\python.exe "advanced_crypto_scraper tuxing.py"

echo.
echo =====================================
echo 📝 脚本执行完成
pause
