{"python.defaultInterpreterPath": "D:\\ProgramData\\miniconda3\\envs\\tqsdk\\python.exe", "python.terminal.activateEnvironment": true, "python.terminal.activateEnvInCurrentTerminal": true, "terminal.integrated.defaultProfile.windows": "Command Prompt", "terminal.integrated.profiles.windows": {"Command Prompt": {"path": ["${env:windir}\\Sysnative\\cmd.exe", "${env:windir}\\System32\\cmd.exe"], "args": [], "icon": "terminal-cmd"}, "PowerShell": {"source": "PowerShell", "icon": "terminal-powershell"}, "Conda (tqsdk)": {"path": "D:\\ProgramData\\miniconda3\\Scripts\\conda.exe", "args": ["activate", "tqsdk"], "icon": "terminal-cmd"}}, "python.linting.enabled": false, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": false, "python.formatting.provider": "none", "python.analysis.typeCheckingMode": "basic", "files.encoding": "utf8", "files.autoSave": "after<PERSON>elay", "files.autoSaveDelay": 1000}