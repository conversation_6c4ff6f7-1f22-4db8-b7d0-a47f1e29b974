import json
from datetime import datetime
import pandas as pd

def generate_report(analysis_results, output_file):
    """生成分析报告"""
    try:
        # 确保所有日期类型都被转换为字符串
        def convert_dates(obj):
            if isinstance(obj, (datetime, pd.Timestamp)):
                return obj.strftime('%Y-%m-%d')
            elif isinstance(obj, dict):
                return {k: convert_dates(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_dates(item) for item in obj]
            return obj
        
        # 转换所有日期
        report_data = convert_dates(analysis_results)
        
        # 生成报告
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"报告已生成: {output_file}")
        return True
    except Exception as e:
        logger.error(f"生成报告失败: {str(e)}")
        return False 