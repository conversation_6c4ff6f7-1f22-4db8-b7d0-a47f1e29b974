# 基于Qlib的数字货币选币系统

## 项目简介

这是一个基于Qlib框架开发的数字货币技术分析选币系统，能够识别K线形态、分析技术指标、进行多时间周期确认，并自动筛选出具有投资价值的数字货币标的。

## 核心功能

### 1. K线形态识别
- **锤子线形态**: 识别下跌趋势中的反转信号
- **十字星形态**: 检测市场犹豫和可能的转折点
- **看涨吞没**: 识别强烈的看涨反转信号
- **突破形态**: 检测价格突破关键阻力位
- **支撑反弹**: 识别支撑位附近的反弹机会

### 2. 技术指标分析
- **RSI超卖回升**: RSI从超卖区域(30以下)回升到35以上
- **MACD金叉**: MACD线上穿信号线，确认上涨趋势
- **布林带支撑**: 价格在布林带下轨获得支撑后反弹
- **均线多头排列**: 短期均线在长期均线上方
- **成交量确认**: 价格上涨伴随成交量放大

### 3. 多时间周期确认
- **日线趋势**: 确认主要趋势方向
- **4小时回调**: 寻找趋势中的回调买点
- **1小时精确**: 精确的入场时机
- **周期协同**: 多个时间周期信号的协同确认

## 系统架构

```
crypto_trading_assistant/qlib_strategies/
├── crypto_alpha_expressions.py    # Qlib Alpha表达式定义
├── crypto_stock_picker.py         # 核心选币算法
├── crypto_data_adapter.py         # 数据源适配器
├── crypto_selection_example.py    # 使用示例
├── config.py                      # 配置文件
├── main.py                        # 主程序
└── README.md                      # 说明文档
```

## 安装和配置

### 1. 环境要求
```bash
Python >= 3.8
pandas >= 1.3.0
numpy >= 1.21.0
requests >= 2.25.0
qlib >= 0.8.0
```

### 2. 安装依赖
```bash
pip install qlib pandas numpy requests
```

### 3. 配置数据源
在 `config.py` 中配置数据源：
```python
'data_config': {
    'data_source': 'binance',  # 支持: binance, okx, huobi
    'api_key': '',             # 如需要
    'api_secret': '',          # 如需要
    'cache_timeout': 300,      # 缓存时间
}
```

### 4. 配置消息推送
```python
'notification_config': {
    'enable_wechat': True,
    'wechat_webhook': 'your_webhook_url',
}
```

## 使用方法

### 1. 基础选币
```bash
# 运行日线选币
python main.py --mode selection --timeframe 1d --category all

# 运行4小时选币
python main.py --mode selection --timeframe 4h --category mainstream

# 指定策略运行
python main.py --strategy strong_breakout --timeframe 1d
```

### 2. 多时间周期分析
```bash
# 分析指定标的
python main.py --mode multi_tf --symbols BTCUSDT ETHUSDT

# 自动分析日线选币结果的前5名
python main.py --mode multi_tf
```

### 3. 演示模式
```bash
# 运行完整演示
python main.py --mode demo
```

### 4. 程序化使用
```python
from crypto_stock_picker import CryptoStockPicker
from config import get_config

# 初始化选币器
config = get_config('strong_breakout')
picker = CryptoStockPicker(config)

# 执行选币
symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
results = picker.select_stocks(symbols, '1d')

# 多时间周期分析
multi_tf_result = picker.multi_timeframe_analysis('BTCUSDT')
```

## Qlib Alpha表达式示例

### 1. 锤子线形态
```python
hammer_pattern = """
(
    # 下影线长度 >= 实体长度的2倍
    (($low - Min($open, $close)) >= 2 * Abs($open - $close)) &
    # 上影线很短 (< 实体长度的0.1倍)
    (($high - Max($open, $close)) <= 0.1 * Abs($open - $close)) &
    # 实体不能太小
    (Abs($open - $close) >= 0.001 * $close) &
    # 前期处于下跌趋势
    (Mean($close, 5) < Ref(Mean($close, 5), 1))
)
"""
```

### 2. RSI超卖回升
```python
rsi_oversold_recovery = """
(
    # 前期RSI < 30 (超卖)
    (Ref(RSI($close, 14), 1) < 30) &
    # 当前RSI > 35 (开始回升)
    (RSI($close, 14) > 35) &
    # RSI呈上升趋势
    (RSI($close, 14) > Ref(RSI($close, 14), 1))
)
"""
```

### 3. MACD金叉
```python
macd_golden_cross = """
(
    # MACD线上穿信号线
    (EMA($close, 12) - EMA($close, 26) > EMA(EMA($close, 12) - EMA($close, 26), 9)) &
    # 前一日MACD线在信号线下方
    (Ref(EMA($close, 12) - EMA($close, 26), 1) <= Ref(EMA(EMA($close, 12) - EMA($close, 26), 9), 1)) &
    # MACD柱状图转正
    ((EMA($close, 12) - EMA($close, 26)) - EMA(EMA($close, 12) - EMA($close, 26), 9) > 0)
)
"""
```

## 策略配置

### 1. 强势突破策略
```python
'strong_breakout': {
    'min_score': 4.0,
    'weights': {
        'pattern': 0.2,
        'indicator': 0.3,
        'trend': 0.3,
        'volume': 0.2
    },
    'filters': {
        'rsi_max': 75,
        'volume_min_ratio': 1.5,
        'price_change_max': 0.12
    }
}
```

### 2. 超跌反弹策略
```python
'oversold_rebound': {
    'min_score': 3.5,
    'weights': {
        'pattern': 0.4,
        'indicator': 0.4,
        'trend': 0.1,
        'volume': 0.1
    },
    'filters': {
        'rsi_max': 40,
        'rsi_min': 25,
        'price_decline_min': -0.1
    }
}
```

## 输出结果

### 选币结果示例
```
排名 代码         总分   形态   指标   趋势   成交量     当前价格      RSI   信号
1    BTCUSDT     5.2   2.0   2.1   1.0   0.1      45234.5600   45.2  MACD金叉, 成交量放大
2    ETHUSDT     4.8   1.5   2.0   1.2   0.1      3156.7800    42.1  RSI回升, 突破阻力
3    BNBUSDT     4.5   2.0   1.8   0.6   0.1      412.3400     38.9  锤子线, 布林带支撑
```

### 多时间周期分析
```
分析 BTCUSDT:
  综合得分: 4.85
    1d: 得分 5.2, RSI 45.2, 信号 3
    4h: 得分 4.1, RSI 42.8, 信号 2
    1h: 得分 3.9, RSI 48.1, 信号 2
  趋势一致: ✓, 信号一致: ✓, 风险等级: low
```

## 风险提示

1. **技术分析局限性**: 技术分析仅基于历史价格数据，无法预测未来走势
2. **市场风险**: 数字货币市场波动极大，存在巨大投资风险
3. **系统风险**: 算法可能存在bug或逻辑缺陷，请谨慎使用
4. **数据风险**: 依赖第三方数据源，可能存在数据延迟或错误

## 免责声明

本系统仅供学习和研究使用，不构成任何投资建议。使用者应当：
- 充分了解数字货币投资风险
- 根据自身风险承受能力进行投资
- 不要投入超过自己承受能力的资金
- 建议咨询专业投资顾问

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目地址: [GitHub Repository]
- 邮箱: [<EMAIL>]

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 支持基础K线形态识别
- 支持主要技术指标分析
- 支持多时间周期确认
- 支持企业微信消息推送
