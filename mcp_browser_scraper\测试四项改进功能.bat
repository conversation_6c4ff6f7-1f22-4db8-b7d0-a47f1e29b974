@echo off
chcp 65001 >nul
title 测试四项改进功能
echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █                  🧪 四项改进功能测试                        █
echo █              双长上影线形态分析系统升级验证                  █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.

cd /d "%~dp0"

echo 📍 当前目录: %CD%
echo 🐍 Python环境: D:\envs\tqsdk\python.exe
echo.

echo 🔧 检查环境...
if not exist "D:\envs\tqsdk\python.exe" (
    echo ❌ Python环境不存在
    pause
    exit /b 1
)

if not exist "advanced_crypto_scraper tuxing.py" (
    echo ❌ 主脚本文件不存在
    pause
    exit /b 1
)

if not exist "测试四项改进功能.py" (
    echo ❌ 测试脚本不存在
    pause
    exit /b 1
)

echo ✅ 环境检查通过
echo.

echo 💡 测试内容:
echo ┌─────────────────────────────────────────────────────────────┐
echo │ 🧪 四项改进功能测试                                        │
echo │                                                             │
echo │ 🔧 1. 网络连接配置优化                                     │
echo │   • 强制使用SSR代理连接                                    │
echo │   • 移除直连模式选项                                       │
echo │   • 多重代理配置和容错机制                                 │
echo │                                                             │
echo │ 📊 2. 形态分析市场筛选功能增强                             │
echo │   • 市场范围选择（全市场/新币/高交易量）                   │
echo │   • 新币筛选（7/15/30/60/90天内上市）                     │
echo │   • 强制刷新最新市场数据                                   │
echo │   • 数据时效性检查                                         │
echo │                                                             │
echo │ 🛡️ 3. 真实数据保障机制                                     │
echo │   • 严格数据验证，禁止模拟数据                             │
echo │   • 数据新鲜度检查（5分钟内）                              │
echo │   • 详细验证日志记录                                       │
echo │   • 网络失败时明确提示                                     │
echo │                                                             │
echo │ 🌐 4. 火币网形态对比验证功能                               │
echo │   • Selenium自动化抓取火币网K线图                          │
echo │   • 图像相似度分析（SSIM算法）                             │
echo │   • 形态对比报告生成                                       │
echo │   • 验证结果微信推送                                       │
echo └─────────────────────────────────────────────────────────────┘
echo.

echo 🚀 开始四项改进功能测试...
echo ================================================================
echo.

D:\envs\tqsdk\python.exe "测试四项改进功能.py"

echo.
echo ================================================================
echo 📝 测试完成
echo.
pause
