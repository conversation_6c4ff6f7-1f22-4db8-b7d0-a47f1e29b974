import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union
import logging
from datetime import datetime, timedelta
from crypto_technical_analyzer import TechnicalAnalyzer

class CryptoBacktest:
    def __init__(self, initial_capital: float = 10000.0):
        """
        初始化回测系统
        
        Args:
            initial_capital: 初始资金，默认10000
        """
        self.initial_capital = initial_capital
        self.technical_analyzer = TechnicalAnalyzer()
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('backtest.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def run_backtest(self, df: pd.DataFrame, position_size: float = 0.1,
                    stop_loss: float = 0.05, take_profit: float = 0.1) -> Dict:
        """
        运行回测
        
        Args:
            df: 包含OHLCV数据的DataFrame
            position_size: 每次交易的资金比例，默认0.1
            stop_loss: 止损比例，默认0.05
            take_profit: 止盈比例，默认0.1
            
        Returns:
            回测结果
        """
        try:
            # 计算技术指标
            df = self.technical_analyzer.calculate_indicators(df)
            
            # 初始化回测结果
            results = {
                'trades': [],
                'equity_curve': [],
                'metrics': {}
            }
            
            # 初始化交易状态
            position = 0
            entry_price = 0
            entry_date = None
            capital = self.initial_capital
            equity = capital
            
            # 遍历数据
            for i in range(len(df)):
                current_date = df.index[i]
                current_price = df['close'].iloc[i]
                
                # 获取交易信号
                signal = self.technical_analyzer.generate_signals(df.iloc[:i+1])
                
                # 处理持仓
                if position != 0:
                    # 计算当前收益
                    pnl = (current_price - entry_price) / entry_price
                    
                    # 检查止损止盈
                    if pnl <= -stop_loss or pnl >= take_profit:
                        # 平仓
                        trade_pnl = position * (current_price - entry_price)
                        capital += trade_pnl
                        equity = capital
                        
                        results['trades'].append({
                            'entry_date': entry_date,
                            'exit_date': current_date,
                            'entry_price': entry_price,
                            'exit_price': current_price,
                            'position': position,
                            'pnl': trade_pnl,
                            'pnl_pct': pnl
                        })
                        
                        position = 0
                        entry_price = 0
                        entry_date = None
                
                # 处理新信号
                if position == 0 and signal['action'] != 'hold':
                    if signal['action'] == 'buy':
                        position = (capital * position_size) / current_price
                        entry_price = current_price
                        entry_date = current_date
                    elif signal['action'] == 'sell':
                        position = -(capital * position_size) / current_price
                        entry_price = current_price
                        entry_date = current_date
                
                # 记录权益曲线
                if position != 0:
                    equity = capital + position * (current_price - entry_price)
                results['equity_curve'].append({
                    'date': current_date,
                    'equity': equity
                })
            
            # 计算回测指标
            results['metrics'] = self._calculate_metrics(results)
            
            return results
            
        except Exception as e:
            self.logger.error(f"回测执行失败: {str(e)}")
            return {}
            
    def _calculate_metrics(self, results: Dict) -> Dict:
        """
        计算回测指标
        
        Args:
            results: 回测结果
            
        Returns:
            回测指标
        """
        try:
            trades = results['trades']
            equity_curve = pd.DataFrame(results['equity_curve'])
            
            if not trades:
                return {
                    'total_trades': 0,
                    'win_rate': 0,
                    'profit_factor': 0,
                    'max_drawdown': 0,
                    'sharpe_ratio': 0,
                    'total_return': 0
                }
            
            # 计算基本指标
            total_trades = len(trades)
            winning_trades = len([t for t in trades if t['pnl'] > 0])
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # 计算盈亏比
            profits = [t['pnl'] for t in trades if t['pnl'] > 0]
            losses = [abs(t['pnl']) for t in trades if t['pnl'] < 0]
            profit_factor = sum(profits) / sum(losses) if sum(losses) > 0 else float('inf')
            
            # 计算最大回撤
            equity_curve['equity'] = pd.to_numeric(equity_curve['equity'])
            equity_curve['peak'] = equity_curve['equity'].cummax()
            equity_curve['drawdown'] = (equity_curve['peak'] - equity_curve['equity']) / equity_curve['peak']
            max_drawdown = equity_curve['drawdown'].max()
            
            # 计算夏普比率
            returns = equity_curve['equity'].pct_change().dropna()
            sharpe_ratio = np.sqrt(252) * returns.mean() / returns.std() if len(returns) > 0 else 0
            
            # 计算总收益率
            total_return = (equity_curve['equity'].iloc[-1] - self.initial_capital) / self.initial_capital
            
            return {
                'total_trades': total_trades,
                'win_rate': win_rate,
                'profit_factor': profit_factor,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'total_return': total_return
            }
            
        except Exception as e:
            self.logger.error(f"计算回测指标失败: {str(e)}")
            return {}
            
    def optimize_parameters(self, df: pd.DataFrame, 
                          position_sizes: List[float] = [0.1, 0.2, 0.3],
                          stop_losses: List[float] = [0.05, 0.1, 0.15],
                          take_profits: List[float] = [0.1, 0.2, 0.3]) -> Dict:
        """
        优化策略参数
        
        Args:
            df: 包含OHLCV数据的DataFrame
            position_sizes: 仓位大小列表
            stop_losses: 止损比例列表
            take_profits: 止盈比例列表
            
        Returns:
            最优参数组合
        """
        try:
            best_result = None
            best_sharpe = -float('inf')
            best_params = None
            
            # 遍历参数组合
            for ps in position_sizes:
                for sl in stop_losses:
                    for tp in take_profits:
                        # 运行回测
                        result = self.run_backtest(df, ps, sl, tp)
                        
                        # 更新最优结果
                        if result['metrics']['sharpe_ratio'] > best_sharpe:
                            best_sharpe = result['metrics']['sharpe_ratio']
                            best_result = result
                            best_params = {
                                'position_size': ps,
                                'stop_loss': sl,
                                'take_profit': tp
                            }
            
            return {
                'best_params': best_params,
                'best_result': best_result
            }
            
        except Exception as e:
            self.logger.error(f"参数优化失败: {str(e)}")
            return {} 