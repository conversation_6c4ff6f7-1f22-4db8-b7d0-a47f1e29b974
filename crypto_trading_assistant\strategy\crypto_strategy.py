"""
基于Qlib框架的加密货币交易策略
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from qlib.strategy.base import BaseStrategy
from qlib.backtest.decision import BaseTradeDecision, TradeRange, TradeRangeByTime
from qlib.backtest.signal import SignalRecord, create_account
from qlib.utils import get_module_logger
from loguru import logger

from ..analysis.signal_generator import SignalGenerator, TradingSignal, SignalType
from ..analysis.pattern_recognition import PatternRecognizer
from ..data.crypto_data_provider import CryptoDataProvider


class CryptoPatternStrategy(BaseStrategy):
    """基于形态识别的加密货币策略"""
    
    def __init__(self, 
                 config: Dict,
                 signal_generator: SignalGenerator,
                 data_provider: CryptoDataProvider,
                 **kwargs):
        """
        初始化策略
        
        Args:
            config: 策略配置
            signal_generator: 信号生成器
            data_provider: 数据提供者
        """
        super().__init__(**kwargs)
        self.config = config
        self.signal_generator = signal_generator
        self.data_provider = data_provider
        self.logger = get_module_logger(self.__class__.__name__)
        
        # 策略参数
        self.max_positions = config.get('risk_management', {}).get('max_positions', 5)
        self.position_size = config.get('risk_management', {}).get('fixed_amount', 1000)
        self.risk_per_trade = config.get('risk_management', {}).get('max_risk_per_trade', 0.02)
        
        # 当前持仓
        self.current_positions = {}
        self.signal_history = []
    
    def generate_trade_decision(self, execute_result: List = None) -> BaseTradeDecision:
        """
        生成交易决策
        
        Args:
            execute_result: 执行结果
            
        Returns:
            交易决策
        """
        try:
            # 获取当前时间
            current_time = pd.Timestamp.now()
            
            # 扫描所有交易对
            all_symbols = (
                self.config['trading_pairs']['major_pairs'] + 
                self.config['trading_pairs']['defi_pairs']
            )
            
            trade_decisions = []
            
            for symbol in all_symbols:
                try:
                    # 获取市场数据
                    df = self._get_market_data(symbol)
                    if df is None or len(df) < 50:
                        continue
                    
                    # 生成信号
                    signals = self.signal_generator.generate_signals(
                        df, symbol, self.config['timeframes']['primary']
                    )
                    
                    # 处理信号
                    for signal in signals:
                        if self._should_execute_signal(signal):
                            decision = self._create_trade_decision(signal, df)
                            if decision:
                                trade_decisions.append(decision)
                                self.signal_history.append(signal)
                
                except Exception as e:
                    self.logger.warning(f"处理 {symbol} 时出错: {e}")
                    continue
            
            # 创建综合交易决策
            if trade_decisions:
                return self._combine_trade_decisions(trade_decisions)
            else:
                return BaseTradeDecision([], TradeRangeByTime(current_time, current_time))
                
        except Exception as e:
            self.logger.error(f"生成交易决策时出错: {e}")
            return BaseTradeDecision([], TradeRangeByTime(pd.Timestamp.now(), pd.Timestamp.now()))
    
    def _get_market_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """获取市场数据"""
        try:
            # 获取最近的数据
            df = self.data_provider.get_ohlcv_data(
                symbol=symbol,
                timeframe=self.config['timeframes']['primary'],
                start_time=pd.Timestamp.now() - pd.Timedelta(days=30),
                end_time=pd.Timestamp.now()
            )
            return df
        except Exception as e:
            self.logger.error(f"获取 {symbol} 市场数据失败: {e}")
            return None
    
    def _should_execute_signal(self, signal: TradingSignal) -> bool:
        """判断是否应该执行信号"""
        try:
            # 检查置信度
            if signal.confidence < 0.7:
                return False
            
            # 检查最大持仓数
            if len(self.current_positions) >= self.max_positions:
                return False
            
            # 检查是否已经持有该币种
            if signal.symbol in self.current_positions:
                return False
            
            # 检查风险回报比
            if signal.risk_reward_ratio and signal.risk_reward_ratio < 1.5:
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"判断信号执行条件时出错: {e}")
            return False
    
    def _create_trade_decision(self, signal: TradingSignal, df: pd.DataFrame) -> Optional[Dict]:
        """创建交易决策"""
        try:
            if signal.signal_type == SignalType.BUY:
                # 计算仓位大小
                position_size = self._calculate_position_size(signal)
                
                return {
                    'instrument': signal.symbol.replace('/', ''),  # 移除斜杠
                    'amount': position_size,
                    'direction': 1,  # 1为买入，-1为卖出
                    'price': signal.entry_price,
                    'stop_loss': signal.stop_loss,
                    'target_price': signal.target_price,
                    'signal_info': signal
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"创建交易决策时出错: {e}")
            return None
    
    def _calculate_position_size(self, signal: TradingSignal) -> float:
        """计算仓位大小"""
        try:
            # 基于固定金额计算
            if self.position_size:
                return self.position_size / signal.entry_price
            
            # 基于风险百分比计算
            risk_amount = self.position_size * self.risk_per_trade
            price_risk = signal.entry_price - signal.stop_loss
            
            if price_risk > 0:
                return risk_amount / price_risk
            
            return 0.0
            
        except Exception as e:
            self.logger.error(f"计算仓位大小时出错: {e}")
            return 0.0
    
    def _combine_trade_decisions(self, decisions: List[Dict]) -> BaseTradeDecision:
        """合并交易决策"""
        try:
            # 按置信度排序，选择最好的几个
            decisions.sort(key=lambda x: x['signal_info'].confidence, reverse=True)
            
            # 限制同时开仓数量
            max_new_positions = min(len(decisions), self.max_positions - len(self.current_positions))
            selected_decisions = decisions[:max_new_positions]
            
            # 转换为Qlib格式的交易记录
            trade_records = []
            for decision in selected_decisions:
                record = SignalRecord(
                    instrument=decision['instrument'],
                    score=decision['signal_info'].confidence,
                    pred=decision['direction']
                )
                trade_records.append(record)
            
            # 创建交易时间范围
            current_time = pd.Timestamp.now()
            trade_range = TradeRangeByTime(current_time, current_time + pd.Timedelta(hours=1))
            
            return BaseTradeDecision(trade_records, trade_range)
            
        except Exception as e:
            self.logger.error(f"合并交易决策时出错: {e}")
            return BaseTradeDecision([], TradeRangeByTime(pd.Timestamp.now(), pd.Timestamp.now()))


class CryptoPortfolioManager:
    """加密货币投资组合管理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.positions = {}
        self.cash = config.get('risk_management', {}).get('initial_capital', 100000)
        self.total_value = self.cash
        
    def update_portfolio(self, market_data: Dict[str, pd.DataFrame]):
        """更新投资组合"""
        try:
            total_value = self.cash
            
            for symbol, position in self.positions.items():
                if symbol in market_data:
                    current_price = float(market_data[symbol]['close'].iloc[-1])
                    position_value = position['quantity'] * current_price
                    total_value += position_value
                    
                    # 更新持仓信息
                    position['current_price'] = current_price
                    position['current_value'] = position_value
                    position['pnl'] = position_value - position['cost']
                    position['pnl_pct'] = (position['pnl'] / position['cost']) * 100
            
            self.total_value = total_value
            
        except Exception as e:
            logger.error(f"更新投资组合时出错: {e}")
    
    def add_position(self, symbol: str, quantity: float, price: float, signal: TradingSignal):
        """添加持仓"""
        try:
            cost = quantity * price
            
            if cost <= self.cash:
                self.positions[symbol] = {
                    'quantity': quantity,
                    'entry_price': price,
                    'current_price': price,
                    'cost': cost,
                    'current_value': cost,
                    'pnl': 0,
                    'pnl_pct': 0,
                    'entry_time': pd.Timestamp.now(),
                    'stop_loss': signal.stop_loss,
                    'target_price': signal.target_price,
                    'signal_info': signal
                }
                
                self.cash -= cost
                logger.info(f"添加持仓: {symbol}, 数量: {quantity}, 价格: {price}")
                return True
            else:
                logger.warning(f"资金不足，无法添加 {symbol} 持仓")
                return False
                
        except Exception as e:
            logger.error(f"添加持仓时出错: {e}")
            return False
    
    def remove_position(self, symbol: str, price: float) -> bool:
        """移除持仓"""
        try:
            if symbol in self.positions:
                position = self.positions[symbol]
                sell_value = position['quantity'] * price
                
                self.cash += sell_value
                pnl = sell_value - position['cost']
                
                logger.info(f"移除持仓: {symbol}, 卖出价格: {price}, 盈亏: {pnl}")
                del self.positions[symbol]
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"移除持仓时出错: {e}")
            return False
    
    def check_stop_loss_targets(self, market_data: Dict[str, pd.DataFrame]) -> List[str]:
        """检查止损和目标价位"""
        to_close = []
        
        try:
            for symbol, position in self.positions.items():
                if symbol in market_data:
                    current_price = float(market_data[symbol]['close'].iloc[-1])
                    
                    # 检查止损
                    if current_price <= position['stop_loss']:
                        logger.info(f"{symbol} 触发止损: {current_price} <= {position['stop_loss']}")
                        to_close.append(symbol)
                    
                    # 检查目标价位
                    elif current_price >= position['target_price']:
                        logger.info(f"{symbol} 达到目标价位: {current_price} >= {position['target_price']}")
                        to_close.append(symbol)
        
        except Exception as e:
            logger.error(f"检查止损目标时出错: {e}")
        
        return to_close
    
    def get_portfolio_summary(self) -> Dict:
        """获取投资组合摘要"""
        try:
            summary = {
                'total_value': self.total_value,
                'cash': self.cash,
                'invested': self.total_value - self.cash,
                'positions_count': len(self.positions),
                'positions': {}
            }
            
            total_pnl = 0
            for symbol, position in self.positions.items():
                summary['positions'][symbol] = {
                    'quantity': position['quantity'],
                    'entry_price': position['entry_price'],
                    'current_price': position['current_price'],
                    'pnl': position['pnl'],
                    'pnl_pct': position['pnl_pct']
                }
                total_pnl += position['pnl']
            
            summary['total_pnl'] = total_pnl
            summary['total_pnl_pct'] = (total_pnl / (self.total_value - self.cash)) * 100 if self.total_value > self.cash else 0
            
            return summary
            
        except Exception as e:
            logger.error(f"获取投资组合摘要时出错: {e}")
            return {}
