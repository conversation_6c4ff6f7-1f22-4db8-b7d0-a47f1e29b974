"""
K线形态识别演示程序
展示各种K线形态的识别功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import requests


class KlinePatternDemo:
    """K线形态演示类"""
    
    def __init__(self):
        """初始化"""
        self.wechat_webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=52d1b8a8-a88a-4d41-9664-f1268da20985"
    
    def create_pattern_data(self, pattern_type: str) -> pd.DataFrame:
        """创建特定形态的K线数据"""
        dates = pd.date_range(start=datetime.now() - timedelta(days=20), periods=20, freq='D')
        base_price = 100
        
        # 生成基础数据
        data = []
        for i, date in enumerate(dates):
            if pattern_type == "hammer" and i == 15:
                # 锤子线：长下影线，短上影线，小实体
                open_price = base_price
                close = base_price + 0.5
                high = base_price + 1
                low = base_price - 4  # 长下影线
                volume = 150000
            elif pattern_type == "doji" and i == 15:
                # 十字星：开收盘价相近，上下影线较长
                open_price = base_price
                close = base_price + 0.1  # 几乎相等
                high = base_price + 2
                low = base_price - 2
                volume = 120000
            elif pattern_type == "engulfing" and i >= 14:
                if i == 14:
                    # 前一日阴线
                    open_price = base_price + 1
                    close = base_price - 1
                    high = base_price + 1.5
                    low = base_price - 1.5
                    volume = 100000
                elif i == 15:
                    # 今日阳线吞没
                    open_price = base_price - 1.5
                    close = base_price + 2
                    high = base_price + 2.5
                    low = base_price - 2
                    volume = 180000
                else:
                    open_price = base_price + np.random.normal(0, 0.5)
                    close = open_price + np.random.normal(0, 1)
                    high = max(open_price, close) + abs(np.random.normal(0, 0.5))
                    low = min(open_price, close) - abs(np.random.normal(0, 0.5))
                    volume = np.random.normal(100000, 20000)
            elif pattern_type == "long_upper_shadow" and i == 15:
                # 长上影线
                open_price = base_price
                close = base_price + 0.5
                high = base_price + 4  # 长上影线
                low = base_price - 0.5
                volume = 130000
            elif pattern_type == "big_yang" and i == 15:
                # 大阳线
                open_price = base_price - 1
                close = base_price + 3  # 大实体阳线
                high = base_price + 3.5
                low = base_price - 1.2
                volume = 200000
            else:
                # 普通K线
                open_price = base_price + np.random.normal(0, 0.5)
                close = open_price + np.random.normal(0, 1)
                high = max(open_price, close) + abs(np.random.normal(0, 0.5))
                low = min(open_price, close) - abs(np.random.normal(0, 0.5))
                volume = np.random.normal(100000, 20000)
            
            data.append({
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': max(volume, 50000)  # 确保成交量为正
            })
            
            base_price = close  # 下一根K线的基准价格
        
        return pd.DataFrame(data, index=dates)
    
    def detect_kline_patterns(self, data: pd.DataFrame) -> dict:
        """检测K线形态"""
        patterns = {
            'detected_patterns': [],
            'pattern_details': {},
            'latest_pattern': None
        }
        
        try:
            if len(data) < 2:
                return patterns
            
            # 检查最近几个K线的形态
            for i in range(-3, 0):
                if abs(i) > len(data):
                    continue
                    
                current = data.iloc[i]
                prev = data.iloc[i-1] if i > -len(data) else current
                
                body = abs(current['close'] - current['open'])
                total_range = current['high'] - current['low']
                upper_shadow = current['high'] - max(current['close'], current['open'])
                lower_shadow = min(current['close'], current['open']) - current['low']
                
                pattern_info = {
                    'date': current.name.strftime('%Y-%m-%d'),
                    'open': current['open'],
                    'high': current['high'],
                    'low': current['low'],
                    'close': current['close'],
                    'body': body,
                    'upper_shadow': upper_shadow,
                    'lower_shadow': lower_shadow,
                    'total_range': total_range
                }
                
                # 锤子线形态
                if (body > 0 and total_range > 0 and 
                    lower_shadow >= 2 * body and upper_shadow <= 0.3 * body):
                    patterns['detected_patterns'].append("锤子线")
                    patterns['pattern_details']['锤子线'] = pattern_info
                    if i == -1:  # 最新K线
                        patterns['latest_pattern'] = "锤子线"
                
                # 十字星形态
                elif body <= 0.002 * current['close'] and total_range >= 0.01 * current['close']:
                    patterns['detected_patterns'].append("十字星")
                    patterns['pattern_details']['十字星'] = pattern_info
                    if i == -1:
                        patterns['latest_pattern'] = "十字星"
                
                # 长上影线
                elif upper_shadow >= 2 * body and body > 0:
                    patterns['detected_patterns'].append("长上影线")
                    patterns['pattern_details']['长上影线'] = pattern_info
                    if i == -1:
                        patterns['latest_pattern'] = "长上影线"
                
                # 长下影线
                elif lower_shadow >= 2 * body and body > 0:
                    patterns['detected_patterns'].append("长下影线")
                    patterns['pattern_details']['长下影线'] = pattern_info
                    if i == -1:
                        patterns['latest_pattern'] = "长下影线"
                
                # 大阳线
                elif (current['close'] > current['open'] and 
                      body >= 0.03 * current['close']):
                    patterns['detected_patterns'].append("大阳线")
                    patterns['pattern_details']['大阳线'] = pattern_info
                    if i == -1:
                        patterns['latest_pattern'] = "大阳线"
                
                # 大阴线
                elif (current['close'] < current['open'] and 
                      body >= 0.03 * current['close']):
                    patterns['detected_patterns'].append("大阴线")
                    patterns['pattern_details']['大阴线'] = pattern_info
                    if i == -1:
                        patterns['latest_pattern'] = "大阴线"
            
            # 检查多K线组合形态
            if len(data) >= 2:
                current = data.iloc[-1]
                prev = data.iloc[-2]
                
                # 看涨吞没
                if (prev['close'] < prev['open'] and current['close'] > current['open'] and
                    current['open'] <= prev['close'] * 1.01 and current['close'] >= prev['open'] * 0.99):
                    patterns['detected_patterns'].append("看涨吞没")
                    patterns['pattern_details']['看涨吞没'] = {
                        'date': current.name.strftime('%Y-%m-%d'),
                        'prev_candle': f"阴线 {prev['open']:.2f}->{prev['close']:.2f}",
                        'curr_candle': f"阳线 {current['open']:.2f}->{current['close']:.2f}",
                        'description': "今日阳线完全吞没昨日阴线"
                    }
                    patterns['latest_pattern'] = "看涨吞没"
            
            # 去重
            patterns['detected_patterns'] = list(dict.fromkeys(patterns['detected_patterns']))
            
            return patterns
            
        except Exception as e:
            print(f"形态识别出错: {e}")
            return patterns
    
    def analyze_pattern_significance(self, pattern_name: str) -> dict:
        """分析形态的技术意义"""
        significance = {
            "锤子线": {
                "类型": "反转形态",
                "出现位置": "下跌趋势末期",
                "技术含义": "卖方力量衰竭，买方开始介入",
                "操作建议": "可考虑逢低买入，设置止损",
                "成功率": "中等偏高",
                "确认条件": "需要后续阳线确认"
            },
            "十字星": {
                "类型": "变盘形态",
                "出现位置": "趋势转折点",
                "技术含义": "多空力量平衡，市场犹豫",
                "操作建议": "观望为主，等待方向明确",
                "成功率": "中等",
                "确认条件": "需要结合位置和成交量"
            },
            "看涨吞没": {
                "类型": "强烈反转形态",
                "出现位置": "下跌趋势中",
                "技术含义": "买方力量强劲，趋势可能反转",
                "操作建议": "积极买入信号",
                "成功率": "较高",
                "确认条件": "成交量放大更佳"
            },
            "长上影线": {
                "类型": "阻力形态",
                "出现位置": "上涨过程中",
                "技术含义": "上方抛压沉重，涨势受阻",
                "操作建议": "谨慎追高，可考虑减仓",
                "成功率": "中等",
                "确认条件": "连续出现更需警惕"
            },
            "大阳线": {
                "类型": "强势形态",
                "出现位置": "任何位置",
                "技术含义": "买方力量强劲，趋势强烈",
                "操作建议": "可跟随趋势操作",
                "成功率": "较高",
                "确认条件": "成交量配合更佳"
            }
        }
        
        return significance.get(pattern_name, {
            "类型": "普通形态",
            "技术含义": "市场正常波动",
            "操作建议": "根据整体趋势操作"
        })
    
    def send_pattern_notification(self, symbol: str, patterns: dict):
        """发送K线形态通知"""
        try:
            if not patterns['detected_patterns']:
                return
            
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            message = f"📊 K线形态识别提醒\n"
            message += f"⏰ 时间: {timestamp}\n"
            message += f"🎯 标的: {symbol}\n"
            message += f"🔍 检测到 {len(patterns['detected_patterns'])} 种形态\n\n"
            
            # 最新形态详情
            if patterns['latest_pattern']:
                latest = patterns['latest_pattern']
                message += f"📈 最新形态: {latest}\n"
                
                if latest in patterns['pattern_details']:
                    details = patterns['pattern_details'][latest]
                    if 'date' in details:
                        message += f"📅 日期: {details['date']}\n"
                        message += f"💰 价格: {details['open']:.2f} → {details['close']:.2f}\n"
                        message += f"📊 实体: {details['body']:.2f}\n"
                        message += f"⬆️ 上影: {details['upper_shadow']:.2f}\n"
                        message += f"⬇️ 下影: {details['lower_shadow']:.2f}\n"
                
                # 技术分析
                significance = self.analyze_pattern_significance(latest)
                message += f"\n🔬 技术分析:\n"
                message += f"类型: {significance.get('类型', '未知')}\n"
                message += f"含义: {significance.get('技术含义', '无')}\n"
                message += f"建议: {significance.get('操作建议', '无')}\n"
            
            # 所有检测到的形态
            if len(patterns['detected_patterns']) > 1:
                message += f"\n📋 全部形态: {', '.join(patterns['detected_patterns'])}\n"
            
            message += f"\n⚠️ 风险提示: K线形态仅供参考，请结合其他指标综合判断"
            
            # 发送企业微信通知
            data = {
                "msgtype": "text",
                "text": {
                    "content": message
                }
            }
            
            response = requests.post(
                self.wechat_webhook,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    print("✅ K线形态通知发送成功")
                else:
                    print(f"❌ 通知发送失败: {result}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 发送通知失败: {e}")
    
    def run_pattern_demo(self):
        """运行K线形态演示"""
        print("📊 K线形态识别演示系统")
        print("=" * 60)
        
        pattern_types = [
            ("锤子线", "hammer"),
            ("十字星", "doji"), 
            ("看涨吞没", "engulfing"),
            ("长上影线", "long_upper_shadow"),
            ("大阳线", "big_yang")
        ]
        
        for pattern_name, pattern_type in pattern_types:
            print(f"\n🔍 演示 {pattern_name} 形态识别:")
            print("-" * 40)
            
            # 创建包含特定形态的数据
            data = self.create_pattern_data(pattern_type)
            
            # 识别形态
            patterns = self.detect_kline_patterns(data)
            
            # 显示结果
            if patterns['detected_patterns']:
                print(f"✅ 成功识别形态: {', '.join(patterns['detected_patterns'])}")
                
                if patterns['latest_pattern']:
                    latest = patterns['latest_pattern']
                    print(f"📈 最新形态: {latest}")
                    
                    if latest in patterns['pattern_details']:
                        details = patterns['pattern_details'][latest]
                        if 'date' in details:
                            print(f"   日期: {details['date']}")
                            if 'open' in details:
                                print(f"   开盘: {details['open']:.2f}")
                                print(f"   收盘: {details['close']:.2f}")
                                print(f"   最高: {details['high']:.2f}")
                                print(f"   最低: {details['low']:.2f}")
                                print(f"   实体: {details['body']:.2f}")
                                print(f"   上影: {details['upper_shadow']:.2f}")
                                print(f"   下影: {details['lower_shadow']:.2f}")
                            else:
                                # 组合形态的显示
                                for key, value in details.items():
                                    if key != 'date':
                                        print(f"   {key}: {value}")
                    
                    # 显示技术意义
                    significance = self.analyze_pattern_significance(latest)
                    print(f"\n🔬 技术分析:")
                    for key, value in significance.items():
                        print(f"   {key}: {value}")
                
                # 发送通知 (仅为演示最后一个形态)
                if pattern_type == "big_yang":
                    print(f"\n📱 发送企业微信通知...")
                    self.send_pattern_notification("DEMO/USDT", patterns)
            else:
                print("❌ 未识别到预期形态")
        
        print(f"\n" + "=" * 60)
        print("✅ K线形态识别演示完成")
        print("=" * 60)
        print("支持的形态类型:")
        print("• 单K线形态: 锤子线、十字星、长上影线、长下影线、大阳线、大阴线")
        print("• 组合形态: 看涨吞没、看跌吞没、启明星等")
        print("• 自动推送: 企业微信实时通知")
        print("• 技术分析: 形态意义和操作建议")


def main():
    """主函数"""
    demo = KlinePatternDemo()
    demo.run_pattern_demo()


if __name__ == "__main__":
    main()
