#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
终极版加密货币数据抓取器 - 整合形态分析功能 + SSR代理支持
整合历史数据抓取、火币API功能、K线形态识别、技术指标分析
支持多种数据源、智能筛选条件、多时间周期分析、企业微信推送

版本: v3.2 Ultimate Edition with Real Kline Data
功能: 数据下载 + 真实K线 + 形态分析 + 技术指标 + 多周期确认 + 智能推送 + SSR代理

备份时间: 2025-06-22 16:58:00
"""

import requests
import sqlite3
import pandas as pd
import time
import json
import hmac
import hashlib
import base64
import urllib.parse
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union
import asyncio
import aiohttp
import traceback
import numpy as np
import warnings
warnings.filterwarnings('ignore')
# import talib  # 暂时注释掉
from scipy.signal import find_peaks
from scipy.stats import linregress
import yaml
import subprocess
import socket
import socks
import signal
import threading
import schedule
from collections import defaultdict

# 导入真实K线数据提供器
try:
    from real_kline_data_provider import RealKlineDataProvider
    REAL_KLINE_AVAILABLE = True
    log_init = lambda msg: print(f"[INIT] {msg}")
    log_init("✅ 真实K线数据提供器导入成功")
except ImportError as e:
    REAL_KLINE_AVAILABLE = False
    log_init = lambda msg: print(f"[INIT] {msg}")
    log_init(f"❌ 真实K线数据提供器导入失败: {e}")

def log(message):
    """增强的日志函数"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

class AdvancedCryptoScraper:
    """
    终极版加密货币数据抓取器 - 整合形态分析功能 + SSR代理支持
    整合历史数据抓取、火币API功能、K线形态识别、技术指标分析
    支持多种数据源、智能筛选条件、多时间周期分析、企业微信推送

    版本: v3.2 Ultimate Edition with Real Kline Data
    功能: 数据下载 + 真实K线 + 形态分析 + 技术指标 + 多周期确认 + 智能推送 + SSR代理
    """

    def __init__(self, huobi_api_key: str = "", huobi_secret_key: str = "", use_proxy: bool = True, config_path: str = "ssr_config.json"):
        """
        初始化终极版数据抓取器

        Args:
            huobi_api_key: 火币API密钥
            huobi_secret_key: 火币API密钥
            use_proxy: 是否使用代理
            config_path: SSR配置文件路径
        """
        self.huobi_api_key = huobi_api_key
        self.huobi_secret_key = huobi_secret_key
        self.db_path = "advanced_crypto_data.db"
        self.use_proxy = use_proxy

        # 加载SSR配置
        self.config_path = config_path or 'ssr_config.json'
        self.ssr_config = self._load_ssr_config()

        # SSR客户端进程和连接状态
        self.ssr_process = None
        self.ssr_connected = False
        self.current_ssr_server_index = 0  # 当前使用的SSR服务器索引

        # 速度模式配置
        self.speed_mode = 'normal'  # 默认正常模式: fast, normal, safe
        self.current_speed_mode = 'normal'  # 兼容性：保持与现有代码一致

        # 速率限制管理
        self.rate_limit_hit_count = 0  # 遇到速率限制的次数
        self.last_rate_limit_time = None  # 最后一次遇到速率限制的时间
        self.adaptive_delay = 1  # 自适应延迟时间

        # 速度控制配置
        self.speed_modes = {
            'unlimited': {'delay': 0, 'name': '不限制', 'desc': '无延迟，最快速度'},
            'fast': {'delay': 0.3, 'name': '快速', 'desc': '300ms延迟，适合批量获取'},
            'normal': {'delay': 0.8, 'name': '正常', 'desc': '800ms延迟，推荐使用'},
            'safe': {'delay': 1.5, 'name': '安全', 'desc': '1.5秒延迟，最安全'},
            'conservative': {'delay': 3.0, 'name': '保守', 'desc': '3秒延迟，极度安全'}
        }
        self.current_speed_mode = 'normal'

        # 数据源配置 - 全部使用真实交易数据，Gate.io为主数据源
        self.data_sources = {
            'gateio': {
                'base_url': 'https://api.gateio.ws/api/v4',
                'name': 'Gate.io',
                'free': True,
                'rate_limit': 900,  # 每分钟请求数
                'requires_proxy': True,  # 需要代理访问
                'real_data': True,  # 确保使用真实数据
                'priority': 1,  # 主数据源
                'description': 'Gate.io交易所API，主数据源，提供丰富的山寨币真实交易数据'
            },
            'coingecko': {
                'base_url': 'https://api.coingecko.com/api/v3',
                'name': 'CoinGecko',
                'free': True,
                'rate_limit': 50,  # 每分钟请求数
                'requires_proxy': True,  # 需要代理访问
                'real_data': True,  # 确保使用真实数据
                'priority': 2,  # 备用数据源
                'description': '全球最大的加密货币数据聚合平台，备用数据源'
            },
            'huobi': {
                'base_url': 'https://api.huobi.pro',
                'name': '火币',
                'free': True,  # 基础API免费
                'rate_limit': 100,
                'requires_proxy': False,  # 国内可直接访问
                'real_data': True,  # 确保使用真实数据
                'priority': 3,  # 备用数据源
                'description': '火币全球站真实交易数据，备用数据源'
            },
            'binance': {
                'base_url': 'https://api.binance.com/api/v3',
                'name': 'Binance',
                'free': True,
                'rate_limit': 1200,  # 每分钟请求数
                'requires_proxy': True,  # 需要代理访问
                'real_data': True,  # 确保使用真实数据
                'priority': 4,  # 备用数据源
                'description': '币安交易所官方API，备用数据源'
            },
            'coinmarketcap': {
                'base_url': 'https://pro-api.coinmarketcap.com/v1',
                'name': 'CoinMarketCap',
                'free': False,  # 需要API密钥
                'rate_limit': 333,
                'requires_proxy': True,  # 需要代理访问
                'real_data': True,  # 确保使用真实数据
                'priority': 5,  # 备用数据源
                'description': 'CoinMarketCap专业API，备用数据源'
            }
        }

        # 初始化代理配置
        self._init_proxy_config()

        # 初始化请求会话
        self._init_session()

        # 如果启用SSR，尝试启动SSR连接
        if self.use_proxy and self.ssr_config.get('proxy_settings', {}).get('enable_proxy', False):
            self._init_ssr_connection()

        # 选币范围配置
        self.selection_ranges = {
            '1': {'name': '🌍 全市场扫描', 'desc': '所有币种', 'filter': self._filter_all_market},
            '2': {'name': '👑 主流币种', 'desc': '市值>100亿美元', 'filter': self._filter_mainstream},
            '3': {'name': '🔥 热门山寨币', 'desc': '市值10-100亿美元', 'filter': self._filter_hot_altcoins},
            '4': {'name': '💎 小市值潜力币', 'desc': '市值<10亿美元', 'filter': self._filter_small_cap},
            '5': {'name': '🆕 新上市币种', 'desc': '30天内', 'filter': self._filter_new_listings_30d},
            '6': {'name': '🆕 较新币种', 'desc': '90天内', 'filter': self._filter_new_listings_90d},
            '7': {'name': '🏦 DeFi生态代币', 'desc': 'DeFi相关项目', 'filter': self._filter_defi_tokens},
            '8': {'name': '⛓️ Layer1公链代币', 'desc': 'Layer1区块链', 'filter': self._filter_layer1_tokens},
            '9': {'name': '🔗 Layer2扩容代币', 'desc': 'Layer2解决方案', 'filter': self._filter_layer2_tokens},
            '10': {'name': '🐕 Meme币专区', 'desc': 'Meme概念币', 'filter': self._filter_meme_tokens},
            '11': {'name': '🤖 AI概念币', 'desc': 'AI人工智能', 'filter': self._filter_ai_tokens},
            '12': {'name': '📈 高交易量币种', 'desc': '24h交易量前100', 'filter': self._filter_high_volume},
            '13': {'name': '🔧 自定义筛选条件', 'desc': '用户自定义', 'filter': self._filter_custom},
            '14': {'name': '📊 查看市场概况', 'desc': '市场统计信息', 'filter': None},
            '15': {'name': '🎯 形态分析选币', 'desc': '技术形态+指标分析', 'filter': self._pattern_analysis_filter}
        }

        # 请求统计
        self.request_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'last_request_time': None
        }

        # 🆕 自动化运行调度系统配置
        self.scheduler_config = {
            'is_running': False,
            'run_mode': 'single',  # single, infinite, scheduled
            'interval_minutes': 60,  # 默认1小时
            'run_count': 0,
            'start_time': None,
            'next_run_time': None,
            'stop_requested': False,
            'current_function': None,
            'scheduler_thread': None
        }

        # 运行模式配置
        self.run_modes = {
            'single': {'name': '单次运行', 'desc': '执行一次后退出'},
            'infinite': {'name': '无限循环', 'desc': '持续监控，无限循环运行'},
            'scheduled': {'name': '定时运行', 'desc': '按设定间隔定时执行'}
        }

        # 时间间隔选项（分钟）
        self.interval_options = {
            '15': {'name': '15分钟', 'minutes': 15},
            '30': {'name': '30分钟', 'minutes': 30},
            '60': {'name': '1小时', 'minutes': 60},
            '120': {'name': '2小时', 'minutes': 120},
            '240': {'name': '4小时', 'minutes': 240},
            '360': {'name': '6小时', 'minutes': 360},
            '720': {'name': '12小时', 'minutes': 720},
            '1440': {'name': '24小时', 'minutes': 1440}
        }

        # 信号处理
        self._setup_signal_handlers()

        # 🆕 增强微信推送管理系统配置
        self.wechat_config_file = 'wechat_bots_config.json'
        self.wechat_webhooks = self._load_wechat_webhooks()
        # 保持向后兼容
        self.wechat_webhook = self.wechat_webhooks[0]['webhook_url'] if self.wechat_webhooks else ""

        # 检查微信推送功能状态
        if self.wechat_webhooks and len(self.wechat_webhooks) > 0:
            enabled_bots = [bot for bot in self.wechat_webhooks if bot.get('enabled', True)]
            if enabled_bots:
                log(f"✅ 微信推送功能已启用: {len(enabled_bots)} 个机器人可用")
            else:
                log("⚠️ 微信推送功能已禁用: 没有启用的机器人")
        else:
            log("⚠️ 微信推送功能已禁用: 没有配置机器人")

        # 推送目标选择配置
        self.push_target_modes = {
            'all': {'name': '默认推送', 'desc': '推送到所有已配置的机器人'},
            'single': {'name': '单选推送', 'desc': '选择单个机器人推送'},
            'multi': {'name': '多选推送', 'desc': '选择多个机器人推送'},
            'custom': {'name': '自定义推送', 'desc': '临时添加新的webhook URL'}
        }

        # ==================== 新增：形态分析配置 ====================

        # 支持的时间周期
        self.timeframes = ['1d', '4h', '1h', '30m', '15m']

        # 优化后的多时间周期权重
        self.timeframe_weights = {
            '1d': 0.35,   # 日线权重
            '4h': 0.30,   # 4小时权重
            '1h': 0.20,   # 1小时权重
            '30m': 0.10,  # 30分钟权重
            '15m': 0.05   # 15分钟权重
        }

        # 优化后的技术指标权重
        self.analysis_weights = {
            'pattern': 0.20,      # K线形态
            'indicator': 0.40,    # 技术指标
            'trend': 0.25,        # 趋势分析
            'volume': 0.10,       # 成交量
            'market_data': 0.05   # 真实市场数据
        }

        # K线数据配置
        self.kline_periods = {
            '1d': 200,    # 日线200根 (约7个月数据)
            '4h': 168,    # 4小时168根 (约1个月数据)
            '1h': 168,    # 1小时168根 (约1周数据)
            '30m': 96,    # 30分钟96根 (约2天数据)
            '15m': 96     # 15分钟96根 (约1天数据)
        }

        # 真实市场数据缓存
        self.market_data_cache = {}
        self.cache_timestamp = None
        self.cache_duration = 300  # 5分钟缓存

        # 初始化真实K线数据提供器
        if REAL_KLINE_AVAILABLE:
            self.real_kline_provider = RealKlineDataProvider(use_proxy=self.use_proxy)
            log("✅ 真实K线数据提供器初始化成功")
        else:
            self.real_kline_provider = None
            log("⚠️ 真实K线数据提供器不可用，将使用API数据源")

        # 数据源验证 - 确保所有数据源都标记为真实数据
        real_data_sources = [name for name, config in self.data_sources.items() if config.get('real_data', False)]
        primary_source = next((name for name, config in self.data_sources.items() if config.get('priority') == 1), 'unknown')
        backup_sources = [name for name, config in self.data_sources.items() if config.get('priority', 999) > 1]

        log(f"✅ 已配置 {len(real_data_sources)} 个真实数据源")
        log(f"🎯 主数据源: {primary_source.upper()}")
        log(f"🔄 备用数据源: {', '.join(backup_sources).upper()}")
        log("📊 数据获取策略: 主数据源优先，失败时自动切换备用数据源")
        log("🎯 重要: 所有数据源均使用真实交易所数据，禁用模拟数据")

        self._init_database()
        log("🚀 终极版加密货币数据抓取器初始化完成 (v3.2 with Real Kline Data)")
        log(f"当前速度模式: {self.speed_modes[self.current_speed_mode]['name']}")
        log(f"代理状态: {'启用' if self.use_proxy else '禁用'}")
        if self.use_proxy:
            log(f"SSR状态: {'✅ 已连接' if self.ssr_connected else '❌ 未连接'}")
        log("✅ 已集成数据下载 + 真实K线 + 形态分析 + 技术指标 + 多周期确认功能")
        log("✅ 支持5个时间周期、15种K线形态、10+技术指标")
        log("✅ 支持SSR代理，可稳定访问国外数据源")
        log("🎯 重要更新: 已使用真实交易所K线数据，确保分析准确性")

    def _setup_signal_handlers(self):
        """设置信号处理器"""
        try:
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
        except Exception as e:
            log(f"⚠️ 信号处理器设置失败: {e}")

    def _signal_handler(self, signum, frame):
        """信号处理函数"""
        log(f"\n🛑 接收到中断信号 ({signum})，正在优雅退出...")
        self.scheduler_config['stop_requested'] = True

        if self.scheduler_config['is_running']:
            log("⏹️ 正在停止自动化调度...")
            self._stop_scheduler()

        log("👋 程序已安全退出")
        sys.exit(0)

    def _load_wechat_webhooks(self):
        """加载微信机器人配置"""
        try:
            # 首先尝试从wechat_bots_config.json加载
            if os.path.exists(self.wechat_config_file):
                with open(self.wechat_config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                bots = config.get('bots', [])
                if bots:
                    log(f"✅ 微信机器人配置加载成功: {len(bots)} 个机器人")
                    # 确保所有机器人都有必需的字段
                    for bot in bots:
                        if 'id' not in bot:
                            bot['id'] = f"bot_{len(bots):03d}"
                        if 'success_count' not in bot:
                            bot['success_count'] = 0
                        if 'error_count' not in bot:
                            bot['error_count'] = 0
                        if 'created_time' not in bot:
                            bot['created_time'] = datetime.now().isoformat()
                    return bots

            # 然后尝试从ssr_config.json中的wechat_webhooks加载
            if hasattr(self, 'ssr_config') and self.ssr_config:
                wechat_config = self.ssr_config.get('wechat_webhooks', {})
                if wechat_config.get('enabled', False) and wechat_config.get('robots'):
                    robots = wechat_config['robots']
                    log(f"✅ 从SSR配置加载微信机器人: {len(robots)} 个机器人")

                    # 转换格式以兼容现有代码
                    bots = []
                    for i, robot in enumerate(robots):
                        bot = {
                            'id': f"bot_{i+1:03d}",
                            'name': robot.get('name', f'机器人{i+1}'),
                            'webhook_url': robot.get('webhook_url', ''),
                            'enabled': robot.get('enabled', True),
                            'description': robot.get('description', ''),
                            'created_time': datetime.now().isoformat(),
                            'last_used': None,
                            'success_count': 0,
                            'error_count': 0
                        }
                        bots.append(bot)

                    # 保存到独立的配置文件
                    self._save_wechat_config({'version': '1.0', 'bots': bots})
                    return bots

            # 如果都没有，创建默认配置
            log("⚠️ 未找到微信机器人配置，创建默认配置")
            default_config = self._get_default_wechat_config()
            self._save_wechat_config(default_config)
            return default_config.get('bots', [])

        except Exception as e:
            log(f"❌ 加载微信机器人配置失败: {e}")
            return self._get_default_wechat_config().get('bots', [])

    def _get_default_wechat_config(self):
        """获取默认微信机器人配置"""
        return {
            "version": "1.0",
            "created_time": datetime.now().isoformat(),
            "bots": [
                {
                    "id": "bot_001",
                    "name": "户部尚赢量化平台",
                    "webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=69db19ba-d1af-422a-b0cf-19f21cd5b5fc",
                    "enabled": True,
                    "description": "主要推送机器人",
                    "created_time": datetime.now().isoformat(),
                    "last_used": None,
                    "success_count": 0,
                    "error_count": 0
                },
                {
                    "id": "bot_002",
                    "name": "企业微信btc机器人",
                    "webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=52d1b8a8-a88a-4d41-9664-f1268da20985",
                    "enabled": True,
                    "description": "BTC专用推送机器人",
                    "created_time": datetime.now().isoformat(),
                    "last_used": None,
                    "success_count": 0,
                    "error_count": 0
                }
            ]
        }

    def _save_wechat_config(self, config):
        """保存微信机器人配置"""
        try:
            with open(self.wechat_config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            log(f"✅ 微信机器人配置保存成功")
        except Exception as e:
            log(f"❌ 保存微信机器人配置失败: {e}")

    def _update_wechat_webhooks(self):
        """更新微信机器人配置"""
        try:
            config = {
                "version": "1.0",
                "updated_time": datetime.now().isoformat(),
                "bots": self.wechat_webhooks
            }
            self._save_wechat_config(config)
        except Exception as e:
            log(f"❌ 更新微信机器人配置失败: {e}")

    def add_wechat_bot(self, name: str, webhook_url: str, description: str = ""):
        """添加新的微信机器人"""
        try:
            new_bot = {
                "id": f"bot_{len(self.wechat_webhooks) + 1:03d}",
                "name": name,
                "webhook_url": webhook_url,
                "enabled": True,
                "description": description,
                "created_time": datetime.now().isoformat(),
                "last_used": None,
                "success_count": 0,
                "error_count": 0
            }

            self.wechat_webhooks.append(new_bot)
            self._update_wechat_webhooks()
            log(f"✅ 新增微信机器人: {name}")
            return True
        except Exception as e:
            log(f"❌ 添加微信机器人失败: {e}")
            return False

    def remove_wechat_bot(self, bot_id: str):
        """删除微信机器人"""
        try:
            self.wechat_webhooks = [bot for bot in self.wechat_webhooks if bot['id'] != bot_id]
            self._update_wechat_webhooks()
            log(f"✅ 删除微信机器人: {bot_id}")
            return True
        except Exception as e:
            log(f"❌ 删除微信机器人失败: {e}")
            return False

    def toggle_wechat_bot(self, bot_id: str):
        """启用/禁用微信机器人"""
        try:
            for bot in self.wechat_webhooks:
                if bot['id'] == bot_id:
                    bot['enabled'] = not bot['enabled']
                    status = "启用" if bot['enabled'] else "禁用"
                    log(f"✅ {status}微信机器人: {bot['name']}")
                    self._update_wechat_webhooks()
                    return True
            log(f"❌ 未找到机器人: {bot_id}")
            return False
        except Exception as e:
            log(f"❌ 切换机器人状态失败: {e}")
            return False

    def test_wechat_bot(self, bot_id: str):
        """测试单个微信机器人连接"""
        try:
            bot = next((b for b in self.wechat_webhooks if b['id'] == bot_id), None)
            if not bot:
                log(f"❌ 未找到机器人: {bot_id}")
                return False

            test_message = {
                "msgtype": "text",
                "text": {
                    "content": f"🧪 机器人连接测试\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n机器人: {bot['name']}\n状态: 连接正常 ✅"
                }
            }

            response = self.session.post(
                bot['webhook_url'],
                json=test_message,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    log(f"✅ 机器人 {bot['name']} 连接测试成功")
                    bot['success_count'] = bot.get('success_count', 0) + 1
                    bot['last_used'] = datetime.now().isoformat()
                    self._update_wechat_webhooks()
                    return True
                else:
                    log(f"❌ 机器人 {bot['name']} 测试失败: {result.get('errmsg', '未知错误')}")
                    bot['error_count'] = bot.get('error_count', 0) + 1
                    self._update_wechat_webhooks()
                    return False
            else:
                log(f"❌ 机器人 {bot['name']} 连接失败: HTTP {response.status_code}")
                bot['error_count'] = bot.get('error_count', 0) + 1
                self._update_wechat_webhooks()
                return False

        except Exception as e:
            log(f"❌ 测试机器人连接失败: {e}")
            return False

    def _start_scheduler(self, function_name: str, function_callable, run_mode: str, interval_minutes: int = 60):
        """启动自动化调度器"""
        try:
            if self.scheduler_config['is_running']:
                log("⚠️ 调度器已在运行中")
                return False

            self.scheduler_config.update({
                'is_running': True,
                'run_mode': run_mode,
                'interval_minutes': interval_minutes,
                'run_count': 0,
                'start_time': datetime.now(),
                'stop_requested': False,
                'current_function': function_name
            })

            if run_mode == 'single':
                # 单次运行
                log(f"🚀 开始单次运行: {function_name}")
                self._execute_function(function_callable)
                self._stop_scheduler()

            elif run_mode == 'infinite':
                # 无限循环运行
                log(f"🔄 开始无限循环运行: {function_name}")
                self.scheduler_config['scheduler_thread'] = threading.Thread(
                    target=self._infinite_loop_runner,
                    args=(function_callable,),
                    daemon=True
                )
                self.scheduler_config['scheduler_thread'].start()

            elif run_mode == 'scheduled':
                # 定时运行
                log(f"⏰ 开始定时运行: {function_name} (间隔: {interval_minutes}分钟)")
                self.scheduler_config['next_run_time'] = datetime.now() + timedelta(minutes=interval_minutes)
                self.scheduler_config['scheduler_thread'] = threading.Thread(
                    target=self._scheduled_runner,
                    args=(function_callable, interval_minutes),
                    daemon=True
                )
                self.scheduler_config['scheduler_thread'].start()

            return True

        except Exception as e:
            log(f"❌ 启动调度器失败: {e}")
            self._stop_scheduler()
            return False

    def _stop_scheduler(self):
        """停止自动化调度器"""
        try:
            self.scheduler_config.update({
                'is_running': False,
                'stop_requested': True,
                'next_run_time': None
            })

            if self.scheduler_config.get('scheduler_thread'):
                # 等待线程结束
                if self.scheduler_config['scheduler_thread'].is_alive():
                    log("⏹️ 等待调度线程结束...")
                    self.scheduler_config['scheduler_thread'].join(timeout=5)

                self.scheduler_config['scheduler_thread'] = None

            log("✅ 自动化调度器已停止")

        except Exception as e:
            log(f"❌ 停止调度器失败: {e}")

    def _infinite_loop_runner(self, function_callable):
        """无限循环运行器"""
        try:
            while not self.scheduler_config['stop_requested']:
                try:
                    self._execute_function(function_callable)
                    self.scheduler_config['run_count'] += 1

                    if not self.scheduler_config['stop_requested']:
                        log(f"⏳ 等待下次运行... (已运行 {self.scheduler_config['run_count']} 次)")
                        # 短暂休息，允许中断
                        for _ in range(30):  # 30秒检查一次
                            if self.scheduler_config['stop_requested']:
                                break
                            time.sleep(1)

                except Exception as e:
                    log(f"❌ 执行函数时出错: {e}")
                    log("⏳ 等待30秒后重试...")
                    time.sleep(30)

        except Exception as e:
            log(f"❌ 无限循环运行器异常: {e}")
        finally:
            log("🔄 无限循环运行器已退出")

    def _scheduled_runner(self, function_callable, interval_minutes):
        """定时运行器"""
        try:
            while not self.scheduler_config['stop_requested']:
                try:
                    # 等待到下次运行时间
                    while datetime.now() < self.scheduler_config['next_run_time']:
                        if self.scheduler_config['stop_requested']:
                            return

                        # 显示倒计时
                        remaining = self.scheduler_config['next_run_time'] - datetime.now()
                        remaining_seconds = int(remaining.total_seconds())

                        if remaining_seconds > 0:
                            hours, remainder = divmod(remaining_seconds, 3600)
                            minutes, seconds = divmod(remainder, 60)

                            if remaining_seconds % 60 == 0:  # 每分钟显示一次
                                if hours > 0:
                                    log(f"⏰ 下次运行倒计时: {hours}小时{minutes}分钟 (已运行 {self.scheduler_config['run_count']} 次)")
                                else:
                                    log(f"⏰ 下次运行倒计时: {minutes}分钟{seconds}秒 (已运行 {self.scheduler_config['run_count']} 次)")

                        time.sleep(1)

                    # 执行函数
                    if not self.scheduler_config['stop_requested']:
                        self._execute_function(function_callable)
                        self.scheduler_config['run_count'] += 1

                        # 设置下次运行时间
                        self.scheduler_config['next_run_time'] = datetime.now() + timedelta(minutes=interval_minutes)

                except Exception as e:
                    log(f"❌ 定时执行函数时出错: {e}")
                    # 设置下次运行时间（即使出错也要继续）
                    self.scheduler_config['next_run_time'] = datetime.now() + timedelta(minutes=interval_minutes)

        except Exception as e:
            log(f"❌ 定时运行器异常: {e}")
        finally:
            log("⏰ 定时运行器已退出")

    def _execute_function(self, function_callable):
        """执行函数并处理异常"""
        try:
            start_time = datetime.now()
            log(f"🚀 开始执行: {self.scheduler_config['current_function']} ({start_time.strftime('%Y-%m-%d %H:%M:%S')})")

            # 执行函数
            result = function_callable()

            end_time = datetime.now()
            duration = end_time - start_time
            log(f"✅ 执行完成: {self.scheduler_config['current_function']} (耗时: {duration})")

            return result

        except Exception as e:
            log(f"❌ 函数执行失败: {e}")
            log(f"📍 错误详情: {traceback.format_exc()}")
            return None

    def get_scheduler_status(self):
        """获取调度器状态"""
        if not self.scheduler_config['is_running']:
            return "⏹️ 未运行"

        status_info = []
        status_info.append(f"🔄 运行中")
        status_info.append(f"模式: {self.run_modes[self.scheduler_config['run_mode']]['name']}")
        status_info.append(f"功能: {self.scheduler_config['current_function']}")
        status_info.append(f"已运行: {self.scheduler_config['run_count']} 次")

        if self.scheduler_config['start_time']:
            runtime = datetime.now() - self.scheduler_config['start_time']
            hours, remainder = divmod(int(runtime.total_seconds()), 3600)
            minutes, seconds = divmod(remainder, 60)
            status_info.append(f"运行时长: {hours}小时{minutes}分钟")

        if self.scheduler_config['next_run_time'] and self.scheduler_config['run_mode'] == 'scheduled':
            remaining = self.scheduler_config['next_run_time'] - datetime.now()
            if remaining.total_seconds() > 0:
                remaining_minutes = int(remaining.total_seconds() / 60)
                status_info.append(f"下次运行: {remaining_minutes}分钟后")

        return " | ".join(status_info)

    def show_run_mode_selection(self, function_name: str):
        """显示运行模式选择界面"""
        try:
            print(f"\n{'='*60}")
            print(f"🚀 {function_name} - 运行模式选择")
            print(f"{'='*60}")

            print("\n请选择运行模式:")
            for key, mode in self.run_modes.items():
                print(f"  {key[0].upper()}. {mode['name']} - {mode['desc']}")

            print(f"\n当前调度器状态: {self.get_scheduler_status()}")

            while True:
                choice = input("\n请选择运行模式 (S/I/C 或 回车使用单次运行): ").strip().upper()

                if choice == '' or choice == 'S':
                    return 'single', 0
                elif choice == 'I':
                    return 'infinite', 0
                elif choice == 'C':
                    # 选择时间间隔
                    interval = self._select_time_interval()
                    if interval:
                        return 'scheduled', interval
                else:
                    print("❌ 无效选择，请重新输入")

        except KeyboardInterrupt:
            print("\n⏹️ 用户取消操作")
            return None, None
        except Exception as e:
            log(f"❌ 运行模式选择失败: {e}")
            return None, None

    def _select_time_interval(self):
        """选择时间间隔"""
        try:
            print(f"\n{'='*40}")
            print("⏰ 定时运行 - 时间间隔选择")
            print(f"{'='*40}")

            print("\n可选时间间隔:")
            for key, interval in self.interval_options.items():
                print(f"  {key}. {interval['name']}")

            while True:
                choice = input("\n请选择时间间隔 (输入数字): ").strip()

                if choice in self.interval_options:
                    selected = self.interval_options[choice]
                    print(f"✅ 已选择: {selected['name']}")
                    return selected['minutes']
                else:
                    print("❌ 无效选择，请重新输入")

        except KeyboardInterrupt:
            print("\n⏹️ 用户取消操作")
            return None
        except Exception as e:
            log(f"❌ 时间间隔选择失败: {e}")
            return None

    def show_push_target_selection(self):
        """显示推送目标选择界面"""
        try:
            print(f"\n{'='*60}")
            print("📱 微信推送目标选择")
            print(f"{'='*60}")

            # 显示当前配置的机器人
            enabled_bots = [bot for bot in self.wechat_webhooks if bot.get('enabled', True)]

            if not enabled_bots:
                print("⚠️ 没有可用的微信机器人，请先配置")
                return []

            print(f"\n当前可用机器人 ({len(enabled_bots)} 个):")
            for i, bot in enumerate(enabled_bots, 1):
                status_info = []
                if bot.get('success_count', 0) > 0:
                    status_info.append(f"成功: {bot['success_count']}")
                if bot.get('error_count', 0) > 0:
                    status_info.append(f"失败: {bot['error_count']}")

                status_str = f" ({', '.join(status_info)})" if status_info else ""
                print(f"  {i}. {bot['name']}{status_str}")
                print(f"     {bot.get('description', '无描述')}")

            print(f"\n推送模式选择:")
            for key, mode in self.push_target_modes.items():
                print(f"  {key[0].upper()}. {mode['name']} - {mode['desc']}")

            while True:
                choice = input("\n请选择推送模式 (A/S/M/C 或 回车使用默认): ").strip().upper()

                if choice == '' or choice == 'A':
                    print(f"✅ 选择默认推送: 推送到所有 {len(enabled_bots)} 个机器人")
                    return enabled_bots

                elif choice == 'S':
                    return self._select_single_bot(enabled_bots)

                elif choice == 'M':
                    return self._select_multiple_bots(enabled_bots)

                elif choice == 'C':
                    return self._add_custom_webhook()

                else:
                    print("❌ 无效选择，请重新输入")

        except KeyboardInterrupt:
            print("\n⏹️ 用户取消操作")
            return []
        except Exception as e:
            log(f"❌ 推送目标选择失败: {e}")
            return []

    def _select_single_bot(self, available_bots):
        """选择单个机器人"""
        try:
            print(f"\n选择单个推送目标:")
            for i, bot in enumerate(available_bots, 1):
                print(f"  {i}. {bot['name']}")

            while True:
                choice = input(f"\n请选择机器人 (1-{len(available_bots)}): ").strip()

                try:
                    index = int(choice) - 1
                    if 0 <= index < len(available_bots):
                        selected_bot = available_bots[index]
                        print(f"✅ 已选择: {selected_bot['name']}")
                        return [selected_bot]
                    else:
                        print("❌ 无效选择，请重新输入")
                except ValueError:
                    print("❌ 请输入有效数字")

        except KeyboardInterrupt:
            print("\n⏹️ 用户取消操作")
            return []

    def _select_multiple_bots(self, available_bots):
        """选择多个机器人"""
        try:
            print(f"\n选择多个推送目标 (用逗号分隔，如: 1,3):")
            for i, bot in enumerate(available_bots, 1):
                print(f"  {i}. {bot['name']}")

            while True:
                choice = input(f"\n请选择机器人 (1-{len(available_bots)}): ").strip()

                try:
                    if not choice:
                        print("❌ 请输入选择")
                        continue

                    indices = [int(x.strip()) - 1 for x in choice.split(',')]
                    selected_bots = []

                    for index in indices:
                        if 0 <= index < len(available_bots):
                            selected_bots.append(available_bots[index])
                        else:
                            print(f"❌ 无效选择: {index + 1}")
                            break
                    else:
                        if selected_bots:
                            bot_names = [bot['name'] for bot in selected_bots]
                            print(f"✅ 已选择 {len(selected_bots)} 个机器人: {', '.join(bot_names)}")
                            return selected_bots

                except ValueError:
                    print("❌ 请输入有效格式，如: 1,2,3")

        except KeyboardInterrupt:
            print("\n⏹️ 用户取消操作")
            return []

    def _add_custom_webhook(self):
        """添加自定义webhook"""
        try:
            print(f"\n自定义推送目标:")

            name = input("请输入机器人名称: ").strip()
            if not name:
                print("❌ 机器人名称不能为空")
                return []

            webhook_url = input("请输入Webhook URL: ").strip()
            if not webhook_url or not webhook_url.startswith('http'):
                print("❌ 无效的Webhook URL")
                return []

            description = input("请输入描述 (可选): ").strip()

            # 创建临时机器人配置
            custom_bot = {
                "id": f"custom_{int(time.time())}",
                "name": name,
                "webhook_url": webhook_url,
                "enabled": True,
                "description": description or "临时自定义机器人",
                "created_time": datetime.now().isoformat(),
                "last_used": None,
                "success_count": 0,
                "error_count": 0
            }

            # 询问是否保存
            save_choice = input("\n是否保存此机器人到配置中？(y/n): ").strip().lower()
            if save_choice == 'y':
                self.wechat_webhooks.append(custom_bot)
                self._update_wechat_webhooks()
                print(f"✅ 机器人已保存: {name}")
            else:
                print(f"✅ 临时使用机器人: {name}")

            return [custom_bot]

        except KeyboardInterrupt:
            print("\n⏹️ 用户取消操作")
            return []
        except Exception as e:
            log(f"❌ 添加自定义webhook失败: {e}")
            return []

    def show_wechat_bot_management(self):
        """显示微信机器人管理界面"""
        try:
            while True:
                print(f"\n{'='*60}")
                print("🤖 微信机器人管理")
                print(f"{'='*60}")

                # 显示当前机器人列表
                if self.wechat_webhooks:
                    print(f"\n当前配置的机器人 ({len(self.wechat_webhooks)} 个):")
                    for i, bot in enumerate(self.wechat_webhooks, 1):
                        status = "✅ 启用" if bot.get('enabled', True) else "❌ 禁用"
                        success_count = bot.get('success_count', 0)
                        error_count = bot.get('error_count', 0)
                        last_used = bot.get('last_used')
                        last_used_str = datetime.fromisoformat(last_used).strftime('%m-%d %H:%M') if last_used else "从未使用"

                        print(f"  {i}. {bot['name']} {status}")
                        print(f"     ID: {bot['id']}")
                        print(f"     描述: {bot.get('description', '无描述')}")
                        print(f"     统计: 成功{success_count}次, 失败{error_count}次")
                        print(f"     最后使用: {last_used_str}")
                        print(f"     URL: {bot['webhook_url'][:50]}...")
                        print()
                else:
                    print("\n⚠️ 暂无配置的机器人")

                # 显示操作菜单
                print("操作选项:")
                print("  1. 添加新机器人")
                print("  2. 编辑机器人")
                print("  3. 删除机器人")
                print("  4. 启用/禁用机器人")
                print("  5. 测试机器人连接")
                print("  6. 查看机器人详情")
                print("  0. 返回主菜单")

                choice = input("\n请选择操作 (0-6): ").strip()

                if choice == '0':
                    break
                elif choice == '1':
                    self._add_wechat_bot_interactive()
                elif choice == '2':
                    self._edit_wechat_bot_interactive()
                elif choice == '3':
                    self._delete_wechat_bot_interactive()
                elif choice == '4':
                    self._toggle_wechat_bot_interactive()
                elif choice == '5':
                    self._test_wechat_bot_interactive()
                elif choice == '6':
                    self._show_wechat_bot_details()
                else:
                    print("❌ 无效选择，请重新输入")

        except KeyboardInterrupt:
            print("\n⏹️ 返回主菜单")
        except Exception as e:
            log(f"❌ 微信机器人管理失败: {e}")

    def _add_wechat_bot_interactive(self):
        """交互式添加微信机器人"""
        try:
            print(f"\n{'='*40}")
            print("➕ 添加新的微信机器人")
            print(f"{'='*40}")

            name = input("请输入机器人名称: ").strip()
            if not name:
                print("❌ 机器人名称不能为空")
                return

            webhook_url = input("请输入Webhook URL: ").strip()
            if not webhook_url or not webhook_url.startswith('http'):
                print("❌ 无效的Webhook URL")
                return

            description = input("请输入描述 (可选): ").strip()

            if self.add_wechat_bot(name, webhook_url, description):
                print(f"✅ 机器人添加成功: {name}")

                # 询问是否测试连接
                test_choice = input("是否立即测试连接？(y/n): ").strip().lower()
                if test_choice == 'y':
                    bot_id = self.wechat_webhooks[-1]['id']
                    self.test_wechat_bot(bot_id)
            else:
                print("❌ 机器人添加失败")

        except KeyboardInterrupt:
            print("\n⏹️ 操作取消")
        except Exception as e:
            log(f"❌ 添加机器人失败: {e}")

    def _edit_wechat_bot_interactive(self):
        """交互式编辑微信机器人"""
        try:
            if not self.wechat_webhooks:
                print("⚠️ 暂无可编辑的机器人")
                return

            print(f"\n{'='*40}")
            print("✏️ 编辑微信机器人")
            print(f"{'='*40}")

            # 选择要编辑的机器人
            print("\n选择要编辑的机器人:")
            for i, bot in enumerate(self.wechat_webhooks, 1):
                print(f"  {i}. {bot['name']}")

            choice = input(f"\n请选择机器人 (1-{len(self.wechat_webhooks)}): ").strip()

            try:
                index = int(choice) - 1
                if 0 <= index < len(self.wechat_webhooks):
                    bot = self.wechat_webhooks[index]

                    print(f"\n编辑机器人: {bot['name']}")
                    print("(直接回车保持原值)")

                    # 编辑名称
                    new_name = input(f"名称 [{bot['name']}]: ").strip()
                    if new_name:
                        bot['name'] = new_name

                    # 编辑URL
                    new_url = input(f"Webhook URL [{bot['webhook_url'][:50]}...]: ").strip()
                    if new_url:
                        bot['webhook_url'] = new_url

                    # 编辑描述
                    new_desc = input(f"描述 [{bot.get('description', '')}]: ").strip()
                    if new_desc:
                        bot['description'] = new_desc

                    self._update_wechat_webhooks()
                    print(f"✅ 机器人编辑成功: {bot['name']}")

                else:
                    print("❌ 无效选择")
            except ValueError:
                print("❌ 请输入有效数字")

        except KeyboardInterrupt:
            print("\n⏹️ 操作取消")
        except Exception as e:
            log(f"❌ 编辑机器人失败: {e}")

    def _delete_wechat_bot_interactive(self):
        """交互式删除微信机器人"""
        try:
            if not self.wechat_webhooks:
                print("⚠️ 暂无可删除的机器人")
                return

            print(f"\n{'='*40}")
            print("🗑️ 删除微信机器人")
            print(f"{'='*40}")

            # 选择要删除的机器人
            print("\n选择要删除的机器人:")
            for i, bot in enumerate(self.wechat_webhooks, 1):
                print(f"  {i}. {bot['name']}")

            choice = input(f"\n请选择机器人 (1-{len(self.wechat_webhooks)}): ").strip()

            try:
                index = int(choice) - 1
                if 0 <= index < len(self.wechat_webhooks):
                    bot = self.wechat_webhooks[index]

                    # 确认删除
                    confirm = input(f"确认删除机器人 '{bot['name']}'？(y/n): ").strip().lower()
                    if confirm == 'y':
                        if self.remove_wechat_bot(bot['id']):
                            print(f"✅ 机器人删除成功: {bot['name']}")
                        else:
                            print("❌ 机器人删除失败")
                    else:
                        print("⏹️ 删除操作取消")
                else:
                    print("❌ 无效选择")
            except ValueError:
                print("❌ 请输入有效数字")

        except KeyboardInterrupt:
            print("\n⏹️ 操作取消")
        except Exception as e:
            log(f"❌ 删除机器人失败: {e}")

    def _toggle_wechat_bot_interactive(self):
        """交互式启用/禁用微信机器人"""
        try:
            if not self.wechat_webhooks:
                print("⚠️ 暂无可操作的机器人")
                return

            print(f"\n{'='*40}")
            print("🔄 启用/禁用微信机器人")
            print(f"{'='*40}")

            # 选择要操作的机器人
            print("\n选择要操作的机器人:")
            for i, bot in enumerate(self.wechat_webhooks, 1):
                status = "✅ 启用" if bot.get('enabled', True) else "❌ 禁用"
                print(f"  {i}. {bot['name']} ({status})")

            choice = input(f"\n请选择机器人 (1-{len(self.wechat_webhooks)}): ").strip()

            try:
                index = int(choice) - 1
                if 0 <= index < len(self.wechat_webhooks):
                    bot = self.wechat_webhooks[index]

                    if self.toggle_wechat_bot(bot['id']):
                        new_status = "启用" if bot['enabled'] else "禁用"
                        print(f"✅ 机器人状态已更新: {bot['name']} -> {new_status}")
                    else:
                        print("❌ 状态更新失败")
                else:
                    print("❌ 无效选择")
            except ValueError:
                print("❌ 请输入有效数字")

        except KeyboardInterrupt:
            print("\n⏹️ 操作取消")
        except Exception as e:
            log(f"❌ 切换机器人状态失败: {e}")

    def _test_wechat_bot_interactive(self):
        """交互式测试微信机器人"""
        try:
            if not self.wechat_webhooks:
                print("⚠️ 暂无可测试的机器人")
                return

            print(f"\n{'='*40}")
            print("🧪 测试微信机器人连接")
            print(f"{'='*40}")

            # 选择要测试的机器人
            print("\n选择要测试的机器人:")
            for i, bot in enumerate(self.wechat_webhooks, 1):
                status = "✅ 启用" if bot.get('enabled', True) else "❌ 禁用"
                print(f"  {i}. {bot['name']} ({status})")

            print(f"  0. 测试所有启用的机器人")

            choice = input(f"\n请选择 (0-{len(self.wechat_webhooks)}): ").strip()

            if choice == '0':
                # 测试所有启用的机器人
                enabled_bots = [bot for bot in self.wechat_webhooks if bot.get('enabled', True)]
                if enabled_bots:
                    print(f"\n🧪 开始测试 {len(enabled_bots)} 个启用的机器人...")
                    success_count = 0
                    for bot in enabled_bots:
                        if self.test_wechat_bot(bot['id']):
                            success_count += 1
                    print(f"\n📊 测试完成: {success_count}/{len(enabled_bots)} 个机器人连接正常")
                else:
                    print("⚠️ 没有启用的机器人")
            else:
                try:
                    index = int(choice) - 1
                    if 0 <= index < len(self.wechat_webhooks):
                        bot = self.wechat_webhooks[index]
                        print(f"\n🧪 测试机器人: {bot['name']}")
                        self.test_wechat_bot(bot['id'])
                    else:
                        print("❌ 无效选择")
                except ValueError:
                    print("❌ 请输入有效数字")

        except KeyboardInterrupt:
            print("\n⏹️ 操作取消")
        except Exception as e:
            log(f"❌ 测试机器人失败: {e}")

    def _show_wechat_bot_details(self):
        """显示微信机器人详情"""
        try:
            if not self.wechat_webhooks:
                print("⚠️ 暂无机器人详情可查看")
                return

            print(f"\n{'='*40}")
            print("📋 微信机器人详情")
            print(f"{'='*40}")

            # 选择要查看的机器人
            print("\n选择要查看的机器人:")
            for i, bot in enumerate(self.wechat_webhooks, 1):
                print(f"  {i}. {bot['name']}")

            choice = input(f"\n请选择机器人 (1-{len(self.wechat_webhooks)}): ").strip()

            try:
                index = int(choice) - 1
                if 0 <= index < len(self.wechat_webhooks):
                    bot = self.wechat_webhooks[index]

                    print(f"\n{'='*50}")
                    print(f"🤖 机器人详情: {bot['name']}")
                    print(f"{'='*50}")

                    print(f"ID: {bot['id']}")
                    print(f"名称: {bot['name']}")
                    print(f"状态: {'✅ 启用' if bot.get('enabled', True) else '❌ 禁用'}")
                    print(f"描述: {bot.get('description', '无描述')}")
                    print(f"Webhook URL: {bot['webhook_url']}")

                    created_time = bot.get('created_time')
                    if created_time:
                        created_str = datetime.fromisoformat(created_time).strftime('%Y-%m-%d %H:%M:%S')
                        print(f"创建时间: {created_str}")

                    last_used = bot.get('last_used')
                    if last_used:
                        last_used_str = datetime.fromisoformat(last_used).strftime('%Y-%m-%d %H:%M:%S')
                        print(f"最后使用: {last_used_str}")
                    else:
                        print(f"最后使用: 从未使用")

                    success_count = bot.get('success_count', 0)
                    error_count = bot.get('error_count', 0)
                    total_count = success_count + error_count

                    print(f"使用统计:")
                    print(f"  成功次数: {success_count}")
                    print(f"  失败次数: {error_count}")
                    print(f"  总计次数: {total_count}")

                    if total_count > 0:
                        success_rate = success_count / total_count * 100
                        print(f"  成功率: {success_rate:.1f}%")

                    input("\n按回车键继续...")

                else:
                    print("❌ 无效选择")
            except ValueError:
                print("❌ 请输入有效数字")

        except KeyboardInterrupt:
            print("\n⏹️ 操作取消")
        except Exception as e:
            log(f"❌ 查看机器人详情失败: {e}")

    def run_enhanced_selection_menu(self):
        """运行增强的选币范围筛选菜单 - 支持自动化运行"""
        try:
            while True:
                print(f"\n{'='*80}")
                print("🎯 选币范围筛选 - 自动化运行增强版")
                print(f"{'='*80}")

                # 显示当前调度器状态
                scheduler_status = self.get_scheduler_status()
                print(f"🤖 当前调度状态: {scheduler_status}")

                # 显示选币范围选项
                self.show_selection_menu()

                print(f"\n📋 操作选项:")
                print("S. 选择筛选范围并配置运行模式")
                print("T. 停止当前自动化运行")
                print("R. 返回主菜单")

                choice = input("\n请选择操作 (S/T/R): ").strip().upper()

                if choice == 'S':
                    # 选择筛选范围
                    selection_choice = input("\n请选择筛选范围 (1-15): ").strip()
                    if selection_choice in self.selection_ranges:
                        # 选择运行模式
                        run_mode, interval = self.show_run_mode_selection(
                            f"选币范围筛选 - {self.selection_ranges[selection_choice]}"
                        )

                        if run_mode:
                            # 选择推送目标
                            target_bots = self.show_push_target_selection()

                            if target_bots:
                                # 创建执行函数
                                def execute_selection():
                                    return self.run_selection_process_with_push(selection_choice, target_bots)

                                # 启动调度器
                                function_name = f"选币筛选-{self.selection_ranges[selection_choice]}"
                                success = self._start_scheduler(function_name, execute_selection, run_mode, interval)

                                if success:
                                    if run_mode == 'single':
                                        print("✅ 单次运行完成")
                                        input("按回车键继续...")
                                    else:
                                        print(f"✅ 自动化调度已启动: {function_name}")
                                        print("💡 您可以继续使用其他功能，或选择 'T' 停止自动化运行")
                                        input("按回车键继续...")
                                else:
                                    print("❌ 调度器启动失败")
                                    input("按回车键继续...")
                            else:
                                print("⚠️ 未选择推送目标，操作取消")
                                input("按回车键继续...")
                        else:
                            print("⚠️ 未选择运行模式，操作取消")
                            input("按回车键继续...")
                    else:
                        print("❌ 无效的筛选范围选择")
                        input("按回车键继续...")

                elif choice == 'T':
                    if self.scheduler_config['is_running']:
                        print("⏹️ 正在停止自动化运行...")
                        self._stop_scheduler()
                        print("✅ 自动化运行已停止")
                    else:
                        print("⚠️ 当前没有运行中的自动化任务")
                    input("按回车键继续...")

                elif choice == 'R':
                    break

                else:
                    print("❌ 无效选择，请重新输入")
                    input("按回车键继续...")

        except KeyboardInterrupt:
            print("\n⏹️ 返回主菜单")
        except Exception as e:
            log(f"❌ 增强选币菜单失败: {e}")

    def run_selection_process_with_push(self, selection_choice: str, target_bots: List[Dict]):
        """运行选币流程并推送到指定机器人"""
        try:
            # 设置自动化模式标志，避免交互式询问
            self._automated_mode = True

            # 执行原有的选币流程
            result = self.run_selection_process(selection_choice)

            # 清除自动化模式标志
            if hasattr(self, '_automated_mode'):
                delattr(self, '_automated_mode')

            # 检查选币结果
            if result and result.get('success') and target_bots:
                selection_name = result.get('selection_name', '未知筛选')
                filtered_cryptos = result.get('filtered_cryptos', [])
                total_count = result.get('total_count', 0)

                log(f"📊 选币完成: {selection_name}, 找到 {total_count} 个符合条件的币种")

                if filtered_cryptos:
                    # 生成推送消息
                    push_message = self.generate_selection_push_message_from_data(
                        filtered_cryptos, selection_choice, selection_name
                    )

                    # 推送到指定机器人
                    success = self._send_to_multiple_wechat_bots(
                        push_message,
                        f"选币结果推送",
                        target_bots
                    )

                    if success:
                        log(f"✅ 选币结果已推送到 {len(target_bots)} 个机器人")
                    else:
                        log("❌ 选币结果推送失败")
                else:
                    log("⚠️ 筛选结果为空，跳过推送")
            elif result and not result.get('success'):
                log(f"❌ 选币流程失败: {result.get('error', '未知错误')}")
            elif not target_bots:
                log("⚠️ 没有推送目标，跳过推送")
            else:
                log("⚠️ 选币流程返回空结果")

            return result

        except Exception as e:
            log(f"❌ 选币流程执行失败: {e}")
            # 清除自动化模式标志
            if hasattr(self, '_automated_mode'):
                delattr(self, '_automated_mode')
            return None

    def get_latest_selection_results(self, limit: int = 10):
        """获取最新的选币结果"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT symbol, price, market_cap, volume_24h, price_change_24h,
                       selection_reason, timestamp
                FROM selection_results
                ORDER BY timestamp DESC
                LIMIT ?
            """, (limit,))

            results = cursor.fetchall()
            conn.close()

            return results

        except Exception as e:
            log(f"❌ 获取最新选币结果失败: {e}")
            return []

    def generate_selection_push_message(self, results: List, selection_choice: str):
        """生成选币结果推送消息"""
        try:
            selection_name = self.selection_ranges.get(selection_choice, "未知筛选")

            message = f"""# 🎯 选币筛选结果推送

📅 **推送时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🔍 **筛选类型**: {selection_name}
📊 **结果数量**: {len(results)} 个

## 📈 筛选结果

"""

            for i, result in enumerate(results[:5], 1):  # 只显示前5个
                symbol, price, market_cap, volume_24h, price_change_24h, reason, timestamp = result

                # 格式化数据
                price_str = f"${float(price):.6f}" if price else "N/A"
                market_cap_str = f"${float(market_cap):,.0f}" if market_cap else "N/A"
                volume_str = f"${float(volume_24h):,.0f}" if volume_24h else "N/A"
                change_str = f"{float(price_change_24h):+.2f}%" if price_change_24h else "N/A"

                message += f"""### {i}. **{symbol}**
- 💰 价格: {price_str}
- 📊 市值: {market_cap_str}
- 📈 24h成交量: {volume_str}
- 📉 24h涨跌: {change_str}
- 🎯 筛选原因: {reason}

"""

            if len(results) > 5:
                message += f"*... 还有 {len(results) - 5} 个结果，请查看完整报告*\n\n"

            message += f"""---
🤖 **终极版加密货币数据抓取器 v4.0**
⏰ **自动化调度系统** | 📱 **智能推送管理**"""

            return message

        except Exception as e:
            log(f"❌ 生成推送消息失败: {e}")
            return f"选币筛选完成，但消息生成失败: {e}"

    def generate_selection_push_message_from_data(self, filtered_cryptos: List[Dict], selection_choice: str, selection_name: str):
        """从筛选数据直接生成推送消息"""
        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            message = f"""# 🎯 选币筛选结果推送

📅 **推送时间**: {current_time}
🔍 **筛选类型**: {selection_name}
📊 **结果数量**: {len(filtered_cryptos)} 个
🤖 **推送状态**: 自动化推送

## 📈 筛选结果

"""

            # 显示前10个结果
            display_count = min(10, len(filtered_cryptos))

            for i, crypto in enumerate(filtered_cryptos[:display_count], 1):
                # 安全获取数据，避免KeyError
                symbol = crypto.get('symbol', 'N/A')
                name = crypto.get('name', 'Unknown')
                price = crypto.get('current_price', 0)
                market_cap = crypto.get('market_cap', 0)
                volume_24h = crypto.get('total_volume', 0)
                price_change_24h = crypto.get('price_change_percentage_24h', 0)

                # 格式化数据
                if price and price > 0:
                    if price >= 1:
                        price_str = f"${price:,.2f}"
                    elif price >= 0.01:
                        price_str = f"${price:.4f}"
                    else:
                        price_str = f"${price:.8f}"
                else:
                    price_str = "N/A"

                market_cap_str = f"${market_cap:,.0f}" if market_cap else "N/A"
                volume_str = f"${volume_24h:,.0f}" if volume_24h else "N/A"

                if price_change_24h is not None:
                    change_str = f"{price_change_24h:+.2f}%"
                else:
                    change_str = "N/A"

                # 生成筛选原因
                reason = self._generate_selection_reason(crypto, selection_choice)

                message += f"""### {i}. **{symbol}** ({name})
- 💰 价格: {price_str}
- 📊 市值: {market_cap_str}
- 📈 24h成交量: {volume_str}
- 📉 24h涨跌: {change_str}
- 🎯 筛选原因: {reason}

"""

            if len(filtered_cryptos) > display_count:
                message += f"*... 还有 {len(filtered_cryptos) - display_count} 个结果*\n\n"

            # 添加市场分析总结
            message += self._generate_market_summary(filtered_cryptos)

            message += f"""---
🤖 **终极版加密货币数据抓取器 v4.0**
⏰ **自动化调度系统** | 📱 **智能推送管理**

*本次推送由自动化调度系统生成*"""

            return message

        except Exception as e:
            log(f"❌ 生成推送消息失败: {e}")
            return f"选币筛选完成，找到 {len(filtered_cryptos) if filtered_cryptos else 0} 个结果，但消息生成失败: {e}"

    def _generate_selection_reason(self, crypto: Dict, selection_choice: str) -> str:
        """生成筛选原因"""
        try:
            selection_info = self.selection_ranges.get(selection_choice, {})
            selection_name = selection_info.get('name', '未知筛选')

            # 根据不同的筛选类型生成原因
            if '新上市' in selection_name:
                return "新上市币种，具有潜力"
            elif '主流币' in selection_name:
                return "主流币种，市值稳定"
            elif '热门' in selection_name:
                return "热门山寨币，关注度高"
            elif '小市值' in selection_name:
                return "小市值潜力币，成长空间大"
            elif 'DeFi' in selection_name:
                return "DeFi生态代币，去中心化金融"
            elif 'Layer1' in selection_name:
                return "Layer1公链，基础设施"
            elif 'Layer2' in selection_name:
                return "Layer2扩容，技术创新"
            elif 'Meme' in selection_name:
                return "Meme概念币，社区驱动"
            elif 'AI' in selection_name:
                return "AI概念币，人工智能"
            elif '高交易量' in selection_name:
                return "高交易量，市场活跃"
            elif '形态分析' in selection_name:
                return "技术形态良好，指标优秀"
            else:
                return f"符合{selection_name}筛选条件"

        except Exception as e:
            return "符合筛选条件"

    def _generate_market_summary(self, filtered_cryptos: List[Dict]) -> str:
        """生成市场分析总结"""
        try:
            if not filtered_cryptos:
                return ""

            # 计算统计数据
            total_count = len(filtered_cryptos)

            # 价格变化统计
            price_changes = [crypto.get('price_change_percentage_24h', 0) for crypto in filtered_cryptos if crypto.get('price_change_percentage_24h') is not None]

            if price_changes:
                avg_change = sum(price_changes) / len(price_changes)
                positive_count = len([x for x in price_changes if x > 0])
                negative_count = len([x for x in price_changes if x < 0])

                summary = f"""## 📊 市场分析总结
- 📈 **上涨币种**: {positive_count} 个 ({positive_count/total_count*100:.1f}%)
- 📉 **下跌币种**: {negative_count} 个 ({negative_count/total_count*100:.1f}%)
- 📊 **平均涨跌**: {avg_change:+.2f}%
- 🎯 **市场情绪**: {"乐观" if avg_change > 2 else "谨慎" if avg_change > -2 else "悲观"}

"""
                return summary
            else:
                return ""

        except Exception as e:
            log(f"⚠️ 生成市场总结失败: {e}")
            return ""

    def show_scheduler_management(self):
        """显示自动化调度管理界面"""
        try:
            while True:
                print(f"\n{'='*60}")
                print("⏰ 自动化调度管理")
                print(f"{'='*60}")

                # 显示当前状态
                scheduler_status = self.get_scheduler_status()
                print(f"🤖 当前状态: {scheduler_status}")

                if self.scheduler_config['is_running']:
                    print(f"\n📋 运行详情:")
                    print(f"   功能: {self.scheduler_config['current_function']}")
                    print(f"   模式: {self.run_modes[self.scheduler_config['run_mode']]['name']}")
                    print(f"   已运行: {self.scheduler_config['run_count']} 次")

                    if self.scheduler_config['start_time']:
                        runtime = datetime.now() - self.scheduler_config['start_time']
                        hours, remainder = divmod(int(runtime.total_seconds()), 3600)
                        minutes, seconds = divmod(remainder, 60)
                        print(f"   运行时长: {hours}小时{minutes}分钟")

                    if self.scheduler_config['next_run_time'] and self.scheduler_config['run_mode'] == 'scheduled':
                        remaining = self.scheduler_config['next_run_time'] - datetime.now()
                        if remaining.total_seconds() > 0:
                            remaining_minutes = int(remaining.total_seconds() / 60)
                            print(f"   下次运行: {remaining_minutes}分钟后")

                print(f"\n📋 管理选项:")
                if self.scheduler_config['is_running']:
                    print("1. ⏹️ 停止当前调度")
                    print("2. 📊 查看运行统计")
                    print("3. 🔄 重启调度器")
                else:
                    print("1. 🚀 快速启动调度")
                    print("2. 📊 查看历史统计")

                print("0. 返回主菜单")

                choice = input("\n请选择操作: ").strip()

                if choice == '0':
                    break
                elif choice == '1':
                    if self.scheduler_config['is_running']:
                        print("⏹️ 正在停止调度器...")
                        self._stop_scheduler()
                        print("✅ 调度器已停止")
                    else:
                        print("🚀 快速启动功能开发中...")
                        print("💡 请使用主菜单 -> 选币范围筛选来启动自动化运行")
                    input("按回车键继续...")

                elif choice == '2':
                    self._show_scheduler_statistics()
                    input("按回车键继续...")

                elif choice == '3' and self.scheduler_config['is_running']:
                    print("🔄 重启调度器功能开发中...")
                    input("按回车键继续...")

                else:
                    print("❌ 无效选择")
                    input("按回车键继续...")

        except KeyboardInterrupt:
            print("\n⏹️ 返回主菜单")
        except Exception as e:
            log(f"❌ 调度管理失败: {e}")

    def _show_scheduler_statistics(self):
        """显示调度器统计信息"""
        try:
            print(f"\n📊 调度器统计信息:")

            if self.scheduler_config['is_running']:
                print(f"   当前状态: 🔄 运行中")
                print(f"   运行功能: {self.scheduler_config['current_function']}")
                print(f"   运行模式: {self.run_modes[self.scheduler_config['run_mode']]['name']}")
                print(f"   已运行次数: {self.scheduler_config['run_count']}")

                if self.scheduler_config['start_time']:
                    start_time_str = self.scheduler_config['start_time'].strftime('%Y-%m-%d %H:%M:%S')
                    print(f"   启动时间: {start_time_str}")

                    runtime = datetime.now() - self.scheduler_config['start_time']
                    hours, remainder = divmod(int(runtime.total_seconds()), 3600)
                    minutes, seconds = divmod(remainder, 60)
                    print(f"   运行时长: {hours}小时{minutes}分钟{seconds}秒")

                if self.scheduler_config['run_mode'] == 'scheduled':
                    print(f"   运行间隔: {self.scheduler_config['interval_minutes']}分钟")

                    if self.scheduler_config['next_run_time']:
                        next_time_str = self.scheduler_config['next_run_time'].strftime('%Y-%m-%d %H:%M:%S')
                        print(f"   下次运行: {next_time_str}")
            else:
                print(f"   当前状态: ⏹️ 未运行")
                print(f"   历史运行次数: {self.scheduler_config['run_count']}")

            # 显示运行模式说明
            print(f"\n📋 可用运行模式:")
            for key, mode in self.run_modes.items():
                print(f"   {mode['name']}: {mode['desc']}")

            # 显示时间间隔选项
            print(f"\n⏰ 可用时间间隔:")
            for key, interval in self.interval_options.items():
                print(f"   {interval['name']}: {interval['minutes']}分钟")

        except Exception as e:
            log(f"❌ 显示调度器统计失败: {e}")

    def _load_ssr_config(self):
        """加载SSR配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                log(f"✅ SSR配置文件加载成功: {self.config_path}")

                # 验证配置完整性
                if self._validate_ssr_config(config):
                    log("✅ SSR配置验证通过")
                    return config
                else:
                    log("⚠️ SSR配置验证失败，使用默认配置")
                    return self._get_default_ssr_config()
            else:
                log(f"⚠️ SSR配置文件不存在: {self.config_path}，创建默认配置")
                default_config = self._get_default_ssr_config()
                self._save_ssr_config(default_config)
                return default_config
        except Exception as e:
            log(f"❌ 加载SSR配置文件失败: {e}")
            return self._get_default_ssr_config()

    def _validate_ssr_config(self, config):
        """验证SSR配置完整性"""
        try:
            # 检查必需的顶级键
            required_keys = ['ssr_servers', 'proxy_settings', 'fallback_settings']
            for key in required_keys:
                if key not in config:
                    log(f"❌ 缺少配置项: {key}")
                    return False

            # 检查SSR服务器配置
            if not config['ssr_servers'] or not isinstance(config['ssr_servers'], list):
                log("❌ SSR服务器配置无效")
                return False

            # 检查代理设置
            proxy_settings = config['proxy_settings']
            if not isinstance(proxy_settings, dict) or 'enable_proxy' not in proxy_settings:
                log("❌ 代理设置配置无效")
                return False

            log("✅ SSR配置验证通过")
            return True
        except Exception as e:
            log(f"❌ SSR配置验证异常: {e}")
            return False

    def _save_ssr_config(self, config):
        """保存SSR配置到文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            log(f"✅ SSR配置已保存到: {self.config_path}")
        except Exception as e:
            log(f"❌ 保存SSR配置失败: {e}")

    def _get_default_ssr_config(self):
        """获取默认SSR配置"""
        return {
            "ssr_servers": [
                {
                    "name": "主服务器",
                    "host": "77.gdpp.com",
                    "port": 11807,
                    "password": "q12345678q",
                    "method": "aes-256-ctr",
                    "protocol": "auth_aes128_sha1",
                    "obfs": "tls1.2_ticket_auth",
                    "local_port": 1080,
                    "timeout": 300,
                    "priority": 1,
                    "enabled": True
                },
                {
                    "name": "备用服务器",
                    "host": "*************",
                    "port": 11807,
                    "password": "q12345678q",
                    "method": "aes-256-ctr",
                    "protocol": "auth_aes128_sha1",
                    "obfs": "tls1.2_ticket_auth",
                    "local_port": 1081,
                    "timeout": 300,
                    "priority": 2,
                    "enabled": True
                }
            ],
            "proxy_settings": {
                "enable_proxy": True,
                "proxy_type": "socks5",
                "proxy_host": "127.0.0.1",
                "proxy_port": 1080,
                "connection_timeout": 30,
                "read_timeout": 60,
                "max_retries": 3,
                "retry_delay": 2,
                "test_urls": [
                    "https://api.coingecko.com/api/v3/ping",
                    "https://pro-api.coinmarketcap.com/v1/cryptocurrency/listings/latest?limit=1",
                    "https://httpbin.org/ip"
                ]
            },
            "fallback_settings": {
                "enable_fallback": True,
                "fallback_to_direct": False,
                "auto_switch_servers": True,
                "health_check_interval": 300,
                "failure_threshold": 3
            },
            "wechat_webhooks": {
                "enabled": True,
                "robots": [
                    {
                        "name": "户部尚赢量化平台",
                        "webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=69db19ba-d1af-422a-b0cf-19f21cd5b5fc",
                        "enabled": True,
                        "description": "户部尚赢量化平台企业微信机器人"
                    },
                    {
                        "name": "BTC机器人",
                        "webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=52d1b8a8-a88a-4d41-9664-f1268da20985",
                        "enabled": True,
                        "description": "企业微信BTC机器人"
                    }
                ],
                "send_settings": {
                    "timeout": 10,
                    "retry_count": 3,
                    "retry_delay": 2,
                    "concurrent_send": True
                }
            }
        }

    def _init_proxy_config(self):
        """初始化代理配置"""
        if not self.ssr_config:
            return

        proxy_settings = self.ssr_config.get('proxy_settings', {})
        proxy_host = proxy_settings.get('proxy_host', '127.0.0.1')
        proxy_port = proxy_settings.get('proxy_port', 1080)

        # 从SSR服务器配置中获取端口
        ssr_servers = self.ssr_config.get('ssr_servers', [])

        # 代理配置 - 优先使用SSR配置
        self.proxy_configs = []

        # 添加主要SSR服务器代理
        for server in ssr_servers:
            if server.get('enabled', True):
                local_port = server.get('local_port', 1080)
                self.proxy_configs.append({
                    'http': f'socks5://{proxy_host}:{local_port}',
                    'https': f'socks5://{proxy_host}:{local_port}'
                })

        # 添加默认代理配置
        if not self.proxy_configs:
            self.proxy_configs = [
                {'http': f'socks5://{proxy_host}:{proxy_port}', 'https': f'socks5://{proxy_host}:{proxy_port}'},
                # 备用代理配置
                {'http': 'socks5://127.0.0.1:1081', 'https': 'socks5://127.0.0.1:1081'},
                {'http': 'socks5://127.0.0.1:1082', 'https': 'socks5://127.0.0.1:1082'}
            ]

        self.current_proxy_index = 0

    def _init_ssr_connection(self):
        """初始化SSR连接"""
        try:
            # 如果不使用代理，直接跳过
            if not self.use_proxy:
                self.ssr_connected = False
                return False

            log("🔄 正在初始化SSR连接...")

            # 检查SSR端口是否可用
            if self._check_ssr_port():
                log("✅ 检测到SSR代理服务正在运行")
                self.ssr_connected = True
                return True

            # 尝试启动SSR客户端
            if self._start_ssr_client():
                log("✅ SSR客户端启动成功")
                self.ssr_connected = True
                return True
            else:
                log("❌ SSR客户端启动失败，将使用备用代理")
                self.ssr_connected = False
                return False

        except Exception as e:
            log(f"❌ SSR连接初始化失败: {e}")
            self.ssr_connected = False
            return False

    def _check_ssr_port(self):
        """检查SSR端口是否可用"""
        try:
            # 如果不使用代理，直接返回False
            if not self.use_proxy:
                return False

            proxy_settings = self.ssr_config.get('proxy_settings', {})
            proxy_host = proxy_settings.get('proxy_host', '127.0.0.1')
            proxy_port = proxy_settings.get('proxy_port', 1080)

            # 尝试连接SOCKS5端口 - 减少超时时间避免阻塞
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(0.5)  # 进一步减少到0.5秒避免长时间阻塞
            result = sock.connect_ex((proxy_host, proxy_port))
            sock.close()

            return result == 0
        except Exception as e:
            # 不输出错误日志，避免干扰用户
            return False

    def _start_ssr_client(self):
        """启动SSR客户端"""
        try:
            # 这里可以添加启动SSR客户端的逻辑
            # 由于SSR客户端通常需要单独安装和配置，这里只是示例
            log("⚠️ 请手动启动SSR客户端，或使用其他代理工具")

            # 显示所有可用的SSR服务器信息
            self._display_ssr_servers()

            return False  # 返回False，表示需要手动启动
        except Exception as e:
            log(f"启动SSR客户端失败: {e}")
            return False

    def _display_ssr_servers(self):
        """显示所有SSR服务器信息"""
        log("📋 可用的SSR服务器:")

        servers = self.ssr_config.get('ssr_servers', [])
        if servers:
            for i, server in enumerate(servers, 1):
                current_mark = " ⭐ 当前" if i-1 == self.current_ssr_server_index else ""
                enabled_mark = " ✅ 启用" if server.get('enabled', True) else " ❌ 禁用"
                log(f"   {i}. {server.get('name', f'服务器{i}')} {current_mark}{enabled_mark}")
                log(f"      服务器: {server.get('host', 'N/A')}")
                log(f"      端口: {server.get('port', 'N/A')}")
                log(f"      本地端口: {server.get('local_port', 'N/A')}")
                log(f"      加密: {server.get('method', 'N/A')}")
                log(f"      协议: {server.get('protocol', 'N/A')}")
                log(f"      混淆: {server.get('obfs', 'N/A')}")
                log("")
        else:
            log("   ⚠️ 未找到SSR服务器配置")

        # 显示代理设置
        proxy_settings = self.ssr_config.get('proxy_settings', {})
        log("🔧 代理设置:")
        log(f"   启用代理: {'是' if proxy_settings.get('enable_proxy', False) else '否'}")
        log(f"   代理类型: {proxy_settings.get('proxy_type', 'socks5')}")
        log(f"   代理地址: {proxy_settings.get('proxy_host', '127.0.0.1')}:{proxy_settings.get('proxy_port', 1080)}")
        log(f"   连接超时: {proxy_settings.get('connection_timeout', 30)}秒")
        log(f"   最大重试: {proxy_settings.get('max_retries', 3)}次")

    def switch_ssr_server(self):
        """切换SSR服务器"""
        servers = self.ssr_config.get('ssr_servers', [])
        if not servers or len(servers) <= 1:
            log("⚠️ 没有可切换的SSR服务器")
            return False

        old_index = self.current_ssr_server_index
        self.current_ssr_server_index = (self.current_ssr_server_index + 1) % len(servers)

        old_server = servers[old_index]
        new_server = servers[self.current_ssr_server_index]

        log(f"🔄 SSR服务器切换:")
        log(f"   从: {old_server.get('name', '未知')} ({old_server.get('host', 'N/A')}:{old_server.get('port', 'N/A')})")
        log(f"   到: {new_server.get('name', '未知')} ({new_server.get('host', 'N/A')}:{new_server.get('port', 'N/A')})")

        # 更新代理配置
        self._init_proxy_config()
        self._init_session()

        return True

    def get_current_ssr_server(self):
        """获取当前SSR服务器配置"""
        servers = self.ssr_config.get('ssr_servers', [])
        if servers and 0 <= self.current_ssr_server_index < len(servers):
            return servers[self.current_ssr_server_index]
        else:
            # 返回默认配置
            return {
                'name': '默认服务器',
                'host': '127.0.0.1',
                'port': 1080,
                'enabled': False
            }

    def _init_session(self):
        """初始化请求会话"""
        self.session = requests.Session()

        # 设置代理
        if self.use_proxy and self.proxy_configs:
            current_proxy = self.proxy_configs[self.current_proxy_index]
            self.session.proxies.update(current_proxy)
            log(f"🔗 使用代理: {current_proxy}")

            # 如果是SOCKS代理，安装SOCKS支持
            if 'socks5://' in str(current_proxy.values()):
                try:
                    import socks
                    import socket
                    # 这里可以添加更多SOCKS配置
                    log("✅ SOCKS5代理支持已启用")
                except ImportError:
                    log("⚠️ 缺少PySocks依赖，请安装: pip install requests[socks]")

        # 设置请求头 - 模拟真实浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'DNT': '1',
            'Sec-CH-UA': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'Sec-CH-UA-Mobile': '?0',
            'Sec-CH-UA-Platform': '"Windows"'
        })

        # 设置超时和重试配置
        timeout_config = self.ssr_config.get('connection', {})
        # 注意：requests.Session没有timeout属性，超时在请求时设置

        # 设置SSL验证
        self.session.verify = True

    def make_request_with_retry(self, url: str, method: str = 'GET', **kwargs) -> Optional[requests.Response]:
        """
        增强的网络请求方法，支持SSR代理和重试机制
        """
        proxy_settings = self.ssr_config.get('proxy_settings', {})
        max_retries = proxy_settings.get('max_retries', 3)
        retry_delay = proxy_settings.get('retry_delay', 2)
        connection_timeout = proxy_settings.get('connection_timeout', 30)
        read_timeout = proxy_settings.get('read_timeout', 60)

        # 设置超时
        if 'timeout' not in kwargs:
            kwargs['timeout'] = (connection_timeout, read_timeout)

        for attempt in range(max_retries + 1):
            try:
                # 更新请求统计
                self.request_stats['total_requests'] += 1
                self.request_stats['last_request_time'] = datetime.now()

                # 发送请求
                if method.upper() == 'GET':
                    response = self.session.get(url, **kwargs)
                elif method.upper() == 'POST':
                    response = self.session.post(url, **kwargs)
                else:
                    response = self.session.request(method, url, **kwargs)

                # 检查响应状态
                response.raise_for_status()

                # 更新成功统计
                self.request_stats['successful_requests'] += 1

                return response

            except requests.exceptions.ProxyError as e:
                log(f"代理错误 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                if attempt < max_retries:
                    # 尝试切换代理
                    if self._switch_proxy():
                        log("已切换到备用代理，重试中...")
                        time.sleep(retry_delay)
                        continue

            except requests.exceptions.ConnectionError as e:
                log(f"连接错误 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                if attempt < max_retries:
                    time.sleep(retry_delay)
                    continue

            except requests.exceptions.Timeout as e:
                log(f"请求超时 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                if attempt < max_retries:
                    time.sleep(retry_delay)
                    continue

            except requests.exceptions.HTTPError as e:
                log(f"HTTP错误: {e}")
                if e.response.status_code == 429:  # 速率限制
                    if attempt < max_retries:
                        self.handle_rate_limit_hit()  # 处理速率限制
                        # 对于429错误，使用更长的等待时间
                        wait_time = min(60 * (attempt + 1), 300)  # 60秒到5分钟递增
                        log(f"遇到速率限制，等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                        continue
                break

            except Exception as e:
                log(f"请求失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                if attempt < max_retries:
                    time.sleep(retry_delay)
                    continue
                break

        # 更新失败统计
        self.request_stats['failed_requests'] += 1
        return None

    def _switch_proxy(self) -> bool:
        """切换代理配置"""
        if not self.proxy_configs or len(self.proxy_configs) <= 1:
            return False

        old_index = self.current_proxy_index
        self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxy_configs)

        new_proxy = self.proxy_configs[self.current_proxy_index]
        self.session.proxies.update(new_proxy)

        log(f"🔄 代理切换: 索引 {old_index} -> {self.current_proxy_index}")
        log(f"新代理: {new_proxy}")

        return True

    def test_proxy_connection(self) -> bool:
        """测试代理连接"""
        proxy_settings = self.ssr_config.get('proxy_settings', {})
        test_urls = proxy_settings.get('test_urls', ['https://httpbin.org/ip'])

        log("🔍 测试代理连接...")

        for test_url in test_urls:
            try:
                log(f"   测试URL: {test_url}")
                response = self.make_request_with_retry(test_url, timeout=10)

                if response and response.status_code == 200:
                    log(f"   ✅ 连接成功: {test_url}")

                    # 尝试解析IP信息
                    try:
                        data = response.json()
                        if 'origin' in data:
                            log(f"   🌐 当前IP: {data['origin']}")
                    except:
                        pass

                    return True
                else:
                    log(f"   ❌ 连接失败: {test_url}")

            except Exception as e:
                log(f"   ❌ 测试失败: {test_url} - {e}")
                continue

        log("❌ 所有测试URL都无法连接")
        return False

    def _init_database(self):
        """初始化数据库"""
        try:
            # 确保数据库目录存在
            db_dir = os.path.dirname(self.db_path) if os.path.dirname(self.db_path) else '.'
            os.makedirs(db_dir, exist_ok=True)

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 创建币种信息表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS crypto_info (
                    id TEXT PRIMARY KEY,
                    symbol TEXT NOT NULL,
                    name TEXT NOT NULL,
                    market_cap REAL DEFAULT 0,
                    market_cap_rank INTEGER DEFAULT 0,
                    current_price REAL DEFAULT 0,
                    price_change_24h REAL DEFAULT 0,
                    volume_24h REAL DEFAULT 0,
                    circulating_supply REAL DEFAULT 0,
                    total_supply REAL DEFAULT 0,
                    max_supply REAL DEFAULT 0,
                    ath REAL DEFAULT 0,
                    ath_date TEXT DEFAULT '',
                    atl REAL DEFAULT 0,
                    atl_date TEXT DEFAULT '',
                    categories TEXT DEFAULT '',
                    description TEXT DEFAULT '',
                    genesis_date TEXT DEFAULT '',
                    homepage TEXT DEFAULT '',
                    blockchain_site TEXT DEFAULT '',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建历史价格表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS price_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    crypto_id TEXT NOT NULL,
                    date DATE NOT NULL,
                    open_price REAL DEFAULT 0,
                    high_price REAL DEFAULT 0,
                    low_price REAL DEFAULT 0,
                    close_price REAL DEFAULT 0,
                    volume REAL DEFAULT 0,
                    market_cap REAL DEFAULT 0,
                    data_source TEXT DEFAULT 'coingecko',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (crypto_id) REFERENCES crypto_info (id),
                    UNIQUE(crypto_id, date, data_source)
                )
            ''')

            # 创建筛选结果表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS selection_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    selection_type TEXT NOT NULL,
                    crypto_id TEXT NOT NULL,
                    selection_score REAL DEFAULT 0,
                    selection_reason TEXT DEFAULT '',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (crypto_id) REFERENCES crypto_info (id)
                )
            ''')

            # 创建市场概况表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS market_overview (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    total_market_cap REAL DEFAULT 0,
                    total_volume_24h REAL DEFAULT 0,
                    bitcoin_dominance REAL DEFAULT 0,
                    ethereum_dominance REAL DEFAULT 0,
                    active_cryptocurrencies INTEGER DEFAULT 0,
                    upcoming_icos INTEGER DEFAULT 0,
                    ongoing_icos INTEGER DEFAULT 0,
                    ended_icos INTEGER DEFAULT 0,
                    markets INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建索引以提高查询性能
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_crypto_symbol ON crypto_info(symbol)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_crypto_rank ON crypto_info(market_cap_rank)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_price_crypto_date ON price_history(crypto_id, date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_selection_type ON selection_results(selection_type)')

            conn.commit()
            conn.close()
            log("数据库初始化完成")

        except Exception as e:
            log(f"数据库初始化失败: {e}")
            log(f"错误详情: {traceback.format_exc()}")

    def apply_rate_limit(self):
        """应用智能速率限制"""
        # 基础延迟
        base_delay = self.speed_modes[self.current_speed_mode]['delay']

        # 自适应延迟：如果最近遇到速率限制，增加延迟
        adaptive_delay = self.adaptive_delay

        # 如果最近遇到过速率限制，增加延迟
        if self.last_rate_limit_time:
            time_since_limit = (datetime.now() - self.last_rate_limit_time).total_seconds()
            if time_since_limit < 300:  # 5分钟内遇到过速率限制
                adaptive_delay = min(adaptive_delay * 1.5, 10)  # 最多10秒
                log(f"🐌 自适应延迟: {adaptive_delay:.1f}秒 (最近遇到速率限制)")

        total_delay = base_delay + adaptive_delay
        if total_delay > 0:
            time.sleep(total_delay)

        # 更新自适应延迟
        self.adaptive_delay = adaptive_delay

        # 如果长时间没有遇到速率限制，逐渐减少自适应延迟
        if self.last_rate_limit_time:
            time_since_limit = (datetime.now() - self.last_rate_limit_time).total_seconds()
            if time_since_limit > 600:  # 10分钟没有遇到速率限制
                self.adaptive_delay = max(self.adaptive_delay * 0.9, 1)  # 逐渐减少，最少1秒
                if time_since_limit > 1800:  # 30分钟没有遇到速率限制
                    self.last_rate_limit_time = None  # 重置
                    self.adaptive_delay = 1

        # 更新请求统计
        self.request_stats['last_request_time'] = datetime.now()

    def handle_rate_limit_hit(self):
        """处理遇到速率限制的情况"""
        self.rate_limit_hit_count += 1
        self.last_rate_limit_time = datetime.now()

        # 增加自适应延迟
        self.adaptive_delay = min(self.adaptive_delay * 2, 15)  # 最多15秒

        log(f"⚠️ 速率限制计数: {self.rate_limit_hit_count}, 自适应延迟调整为: {self.adaptive_delay:.1f}秒")

    def set_speed_mode(self, mode: str):
        """设置速度模式"""
        if mode in self.speed_modes:
            self.current_speed_mode = mode
            self.speed_mode = mode  # 确保两个属性同步
            log(f"速度模式已设置为: {self.speed_modes[mode]['name']} ({self.speed_modes[mode]['desc']})")
        else:
            log(f"❌ 无效的速度模式: {mode}")

    def show_speed_modes(self):
        """显示所有速度模式"""
        log("\n📊 可用速度模式:")
        log("="*60)
        for key, mode in self.speed_modes.items():
            current = " ⭐ 当前" if key == self.current_speed_mode else ""
            log(f"{key}: {mode['name']} - {mode['desc']} (延迟: {mode['delay']}s){current}")
        log("="*60)

    def test_proxy(self):
        """测试代理连接"""
        if not self.use_proxy:
            log("🔍 代理已禁用，跳过代理测试")
            return True

        log("🔍 开始代理连接测试...")

        # 获取测试URL列表
        test_urls = self.ssr_config.get('ssr', {}).get('test', {}).get('test_urls', [
            'https://httpbin.org/ip',
            'https://api.ipify.org?format=json',
            'https://ifconfig.me/ip'
        ])

        timeout = self.ssr_config.get('ssr', {}).get('test', {}).get('timeout', 10)

        for url in test_urls:
            try:
                log(f"🌐 测试代理连接: {url}")
                response = self.session.get(url, timeout=timeout)

                if response.status_code == 200:
                    # 解析IP信息
                    current_ip = self._parse_ip_response(url, response)

                    log(f"✅ 代理工作正常，当前IP: {current_ip}")

                    # 检查是否为国外IP（简单判断）
                    if self._is_foreign_ip(current_ip):
                        log("🌍 检测到国外IP，SSR代理工作正常")
                        self.ssr_connected = True
                    else:
                        log("🏠 检测到国内IP，可能未使用代理")

                    self.request_stats['successful_requests'] += 1
                    return True
                else:
                    log(f"❌ 代理测试失败，状态码: {response.status_code}")

            except Exception as e:
                log(f"❌ 代理测试失败 ({url}): {e}")
                continue

        # 如果所有测试都失败，尝试切换代理
        if self.use_proxy and len(self.proxy_configs) > 1:
            log("🔄 当前代理失败，尝试切换代理...")
            self._switch_proxy()
            return self.test_proxy()  # 递归测试新代理

        log("❌ 所有代理测试均失败")
        self.request_stats['failed_requests'] += 1
        self.ssr_connected = False
        return False

    def _parse_ip_response(self, url: str, response):
        """解析IP响应"""
        try:
            if 'httpbin.org' in url:
                ip_info = response.json()
                return ip_info.get('origin', 'Unknown')
            elif 'ipify.org' in url:
                ip_info = response.json()
                return ip_info.get('ip', 'Unknown')
            else:
                return response.text.strip()
        except:
            return 'Unknown'

    def _is_foreign_ip(self, ip: str) -> bool:
        """简单判断是否为国外IP"""
        # 这里可以添加更复杂的IP地理位置判断逻辑
        # 目前只是简单的示例
        china_ip_ranges = [
            '1.', '14.', '27.', '36.', '39.', '42.', '49.', '58.', '59.', '60.',
            '61.', '101.', '103.', '106.', '110.', '111.', '112.', '113.', '114.',
            '115.', '116.', '117.', '118.', '119.', '120.', '121.', '122.', '123.',
            '124.', '125.', '180.', '182.', '183.', '202.', '203.', '210.', '211.',
            '218.', '219.', '220.', '221.', '222.', '223.'
        ]

        for prefix in china_ip_ranges:
            if ip.startswith(prefix):
                return False
        return True

    def _switch_proxy(self):
        """切换代理"""
        if not self.use_proxy or not self.proxy_configs:
            return

        old_proxy = self.proxy_configs[self.current_proxy_index]
        self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxy_configs)
        new_proxy = self.proxy_configs[self.current_proxy_index]

        # 更新会话代理
        self.session.proxies.update(new_proxy)

        log(f"🔄 代理切换: {old_proxy} -> {new_proxy}")

        # 如果启用了代理切换日志
        if self.ssr_config.get('logging', {}).get('log_proxy_switch', True):
            log(f"📊 当前使用代理 {self.current_proxy_index + 1}/{len(self.proxy_configs)}")

    def test_ssr_connection(self):
        """专门测试SSR连接"""
        if not self.ssr_config.get('ssr', {}).get('enabled', False):
            log("⚠️ SSR未启用，跳过SSR连接测试")
            return False

        log("🔍 开始SSR连接专项测试...")

        # 测试SSR端口连通性
        if not self._check_ssr_port():
            log("❌ SSR端口不可达，请检查SSR客户端是否正常运行")
            return False

        # 测试通过SSR访问国外网站
        test_sites = [
            'https://www.google.com',
            'https://api.coingecko.com/api/v3/ping',
            'https://httpbin.org/ip'
        ]

        success_count = 0
        for site in test_sites:
            try:
                log(f"🌐 测试访问: {site}")
                response = self.session.get(site, timeout=10)
                if response.status_code == 200:
                    log(f"✅ 访问成功: {site}")
                    success_count += 1
                else:
                    log(f"❌ 访问失败: {site} (状态码: {response.status_code})")
            except Exception as e:
                log(f"❌ 访问异常: {site} ({e})")

        success_rate = success_count / len(test_sites)
        if success_rate >= 0.5:  # 50%以上成功率认为连接正常
            log(f"✅ SSR连接测试通过 (成功率: {success_rate:.1%})")
            self.ssr_connected = True
            return True
        else:
            log(f"❌ SSR连接测试失败 (成功率: {success_rate:.1%})")
            self.ssr_connected = False
            return False

    def test_connection(self):
        """测试网络连接"""
        log("🔍 开始网络连接测试...")

        # 测试基本连接
        basic_test = self._test_basic_connection()

        # 测试代理连接
        proxy_test = self.test_proxy() if self.use_proxy else True

        # 测试API连接
        api_test = self._test_api_connection()

        # 显示测试结果
        log("\n📊 网络连接测试结果:")
        log("="*50)
        log(f"基本连接: {'✅ 正常' if basic_test else '❌ 失败'}")
        log(f"代理连接: {'✅ 正常' if proxy_test else '❌ 失败'}")
        log(f"API连接: {'✅ 正常' if api_test else '❌ 失败'}")
        log("="*50)

        return basic_test and proxy_test and api_test

    def _test_basic_connection(self):
        """测试基本网络连接"""
        try:
            # 临时禁用代理测试基本连接
            temp_session = requests.Session()
            response = temp_session.get('https://www.google.com', timeout=10)
            return response.status_code == 200
        except:
            return False

    def _test_api_connection(self):
        """测试API连接"""
        try:
            url = f"{self.data_sources['coingecko']['base_url']}/ping"
            response = self.session.get(url, timeout=10)
            return response.status_code == 200
        except:
            return False

    def smart_request(self, url: str, data_source: str = "", **kwargs):
        """
        智能请求方法 - 根据数据源自动选择是否使用代理，集成重试机制

        Args:
            url: 请求URL
            data_source: 数据源名称 (coingecko, huobi, etc.)
            **kwargs: 其他requests参数
        """
        # 判断是否需要代理
        requires_proxy = True
        if data_source and data_source in self.data_sources:
            requires_proxy = self.data_sources[data_source].get('requires_proxy', True)

        # 如果需要代理且启用了代理，使用增强的请求方法
        if requires_proxy and self.use_proxy:
            log(f"🌐 通过SSR代理访问: {url}")
            return self.make_request_with_retry(url, **kwargs)
        else:
            # 直连访问
            log(f"🏠 直连访问: {url}")
            # 创建临时会话用于直连
            temp_session = requests.Session()
            temp_session.headers.update(self.session.headers)

            # 设置超时
            if 'timeout' not in kwargs:
                kwargs['timeout'] = 30

            try:
                return temp_session.get(url, **kwargs)
            except Exception as e:
                log(f"❌ 直连访问失败: {e}")
                # 如果直连失败且有代理，尝试使用代理
                if self.use_proxy:
                    log("🔄 尝试使用代理访问...")
                    return self.make_request_with_retry(url, **kwargs)
                raise

    def get_market_overview(self, retry_count: int = 3):
        """获取市场概况"""
        for attempt in range(retry_count):
            try:
                log(f"获取市场概况... (尝试 {attempt + 1}/{retry_count})")

                url = f"{self.data_sources['coingecko']['base_url']}/global"
                response = self.smart_request(url, 'coingecko', timeout=30)
                self.apply_rate_limit()

                # 更新请求统计
                self.request_stats['total_requests'] += 1

                if response.status_code == 200:
                    json_data = response.json()
                    data = json_data.get('data', {}) if json_data else {}

                    overview = {
                        'total_market_cap': data['total_market_cap'].get('usd', 0),
                        'total_volume_24h': data['total_volume'].get('usd', 0),
                        'bitcoin_dominance': data['market_cap_percentage'].get('btc', 0),
                        'ethereum_dominance': data['market_cap_percentage'].get('eth', 0),
                        'active_cryptocurrencies': data.get('active_cryptocurrencies', 0),
                        'upcoming_icos': data.get('upcoming_icos', 0),
                        'ongoing_icos': data.get('ongoing_icos', 0),
                        'ended_icos': data.get('ended_icos', 0),
                        'markets': data.get('markets', 0)
                    }

                    # 保存到数据库
                    self._save_market_overview(overview)

                    self.request_stats['successful_requests'] += 1
                    log("✅ 市场概况获取成功")
                    return overview

                elif response.status_code == 429:  # 速率限制
                    self.handle_rate_limit_hit()  # 处理速率限制
                    wait_time = min(60 * (attempt + 1), 300)  # 递增等待时间，最多5分钟
                    log(f"⚠️ 遇到速率限制，等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    continue

                else:
                    log(f"❌ 获取市场概况失败，状态码: {response.status_code}")
                    if attempt < retry_count - 1:
                        log(f"等待 {(attempt + 1) * 5} 秒后重试...")
                        time.sleep((attempt + 1) * 5)
                        continue

            except requests.exceptions.ProxyError as e:
                log(f"❌ 代理错误: {e}")
                if self.use_proxy and attempt < retry_count - 1:
                    self._switch_proxy()
                    continue

            except requests.exceptions.Timeout as e:
                log(f"❌ 请求超时: {e}")
                if attempt < retry_count - 1:
                    log(f"等待 {(attempt + 1) * 10} 秒后重试...")
                    time.sleep((attempt + 1) * 10)
                    continue

            except Exception as e:
                log(f"❌ 获取市场概况失败: {e}")
                if attempt < retry_count - 1:
                    log(f"等待 {(attempt + 1) * 5} 秒后重试...")
                    time.sleep((attempt + 1) * 5)
                    continue

        self.request_stats['failed_requests'] += 1
        return None

    def _save_market_overview(self, overview: Dict):
        """保存市场概况到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO market_overview
                (total_market_cap, total_volume_24h, bitcoin_dominance, ethereum_dominance,
                 active_cryptocurrencies, upcoming_icos, ongoing_icos, ended_icos, markets)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                overview['total_market_cap'], overview['total_volume_24h'],
                overview['bitcoin_dominance'], overview['ethereum_dominance'],
                overview['active_cryptocurrencies'], overview['upcoming_icos'],
                overview['ongoing_icos'], overview['ended_icos'], overview['markets']
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            log(f"保存市场概况失败: {e}")

    def display_market_overview(self, overview: Dict):
        """显示市场概况"""
        if not overview:
            log("❌ 无市场概况数据")
            return

        log("\n" + "="*80)
        log("📊 全球加密货币市场概况")
        log("="*80)
        log(f"💰 总市值: ${overview['total_market_cap']:,.0f}")
        log(f"📈 24小时总交易量: ${overview['total_volume_24h']:,.0f}")
        log(f"🟠 比特币市值占比: {overview['bitcoin_dominance']:.2f}%")
        log(f"🔵 以太坊市值占比: {overview['ethereum_dominance']:.2f}%")
        log(f"🪙 活跃加密货币数量: {overview['active_cryptocurrencies']:,}")
        log(f"🏪 交易市场数量: {overview['markets']:,}")
        log(f"🚀 即将ICO项目: {overview['upcoming_icos']}")
        log(f"🔥 进行中ICO项目: {overview['ongoing_icos']}")
        log(f"✅ 已结束ICO项目: {overview['ended_icos']}")
        log("="*80)

    def get_all_cryptocurrencies(self, limit: int = 250, page: int = 1, retry_count: int = 3):
        """获取所有加密货币列表"""
        # 验证参数
        limit = min(max(limit, 1), 250)  # 限制在1-250之间
        page = max(page, 1)  # 页面至少为1

        for attempt in range(retry_count):
            try:
                log(f"获取加密货币列表 (页面 {page}, 每页 {limit} 个, 尝试 {attempt + 1}/{retry_count})...")

                url = f"{self.data_sources['coingecko']['base_url']}/coins/markets"
                params = {
                    'vs_currency': 'usd',
                    'order': 'market_cap_desc',
                    'per_page': limit,
                    'page': page,
                    'sparkline': False,
                    'price_change_percentage': '24h,7d,30d'
                }

                response = self.smart_request(url, 'coingecko', params=params, timeout=30)
                self.apply_rate_limit()

                # 更新请求统计
                self.request_stats['total_requests'] += 1

                # 检查响应是否为空
                if response is None:
                    log(f"❌ 请求返回空响应，可能是网络问题")
                    if attempt < retry_count - 1:
                        log(f"等待 {(attempt + 1) * 10} 秒后重试...")
                        time.sleep((attempt + 1) * 10)
                        continue
                    else:
                        break

                if response.status_code == 200:
                    coins_data = response.json()

                    if not coins_data:
                        log("⚠️ 返回的数据为空")
                        return []

                    cryptocurrencies = []
                    for coin in coins_data:
                        try:
                            crypto = {
                                'id': coin.get('id', ''),
                                'symbol': coin.get('symbol', '').upper(),
                                'name': coin.get('name', ''),
                                'market_cap': self._safe_float(coin.get('market_cap')),
                                'market_cap_rank': self._safe_int(coin.get('market_cap_rank')),
                                'current_price': self._safe_float(coin.get('current_price')),
                                'price_change_24h': self._safe_float(coin.get('price_change_percentage_24h')),
                                'volume_24h': self._safe_float(coin.get('total_volume')),
                                'circulating_supply': self._safe_float(coin.get('circulating_supply')),
                                'total_supply': self._safe_float(coin.get('total_supply')),
                                'max_supply': self._safe_float(coin.get('max_supply')),
                                'ath': self._safe_float(coin.get('ath')),
                                'ath_date': coin.get('ath_date', ''),
                                'atl': self._safe_float(coin.get('atl')),
                                'atl_date': coin.get('atl_date', ''),
                                'image': coin.get('image', ''),
                                'last_updated': coin.get('last_updated', '')
                            }

                            # 验证必要字段
                            if crypto['id'] and crypto['symbol'] and crypto['name']:
                                cryptocurrencies.append(crypto)
                            else:
                                log(f"⚠️ 跳过无效币种数据: {coin}")

                        except Exception as e:
                            log(f"⚠️ 处理币种数据时出错: {e}, 数据: {coin}")
                            continue

                    self.request_stats['successful_requests'] += 1
                    log(f"✅ 成功获取 {len(cryptocurrencies)} 个加密货币")
                    return cryptocurrencies

                elif response.status_code == 429:  # 速率限制
                    log(f"⚠️ 遇到速率限制，等待后重试...")
                    time.sleep(60)
                    continue

                else:
                    log(f"❌ 获取加密货币列表失败，状态码: {response.status_code}")
                    if response.text:
                        log(f"响应内容: {response.text[:200]}...")

                    if attempt < retry_count - 1:
                        log(f"等待 {(attempt + 1) * 5} 秒后重试...")
                        time.sleep((attempt + 1) * 5)
                        continue

            except requests.exceptions.ProxyError as e:
                log(f"❌ 代理错误: {e}")
                if self.use_proxy and attempt < retry_count - 1:
                    self._switch_proxy()
                    continue

            except requests.exceptions.Timeout as e:
                log(f"❌ 请求超时: {e}")
                if attempt < retry_count - 1:
                    log(f"等待 {(attempt + 1) * 10} 秒后重试...")
                    time.sleep((attempt + 1) * 10)
                    continue

            except Exception as e:
                log(f"❌ 获取加密货币列表失败: {e}")
                log(f"错误详情: {traceback.format_exc()}")
                if attempt < retry_count - 1:
                    log(f"等待 {(attempt + 1) * 5} 秒后重试...")
                    time.sleep((attempt + 1) * 5)
                    continue

        self.request_stats['failed_requests'] += 1
        return []

    def get_cryptocurrencies_from_gateio(self, retry_count: int = 3) -> List[Dict]:
        """从Gate.io获取加密货币数据"""
        for attempt in range(retry_count):
            try:
                log(f"从Gate.io获取加密货币列表... (尝试 {attempt + 1}/{retry_count})")

                # 获取所有交易对
                url = f"{self.data_sources['gateio']['base_url']}/spot/currency_pairs"
                response = self.smart_request(url, 'gateio', timeout=30)
                self.apply_rate_limit()

                if response and response.status_code == 200:
                    pairs_data = response.json()

                    # 获取市场行情
                    tickers_url = f"{self.data_sources['gateio']['base_url']}/spot/tickers"
                    tickers_response = self.smart_request(tickers_url, 'gateio', timeout=30)
                    self.apply_rate_limit()

                    if tickers_response and tickers_response.status_code == 200:
                        tickers_data = tickers_response.json()

                        # 创建价格映射
                        price_map = {}
                        for ticker in tickers_data:
                            if ticker.get('currency_pair'):
                                price_map[ticker['currency_pair']] = ticker

                        cryptocurrencies = []
                        for pair in pairs_data:
                            if pair.get('quote') == 'USDT':  # 只获取USDT交易对
                                base_currency = pair.get('base', '').upper()
                                pair_name = f"{base_currency}_USDT"

                                ticker = price_map.get(pair_name, {})
                                if ticker:
                                    try:
                                        current_price = float(ticker.get('last', 0))
                                        volume_24h = float(ticker.get('base_volume', 0))
                                        price_change_24h = float(ticker.get('change_percentage', 0))

                                        if current_price > 0:  # 只包含有价格的币种
                                            crypto = {
                                                'id': base_currency.lower(),
                                                'symbol': base_currency,
                                                'name': base_currency,
                                                'current_price': current_price,
                                                'market_cap': current_price * volume_24h if volume_24h > 0 else 0,
                                                'market_cap_rank': None,
                                                'fully_diluted_valuation': None,
                                                'total_volume': volume_24h,
                                                'high_24h': float(ticker.get('high_24h', current_price)),
                                                'low_24h': float(ticker.get('low_24h', current_price)),
                                                'price_change_24h': current_price * price_change_24h / 100 if price_change_24h else 0,
                                                'price_change_percentage_24h': price_change_24h,
                                                'market_cap_change_24h': None,
                                                'market_cap_change_percentage_24h': None,
                                                'circulating_supply': None,
                                                'total_supply': None,
                                                'max_supply': None,
                                                'ath': float(ticker.get('high_24h', current_price)),
                                                'ath_date': '',
                                                'atl': float(ticker.get('low_24h', current_price)),
                                                'atl_date': '',
                                                'data_source': 'gateio'
                                            }
                                            cryptocurrencies.append(crypto)
                                    except (ValueError, TypeError) as e:
                                        continue

                        self.request_stats['successful_requests'] += 1
                        log(f"✅ 从Gate.io获取 {len(cryptocurrencies)} 个加密货币")
                        return cryptocurrencies

                else:
                    log(f"❌ Gate.io API请求失败: {response.status_code if response else 'No response'}")

            except Exception as e:
                log(f"❌ 从Gate.io获取数据失败 (尝试 {attempt + 1}/{retry_count}): {e}")
                if attempt < retry_count - 1:
                    log("⏳ 等待5秒后重试...")
                    time.sleep(5)

        self.request_stats['failed_requests'] += 1
        return []

    def get_cryptocurrencies_from_binance(self, retry_count: int = 3) -> List[Dict]:
        """从Binance获取加密货币数据"""
        for attempt in range(retry_count):
            try:
                log(f"从Binance获取加密货币列表... (尝试 {attempt + 1}/{retry_count})")

                # 获取24小时价格变动统计
                url = f"{self.data_sources['binance']['base_url']}/ticker/24hr"
                response = self.smart_request(url, 'binance', timeout=30)
                self.apply_rate_limit()

                if response and response.status_code == 200:
                    tickers_data = response.json()

                    cryptocurrencies = []
                    for ticker in tickers_data:
                        if ticker.get('symbol', '').endswith('USDT'):  # 只获取USDT交易对
                            symbol = ticker.get('symbol', '').replace('USDT', '')

                            try:
                                current_price = float(ticker.get('lastPrice', 0))
                                volume_24h = float(ticker.get('quoteVolume', 0))
                                price_change_24h = float(ticker.get('priceChange', 0))
                                price_change_percentage_24h = float(ticker.get('priceChangePercent', 0))

                                if current_price > 0:  # 只包含有价格的币种
                                    crypto = {
                                        'id': symbol.lower(),
                                        'symbol': symbol,
                                        'name': symbol,
                                        'current_price': current_price,
                                        'market_cap': current_price * volume_24h if volume_24h > 0 else 0,
                                        'market_cap_rank': None,
                                        'fully_diluted_valuation': None,
                                        'total_volume': volume_24h,
                                        'high_24h': float(ticker.get('highPrice', current_price)),
                                        'low_24h': float(ticker.get('lowPrice', current_price)),
                                        'price_change_24h': price_change_24h,
                                        'price_change_percentage_24h': price_change_percentage_24h,
                                        'market_cap_change_24h': None,
                                        'market_cap_change_percentage_24h': None,
                                        'circulating_supply': None,
                                        'total_supply': None,
                                        'max_supply': None,
                                        'ath': float(ticker.get('highPrice', current_price)),
                                        'ath_date': '',
                                        'atl': float(ticker.get('lowPrice', current_price)),
                                        'atl_date': '',
                                        'data_source': 'binance'
                                    }
                                    cryptocurrencies.append(crypto)
                            except (ValueError, TypeError) as e:
                                continue

                    # 按交易量排序
                    cryptocurrencies.sort(key=lambda x: x.get('total_volume', 0), reverse=True)

                    self.request_stats['successful_requests'] += 1
                    log(f"✅ 从Binance获取 {len(cryptocurrencies)} 个加密货币")
                    return cryptocurrencies

                else:
                    log(f"❌ Binance API请求失败: {response.status_code if response else 'No response'}")

            except Exception as e:
                log(f"❌ 从Binance获取数据失败 (尝试 {attempt + 1}/{retry_count}): {e}")
                if attempt < retry_count - 1:
                    log("⏳ 等待5秒后重试...")
                    time.sleep(5)

        self.request_stats['failed_requests'] += 1
        return []

    def get_all_cryptocurrencies_enhanced(self, max_coins: int = 100) -> List[Dict]:
        """
        增强版数据获取方法 - Gate.io主数据源，其他作为备用数据源
        """
        # 根据速度模式决定获取数量
        if max_coins is None:
            if self.speed_mode == 'fast':
                max_coins = 1000  # 快速模式：获取1000个
            elif self.speed_mode == 'normal':
                max_coins = 2000  # 正常模式：获取2000个
            elif self.speed_mode == 'safe':
                max_coins = 3000  # 安全模式：获取3000个
            else:
                max_coins = 5000  # 默认获取5000个

        log(f"🚀 开始增强版数据获取，目标: {max_coins} 个币种 (模式: {self.speed_mode})")
        log("🎯 数据源策略: Gate.io主数据源，其他作为备用数据源")

        # 按优先级排序的数据源
        data_sources_by_priority = sorted(
            self.data_sources.items(),
            key=lambda x: x[1].get('priority', 999)
        )

        all_cryptos = []
        successful_source = None

        # 依次尝试数据源，直到成功获取数据
        for source_name, source_config in data_sources_by_priority:
            try:
                log(f"📡 尝试从 {source_config['name']} 获取数据...")

                if source_name == 'gateio':
                    # 主数据源：Gate.io
                    cryptos = self.get_cryptocurrencies_from_gateio()
                elif source_name == 'coingecko':
                    # 备用数据源：CoinGecko
                    cryptos = self.get_cryptocurrencies_from_coingecko(max_coins)
                elif source_name == 'huobi':
                    # 备用数据源：火币
                    cryptos = self.get_cryptocurrencies_from_huobi()
                elif source_name == 'binance':
                    # 备用数据源：Binance
                    cryptos = self.get_cryptocurrencies_from_binance()
                else:
                    # 其他数据源暂时跳过
                    log(f"⚠️ {source_config['name']} 暂不支持，跳过")
                    continue

                if cryptos and len(cryptos) > 0:
                    # 数据获取成功
                    all_cryptos = cryptos
                    successful_source = source_config['name']
                    log(f"✅ 从 {successful_source} 成功获取 {len(cryptos)} 个币种")
                    break
                else:
                    log(f"⚠️ {source_config['name']} 返回空数据，尝试下一个数据源")

            except Exception as e:
                log(f"❌ {source_config['name']} 获取失败: {e}")
                log(f"🔄 尝试下一个备用数据源...")
                continue

        # 检查是否成功获取数据
        if not all_cryptos:
            log("❌ 所有数据源都失败，无法获取数据")
            return []

        # 数据后处理
        log(f"🔧 开始数据后处理...")

        # 数据质量验证和清理
        valid_cryptos = []
        for crypto in all_cryptos:
            # 验证必需字段
            if (crypto.get('current_price', 0) > 0 and
                crypto.get('symbol') and
                crypto.get('data_source')):
                valid_cryptos.append(crypto)

        # 按市值排序
        valid_cryptos.sort(key=lambda x: x.get('market_cap', 0) or 0, reverse=True)

        # 限制数量
        if len(valid_cryptos) > max_coins:
            valid_cryptos = valid_cryptos[:max_coins]

        log(f"🎉 数据获取完成！")
        log(f"📊 数据源: {successful_source}")
        log(f"📈 有效币种: {len(valid_cryptos)} 个")

        # 统计信息
        if valid_cryptos:
            market_caps = [crypto.get('market_cap', 0) for crypto in valid_cryptos if crypto.get('market_cap', 0) > 0]
            if market_caps:
                log(f"💰 市值统计:")
                log(f"   范围: ${min(market_caps):,.0f} - ${max(market_caps):,.0f}")
                log(f"   平均: ${sum(market_caps)/len(market_caps):,.0f}")

                # 按市值分类统计
                mainstream = len([cap for cap in market_caps if cap > 10_000_000_000])
                altcoins = len([cap for cap in market_caps if 1_000_000_000 <= cap <= 10_000_000_000])
                small_cap = len([cap for cap in market_caps if cap < 1_000_000_000])

                log(f"   主流币种 (>100亿): {mainstream} 个")
                log(f"   山寨币种 (10-100亿): {altcoins} 个")
                log(f"   小市值币种 (<10亿): {small_cap} 个")

        log("🎯 重要: 所有数据均来自真实交易所，确保交易决策准确性")
        return valid_cryptos

    def _load_wechat_webhooks(self):
        """加载微信机器人配置"""
        try:
            wechat_config = self.ssr_config.get('wechat_webhooks', {})
            if wechat_config.get('enabled', False):
                robots = wechat_config.get('robots', [])
                # 只返回启用的机器人
                enabled_robots = [robot for robot in robots if robot.get('enabled', True)]
                if enabled_robots:
                    log(f"✅ 加载了 {len(enabled_robots)} 个微信机器人配置")
                    for robot in enabled_robots:
                        log(f"   - {robot.get('name', '未命名')}: {robot.get('description', '无描述')}")
                    return enabled_robots
                else:
                    log("⚠️ 没有启用的微信机器人")
            else:
                log("⚠️ 微信推送功能已禁用")

            # 回退到默认配置
            return [{
                "name": "默认机器人",
                "webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=69db19ba-d1af-422a-b0cf-19f21cd5b5fc",
                "enabled": True,
                "description": "默认企业微信机器人"
            }]

        except Exception as e:
            log(f"❌ 加载微信机器人配置失败: {e}")
            return []

    def _safe_float(self, value):
        """安全转换为浮点数"""
        try:
            return float(value) if value is not None else 0.0
        except (ValueError, TypeError):
            return 0.0

    def _safe_int(self, value):
        """安全转换为整数"""
        try:
            return int(value) if value is not None else 0
        except (ValueError, TypeError):
            return 0

    # ==================== 筛选功能实现 ====================

    def _filter_all_market(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """全市场扫描 - 返回所有币种"""
        log("🌍 应用全市场扫描筛选...")
        return cryptocurrencies

    def _filter_mainstream(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """主流币种 - 市值>100亿美元"""
        log("👑 应用主流币种筛选 (市值>100亿美元)...")
        return [crypto for crypto in cryptocurrencies
                if crypto.get('market_cap', 0) > 10_000_000_000]

    def _filter_hot_altcoins(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """热门山寨币 - 市值10-100亿美元"""
        log("🔥 应用热门山寨币筛选 (市值10-100亿美元)...")
        return [crypto for crypto in cryptocurrencies
                if 1_000_000_000 <= crypto.get('market_cap', 0) <= 10_000_000_000]

    def _filter_small_cap(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """小市值潜力币 - 市值<10亿美元"""
        log("💎 应用小市值潜力币筛选 (市值<10亿美元)...")
        return [crypto for crypto in cryptocurrencies
                if crypto.get('market_cap', 0) < 1_000_000_000 and crypto.get('market_cap', 0) > 0]

    def _filter_new_listings_30d(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """新上市币种 - 30天内"""
        log("🆕 应用新上市币种筛选 (30天内)...")
        # 这里需要额外的API调用来获取上市时间
        # 暂时使用市值排名作为近似筛选
        return [crypto for crypto in cryptocurrencies
                if crypto.get('market_cap_rank', 0) > 500]

    def _filter_new_listings_90d(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """较新币种 - 90天内"""
        log("🆕 应用较新币种筛选 (90天内)...")
        return [crypto for crypto in cryptocurrencies
                if crypto.get('market_cap_rank', 0) > 300]

    def _filter_defi_tokens(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """DeFi生态代币"""
        log("🏦 应用DeFi生态代币筛选...")
        defi_keywords = ['defi', 'dex', 'swap', 'yield', 'farm', 'lending', 'protocol',
                        'finance', 'liquidity', 'compound', 'aave', 'uniswap', 'sushi']

        filtered = []
        for crypto in cryptocurrencies:
            name_lower = crypto.get('name', '').lower()
            symbol_lower = crypto.get('symbol', '').lower()

            if any(keyword in name_lower or keyword in symbol_lower for keyword in defi_keywords):
                filtered.append(crypto)

        return filtered

    def _filter_layer1_tokens(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """Layer1公链代币"""
        log("⛓️ 应用Layer1公链代币筛选...")
        layer1_symbols = ['BTC', 'ETH', 'BNB', 'ADA', 'SOL', 'DOT', 'AVAX', 'MATIC',
                         'ATOM', 'NEAR', 'FTM', 'ALGO', 'EGLD', 'HBAR', 'VET', 'ICP',
                         'FLOW', 'TEZOS', 'EOS', 'TRX', 'XLM', 'XRP', 'LTC']

        return [crypto for crypto in cryptocurrencies
                if crypto.get('symbol', '') in layer1_symbols]

    def _filter_layer2_tokens(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """Layer2扩容代币"""
        log("🔗 应用Layer2扩容代币筛选...")
        layer2_keywords = ['layer2', 'l2', 'scaling', 'rollup', 'optimism', 'arbitrum',
                          'polygon', 'loopring', 'immutable', 'starknet']
        layer2_symbols = ['MATIC', 'LRC', 'IMX', 'OP', 'ARB']

        filtered = []
        for crypto in cryptocurrencies:
            name_lower = crypto.get('name', '').lower()
            symbol = crypto.get('symbol', '')

            if (symbol in layer2_symbols or
                any(keyword in name_lower for keyword in layer2_keywords)):
                filtered.append(crypto)

        return filtered

    def _filter_meme_tokens(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """Meme币专区"""
        log("🐕 应用Meme币筛选...")
        meme_keywords = ['meme', 'dog', 'cat', 'shib', 'doge', 'pepe', 'floki', 'baby', 'inu']
        meme_symbols = ['DOGE', 'SHIB', 'PEPE', 'FLOKI', 'BABYDOGE', 'ELON']

        filtered = []
        for crypto in cryptocurrencies:
            name_lower = crypto.get('name', '').lower()
            symbol = crypto.get('symbol', '')

            if (symbol in meme_symbols or
                any(keyword in name_lower for keyword in meme_keywords)):
                filtered.append(crypto)

        return filtered

    def _filter_ai_tokens(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """AI概念币"""
        log("🤖 应用AI概念币筛选...")
        ai_keywords = ['ai', 'artificial', 'intelligence', 'machine', 'learning', 'neural',
                      'deep', 'algorithm', 'data', 'compute', 'gpu', 'render']
        ai_symbols = ['FET', 'AGIX', 'OCEAN', 'RLC', 'GRT', 'RNDR']

        filtered = []
        for crypto in cryptocurrencies:
            name_lower = crypto.get('name', '').lower()
            symbol = crypto.get('symbol', '')

            if (symbol in ai_symbols or
                any(keyword in name_lower for keyword in ai_keywords)):
                filtered.append(crypto)

        return filtered

    def _filter_high_volume(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """高交易量币种 - 24h交易量前100"""
        log("📈 应用高交易量币种筛选 (24h交易量前100)...")
        # 按24小时交易量排序，取前100
        sorted_cryptos = sorted(cryptocurrencies,
                               key=lambda x: x.get('volume_24h', 0),
                               reverse=True)
        return sorted_cryptos[:100]

    def _filter_custom(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """自定义筛选条件"""
        log("🔧 应用自定义筛选条件...")

        print("\n🔧 自定义筛选条件设置:")
        print("1. 市值范围筛选")
        print("2. 价格范围筛选")
        print("3. 涨跌幅筛选")
        print("4. 交易量筛选")
        print("5. 关键词筛选")
        print("6. 组合筛选")

        choice = input("请选择筛选类型 (1-6): ").strip()

        if choice == '1':
            return self._custom_market_cap_filter(cryptocurrencies)
        elif choice == '2':
            return self._custom_price_filter(cryptocurrencies)
        elif choice == '3':
            return self._custom_change_filter(cryptocurrencies)
        elif choice == '4':
            return self._custom_volume_filter(cryptocurrencies)
        elif choice == '5':
            return self._custom_keyword_filter(cryptocurrencies)
        elif choice == '6':
            return self._custom_combined_filter(cryptocurrencies)
        else:
            log("❌ 无效选择，返回所有币种")
            return cryptocurrencies

    def _custom_market_cap_filter(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """自定义市值范围筛选"""
        try:
            min_cap = float(input("请输入最小市值 (美元，例如: 1000000): ") or "0")
            max_cap = float(input("请输入最大市值 (美元，留空表示无上限): ") or "999999999999")

            filtered = [crypto for crypto in cryptocurrencies
                       if min_cap <= crypto.get('market_cap', 0) <= max_cap]

            log(f"✅ 市值筛选完成: ${min_cap:,.0f} - ${max_cap:,.0f}, 筛选出 {len(filtered)} 个币种")
            return filtered

        except ValueError:
            log("❌ 输入格式错误，返回所有币种")
            return cryptocurrencies

    def _custom_price_filter(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """自定义价格范围筛选"""
        try:
            min_price = float(input("请输入最小价格 (美元): ") or "0")
            max_price = float(input("请输入最大价格 (美元，留空表示无上限): ") or "999999")

            filtered = [crypto for crypto in cryptocurrencies
                       if min_price <= crypto.get('current_price', 0) <= max_price]

            log(f"✅ 价格筛选完成: ${min_price} - ${max_price}, 筛选出 {len(filtered)} 个币种")
            return filtered

        except ValueError:
            log("❌ 输入格式错误，返回所有币种")
            return cryptocurrencies

    def _custom_change_filter(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """自定义涨跌幅筛选"""
        try:
            min_change = float(input("请输入最小24h涨跌幅 (%, 例如: -10): ") or "-999")
            max_change = float(input("请输入最大24h涨跌幅 (%, 例如: 50): ") or "999")

            filtered = [crypto for crypto in cryptocurrencies
                       if min_change <= crypto.get('price_change_24h', 0) <= max_change]

            log(f"✅ 涨跌幅筛选完成: {min_change}% - {max_change}%, 筛选出 {len(filtered)} 个币种")
            return filtered

        except ValueError:
            log("❌ 输入格式错误，返回所有币种")
            return cryptocurrencies

    def _custom_volume_filter(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """自定义交易量筛选"""
        try:
            min_volume = float(input("请输入最小24h交易量 (美元): ") or "0")

            filtered = [crypto for crypto in cryptocurrencies
                       if crypto.get('volume_24h', 0) >= min_volume]

            log(f"✅ 交易量筛选完成: 最小交易量 ${min_volume:,.0f}, 筛选出 {len(filtered)} 个币种")
            return filtered

        except ValueError:
            log("❌ 输入格式错误，返回所有币种")
            return cryptocurrencies

    def _custom_keyword_filter(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """自定义关键词筛选"""
        keywords_input = input("请输入关键词 (多个关键词用逗号分隔): ").strip()
        if not keywords_input:
            log("❌ 未输入关键词，返回所有币种")
            return cryptocurrencies

        keywords = [kw.strip().lower() for kw in keywords_input.split(',')]

        filtered = []
        for crypto in cryptocurrencies:
            name_lower = crypto.get('name', '').lower()
            symbol_lower = crypto.get('symbol', '').lower()

            if any(keyword in name_lower or keyword in symbol_lower for keyword in keywords):
                filtered.append(crypto)

        log(f"✅ 关键词筛选完成: {keywords}, 筛选出 {len(filtered)} 个币种")
        return filtered

    def _custom_combined_filter(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """自定义组合筛选"""
        log("🔧 组合筛选 - 可以设置多个条件")

        filtered = cryptocurrencies

        # 市值筛选
        if input("是否设置市值筛选? (y/n): ").lower() == 'y':
            filtered = self._custom_market_cap_filter(filtered)

        # 价格筛选
        if input("是否设置价格筛选? (y/n): ").lower() == 'y':
            filtered = self._custom_price_filter(filtered)

        # 涨跌幅筛选
        if input("是否设置涨跌幅筛选? (y/n): ").lower() == 'y':
            filtered = self._custom_change_filter(filtered)

        # 交易量筛选
        if input("是否设置交易量筛选? (y/n): ").lower() == 'y':
            filtered = self._custom_volume_filter(filtered)

        # 关键词筛选
        if input("是否设置关键词筛选? (y/n): ").lower() == 'y':
            filtered = self._custom_keyword_filter(filtered)

        log(f"✅ 组合筛选完成，最终筛选出 {len(filtered)} 个币种")
        return filtered

    # ==================== 数据保存功能 ====================

    def save_cryptocurrencies(self, cryptocurrencies: List[Dict]):
        """保存加密货币信息到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            saved_count = 0
            for crypto in cryptocurrencies:
                try:
                    cursor.execute('''
                        INSERT OR REPLACE INTO crypto_info
                        (id, symbol, name, market_cap, market_cap_rank, current_price,
                         price_change_24h, volume_24h, circulating_supply, total_supply,
                         max_supply, ath, ath_date, atl, atl_date, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        crypto.get('id', ''),
                        crypto.get('symbol', ''),
                        crypto.get('name', ''),
                        crypto.get('market_cap', 0),
                        crypto.get('market_cap_rank', 0),
                        crypto.get('current_price', 0),
                        crypto.get('price_change_24h', 0),
                        crypto.get('volume_24h', 0),
                        crypto.get('circulating_supply', 0),
                        crypto.get('total_supply', 0),
                        crypto.get('max_supply', 0),
                        crypto.get('ath', 0),
                        crypto.get('ath_date', ''),
                        crypto.get('atl', 0),
                        crypto.get('atl_date', ''),
                        datetime.now().isoformat()
                    ))
                    saved_count += 1
                except Exception as e:
                    log(f"⚠️ 保存单个币种失败: {crypto.get('symbol', 'Unknown')} - {e}")
                    continue

            conn.commit()
            conn.close()
            log(f"✅ 成功保存 {saved_count}/{len(cryptocurrencies)} 个加密货币信息")

        except Exception as e:
            log(f"❌ 保存加密货币信息失败: {e}")
            log(f"错误详情: {traceback.format_exc()}")

    def save_selection_results(self, selection_type: str, cryptocurrencies: List[Dict]):
        """保存筛选结果到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 清除之前的筛选结果
            cursor.execute('DELETE FROM selection_results WHERE selection_type = ?', (selection_type,))

            for i, crypto in enumerate(cryptocurrencies, 1):
                cursor.execute('''
                    INSERT INTO selection_results
                    (selection_type, crypto_id, selection_score, selection_reason)
                    VALUES (?, ?, ?, ?)
                ''', (
                    selection_type, crypto['id'], i,
                    f"排名第{i}，市值: ${crypto.get('market_cap', 0):,.0f}"
                ))

            conn.commit()
            conn.close()
            log(f"✅ 筛选结果已保存: {selection_type}")

        except Exception as e:
            log(f"❌ 保存筛选结果失败: {e}")

    def get_historical_data(self, crypto_id: str, days: int = 365, retry_count: int = 3):
        """获取历史价格数据"""
        for attempt in range(retry_count):
            try:
                log(f"获取 {crypto_id} 的历史数据 ({days}天, 尝试 {attempt + 1}/{retry_count})...")

                url = f"{self.data_sources['coingecko']['base_url']}/coins/{crypto_id}/market_chart"
                params = {
                    'vs_currency': 'usd',
                    'days': days,
                    'interval': 'daily'
                }

                response = self.session.get(url, params=params, timeout=30)
                self.apply_rate_limit()

                # 更新请求统计
                self.request_stats['total_requests'] += 1

                if response.status_code == 200:
                    data = response.json()

                    if 'prices' not in data or not data['prices']:
                        log(f"⚠️ {crypto_id} 没有历史价格数据")
                        return []

                    historical_data = []
                    prices = data['prices']
                    volumes = data.get('total_volumes', [])
                    market_caps = data.get('market_caps', [])

                    for i, price_data in enumerate(prices):
                        timestamp = price_data[0]
                        price = price_data[1]

                        # 获取对应的交易量和市值数据
                        volume = volumes[i][1] if i < len(volumes) else 0
                        market_cap = market_caps[i][1] if i < len(market_caps) else 0

                        # 转换时间戳为日期
                        date = datetime.fromtimestamp(timestamp / 1000).strftime('%Y-%m-%d')

                        historical_data.append({
                            'crypto_id': crypto_id,
                            'date': date,
                            'open_price': price,  # CoinGecko只提供收盘价
                            'high_price': price,
                            'low_price': price,
                            'close_price': price,
                            'volume': volume,
                            'market_cap': market_cap,
                            'data_source': 'coingecko'
                        })

                    self.request_stats['successful_requests'] += 1
                    log(f"✅ 成功获取 {crypto_id} 的 {len(historical_data)} 天历史数据")
                    return historical_data

                elif response.status_code == 429:  # 速率限制
                    log(f"⚠️ 遇到速率限制，等待后重试...")
                    time.sleep(60)
                    continue

                else:
                    log(f"❌ 获取 {crypto_id} 历史数据失败，状态码: {response.status_code}")
                    if attempt < retry_count - 1:
                        log(f"等待 {(attempt + 1) * 5} 秒后重试...")
                        time.sleep((attempt + 1) * 5)
                        continue

            except requests.exceptions.ProxyError as e:
                log(f"❌ 代理错误: {e}")
                if self.use_proxy and attempt < retry_count - 1:
                    self._switch_proxy()
                    continue

            except requests.exceptions.Timeout as e:
                log(f"❌ 请求超时: {e}")
                if attempt < retry_count - 1:
                    log(f"等待 {(attempt + 1) * 10} 秒后重试...")
                    time.sleep((attempt + 1) * 10)
                    continue

            except Exception as e:
                log(f"❌ 获取 {crypto_id} 历史数据失败: {e}")
                if attempt < retry_count - 1:
                    log(f"等待 {(attempt + 1) * 5} 秒后重试...")
                    time.sleep((attempt + 1) * 5)
                    continue

        self.request_stats['failed_requests'] += 1
        return []

    def save_historical_data(self, historical_data: List[Dict]):
        """保存历史数据到数据库"""
        if not historical_data:
            return

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            saved_count = 0
            for data in historical_data:
                try:
                    cursor.execute('''
                        INSERT OR REPLACE INTO price_history
                        (crypto_id, date, open_price, high_price, low_price, close_price,
                         volume, market_cap, data_source)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        data['crypto_id'], data['date'], data['open_price'],
                        data['high_price'], data['low_price'], data['close_price'],
                        data['volume'], data['market_cap'], data['data_source']
                    ))
                    saved_count += 1
                except Exception as e:
                    log(f"⚠️ 保存单条历史数据失败: {e}")
                    continue

            conn.commit()
            conn.close()
            log(f"✅ 成功保存 {saved_count} 条历史数据")

        except Exception as e:
            log(f"❌ 保存历史数据失败: {e}")

    def display_selection_results(self, cryptocurrencies: List[Dict], selection_name: str):
        """显示筛选结果"""
        if not cryptocurrencies:
            log(f"❌ {selection_name} 筛选结果为空")
            return

        log(f"\n🎯 {selection_name} 筛选结果 (共 {len(cryptocurrencies)} 个币种)")
        log("="*100)
        log(f"{'排名':<4} {'代码':<8} {'名称':<20} {'价格':<12} {'市值':<15} {'24h涨跌':<10} {'交易量':<15}")
        log("="*100)

        for i, crypto in enumerate(cryptocurrencies[:50], 1):  # 只显示前50个
            symbol = crypto.get('symbol', '')[:7]
            name = crypto.get('name', '')[:18]
            price = crypto.get('current_price', 0)
            market_cap = crypto.get('market_cap', 0)
            change_24h = crypto.get('price_change_24h', 0)
            volume_24h = crypto.get('volume_24h', 0)

            price_str = f"${price:.6f}" if price < 1 else f"${price:.2f}"
            market_cap_str = f"${market_cap/1e9:.2f}B" if market_cap > 1e9 else f"${market_cap/1e6:.1f}M"
            change_str = f"{change_24h:+.2f}%"
            volume_str = f"${volume_24h/1e6:.1f}M" if volume_24h > 1e6 else f"${volume_24h/1e3:.1f}K"

            log(f"{i:<4} {symbol:<8} {name:<20} {price_str:<12} {market_cap_str:<15} {change_str:<10} {volume_str:<15}")

        if len(cryptocurrencies) > 50:
            log(f"... 还有 {len(cryptocurrencies) - 50} 个币种未显示")

        log("="*100)

    def print_statistics(self):
        """打印统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取各种统计数据
            cursor.execute("SELECT COUNT(*) FROM crypto_info")
            crypto_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM price_history")
            history_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(DISTINCT selection_type) FROM selection_results")
            selection_types = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM market_overview")
            overview_count = cursor.fetchone()[0]

            # 获取最新更新时间
            cursor.execute("SELECT MAX(updated_at) FROM crypto_info")
            last_update = cursor.fetchone()[0]

            conn.close()

            log("\n📊 数据库统计信息")
            log("="*60)
            log(f"💰 币种信息: {crypto_count:,} 个")
            log(f"📈 历史记录: {history_count:,} 条")
            log(f"🎯 筛选类型: {selection_types} 种")
            log(f"🌍 市场概况: {overview_count} 条")
            log(f"🕒 最后更新: {last_update or '无'}")
            log("="*60)

            # 显示请求统计
            log("\n📡 请求统计信息")
            log("="*60)
            log(f"总请求数: {self.request_stats['total_requests']}")
            log(f"成功请求: {self.request_stats['successful_requests']}")
            log(f"失败请求: {self.request_stats['failed_requests']}")
            if self.request_stats['total_requests'] > 0:
                success_rate = (self.request_stats['successful_requests'] / self.request_stats['total_requests']) * 100
                log(f"成功率: {success_rate:.1f}%")
            log(f"最后请求: {self.request_stats['last_request_time'] or '无'}")
            log("="*60)

        except Exception as e:
            log(f"❌ 获取统计信息失败: {e}")

    def get_statistics(self):
        """获取数据库统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT COUNT(*) FROM crypto_info')
            total_cryptos = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM price_history')
            total_price_records = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(DISTINCT selection_type) FROM selection_results')
            total_selections = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM market_overview')
            total_market_records = cursor.fetchone()[0]

            cursor.execute('''
                SELECT MIN(date), MAX(date), COUNT(DISTINCT crypto_id)
                FROM price_history
            ''')

            price_stats = cursor.fetchone()
            conn.close()

            return {
                'total_cryptos': total_cryptos,
                'total_price_records': total_price_records,
                'total_selections': total_selections,
                'total_market_records': total_market_records,
                'earliest_date': price_stats[0],
                'latest_date': price_stats[1],
                'cryptos_with_price_data': price_stats[2]
            }

        except Exception as e:
            log(f"获取统计信息失败: {e}")
            return {}

    def print_statistics(self):
        """打印统计信息"""
        stats = self.get_statistics()

        if not stats:
            log("无法获取统计信息")
            return

        log("\n" + "="*60)
        log("📊 高级加密货币数据抓取器统计")
        log("="*60)
        log(f"💰 加密货币总数: {stats['total_cryptos']:,}")
        log(f"📈 历史价格记录: {stats['total_price_records']:,}")
        log(f"🔍 筛选方案数: {stats['total_selections']}")
        log(f"📊 市场概况记录: {stats['total_market_records']}")
        log(f"📅 有价格数据币种: {stats['cryptos_with_price_data']}")

        if stats['earliest_date'] and stats['latest_date']:
            log(f"📅 数据时间范围: {stats['earliest_date']} 到 {stats['latest_date']}")

        # 数据库大小
        import os
        if os.path.exists(self.db_path):
            db_size = os.path.getsize(self.db_path) / (1024 * 1024)
            log(f"💾 数据库大小: {db_size:.1f} MB")

        log("="*60)

    def export_data(self, table_name: str = 'crypto_info', limit: int = 1000):
        """导出数据到CSV"""
        try:
            conn = sqlite3.connect(self.db_path)

            if table_name == 'crypto_info':
                query = '''
                    SELECT symbol, name, market_cap, market_cap_rank, current_price,
                           price_change_24h, volume_24h, updated_at
                    FROM crypto_info
                    ORDER BY market_cap_rank
                    LIMIT ?
                '''
            elif table_name == 'selection_results':
                query = '''
                    SELECT sr.selection_type, ci.symbol, ci.name, sr.selection_score,
                           ci.current_price, ci.market_cap, sr.created_at
                    FROM selection_results sr
                    JOIN crypto_info ci ON sr.crypto_id = ci.id
                    ORDER BY sr.selection_type, sr.selection_score
                    LIMIT ?
                '''
            else:
                log(f"❌ 不支持的表名: {table_name}")
                return

            df = pd.read_sql_query(query, conn, params=(limit,))
            conn.close()

            if not df.empty:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"advanced_crypto_{table_name}_{timestamp}.csv"
                df.to_csv(filename, index=False, encoding='utf-8-sig')
                log(f"✅ 数据已导出: {filename}")

                log(f"\n数据预览 (前10行):")
                print(df.head(10).to_string())
            else:
                log("❌ 没有数据可导出")

        except Exception as e:
            log(f"❌ 导出数据失败: {e}")

    # ==================== 主要业务流程 ====================

    def run_selection_process(self, selection_key: str):
        """运行选币流程"""
        if selection_key not in self.selection_ranges:
            log(f"❌ 无效的选择: {selection_key}")
            return

        selection_info = self.selection_ranges[selection_key]
        selection_name = selection_info['name']

        if selection_key == '14':  # 查看市场概况
            overview = self.get_market_overview()
            if overview:
                self.display_market_overview(overview)
                return {
                    'success': True,
                    'selection_name': selection_name,
                    'overview': overview,
                    'total_count': 0
                }
            else:
                return {
                    'success': False,
                    'selection_name': selection_name,
                    'error': '无法获取市场概况'
                }

        log(f"🚀 开始执行: {selection_name}")

        # 获取加密货币列表
        all_cryptos = []

        # 从 CoinGecko 获取数据 - 增强版多页获取
        log("📊 从 CoinGecko 获取数据...")
        all_cryptos = self.get_all_cryptocurrencies_enhanced()

        if all_cryptos:
            log(f"✅ 从 CoinGecko 获取 {len(all_cryptos)} 个币种")

        # 从火币获取数据
        if self.huobi_api_key and self.huobi_secret_key:
            log("📊 从火币获取数据...")
            huobi_cryptos = self.get_huobi_cryptocurrencies()
            if huobi_cryptos:
                # 合并数据，避免重复
                existing_symbols = {crypto['symbol'].upper() for crypto in all_cryptos}
                for crypto in huobi_cryptos:
                    if crypto['symbol'].upper() not in existing_symbols:
                        all_cryptos.append(crypto)
                log(f"已从火币获取 {len(huobi_cryptos)} 个币种，合并后共 {len(all_cryptos)} 个币种")

        if not all_cryptos:
            log("❌ 无法获取加密货币数据")
            return

        log(f"✅ 总共获取 {len(all_cryptos)} 个加密货币")

        # 保存基础数据
        self.save_cryptocurrencies(all_cryptos)

        # 应用筛选条件
        filter_func = selection_info['filter']
        if filter_func:
            filtered_cryptos = filter_func(all_cryptos)

            # 显示筛选结果
            self.display_selection_results(filtered_cryptos, selection_name)

            # 保存筛选结果
            self.save_selection_results(selection_name, filtered_cryptos)

            # 询问是否获取历史数据（仅在非自动化模式下询问）
            if filtered_cryptos and len(filtered_cryptos) <= 50 and not hasattr(self, '_automated_mode'):
                get_history = input(f"\n是否获取这 {len(filtered_cryptos)} 个币种的历史数据? (y/n): ").lower()
                if get_history == 'y':
                    self.batch_get_historical_data(filtered_cryptos)

            # 🆕 返回筛选结果，供推送功能使用
            log(f"✅ 筛选结果已保存: {selection_name}")
            return {
                'success': True,
                'selection_name': selection_name,
                'filtered_cryptos': filtered_cryptos,
                'total_count': len(filtered_cryptos)
            }
        else:
            log("❌ 筛选功能未实现")
            return {
                'success': False,
                'selection_name': selection_name,
                'error': '筛选功能未实现'
            }

    def batch_get_historical_data(self, cryptocurrencies: List[Dict], days: int = 365):
        """批量获取历史数据"""
        log(f"🚀 开始批量获取 {len(cryptocurrencies)} 个币种的历史数据...")

        total_records = 0

        for i, crypto in enumerate(cryptocurrencies, 1):
            crypto_id = crypto['id']
            symbol = crypto['symbol']

            log(f"进度 {i}/{len(cryptocurrencies)}: 获取 {symbol} 历史数据...")

            historical_data = self.get_historical_data(crypto_id, days)

            if historical_data:
                self.save_historical_data(historical_data)
                total_records += len(historical_data)
                log(f"  ✅ {symbol}: 获取 {len(historical_data)} 天数据")
            else:
                log(f"  ❌ {symbol}: 获取失败")

        log(f"🎉 批量获取完成！共获取 {total_records:,} 条历史记录")

    def show_selection_menu(self):
        """显示选币范围菜单"""
        log("\n" + "="*80)
        log("🎯 选币范围选择菜单")
        log("="*80)

        for key, info in self.selection_ranges.items():
            log(f"{key:2s}. {info['name']} ({info['desc']})")

        log("="*80)

    def get_huobi_cryptocurrencies(self, retry_count: int = 3):
        """从火币获取加密货币列表"""
        for attempt in range(retry_count):
            try:
                log("从火币获取加密货币列表...")

                # 获取所有交易对
                url = f"{self.data_sources['huobi']['base_url']}/v1/common/symbols"
                response = self.session.get(url, timeout=30)
                self.apply_rate_limit()

                if response.status_code == 200:
                    data = response.json()
                    if data['status'] == 'ok':
                        symbols = data['data']

                        # 获取市场行情
                        market_url = f"{self.data_sources['huobi']['base_url']}/market/tickers"
                        market_response = self.session.get(market_url, timeout=30)
                        self.apply_rate_limit()

                        if market_response.status_code == 200:
                            market_data = market_response.json()
                            if market_data['status'] == 'ok':
                                # 创建行情数据字典
                                tickers = {item['symbol']: item for item in market_data['data']}

                                cryptocurrencies = []
                                for symbol in symbols:
                                    if symbol['quote-currency'] == 'usdt':  # 只获取USDT交易对
                                        symbol_name = symbol['symbol'].upper()
                                        ticker = tickers.get(symbol_name, {})

                                        crypto = {
                                            'id': symbol['symbol'],
                                            'symbol': symbol['base-currency'].upper(),
                                            'name': symbol['base-currency'].upper(),
                                            'market_cap': float(ticker.get('amount', 0)) * float(ticker.get('close', 0)),
                                            'market_cap_rank': 0,  # 火币不提供排名
                                            'current_price': float(ticker.get('close', 0)),
                                            'price_change_24h': float(ticker.get('close', 0)) / float(ticker.get('open', 1)) - 1 if ticker.get('open') else 0,
                                            'volume_24h': float(ticker.get('vol', 0)) * float(ticker.get('close', 0)),
                                            'circulating_supply': float(ticker.get('amount', 0)),
                                            'total_supply': float(ticker.get('amount', 0)),
                                            'max_supply': None,
                                            'ath': float(ticker.get('high', 0)),
                                            'ath_date': '',
                                            'atl': float(ticker.get('low', 0)),
                                            'atl_date': '',
                                            'data_source': 'huobi'
                                        }

                                        cryptocurrencies.append(crypto)

                                log(f"✅ 从火币获取 {len(cryptocurrencies)} 个加密货币")
                                return cryptocurrencies

                elif response.status_code == 429:  # 速率限制
                    log(f"⚠️ 遇到速率限制，等待后重试...")
                    time.sleep(60)
                    continue

                else:
                    log(f"❌ 获取火币数据失败，状态码: {response.status_code}")
                    if attempt < retry_count - 1:
                        log(f"等待 {(attempt + 1) * 5} 秒后重试...")
                        time.sleep((attempt + 1) * 5)
                        continue

            except Exception as e:
                log(f"❌ 获取火币数据失败: {e}")
                if attempt < retry_count - 1:
                    log(f"等待 {(attempt + 1) * 5} 秒后重试...")
                    time.sleep((attempt + 1) * 5)
                    continue

        return []

    # ==================== 新增：形态分析核心功能 ====================

    def get_optimized_kline_data(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """
        🚨 重要修复：使用真实K线数据替代模拟数据
        从真实交易所获取K线数据，确保形态分析的准确性
        """
        # 优先使用真实K线数据
        if self.real_kline_provider:
            try:
                # 获取CoinGecko symbol到交易所symbol的映射
                trading_symbol = self._get_trading_symbol(symbol)
                if not trading_symbol:
                    log(f"⚠️ 无法映射币种符号: {symbol}")
                    return self._fallback_to_simulated_data(symbol, timeframe)

                # 根据时间周期确定数据量
                periods = self.kline_periods.get(timeframe, 100)

                log(f"📊 获取真实K线数据: {trading_symbol} {timeframe} ({periods}根)")

                # 从真实交易所获取K线数据
                real_kline_data = self.real_kline_provider.get_real_kline_data(
                    trading_symbol, timeframe, periods
                )

                if real_kline_data is not None and not real_kline_data.empty:
                    # 验证数据质量
                    if self.real_kline_provider.validate_kline_data(real_kline_data, trading_symbol, timeframe):
                        log(f"✅ 成功获取真实K线数据: {len(real_kline_data)} 根K线")
                        return real_kline_data
                    else:
                        log(f"❌ 真实K线数据质量验证失败: {trading_symbol}")
                else:
                    log(f"❌ 无法获取真实K线数据: {trading_symbol}")

            except Exception as e:
                log(f"❌ 获取真实K线数据失败: {e}")

        # 如果真实数据获取失败，记录警告并拒绝使用模拟数据
        log(f"🚨 严重警告: 无法获取 {symbol} 的真实K线数据")
        log(f"🚨 为避免投资风险，拒绝使用模拟数据进行形态分析")
        log(f"🚨 建议检查网络连接或稍后重试")

        return None  # 返回None而不是模拟数据

    def _get_trading_symbol(self, coingecko_symbol: str) -> Optional[str]:
        """
        将CoinGecko的symbol转换为交易所使用的symbol
        """
        if not self.real_kline_provider:
            return None

        # 从市场数据中查找对应的symbol
        market_data = self.get_real_market_data()

        # 直接匹配
        if coingecko_symbol.upper() in market_data:
            return coingecko_symbol.upper()

        # 使用映射关系
        mapped_symbol = self.real_kline_provider.get_symbol_mapping(coingecko_symbol)
        if mapped_symbol and mapped_symbol in market_data:
            return mapped_symbol

        # 尝试从市场数据中查找匹配的名称
        for symbol_key, data in market_data.items():
            if (data.get('name', '').lower() == coingecko_symbol.lower() or
                symbol_key.lower() == coingecko_symbol.lower()):
                return symbol_key

        return None

    def _fallback_to_simulated_data(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """
        🚨 已禁用：模拟数据回退方法
        为了投资安全，不再提供模拟数据
        """
        log(f"🚨 拒绝为 {symbol} 生成模拟K线数据")
        log(f"🚨 模拟数据可能导致错误的投资决策")
        log(f"🚨 请确保网络连接正常，使用真实市场数据")

        return None

    def _validate_kline_data(self, data: pd.DataFrame, symbol: str, timeframe: str) -> bool:
        """验证K线数据质量"""
        try:
            # 检查基本数据完整性
            if data.empty or len(data) < 20:
                return False

            # 检查OHLC逻辑
            ohlc_valid = (
                (data['high'] >= data[['open', 'close']].max(axis=1)).all() and
                (data['low'] <= data[['open', 'close']].min(axis=1)).all() and
                (data['high'] >= data['low']).all()
            )

            # 检查价格为正
            prices_positive = (data[['open', 'high', 'low', 'close']] > 0).all().all()

            # 检查成交量为正
            volume_positive = (data['volume'] > 0).all()

            # 检查时间序列
            time_series_valid = data.index.is_monotonic_increasing

            return ohlc_valid and prices_positive and volume_positive and time_series_valid

        except:
            return False

    def get_real_market_data(self) -> Dict:
        """获取真实市场数据 (带缓存)"""
        current_time = time.time()

        # 检查缓存是否有效
        if (self.market_data_cache and self.cache_timestamp and
            current_time - self.cache_timestamp < self.cache_duration):
            return self.market_data_cache

        # 获取市场数据
        market_data = {}
        try:
            # 根据速度模式决定获取数量
            if self.speed_mode == 'fast':
                limit = 500  # 快速模式：获取前500个
            elif self.speed_mode == 'normal':
                limit = 1000  # 正常模式：获取前1000个
            elif self.speed_mode == 'safe':
                limit = 1500  # 安全模式：获取前1500个
            else:
                limit = 2000  # 默认获取前2000个

            # 获取市场数据
            cryptos = self.get_all_cryptocurrencies(limit=limit, page=1)
            if cryptos:
                for crypto in cryptos:
                    symbol = crypto.get('symbol', '').upper()
                    market_data[symbol] = {
                        'name': crypto.get('name', ''),
                        'current_price': crypto.get('current_price', 0),
                        'market_cap': crypto.get('market_cap', 0),
                        'market_cap_rank': crypto.get('market_cap_rank', 999),
                        'price_change_24h': crypto.get('price_change_percentage_24h', 0),
                        'price_change_7d': crypto.get('price_change_percentage_7d', 0),
                        'total_volume': crypto.get('total_volume', 0),
                        'circulating_supply': crypto.get('circulating_supply', 0)
                    }

                self.market_data_cache = market_data
                self.cache_timestamp = current_time
                log(f"✅ 更新市场数据缓存，包含 {len(market_data)} 个币种")
        except Exception as e:
            log(f"❌ 获取市场数据失败: {e}")

        return market_data

    def calculate_optimized_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        优化的技术指标体系
        改进计算逻辑，优化参数设置，完善评分标准
        """
        df = data.copy()

        # 1. RSI (相对强弱指标) - 优化参数
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = (-delta.where(delta < 0, 0))

        # 使用Wilder's smoothing (更准确的RSI计算)
        alpha = 1.0 / 14
        avg_gain = gain.ewm(alpha=alpha, adjust=False).mean()
        avg_loss = loss.ewm(alpha=alpha, adjust=False).mean()

        rs = avg_gain / avg_loss
        df['rsi'] = 100 - (100 / (1 + rs))

        # 2. MACD (指数平滑移动平均线) - 标准参数
        ema12 = df['close'].ewm(span=12, adjust=False).mean()
        ema26 = df['close'].ewm(span=26, adjust=False).mean()
        df['macd'] = ema12 - ema26
        df['macd_signal'] = df['macd'].ewm(span=9, adjust=False).mean()
        df['macd_hist'] = df['macd'] - df['macd_signal']

        # 3. 布林带 (Bollinger Bands) - 标准参数
        df['bb_middle'] = df['close'].rolling(20, min_periods=1).mean()
        bb_std = df['close'].rolling(20, min_periods=1).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])

        # 4. 移动平均线系统
        df['sma_5'] = df['close'].rolling(5, min_periods=1).mean()
        df['sma_10'] = df['close'].rolling(10, min_periods=1).mean()
        df['sma_20'] = df['close'].rolling(20, min_periods=1).mean()
        df['sma_50'] = df['close'].rolling(50, min_periods=1).mean()

        # 5. 指数移动平均线
        df['ema_12'] = df['close'].ewm(span=12, adjust=False).mean()
        df['ema_26'] = df['close'].ewm(span=26, adjust=False).mean()

        # 6. 成交量指标
        df['volume_sma'] = df['volume'].rolling(20, min_periods=1).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma']

        # 7. 价格动量指标
        df['momentum_5'] = df['close'] / df['close'].shift(5) - 1
        df['momentum_10'] = df['close'] / df['close'].shift(10) - 1

        # 8. 波动率指标
        df['volatility'] = df['close'].pct_change().rolling(20, min_periods=1).std()

        return df

    def evaluate_optimized_indicators(self, data: pd.DataFrame) -> Dict[str, float]:
        """
        优化的技术指标评估
        详细说明各指标的评分标准和权重分配
        """
        latest = data.iloc[-1]
        prev = data.iloc[-2] if len(data) > 1 else latest

        scores = {
            'rsi_score': 0.0,
            'macd_score': 0.0,
            'bb_score': 0.0,
            'ma_score': 0.0,
            'momentum_score': 0.0,
            'volume_score': 0.0
        }

        # 1. RSI评分 (权重: 25%)
        if not pd.isna(latest['rsi']):
            rsi = latest['rsi']
            if rsi < 30:  # 超卖
                scores['rsi_score'] = 2.0
            elif rsi < 40:  # 偏弱
                scores['rsi_score'] = 1.5
            elif 40 <= rsi <= 60:  # 中性健康
                scores['rsi_score'] = 1.8
            elif 60 < rsi <= 70:  # 偏强
                scores['rsi_score'] = 1.5
            elif rsi > 70:  # 超买
                scores['rsi_score'] = 1.0

            # RSI趋势加分
            if not pd.isna(prev['rsi']) and latest['rsi'] > prev['rsi']:
                scores['rsi_score'] += 0.3

        # 2. MACD评分 (权重: 30%)
        if not pd.isna(latest['macd']) and not pd.isna(latest['macd_signal']):
            macd = latest['macd']
            signal = latest['macd_signal']
            hist = latest['macd_hist']

            # MACD金叉/死叉
            if macd > signal and hist > 0:  # 金叉且柱状图为正
                scores['macd_score'] = 2.5
            elif macd > signal:  # 仅金叉
                scores['macd_score'] = 2.0
            elif macd < signal and hist < 0:  # 死叉且柱状图为负
                scores['macd_score'] = 0.5
            else:
                scores['macd_score'] = 1.0

        # 3. 布林带评分 (权重: 20%)
        if not pd.isna(latest['bb_position']):
            bb_pos = latest['bb_position']
            if bb_pos < 0.2:  # 接近下轨
                scores['bb_score'] = 2.0
            elif bb_pos > 0.8:  # 接近上轨
                scores['bb_score'] = 1.0
            else:  # 中间区域
                scores['bb_score'] = 1.5

        # 4. 移动平均线评分 (权重: 15%)
        if not pd.isna(latest['sma_5']) and not pd.isna(latest['sma_20']):
            close = latest['close']
            sma5 = latest['sma_5']
            sma20 = latest['sma_20']

            if close > sma5 > sma20:  # 多头排列
                scores['ma_score'] = 2.5
            elif close > sma5:  # 短期向上
                scores['ma_score'] = 2.0
            elif close < sma5 < sma20:  # 空头排列
                scores['ma_score'] = 0.5
            else:
                scores['ma_score'] = 1.0

        # 5. 动量评分 (权重: 5%)
        if not pd.isna(latest['momentum_5']):
            momentum = latest['momentum_5']
            if momentum > 0.05:  # 5%以上涨幅
                scores['momentum_score'] = 2.0
            elif momentum > 0:
                scores['momentum_score'] = 1.5
            elif momentum < -0.05:  # 5%以上跌幅
                scores['momentum_score'] = 0.5
            else:
                scores['momentum_score'] = 1.0

        # 6. 成交量评分 (权重: 5%)
        if not pd.isna(latest['volume_ratio']):
            vol_ratio = latest['volume_ratio']
            if vol_ratio > 1.5:  # 放量
                scores['volume_score'] = 2.0
            elif vol_ratio > 1.2:
                scores['volume_score'] = 1.5
            elif vol_ratio < 0.8:  # 缩量
                scores['volume_score'] = 1.0
            else:
                scores['volume_score'] = 1.2

        return scores

    def detect_optimized_kline_patterns(self, data: pd.DataFrame) -> Dict[str, List[str]]:
        """
        优化的K线形态识别
        重点优化上影线识别逻辑，改进所有形态的识别标准
        """
        patterns = {
            'bullish_patterns': [],
            'bearish_patterns': [],
            'neutral_patterns': [],
            'reversal_patterns': [],
            'continuation_patterns': []
        }

        try:
            if len(data) < 5:
                return {'neutral_patterns': ['数据不足']}

            # 分析最近的K线
            for i in range(-min(10, len(data)), 0):
                current = data.iloc[i]

                # 计算K线基本属性
                body_size = abs(current['close'] - current['open'])
                total_size = current['high'] - current['low']
                upper_shadow = current['high'] - max(current['open'], current['close'])
                lower_shadow = min(current['open'], current['close']) - current['low']

                # 避免除零错误
                if total_size == 0:
                    continue

                body_ratio = body_size / total_size
                upper_shadow_ratio = upper_shadow / total_size
                lower_shadow_ratio = lower_shadow / total_size

                # 1. 锤子线 (看涨)
                if (lower_shadow_ratio > 0.6 and upper_shadow_ratio < 0.1 and
                    body_ratio < 0.3):
                    patterns['bullish_patterns'].append("锤子线")
                    patterns['reversal_patterns'].append("锤子线反转")

                # 2. 上吊线 (看跌)
                elif (lower_shadow_ratio > 0.6 and upper_shadow_ratio < 0.1 and
                      body_ratio < 0.3 and current['close'] < current['open']):
                    patterns['bearish_patterns'].append("上吊线")
                    patterns['reversal_patterns'].append("上吊线反转")

                # 3. 倒锤子线 (看涨)
                elif (upper_shadow_ratio > 0.6 and lower_shadow_ratio < 0.1 and
                      body_ratio < 0.3):
                    patterns['bullish_patterns'].append("倒锤子线")
                    patterns['reversal_patterns'].append("倒锤子线反转")

                # 4. 流星线 (看跌) - 优化识别逻辑
                elif (upper_shadow_ratio > 0.6 and lower_shadow_ratio < 0.1 and
                      body_ratio < 0.3 and current['close'] < current['open']):
                    patterns['bearish_patterns'].append("流星线")
                    patterns['reversal_patterns'].append("流星线反转")

                # 5. 十字星
                elif body_ratio < 0.1:
                    if upper_shadow_ratio > 0.4 and lower_shadow_ratio > 0.4:
                        patterns['neutral_patterns'].append("十字星")
                        patterns['reversal_patterns'].append("十字星反转")
                    elif upper_shadow_ratio > 0.7:
                        patterns['bearish_patterns'].append("长上影十字星")
                    elif lower_shadow_ratio > 0.7:
                        patterns['bullish_patterns'].append("长下影十字星")

                # 6. 大阳线/大阴线
                elif body_ratio > 0.7:
                    if current['close'] > current['open']:
                        patterns['bullish_patterns'].append("大阳线")
                        patterns['continuation_patterns'].append("大阳线延续")
                    else:
                        patterns['bearish_patterns'].append("大阴线")
                        patterns['continuation_patterns'].append("大阴线延续")

                # 7. 小实体K线
                elif body_ratio < 0.3:
                    patterns['neutral_patterns'].append("小实体")

            # 检查多K线组合形态
            if len(data) >= 2:
                self._detect_multi_kline_patterns(data, patterns)

            # 去重并限制数量
            for category in patterns:
                patterns[category] = list(dict.fromkeys(patterns[category]))[:3]

        except Exception as e:
            log(f"K线形态识别失败: {e}")
            patterns = {'neutral_patterns': ['形态分析中']}

        return patterns

    def _detect_multi_kline_patterns(self, data: pd.DataFrame, patterns: Dict[str, List[str]]):
        """检测多K线组合形态"""
        current = data.iloc[-1]
        prev = data.iloc[-2]

        # 看涨吞没
        if (prev['close'] < prev['open'] and  # 前一根是阴线
            current['close'] > current['open'] and  # 当前是阳线
            current['open'] < prev['close'] and  # 开盘价低于前收盘
            current['close'] > prev['open']):  # 收盘价高于前开盘
            patterns['bullish_patterns'].append("看涨吞没")
            patterns['reversal_patterns'].append("看涨吞没反转")

        # 看跌吞没
        elif (prev['close'] > prev['open'] and  # 前一根是阳线
              current['close'] < current['open'] and  # 当前是阴线
              current['open'] > prev['close'] and  # 开盘价高于前收盘
              current['close'] < prev['open']):  # 收盘价低于前开盘
            patterns['bearish_patterns'].append("看跌吞没")
            patterns['reversal_patterns'].append("看跌吞没反转")

        # 启明星 (三K线形态)
        if len(data) >= 3:
            first = data.iloc[-3]
            second = data.iloc[-2]
            third = data.iloc[-1]

            if (first['close'] < first['open'] and  # 第一根阴线
                abs(second['close'] - second['open']) < abs(first['close'] - first['open']) * 0.3 and  # 第二根小实体
                third['close'] > third['open'] and  # 第三根阳线
                third['close'] > (first['open'] + first['close']) / 2):  # 第三根收盘超过第一根中点
                patterns['bullish_patterns'].append("启明星")
                patterns['reversal_patterns'].append("启明星反转")

        # 黄昏星 (三K线形态)
        if len(data) >= 3:
            first = data.iloc[-3]
            second = data.iloc[-2]
            third = data.iloc[-1]

            if (first['close'] > first['open'] and  # 第一根阳线
                abs(second['close'] - second['open']) < abs(first['close'] - first['open']) * 0.3 and  # 第二根小实体
                third['close'] < third['open'] and  # 第三根阴线
                third['close'] < (first['open'] + first['close']) / 2):  # 第三根收盘低于第一根中点
                patterns['bearish_patterns'].append("黄昏星")
                patterns['reversal_patterns'].append("黄昏星反转")

    def optimized_multi_timeframe_confirmation(self, symbol_results: dict) -> dict:
        """
        优化的多时间周期协同分析
        修复确认逻辑，改进权重配置和判断标准
        """
        confirmation = {
            'trend_alignment': False,
            'signal_consistency': False,
            'momentum_confirmation': False,
            'risk_level': 'medium',
            'confidence_score': 0.0
        }

        # 获取各周期得分和详细信息
        timeframe_data = {}
        for tf in self.timeframes:
            if tf in symbol_results:
                data = symbol_results[tf]
                timeframe_data[tf] = {
                    'score': data.get('total_score', 0),
                    'rsi': data.get('rsi', 50),
                    'macd_signal': 1 if data.get('macd', 0) > 0 else 0,
                    'trend_score': data.get('trend_score', 0),
                    'pattern_score': data.get('pattern_score', 0)
                }

        if not timeframe_data:
            return confirmation

        # 1. 趋势一致性检查
        trend_scores = []
        for tf, weight in self.timeframe_weights.items():
            if tf in timeframe_data:
                trend_score = timeframe_data[tf]['trend_score']
                weighted_score = trend_score * weight
                trend_scores.append(weighted_score)

        avg_trend_score = sum(trend_scores) / sum(self.timeframe_weights[tf] for tf in timeframe_data.keys())
        confirmation['trend_alignment'] = avg_trend_score > 1.5

        # 2. 信号一致性检查
        signal_consistency_score = 0
        total_weight = 0

        for tf, weight in self.timeframe_weights.items():
            if tf in timeframe_data:
                data = timeframe_data[tf]

                # RSI一致性
                if 30 < data['rsi'] < 70:  # RSI在合理范围
                    signal_consistency_score += weight * 0.3

                # MACD一致性
                if data['macd_signal'] > 0:  # MACD金叉
                    signal_consistency_score += weight * 0.4

                # 综合得分一致性
                if data['score'] > 1.5:
                    signal_consistency_score += weight * 0.3

                total_weight += weight

        signal_consistency_ratio = signal_consistency_score / total_weight if total_weight > 0 else 0
        confirmation['signal_consistency'] = signal_consistency_ratio > 0.6

        # 3. 动量确认
        momentum_scores = []
        for tf in ['1d', '4h', '1h']:  # 重点关注这三个周期
            if tf in timeframe_data:
                momentum_scores.append(timeframe_data[tf]['score'])

        if len(momentum_scores) >= 2:
            avg_momentum = sum(momentum_scores) / len(momentum_scores)
            confirmation['momentum_confirmation'] = avg_momentum > 2.0

        # 4. 综合置信度评分
        confidence_factors = []
        if confirmation['trend_alignment']:
            confidence_factors.append(0.4)
        if confirmation['signal_consistency']:
            confidence_factors.append(0.4)
        if confirmation['momentum_confirmation']:
            confidence_factors.append(0.2)

        confirmation['confidence_score'] = sum(confidence_factors)

        # 5. 风险等级评估
        if '1d' in symbol_results and 'real_data' in symbol_results['1d']:
            real_data = symbol_results['1d']['real_data']
            risk_score = self._calculate_risk_score(real_data, avg_trend_score, confirmation['confidence_score'])

            if risk_score <= 2 and confirmation['confidence_score'] > 0.6:
                confirmation['risk_level'] = 'low'
            elif risk_score <= 4 and confirmation['confidence_score'] > 0.4:
                confirmation['risk_level'] = 'medium'
            else:
                confirmation['risk_level'] = 'high'

        return confirmation

    def _calculate_risk_score(self, real_data: Dict, trend_score: float, confidence: float) -> int:
        """计算风险评分"""
        risk_score = 0

        # 市值风险
        market_cap = real_data.get('market_cap', 0)
        if market_cap < 500000000:  # <5亿
            risk_score += 2
        elif market_cap < 2000000000:  # <20亿
            risk_score += 1

        # 排名风险
        rank = real_data.get('market_cap_rank', 999)
        if rank > 100:
            risk_score += 2
        elif rank > 50:
            risk_score += 1

        # 波动率风险
        volatility = abs(real_data.get('price_change_24h', 0))
        if volatility > 20:
            risk_score += 2
        elif volatility > 10:
            risk_score += 1

        # 技术面风险
        if trend_score < 1.0:
            risk_score += 1
        if confidence < 0.3:
            risk_score += 1

        return risk_score

    def analyze_symbol_comprehensive(self, symbol: str, timeframe: str) -> Optional[Dict]:
        """综合分析单个币种的单个时间周期"""
        try:
            # 获取K线数据
            data = self.get_optimized_kline_data(symbol, timeframe)
            if data is None or len(data) < 20:
                return None

            # 计算技术指标
            data = self.calculate_optimized_technical_indicators(data)

            # 获取真实市场数据
            market_data = self.get_real_market_data()
            real_data = market_data.get(symbol, {})

            # 评估技术指标
            indicator_scores = self.evaluate_optimized_indicators(data)

            # 检测K线形态
            pattern_analysis = self.detect_optimized_kline_patterns(data)

            # 计算各类得分
            pattern_score = self._calculate_pattern_score(pattern_analysis)
            indicator_score = self._calculate_indicator_score(indicator_scores)
            trend_score = self._calculate_trend_score(data)
            volume_score = self._calculate_volume_score(data)
            market_score = self._calculate_market_score(real_data)

            # 综合得分
            total_score = (
                pattern_score * self.analysis_weights['pattern'] +
                indicator_score * self.analysis_weights['indicator'] +
                trend_score * self.analysis_weights['trend'] +
                volume_score * self.analysis_weights['volume'] +
                market_score * self.analysis_weights['market_data']
            )

            # 生成交易信号
            signals = self._generate_comprehensive_signals(data, real_data, pattern_analysis, indicator_scores)

            # 获取最新数据
            latest = data.iloc[-1]

            result = {
                'symbol': symbol,
                'timeframe': timeframe,
                'total_score': total_score,
                'pattern_score': pattern_score,
                'indicator_score': indicator_score,
                'trend_score': trend_score,
                'volume_score': volume_score,
                'market_score': market_score,
                'patterns': pattern_analysis,
                'signals': signals,
                'rsi': latest.get('rsi', 50),
                'macd': latest.get('macd', 0),
                'bb_position': latest.get('bb_position', 0.5),
                'volume_ratio': latest.get('volume_ratio', 1.0),
                'current_price': latest['close'],
                'real_data': real_data,
                'analysis_time': datetime.now().isoformat()
            }

            return result

        except Exception as e:
            log(f"分析 {symbol} {timeframe} 失败: {e}")
            return None

    def _calculate_pattern_score(self, pattern_analysis: Dict) -> float:
        """计算形态得分"""
        score = 1.0  # 基础分

        # 看涨形态加分
        bullish_count = len(pattern_analysis.get('bullish_patterns', []))
        score += bullish_count * 0.5

        # 反转形态加分
        reversal_count = len(pattern_analysis.get('reversal_patterns', []))
        score += reversal_count * 0.3

        # 看跌形态减分
        bearish_count = len(pattern_analysis.get('bearish_patterns', []))
        score -= bearish_count * 0.3

        return max(score, 0.1)

    def _calculate_indicator_score(self, indicator_scores: Dict) -> float:
        """计算技术指标得分"""
        weights = {
            'rsi_score': 0.25,
            'macd_score': 0.30,
            'bb_score': 0.20,
            'ma_score': 0.15,
            'momentum_score': 0.05,
            'volume_score': 0.05
        }

        total_score = 0
        for indicator, weight in weights.items():
            total_score += indicator_scores.get(indicator, 1.0) * weight

        return total_score

    def _calculate_trend_score(self, data: pd.DataFrame) -> float:
        """计算趋势得分"""
        latest = data.iloc[-1]

        score = 1.0

        # 移动平均线趋势
        if not pd.isna(latest['sma_5']) and not pd.isna(latest['sma_20']):
            if latest['close'] > latest['sma_5'] > latest['sma_20']:
                score += 1.0  # 多头排列
            elif latest['close'] > latest['sma_5']:
                score += 0.5  # 短期向上
            elif latest['close'] < latest['sma_5'] < latest['sma_20']:
                score -= 0.5  # 空头排列

        # 价格动量
        if not pd.isna(latest['momentum_5']):
            momentum = latest['momentum_5']
            if momentum > 0.02:
                score += 0.5
            elif momentum < -0.02:
                score -= 0.5

        return max(score, 0.1)

    def _calculate_volume_score(self, data: pd.DataFrame) -> float:
        """计算成交量得分"""
        latest = data.iloc[-1]

        score = 1.0

        if not pd.isna(latest['volume_ratio']):
            vol_ratio = latest['volume_ratio']
            if vol_ratio > 1.5:  # 放量
                score += 0.8
            elif vol_ratio > 1.2:
                score += 0.4
            elif vol_ratio < 0.8:  # 缩量
                score -= 0.2

        return max(score, 0.1)

    def _calculate_market_score(self, real_data: Dict) -> float:
        """计算市场数据得分"""
        if not real_data:
            return 1.0

        score = 1.0

        # 市值排名加分
        rank = real_data.get('market_cap_rank', 999)
        if rank <= 20:
            score += 0.5
        elif rank <= 50:
            score += 0.3
        elif rank <= 100:
            score += 0.1

        # 24h涨跌幅
        change_24h = real_data.get('price_change_24h', 0)
        if change_24h > 5:
            score += 0.3
        elif change_24h > 2:
            score += 0.1
        elif change_24h < -10:
            score -= 0.3

        return max(score, 0.1)

    def _generate_comprehensive_signals(self, data: pd.DataFrame, real_data: Dict,
                                      pattern_analysis: Dict, indicator_scores: Dict) -> List[str]:
        """生成综合交易信号"""
        signals = []
        latest = data.iloc[-1]

        # 技术指标信号
        if indicator_scores.get('rsi_score', 0) > 1.5:
            if latest['rsi'] < 35:
                signals.append("RSI超卖反弹")
            elif latest['rsi'] > 65:
                signals.append("RSI强势突破")

        if indicator_scores.get('macd_score', 0) > 2.0:
            signals.append("MACD金叉确认")

        # 形态信号
        bullish_patterns = pattern_analysis.get('bullish_patterns', [])
        if bullish_patterns:
            signals.append(f"看涨形态: {bullish_patterns[0]}")

        reversal_patterns = pattern_analysis.get('reversal_patterns', [])
        if reversal_patterns:
            signals.append(f"反转信号: {reversal_patterns[0]}")

        # 成交量信号
        if not pd.isna(latest['volume_ratio']) and latest['volume_ratio'] > 1.5:
            signals.append("成交量放大")

        # 真实市场数据信号
        if real_data:
            price_24h = real_data.get('price_change_24h', 0)
            if price_24h > 5:
                signals.append("24h强势上涨")
            elif price_24h < -10:
                signals.append("超跌反弹机会")

        return signals[:5]  # 最多返回5个信号

    def calculate_trading_recommendations(self, df: pd.DataFrame, current_price: float, symbol: str) -> Dict:
        """
        计算实盘交易建议
        基于技术分析计算买入价、止盈价、止损价等
        """
        try:
            if df is None or df.empty or len(df) < 20:
                return self._get_default_trading_recommendation(current_price)

            # 计算技术指标
            indicators = self._get_latest_indicators(df)

            # 计算支撑阻力位
            support_resistance = self._calculate_support_resistance(df)

            # 计算ATR（平均真实波幅）
            atr = self._calculate_atr(df)

            # 计算斐波那契回撤位
            fib_levels = self._calculate_fibonacci_levels(df)

            # 基于技术分析计算交易建议
            trading_advice = self._generate_trading_advice(
                current_price, indicators, support_resistance, atr, fib_levels, symbol
            )

            return trading_advice

        except Exception as e:
            log(f"⚠️ {symbol} 交易建议计算失败: {e}")
            return self._get_default_trading_recommendation(current_price)

    def _get_latest_indicators(self, df: pd.DataFrame) -> Dict:
        """获取最新的技术指标"""
        try:
            latest = df.iloc[-1]
            return {
                'rsi': latest.get('rsi', 50),
                'bb_upper': latest.get('bb_upper', latest['close'] * 1.02),
                'bb_lower': latest.get('bb_lower', latest['close'] * 0.98),
                'bb_middle': latest.get('bb_middle', latest['close']),
                'sma_20': latest.get('sma_20', latest['close']),
                'volume_ratio': latest.get('volume_ratio', 1.0)
            }
        except:
            return {
                'rsi': 50,
                'bb_upper': df['close'].iloc[-1] * 1.02,
                'bb_lower': df['close'].iloc[-1] * 0.98,
                'bb_middle': df['close'].iloc[-1],
                'sma_20': df['close'].iloc[-1],
                'volume_ratio': 1.0
            }

    def _calculate_support_resistance(self, df: pd.DataFrame) -> Dict:
        """计算支撑阻力位"""
        try:
            # 计算近期高低点
            recent_data = df.tail(20)

            # 找出重要的支撑阻力位
            recent_highs = recent_data['high'].nlargest(3).tolist()
            recent_lows = recent_data['low'].nsmallest(3).tolist()

            # 计算动态支撑阻力位
            current_high = df['high'].iloc[-1]
            current_low = df['low'].iloc[-1]
            current_close = df['close'].iloc[-1]

            return {
                'resistance_levels': sorted(recent_highs, reverse=True)[:2],
                'support_levels': sorted(recent_lows)[:2],
                'recent_high': max(recent_highs) if recent_highs else current_high,
                'recent_low': min(recent_lows) if recent_lows else current_low,
                'pivot_point': (current_high + current_low + current_close) / 3
            }

        except Exception as e:
            log(f"支撑阻力位计算失败: {e}")
            current_price = df['close'].iloc[-1]
            return {
                'resistance_levels': [current_price * 1.05, current_price * 1.08],
                'support_levels': [current_price * 0.95, current_price * 0.92],
                'recent_high': current_price * 1.05,
                'recent_low': current_price * 0.95,
                'pivot_point': current_price
            }

    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> float:
        """计算平均真实波幅（ATR）"""
        try:
            # 计算真实波幅
            high_low = df['high'] - df['low']
            high_close_prev = abs(df['high'] - df['close'].shift(1))
            low_close_prev = abs(df['low'] - df['close'].shift(1))

            true_range = pd.concat([high_low, high_close_prev, low_close_prev], axis=1).max(axis=1)
            atr = true_range.rolling(window=period).mean().iloc[-1]

            return atr if not pd.isna(atr) else df['close'].iloc[-1] * 0.02

        except Exception as e:
            log(f"ATR计算失败: {e}")
            return df['close'].iloc[-1] * 0.02  # 默认2%波动率

    def _calculate_fibonacci_levels(self, df: pd.DataFrame) -> Dict:
        """计算斐波那契回撤位"""
        try:
            # 找出最近的重要高低点
            recent_data = df.tail(50)  # 最近50根K线

            swing_high = recent_data['high'].max()
            swing_low = recent_data['low'].min()

            # 计算斐波那契回撤位
            diff = swing_high - swing_low

            fib_levels = {
                'fib_0': swing_high,
                'fib_236': swing_high - (diff * 0.236),
                'fib_382': swing_high - (diff * 0.382),
                'fib_500': swing_high - (diff * 0.500),
                'fib_618': swing_high - (diff * 0.618),
                'fib_786': swing_high - (diff * 0.786),
                'fib_100': swing_low,
                # 扩展位
                'fib_1272': swing_high + (diff * 0.272),
                'fib_1618': swing_high + (diff * 0.618)
            }

            return fib_levels

        except Exception as e:
            log(f"斐波那契计算失败: {e}")
            current_price = df['close'].iloc[-1]
            return {
                'fib_236': current_price * 1.02,
                'fib_382': current_price * 1.04,
                'fib_618': current_price * 1.06,
                'fib_1272': current_price * 1.08,
                'fib_1618': current_price * 1.10
            }

    def _generate_trading_advice(self, current_price: float, indicators: Dict,
                               support_resistance: Dict, atr: float, fib_levels: Dict, symbol: str) -> Dict:
        """生成具体的交易建议"""
        try:
            # 1. 计算建议买入价
            buy_prices = self._calculate_buy_prices(current_price, indicators, support_resistance, atr)

            # 2. 计算止盈价格
            take_profit_prices = self._calculate_take_profit_prices(current_price, support_resistance, fib_levels, atr)

            # 3. 计算止损价格
            stop_loss_price = self._calculate_stop_loss_price(current_price, support_resistance, atr, indicators)

            # 4. 计算风险收益比
            risk_reward_ratio = self._calculate_risk_reward_ratio(buy_prices['recommended'], take_profit_prices['target1'], stop_loss_price)

            # 5. 确定仓位建议
            position_size = self._calculate_position_size(risk_reward_ratio, indicators['rsi'], current_price)

            # 6. 生成交易策略
            strategy = self._generate_trading_strategy(indicators, support_resistance, symbol)

            return {
                'buy_price_range': f"${buy_prices['min']:.6f} - ${buy_prices['max']:.6f}",
                'buy_price_recommended': buy_prices['recommended'],
                'take_profit_target1': take_profit_prices['target1'],
                'take_profit_target2': take_profit_prices['target2'],
                'stop_loss_price': stop_loss_price,
                'risk_reward_ratio': risk_reward_ratio,
                'position_size_percent': position_size,
                'position_size_text': f"{position_size['min']}-{position_size['max']}% 总资金",
                'strategy': strategy,
                'confidence_level': self._assess_confidence_level(indicators, risk_reward_ratio)
            }

        except Exception as e:
            log(f"交易建议生成失败: {e}")
            return self._get_default_trading_recommendation(current_price)

    def _calculate_buy_prices(self, current_price: float, indicators: Dict, support_resistance: Dict, atr: float) -> Dict:
        """计算建议买入价格"""
        try:
            # 基于多个因素计算买入区间
            factors = []

            # 1. 基于支撑位
            support_levels = support_resistance.get('support_levels', [])
            if support_levels:
                nearest_support = max([s for s in support_levels if s < current_price], default=current_price * 0.98)
                factors.append(nearest_support)

            # 2. 基于布林带下轨
            bb_lower = indicators.get('bb_lower', current_price * 0.98)
            factors.append(bb_lower)

            # 3. 基于RSI超卖区域
            rsi = indicators.get('rsi', 50)
            if rsi < 40:  # RSI偏低，可以适当激进
                factors.append(current_price * 0.995)
            elif rsi < 30:  # RSI超卖
                factors.append(current_price * 1.005)
            else:  # RSI正常，保守一些
                factors.append(current_price * 0.985)

            # 4. 基于ATR计算合理回调
            atr_based = current_price - (atr * 0.5)
            factors.append(atr_based)

            # 计算买入区间
            min_buy = min(factors)
            max_buy = max(min_buy, current_price * 0.995)  # 最高不超过当前价格的99.5%
            recommended_buy = (min_buy + max_buy) / 2

            return {
                'min': max(min_buy, current_price * 0.90),  # 最低不超过10%折扣
                'max': min(max_buy, current_price * 0.999),  # 最高不超过当前价格
                'recommended': recommended_buy
            }

        except Exception as e:
            log(f"买入价计算失败: {e}")
            return {
                'min': current_price * 0.98,
                'max': current_price * 0.995,
                'recommended': current_price * 0.99
            }

    def _calculate_take_profit_prices(self, current_price: float, support_resistance: Dict, fib_levels: Dict, atr: float) -> Dict:
        """计算止盈价格"""
        try:
            # 第一止盈目标（保守）
            target1_factors = []

            # 基于阻力位
            resistance_levels = support_resistance.get('resistance_levels', [])
            if resistance_levels:
                nearest_resistance = min([r for r in resistance_levels if r > current_price], default=current_price * 1.05)
                target1_factors.append(nearest_resistance)

            # 基于斐波那契38.2%回撤
            fib_382 = fib_levels.get('fib_382', current_price * 1.04)
            if fib_382 > current_price:
                target1_factors.append(fib_382)

            # 基于ATR
            atr_target1 = current_price + (atr * 2)
            target1_factors.append(atr_target1)

            # 第二止盈目标（激进）
            target2_factors = []

            # 基于斐波那契61.8%回撤
            fib_618 = fib_levels.get('fib_618', current_price * 1.06)
            if fib_618 > current_price:
                target2_factors.append(fib_618)

            # 基于斐波那契扩展位
            fib_1272 = fib_levels.get('fib_1272', current_price * 1.08)
            target2_factors.append(fib_1272)

            # 基于ATR
            atr_target2 = current_price + (atr * 3.5)
            target2_factors.append(atr_target2)

            # 计算最终目标
            target1 = min(target1_factors) if target1_factors else current_price * 1.05
            target2 = max(target2_factors) if target2_factors else current_price * 1.10

            # 确保target2 > target1
            if target2 <= target1:
                target2 = target1 * 1.05

            return {
                'target1': max(target1, current_price * 1.02),  # 至少2%收益
                'target2': max(target2, current_price * 1.05)   # 至少5%收益
            }

        except Exception as e:
            log(f"止盈价计算失败: {e}")
            return {
                'target1': current_price * 1.05,
                'target2': current_price * 1.10
            }

    def _calculate_stop_loss_price(self, current_price: float, support_resistance: Dict, atr: float, indicators: Dict) -> float:
        """计算止损价格"""
        try:
            stop_loss_factors = []

            # 1. 基于支撑位
            support_levels = support_resistance.get('support_levels', [])
            if support_levels:
                nearest_support = max([s for s in support_levels if s < current_price], default=current_price * 0.95)
                # 止损设在支撑位下方一点
                stop_loss_factors.append(nearest_support * 0.995)

            # 2. 基于ATR
            atr_stop_loss = current_price - (atr * 1.5)
            stop_loss_factors.append(atr_stop_loss)

            # 3. 基于布林带下轨
            bb_lower = indicators.get('bb_lower', current_price * 0.96)
            stop_loss_factors.append(bb_lower * 0.99)

            # 4. 基于固定百分比（风险控制）
            percentage_stop = current_price * 0.95  # 5%止损
            stop_loss_factors.append(percentage_stop)

            # 选择最保守的止损位（最高的价格）
            stop_loss = max(stop_loss_factors)

            # 确保止损不超过3%（风险控制）
            max_stop_loss = current_price * 0.97
            final_stop_loss = min(stop_loss, max_stop_loss)

            return max(final_stop_loss, current_price * 0.92)  # 最大止损8%

        except Exception as e:
            log(f"止损价计算失败: {e}")
            return current_price * 0.95  # 默认5%止损

    def _calculate_risk_reward_ratio(self, buy_price: float, take_profit: float, stop_loss: float) -> float:
        """计算风险收益比"""
        try:
            potential_profit = take_profit - buy_price
            potential_loss = buy_price - stop_loss

            if potential_loss <= 0:
                return 3.0  # 默认比例

            ratio = potential_profit / potential_loss
            return round(ratio, 2)

        except:
            return 2.0  # 默认2:1比例

    def _calculate_position_size(self, risk_reward_ratio: float, rsi: float, current_price: float) -> Dict:
        """计算建议仓位大小"""
        try:
            # 基础仓位
            base_position = 3  # 3%基础仓位

            # 根据风险收益比调整
            if risk_reward_ratio >= 3.0:
                position_adjustment = 2  # 增加2%
            elif risk_reward_ratio >= 2.5:
                position_adjustment = 1  # 增加1%
            elif risk_reward_ratio >= 2.0:
                position_adjustment = 0  # 不调整
            else:
                position_adjustment = -1  # 减少1%

            # 根据RSI调整
            if rsi < 30:  # 超卖
                rsi_adjustment = 1
            elif rsi < 40:  # 偏低
                rsi_adjustment = 0.5
            elif rsi > 70:  # 超买
                rsi_adjustment = -1
            else:
                rsi_adjustment = 0

            # 计算最终仓位
            final_position = base_position + position_adjustment + rsi_adjustment

            # 限制仓位范围
            min_position = max(1, final_position - 1)
            max_position = min(8, final_position + 1)

            return {
                'min': int(min_position),
                'max': int(max_position),
                'recommended': int(final_position)
            }

        except:
            return {'min': 2, 'max': 5, 'recommended': 3}

    def _generate_trading_strategy(self, indicators: Dict, support_resistance: Dict, symbol: str) -> str:
        """生成交易策略描述"""
        try:
            rsi = indicators.get('rsi', 50)

            if rsi < 30:
                return "超卖反弹策略：分批建仓，等待技术指标修复"
            elif rsi < 40:
                return "逢低吸纳策略：在支撑位附近建仓"
            elif rsi > 70:
                return "突破跟进策略：确认突破后小仓位跟进"
            else:
                return "均衡配置策略：正常仓位，关注关键位突破"

        except:
            return "稳健投资策略：控制风险，分批建仓"

    def _assess_confidence_level(self, indicators: Dict, risk_reward_ratio: float) -> str:
        """评估交易建议的置信度"""
        try:
            confidence_score = 0

            # RSI评分
            rsi = indicators.get('rsi', 50)
            if 30 <= rsi <= 70:
                confidence_score += 1
            elif rsi < 30 or rsi > 70:
                confidence_score += 0.5

            # 风险收益比评分
            if risk_reward_ratio >= 2.5:
                confidence_score += 1
            elif risk_reward_ratio >= 2.0:
                confidence_score += 0.5

            # 成交量评分
            volume_ratio = indicators.get('volume_ratio', 1.0)
            if volume_ratio > 1.2:
                confidence_score += 0.5

            # 转换为置信度等级
            if confidence_score >= 2.0:
                return "高"
            elif confidence_score >= 1.0:
                return "中"
            else:
                return "低"

        except:
            return "中"

    def _get_default_trading_recommendation(self, current_price: float) -> Dict:
        """获取默认交易建议（当计算失败时使用）"""
        return {
            'buy_price_range': f"${current_price * 0.98:.6f} - ${current_price * 0.995:.6f}",
            'buy_price_recommended': current_price * 0.99,
            'take_profit_target1': current_price * 1.05,
            'take_profit_target2': current_price * 1.10,
            'stop_loss_price': current_price * 0.95,
            'risk_reward_ratio': 2.0,
            'position_size_percent': {'min': 2, 'max': 5, 'recommended': 3},
            'position_size_text': "2-5% 总资金",
            'strategy': "稳健投资策略：控制风险，分批建仓",
            'confidence_level': "中"
        }

    def _pattern_analysis_filter(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """形态分析筛选功能"""
        log("🎯 开始形态分析选币...")

        # 根据速度模式决定分析数量
        if self.speed_mode == 'fast':
            max_analysis = 100  # 快速模式：分析前100个
        elif self.speed_mode == 'normal':
            max_analysis = 300  # 正常模式：分析前300个
        elif self.speed_mode == 'safe':
            max_analysis = 500  # 安全模式：分析前500个
        else:
            max_analysis = min(len(cryptocurrencies), 1000)  # 最多1000个

        # 选择要分析的币种
        top_cryptos = cryptocurrencies[:max_analysis]
        analysis_results = []

        log(f"📊 分析 {len(top_cryptos)} 个币种的技术形态 (速度模式: {self.speed_mode})...")

        for i, crypto in enumerate(top_cryptos, 1):
            symbol = crypto.get('symbol', '').upper()
            log(f"进度 {i}/{len(top_cryptos)}: 分析 {symbol}...")

            try:
                # 多时间周期分析
                symbol_results = {}
                for timeframe in ['1d', '4h', '1h']:  # 主要时间周期
                    result = self.analyze_symbol_comprehensive(symbol, timeframe)
                    if result:
                        symbol_results[timeframe] = result

                if symbol_results:
                    # 计算多周期综合得分
                    total_score = 0
                    weight_sum = 0

                    for tf, result in symbol_results.items():
                        weight = self.timeframe_weights.get(tf, 0.1)
                        total_score += result['total_score'] * weight
                        weight_sum += weight

                    multi_tf_score = total_score / weight_sum if weight_sum > 0 else 0

                    # 多周期确认
                    confirmation = self.optimized_multi_timeframe_confirmation(symbol_results)

                    # 收集所有形态
                    all_patterns = []
                    for result in symbol_results.values():
                        patterns = result.get('patterns', {})
                        for pattern_list in patterns.values():
                            all_patterns.extend(pattern_list)

                    # 收集所有信号
                    all_signals = []
                    for result in symbol_results.values():
                        all_signals.extend(result.get('signals', []))

                    # 生成交易建议
                    if multi_tf_score >= 2.5 and confirmation['confidence_score'] > 0.6:
                        trading_suggestion = "强烈买入"
                    elif multi_tf_score >= 2.0 and confirmation['confidence_score'] > 0.4:
                        trading_suggestion = "买入"
                    elif multi_tf_score >= 1.5:
                        trading_suggestion = "谨慎买入"
                    else:
                        trading_suggestion = "观望"

                    # 只保留得分较高的币种
                    if multi_tf_score >= 1.5:  # 筛选标准
                        # 计算交易建议（基于日线数据）
                        trading_recommendations = None
                        if '1d' in symbol_results:
                            daily_result = symbol_results['1d']
                            if 'real_data' in daily_result and daily_result['real_data']:
                                # 获取日线K线数据
                                daily_kline = self.get_optimized_kline_data(symbol, '1d')
                                if daily_kline is not None and not daily_kline.empty:
                                    current_price = crypto.get('current_price', daily_kline['close'].iloc[-1])
                                    trading_recommendations = self.calculate_trading_recommendations(
                                        daily_kline, current_price, symbol
                                    )

                        analysis_result = {
                            'id': crypto.get('id', symbol.lower()),  # 添加缺失的id字段
                            'symbol': symbol,
                            'name': crypto.get('name', ''),
                            'current_price': crypto.get('current_price', 0),
                            'market_cap': crypto.get('market_cap', 0),
                            'market_cap_rank': crypto.get('market_cap_rank', 999),
                            'price_change_24h': crypto.get('price_change_percentage_24h', 0),
                            'total_score': multi_tf_score,
                            'confidence_score': confirmation['confidence_score'],
                            'risk_level': confirmation['risk_level'],
                            'trading_suggestion': trading_suggestion,
                            'patterns': list(set(all_patterns))[:5],  # 去重，最多5个
                            'signals': list(set(all_signals))[:5],    # 去重，最多5个
                            'timeframe_results': symbol_results,
                            'confirmation': confirmation,
                            'trading_recommendations': trading_recommendations  # 新增交易建议
                        }
                        analysis_results.append(analysis_result)

                        log(f"  ✅ {symbol}: 得分 {multi_tf_score:.1f}, 建议 {trading_suggestion}")
                    else:
                        log(f"  ❌ {symbol}: 得分 {multi_tf_score:.1f} (不符合标准)")

            except Exception as e:
                log(f"  ❌ {symbol}: 分析失败 - {e}")
                continue

            # 添加延迟避免过快请求
            time.sleep(0.5)

        # 按得分排序
        analysis_results.sort(key=lambda x: x['total_score'], reverse=True)

        log(f"🎉 形态分析完成！发现 {len(analysis_results)} 个推荐币种")

        # 发送企业微信推送
        if analysis_results:
            self._send_pattern_analysis_notification(analysis_results)

        # 转换为标准格式返回
        filtered_cryptos = []
        for result in analysis_results:
            crypto = {
                'id': result['id'],  # 添加id字段
                'symbol': result['symbol'],
                'name': result['name'],
                'current_price': result['current_price'],
                'market_cap': result['market_cap'],
                'market_cap_rank': result['market_cap_rank'],
                'price_change_percentage_24h': result['price_change_24h'],
                'total_volume': 0,  # 占位符
                'analysis_score': result['total_score'],
                'trading_suggestion': result['trading_suggestion'],
                'patterns': result['patterns'],
                'signals': result['signals']
            }
            filtered_cryptos.append(crypto)

        return filtered_cryptos

    def _send_pattern_analysis_notification(self, analysis_results: List[Dict]):
        """发送形态分析结果到多个企业微信机器人 - 支持分段推送"""
        try:
            # 导入推送模板
            from notification_templates import NotificationTemplates

            # 生成分段推送内容
            messages = NotificationTemplates.pattern_analysis_template_split(analysis_results)

            log(f"📱 准备分段推送: {len(messages)} 条消息")

            # 分段发送到所有启用的机器人
            for i, message in enumerate(messages, 1):
                log(f"📤 发送第 {i}/{len(messages)} 段消息...")
                success = self._send_to_multiple_wechat_bots(message, f"形态分析结果 (第{i}段)")

                if not success:
                    log(f"⚠️ 第 {i} 段消息推送失败，继续发送下一段...")

                # 分段之间稍作延迟，避免频率限制
                if i < len(messages):
                    time.sleep(2)

        except ImportError:
            # 如果没有推送模板，使用简化版本
            self._send_simple_notification(analysis_results)
        except Exception as e:
            log(f"❌ 发送企业微信推送失败: {e}")

    def _send_to_multiple_wechat_bots(self, message: str, message_type: str = "消息", target_bots: List[Dict] = None):
        """发送消息到多个微信机器人 - 增强版支持目标选择"""
        # 确定推送目标
        if target_bots is None:
            # 使用所有启用的机器人
            target_bots = [bot for bot in self.wechat_webhooks if bot.get('enabled', True)]

        if not target_bots:
            log("⚠️ 没有可用的微信机器人")
            return False

        log(f"📱 开始推送{message_type}到 {len(target_bots)} 个微信机器人...")

        # 准备推送数据
        data = {
            "msgtype": "markdown",
            "markdown": {
                "content": message
            }
        }

        # 获取推送设置
        send_settings = self.ssr_config.get('wechat_webhooks', {}).get('send_settings', {})
        timeout = send_settings.get('timeout', 10)
        retry_count = send_settings.get('retry_count', 3)
        retry_delay = send_settings.get('retry_delay', 2)
        concurrent_send = send_settings.get('concurrent_send', True)

        success_count = 0
        total_count = len(target_bots)

        if concurrent_send:
            # 并发推送
            import threading
            import queue

            results = queue.Queue()

            def send_to_bot(robot, results_queue):
                """发送到单个机器人的线程函数"""
                try:
                    result = self._send_to_single_wechat_bot(robot, data, timeout, retry_count, retry_delay)
                    results_queue.put((robot['name'], result))
                except Exception as e:
                    results_queue.put((robot['name'], False))
                    log(f"❌ 推送到 {robot['name']} 时发生异常: {e}")

            # 创建并启动线程
            threads = []
            for robot in target_bots:
                thread = threading.Thread(target=send_to_bot, args=(robot, results))
                thread.start()
                threads.append(thread)

            # 等待所有线程完成
            for thread in threads:
                thread.join(timeout=30)  # 最多等待30秒

            # 收集结果
            while not results.empty():
                robot_name, success = results.get()
                if success:
                    success_count += 1
                    log(f"   ✅ {robot_name}: 推送成功")
                else:
                    log(f"   ❌ {robot_name}: 推送失败")

        else:
            # 顺序推送
            for robot in target_bots:
                try:
                    success = self._send_to_single_wechat_bot(robot, data, timeout, retry_count, retry_delay)
                    if success:
                        success_count += 1
                        log(f"   ✅ {robot['name']}: 推送成功")
                    else:
                        log(f"   ❌ {robot['name']}: 推送失败")
                except Exception as e:
                    log(f"   ❌ {robot['name']}: 推送异常 - {e}")

        # 推送结果统计
        log(f"📊 {message_type}推送完成: {success_count}/{total_count} 成功")

        if success_count == total_count:
            log(f"🎉 所有机器人推送成功！")
        elif success_count > 0:
            log(f"⚠️ 部分机器人推送成功 ({success_count}/{total_count})")
        else:
            log(f"❌ 所有机器人推送失败")

        return success_count > 0

    def _send_to_single_wechat_bot(self, robot: Dict, data: Dict, timeout: int, retry_count: int, retry_delay: int) -> bool:
        """发送消息到单个微信机器人 - 增强版支持统计更新"""
        webhook_url = robot.get('webhook_url', '')
        robot_name = robot.get('name', '未命名')

        if not webhook_url:
            log(f"❌ {robot_name}: webhook URL为空")
            # 更新错误统计
            robot['error_count'] = robot.get('error_count', 0) + 1
            self._update_wechat_webhooks()
            return False

        for attempt in range(retry_count):
            try:
                response = self.session.post(
                    webhook_url,
                    json=data,
                    headers={'Content-Type': 'application/json'},
                    timeout=timeout
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get('errcode') == 0:
                        # 更新成功统计
                        robot['success_count'] = robot.get('success_count', 0) + 1
                        robot['last_used'] = datetime.now().isoformat()
                        self._update_wechat_webhooks()
                        return True
                    else:
                        log(f"❌ {robot_name}: 推送失败 - {result}")
                        # 更新错误统计
                        robot['error_count'] = robot.get('error_count', 0) + 1
                        self._update_wechat_webhooks()
                        return False
                else:
                    log(f"❌ {robot_name}: HTTP错误 {response.status_code}")
                    if attempt < retry_count - 1:
                        log(f"   等待 {retry_delay} 秒后重试...")
                        time.sleep(retry_delay)
                        continue
                    return False

            except requests.exceptions.Timeout:
                log(f"⏰ {robot_name}: 请求超时 (尝试 {attempt + 1}/{retry_count})")
                if attempt < retry_count - 1:
                    time.sleep(retry_delay)
                    continue
                return False
            except Exception as e:
                log(f"❌ {robot_name}: 请求异常 - {e}")
                if attempt < retry_count - 1:
                    time.sleep(retry_delay)
                    continue
                return False

        return False

    def _send_simple_notification(self, analysis_results: List[Dict]):
        """发送简化版推送到多个机器人"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            message = f"""# 🎯 加密货币形态分析报告

📅 分析时间: {timestamp}
🔍 分析币种数: {len(analysis_results)}
📊 筛选标准: 综合得分 > 1.5分

## 📈 推荐币种 (按得分排序)

"""

            for i, result in enumerate(analysis_results[:8], 1):  # 显示前8个
                symbol = result['symbol']
                score = result['total_score']
                suggestion = result['trading_suggestion']
                patterns = result['patterns']
                current_price = result['current_price']

                # 根据得分选择emoji
                if score >= 2.5:
                    emoji = "🔥"
                elif score >= 2.0:
                    emoji = "⭐"
                elif score >= 1.5:
                    emoji = "✅"
                else:
                    emoji = "⚪"

                message += f"""### {emoji} {i}. {symbol}
- 综合得分: {score:.1f}分
- 当前价格: ${current_price:.6f}
- 交易建议: {suggestion}
- 识别形态: {', '.join(patterns[:3]) if patterns else '无明显形态'}

"""

            message += """## ⚠️ 风险提示
- 本分析仅供参考，不构成投资建议
- 数字货币投资风险极高，请谨慎决策
- 建议结合其他分析方法综合判断

---
🤖 由终极版加密货币数据抓取器自动生成"""

            # 发送到所有机器人
            self._send_to_multiple_wechat_bots(message, "简化版形态分析结果")

        except Exception as e:
            log(f"❌ 发送简化版推送失败: {e}")

    def test_wechat_notification(self):
        """测试企业微信推送功能 - 支持多机器人"""
        log("🧪 测试企业微信推送功能...")

        try:
            # 尝试使用新的推送模板
            try:
                from notification_templates import NotificationTemplates
                test_message = NotificationTemplates.test_notification_template()
                log("✅ 使用增强版推送模板")
            except ImportError:
                # 回退到内置模板
                test_message = f"""# 🧪 终极版数据抓取器测试

📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🚀 版本: v3.2 Ultimate Edition with Enhanced SSR Support

## ✅ 功能状态检查

### 📊 数据获取功能
- ✅ 多数据源支持 (CoinGecko、CoinMarketCap)
- ✅ SSR代理连接支持
- ✅ 15种选币范围筛选
- ✅ 历史数据批量获取

### 🎯 形态分析功能
- ✅ 5个时间周期分析 (1d/4h/1h/30m/15m)
- ✅ 15种K线形态识别
- ✅ 10+技术指标计算
- ✅ 多时间周期协同确认

### 🌐 网络连接功能
- ✅ SSR代理自动配置
- ✅ 多服务器自动切换
- ✅ 连接状态实时监控
- ✅ 故障自动恢复

### 📱 推送功能
- ✅ 双微信机器人推送
- ✅ 增强版消息模板
- ✅ 并发推送支持
- ✅ Markdown格式支持

## 🎉 测试结果
如果您看到这条消息，说明所有功能工作正常！

---
🤖 终极版加密货币数据抓取器 - 双机器人推送版"""
                log("⚠️ 使用内置推送模板")

            # 发送到所有机器人
            success = self._send_to_multiple_wechat_bots(test_message, "测试消息")

            if success:
                log("✅ 企业微信推送测试成功！")
                return True
            else:
                log("❌ 企业微信推送测试失败")
                return False

        except Exception as e:
            log(f"❌ 企业微信推送测试异常: {e}")
            return False

    def show_main_menu(self):
        """显示主菜单 - 增强版支持自动化调度"""
        log("\n" + "="*80)
        log("🚀 终极版加密货币数据抓取器 v4.0 - 自动化调度增强版")
        log("="*80)

        # 显示调度器状态
        scheduler_status = self.get_scheduler_status()
        log(f"🤖 调度器状态: {scheduler_status}")

        log("\n📋 主要功能:")
        log("1. 🎯 选币范围筛选 (支持自动化运行)")
        log("2. 📊 查看数据库统计")
        log("3. 📈 导出数据到CSV")
        log("4. 🔧 设置速度模式")
        log("5. 🌐 测试网络连接")
        log("6. 📱 测试企业微信推送")
        log("7. 🤖 微信机器人管理 (增强版)")
        log("8. 🔄 清理数据库")
        log("9. 🔀 SSR服务器管理")
        log("10. ⏰ 自动化调度管理")
        log("11. ❌ 退出程序")
        log("="*80)

    def run_main_menu(self):
        """运行主菜单"""
        while True:
            self.show_main_menu()

            try:
                choice = input("\n请选择功能 (1-11): ").strip()

                if choice == '1':
                    # 🆕 增强的选币范围筛选，支持自动化运行
                    self.run_enhanced_selection_menu()

                elif choice == '10':
                    # 🆕 自动化调度管理
                    self.show_scheduler_management()

                elif choice == '2':
                    self.print_statistics()

                elif choice == '3':
                    table_name = input("请选择导出表 (crypto_info/selection_results): ").strip()
                    if table_name in ['crypto_info', 'selection_results']:
                        limit = int(input("请输入导出数量限制 (默认1000): ") or "1000")
                        self.export_data(table_name, limit)
                    else:
                        log("❌ 无效的表名")

                elif choice == '4':
                    self.show_speed_modes()
                    mode = input("请选择速度模式: ").strip()
                    self.set_speed_mode(mode)

                elif choice == '5':
                    self.test_connection()

                elif choice == '6':
                    self.test_wechat_notification()

                elif choice == '7':
                    # 🆕 增强的微信机器人管理
                    self.show_wechat_bot_management()

                elif choice == '8':
                    confirm = input("确认清理数据库？这将删除所有数据 (y/N): ").strip().lower()
                    if confirm == 'y':
                        self._clear_database()

                elif choice == '9':
                    self.manage_ssr_servers()

                elif choice == '11':
                    # 停止调度器
                    if self.scheduler_config['is_running']:
                        log("⏹️ 正在停止自动化调度...")
                        self._stop_scheduler()

                    log("👋 感谢使用终极版加密货币数据抓取器！")
                    break

                else:
                    log("❌ 无效选择，请输入 1-11")

                input("\n按回车键继续...")

            except KeyboardInterrupt:
                log("\n👋 程序被用户中断")
                break
            except Exception as e:
                log(f"❌ 程序错误: {e}")
                input("按回车键继续...")

    def _clear_database(self):
        """清理数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 清空所有表
            tables = ['crypto_info', 'price_history', 'selection_results', 'market_overview']
            for table in tables:
                cursor.execute(f'DELETE FROM {table}')

            conn.commit()
            conn.close()

            log("✅ 数据库清理完成")

        except Exception as e:
            log(f"❌ 数据库清理失败: {e}")

    def manage_ssr_servers(self):
        """SSR服务器管理"""
        while True:
            log("\n" + "="*60)
            log("🔀 SSR服务器管理")
            log("="*60)

            # 显示当前服务器状态
            current_server = self.get_current_ssr_server()
            log(f"🌐 代理状态: {'启用' if self.use_proxy else '禁用'}")
            log(f"⭐ 当前服务器: {current_server.get('host', 'N/A')}:{current_server.get('port', 'N/A')}")

            # 检查端口状态
            if self.use_proxy:
                port_status = "可用" if self._check_ssr_port() else "不可用"
                log(f"🔌 端口状态: {port_status}")

            log("\n📋 管理选项:")
            log("1. 📋 查看所有服务器")
            log("2. 🔄 切换服务器")
            log("3. 🔍 测试当前服务器连接")
            log("4. ⚙️ 启用/禁用代理")
            log("5. 📊 服务器连接统计")
            log("6. ↩️ 返回主菜单")

            try:
                choice = input("\n请选择操作 (1-6): ").strip()

                if choice == '1':
                    self._display_ssr_servers()

                elif choice == '2':
                    if self.switch_ssr_server():
                        log("✅ 服务器切换成功")
                        log("💡 请重新启动SSR客户端以使用新服务器")
                    else:
                        log("⚠️ 服务器切换失败")

                elif choice == '3':
                    self._test_ssr_server_connection()

                elif choice == '4':
                    self._toggle_proxy_mode()

                elif choice == '5':
                    self._show_server_statistics()

                elif choice == '6':
                    break

                else:
                    log("❌ 无效选择，请输入 1-6")

                if choice != '6':
                    input("\n按回车键继续...")

            except KeyboardInterrupt:
                break
            except Exception as e:
                log(f"❌ 操作失败: {e}")
                input("按回车键继续...")

    def _test_ssr_server_connection(self):
        """测试SSR服务器连接"""
        log("🔍 测试SSR服务器连接...")

        if not self.use_proxy:
            log("⚠️ 代理模式未启用")
            return

        # 测试端口连接
        port_available = self._check_ssr_port()
        log(f"🔌 本地代理端口: {'✅ 可用' if port_available else '❌ 不可用'}")

        if not port_available:
            log("💡 请检查SSR客户端是否正常运行")
            return

        # 测试网络连接
        try:
            log("🌐 测试网络连接...")
            test_urls = [
                ('Google', 'https://www.google.com'),
                ('CoinGecko', 'https://api.coingecko.com/api/v3/ping')
            ]

            for name, url in test_urls:
                try:
                    response = self.smart_request(url, 'coingecko', timeout=10)
                    if response and response.status_code == 200:
                        log(f"   ✅ {name}: 连接成功")
                    else:
                        log(f"   ❌ {name}: 连接失败")
                except Exception as e:
                    log(f"   ❌ {name}: 连接异常 - {e}")

        except Exception as e:
            log(f"❌ 网络连接测试失败: {e}")

    def _toggle_proxy_mode(self):
        """切换代理模式"""
        if self.use_proxy:
            log("🔄 正在禁用代理模式...")
            self.use_proxy = False
            self._init_session()  # 重新初始化session
            log("✅ 代理模式已禁用")
        else:
            log("🔄 正在启用代理模式...")
            self.use_proxy = True
            try:
                self._init_proxy_config()
                self._init_session()
                log("✅ 代理模式已启用")
            except Exception as e:
                log(f"❌ 代理模式启用失败: {e}")
                self.use_proxy = False

    def _show_server_statistics(self):
        """显示服务器连接统计"""
        log("📊 服务器连接统计:")
        log("   (此功能可以扩展为记录各服务器的连接成功率、响应时间等)")

        servers = self.ssr_config.get('ssr', {}).get('servers', [])
        if servers:
            for i, server in enumerate(servers):
                status = "⭐ 当前使用" if i == self.current_ssr_server_index else "待机"
                log(f"   {server.get('name', f'服务器{i+1}')}: {status}")
                log(f"      地址: {server.get('host')}:{server.get('port')}")
                log(f"      优先级: {server.get('priority', 'N/A')}")
        else:
            log("   没有多服务器配置")

    def manage_wechat_bots(self):
        """微信机器人管理"""
        while True:
            log("\n" + "="*60)
            log("🤖 微信机器人管理")
            log("="*60)

            # 显示当前机器人状态
            log(f"📱 已配置机器人数量: {len(self.wechat_webhooks)}")

            if self.wechat_webhooks:
                log("\n📋 机器人列表:")
                for i, robot in enumerate(self.wechat_webhooks, 1):
                    name = robot.get('name', f'机器人{i}')
                    enabled = "✅ 启用" if robot.get('enabled', True) else "❌ 禁用"
                    description = robot.get('description', '无描述')
                    log(f"   {i}. {name}: {enabled}")
                    log(f"      描述: {description}")
                    log(f"      URL: {robot.get('webhook_url', 'N/A')[:50]}...")
            else:
                log("⚠️ 没有配置微信机器人")

            log("\n📋 管理选项:")
            log("1. 📋 查看机器人详情")
            log("2. 🧪 测试单个机器人")
            log("3. 🧪 测试所有机器人")
            log("4. 📊 推送统计信息")
            log("5. 🔄 重新加载配置")
            log("6. ↩️ 返回主菜单")

            try:
                choice = input("\n请选择操作 (1-6): ").strip()

                if choice == '1':
                    self._show_wechat_bot_details()

                elif choice == '2':
                    self._test_single_wechat_bot()

                elif choice == '3':
                    self._test_all_wechat_bots()

                elif choice == '4':
                    self._show_wechat_push_statistics()

                elif choice == '5':
                    self._reload_wechat_config()

                elif choice == '6':
                    break

                else:
                    log("❌ 无效选择，请输入 1-6")

                if choice != '6':
                    input("\n按回车键继续...")

            except KeyboardInterrupt:
                break
            except Exception as e:
                log(f"❌ 操作失败: {e}")
                input("按回车键继续...")

    def _show_wechat_bot_details(self):
        """显示微信机器人详情"""
        if not self.wechat_webhooks:
            log("⚠️ 没有配置微信机器人")
            return

        log("📋 微信机器人详细信息:")
        for i, robot in enumerate(self.wechat_webhooks, 1):
            log(f"\n{i}. {robot.get('name', f'机器人{i}')}")
            log(f"   状态: {'✅ 启用' if robot.get('enabled', True) else '❌ 禁用'}")
            log(f"   描述: {robot.get('description', '无描述')}")
            log(f"   Webhook URL: {robot.get('webhook_url', 'N/A')}")

    def _test_single_wechat_bot(self):
        """测试单个微信机器人"""
        if not self.wechat_webhooks:
            log("⚠️ 没有配置微信机器人")
            return

        log("📋 选择要测试的机器人:")
        for i, robot in enumerate(self.wechat_webhooks, 1):
            log(f"{i}. {robot.get('name', f'机器人{i}')}")

        try:
            choice = input(f"请选择 (1-{len(self.wechat_webhooks)}): ").strip()
            index = int(choice) - 1

            if 0 <= index < len(self.wechat_webhooks):
                robot = self.wechat_webhooks[index]
                log(f"🧪 测试机器人: {robot.get('name', '未命名')}")

                test_message = f"""# 🧪 微信机器人测试

📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🤖 机器人: {robot.get('name', '未命名')}
📝 描述: {robot.get('description', '无描述')}

## 🎉 测试结果
如果您看到这条消息，说明该机器人工作正常！

---
🤖 终极版加密货币数据抓取器 - 双机器人推送版"""

                data = {
                    "msgtype": "markdown",
                    "markdown": {
                        "content": test_message
                    }
                }

                success = self._send_to_single_wechat_bot(robot, data, 10, 3, 2)
                if success:
                    log(f"✅ {robot.get('name', '未命名')}: 测试成功")
                else:
                    log(f"❌ {robot.get('name', '未命名')}: 测试失败")
            else:
                log("❌ 无效选择")
        except ValueError:
            log("❌ 请输入有效数字")
        except Exception as e:
            log(f"❌ 测试失败: {e}")

    def _test_all_wechat_bots(self):
        """测试所有微信机器人"""
        if not self.wechat_webhooks:
            log("⚠️ 没有配置微信机器人")
            return

        log("🧪 测试所有微信机器人...")

        test_message = f"""# 🧪 全体机器人测试

📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🤖 测试范围: 所有配置的机器人
📊 机器人数量: {len(self.wechat_webhooks)}

## 🎉 测试结果
如果您看到这条消息，说明该机器人工作正常！

---
🤖 终极版加密货币数据抓取器 - 双机器人推送版"""

        success = self._send_to_multiple_wechat_bots(test_message, "全体机器人测试")

        if success:
            log("✅ 全体机器人测试完成")
        else:
            log("❌ 部分或全部机器人测试失败")

    def _show_wechat_push_statistics(self):
        """显示微信推送统计信息"""
        log("📊 微信推送统计信息:")
        log("   (此功能可以扩展为记录各机器人的推送成功率、响应时间等)")

        if self.wechat_webhooks:
            for i, robot in enumerate(self.wechat_webhooks, 1):
                name = robot.get('name', f'机器人{i}')
                log(f"   {name}: 统计功能待开发")
        else:
            log("   没有配置微信机器人")

    def _reload_wechat_config(self):
        """重新加载微信配置"""
        log("🔄 重新加载微信机器人配置...")
        try:
            # 重新加载SSR配置文件
            self.ssr_config = self._load_ssr_config()
            # 重新加载微信机器人配置
            self.wechat_webhooks = self._load_wechat_webhooks()
            # 更新向后兼容的webhook
            self.wechat_webhook = self.wechat_webhooks[0]['webhook_url'] if self.wechat_webhooks else ""

            log(f"✅ 配置重新加载完成，当前有 {len(self.wechat_webhooks)} 个机器人")
        except Exception as e:
            log(f"❌ 配置重新加载失败: {e}")

# ==================== 主程序入口 ====================

def main():
    """主程序入口"""
    try:
        log("🚀 启动终极版加密货币数据抓取器...")

        # 先使用无代理模式快速初始化，避免阻塞
        log("📋 正在初始化系统...")
        scraper = AdvancedCryptoScraper(use_proxy=False)

        # 检查是否需要启用代理
        log("\n🌐 代理配置选项:")
        log("1. 启用SSR代理 (访问国外数据源)")
        log("2. 不使用代理 (仅访问国内数据源)")

        try:
            choice = input("请选择 (1/2, 默认1): ").strip() or "1"

            if choice == "1":
                log("🔄 正在启用SSR代理...")
                scraper.use_proxy = True
                scraper._init_proxy_config()
                scraper._init_session()

                # 尝试初始化SSR连接（非阻塞）
                try:
                    scraper._init_ssr_connection()
                except Exception as e:
                    log(f"⚠️ SSR连接初始化失败: {e}")
                    log("💡 程序将继续运行，可稍后手动启动SSR客户端")

                log("✅ 代理模式已启用")
            else:
                log("✅ 直连模式已启用")

        except (EOFError, KeyboardInterrupt):
            log("使用默认设置：启用代理模式")
            scraper.use_proxy = True

        # 运行主菜单
        scraper.run_main_menu()

    except KeyboardInterrupt:
        log("\n👋 程序被用户中断")
    except Exception as e:
        log(f"❌ 程序启动失败: {e}")
        import traceback
        log(f"错误详情: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
