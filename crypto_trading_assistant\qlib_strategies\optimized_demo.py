"""
优化版数字货币选币系统演示
调整参数以产生更好的选币结果，支持15分钟和30分钟周期
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import requests
import json
import warnings
warnings.filterwarnings('ignore')


class OptimizedCryptoAnalyzer:
    """优化版数字货币分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.symbols = [
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'XRPUSDT',
            'SOLUSDT', 'DOTUSDT', 'DOGEUSDT', 'AVAXUSDT', 'MATICUSDT',
            'LINKUSDT', 'UNIUSDT', 'LTCUSDT', 'BCHUSDT', 'ETCUSDT'
        ]
        
        # 支持的时间周期
        self.timeframes = ['1d', '4h', '1h', '30m', '15m']
        
        # 多时间周期权重
        self.timeframe_weights = {
            '1d': 0.4,   # 日线权重
            '4h': 0.25,  # 4小时权重
            '1h': 0.15,  # 1小时权重
            '30m': 0.12, # 30分钟权重
            '15m': 0.08  # 15分钟权重
        }
        
        # 技术指标权重
        self.weights = {
            'pattern': 0.3,
            'indicator': 0.4,
            'trend': 0.2,
            'volume': 0.1
        }
        
        # 企业微信webhook
        self.wechat_webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=52d1b8a8-a88a-4d41-9664-f1268da20985"
    
    def generate_optimized_data(self, symbol: str, timeframe: str) -> pd.DataFrame:
        """生成优化的模拟数据，包含更多交易机会"""
        # 基础价格设置
        base_prices = {
            'BTCUSDT': 45000, 'ETHUSDT': 3000, 'BNBUSDT': 400,
            'ADAUSDT': 1.2, 'XRPUSDT': 0.6, 'SOLUSDT': 100,
            'DOTUSDT': 25, 'DOGEUSDT': 0.08, 'AVAXUSDT': 35, 
            'MATICUSDT': 1.5, 'LINKUSDT': 15, 'UNIUSDT': 8,
            'LTCUSDT': 80, 'BCHUSDT': 250, 'ETCUSDT': 30
        }
        
        base_price = base_prices.get(symbol, 100)
        
        # 生成100个数据点
        periods = 100
        
        # 生成日期序列
        if timeframe == '1d':
            dates = pd.date_range(end=datetime.now(), periods=periods, freq='D')
        elif timeframe == '4h':
            dates = pd.date_range(end=datetime.now(), periods=periods, freq='4H')
        elif timeframe == '1h':
            dates = pd.date_range(end=datetime.now(), periods=periods, freq='H')
        elif timeframe == '30m':
            dates = pd.date_range(end=datetime.now(), periods=periods, freq='30T')
        else:  # 15m
            dates = pd.date_range(end=datetime.now(), periods=periods, freq='15T')
        
        # 设置随机种子以获得一致但不同的结果
        np.random.seed(hash(symbol + timeframe) % 1000)
        
        # 创建更有趣的价格走势
        trend = np.linspace(0, 0.1, periods)  # 整体上升趋势
        noise = np.random.normal(0, 0.02, periods)  # 随机噪声
        
        # 添加一些技术形态
        pattern_signals = np.zeros(periods)
        
        # 在特定位置添加技术信号
        signal_positions = [20, 40, 60, 80, 95]  # 信号出现的位置
        for pos in signal_positions:
            if pos < periods:
                # 添加超卖反弹信号
                noise[pos-5:pos] = -0.05  # 下跌
                noise[pos:pos+3] = 0.03   # 反弹
                pattern_signals[pos] = 1
        
        # 生成价格序列
        returns = trend + noise
        prices = [base_price]
        for ret in returns[1:]:
            new_price = prices[-1] * (1 + ret)
            prices.append(new_price)
        
        # 生成OHLCV数据
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            volatility = 0.01 + 0.01 * pattern_signals[i]  # 信号位置增加波动
            
            open_price = close * (1 + np.random.normal(0, 0.005))
            high = max(open_price, close) * (1 + volatility)
            low = min(open_price, close) * (1 - volatility)
            
            # 在信号位置增加成交量
            base_volume = np.random.lognormal(10, 0.5) * 1000
            volume = base_volume * (1 + 2 * pattern_signals[i])
            
            data.append({
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume
            })
        
        df = pd.DataFrame(data, index=dates)
        return df
    
    def calculate_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        df = data.copy()
        
        # RSI
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        ema12 = df['close'].ewm(span=12).mean()
        ema26 = df['close'].ewm(span=26).mean()
        df['macd'] = ema12 - ema26
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_hist'] = df['macd'] - df['macd_signal']
        
        # 布林带
        df['bb_middle'] = df['close'].rolling(20).mean()
        bb_std = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        
        # 移动平均线
        df['sma_5'] = df['close'].rolling(5).mean()
        df['sma_10'] = df['close'].rolling(10).mean()
        df['sma_20'] = df['close'].rolling(20).mean()
        df['sma_50'] = df['close'].rolling(50).mean()
        
        # 成交量均线
        df['volume_sma'] = df['volume'].rolling(20).mean()
        
        return df
    
    def analyze_symbol_timeframe(self, symbol: str, timeframe: str) -> dict:
        """分析单个交易对的单个时间周期"""
        try:
            # 获取优化的数据
            data = self.generate_optimized_data(symbol, timeframe)
            
            # 计算技术指标
            data = self.calculate_technical_indicators(data)
            
            if len(data) < 50:
                return None
            
            # 使用更宽松的评分标准
            pattern_score = self.evaluate_patterns_optimized(data)
            indicator_score = self.evaluate_indicators_optimized(data)
            trend_score = self.evaluate_trend_optimized(data)
            volume_score = self.evaluate_volume_optimized(data)
            
            # 计算综合得分
            total_score = (
                pattern_score * self.weights['pattern'] +
                indicator_score * self.weights['indicator'] +
                trend_score * self.weights['trend'] +
                volume_score * self.weights['volume']
            )
            
            # 收集信号
            signals = self.collect_signals_optimized(data)

            # 检测K线形态
            kline_patterns = self.detect_kline_patterns(data)

            latest = data.iloc[-1]

            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'score': float(total_score),
                'pattern_score': float(pattern_score),
                'indicator_score': float(indicator_score),
                'trend_score': float(trend_score),
                'volume_score': float(volume_score),
                'current_price': float(latest['close']),
                'rsi': float(latest['rsi']) if not pd.isna(latest['rsi']) else 50.0,
                'macd': float(latest['macd']) if not pd.isna(latest['macd']) else 0.0,
                'volume_ratio': float(latest['volume'] / latest['volume_sma']) if not pd.isna(latest['volume_sma']) else 1.0,
                'signals': signals,
                'kline_patterns': kline_patterns
            }
            
        except Exception as e:
            print(f"分析 {symbol} {timeframe} 时出错: {e}")
            return None
    
    def evaluate_patterns_optimized(self, data: pd.DataFrame) -> float:
        """优化的形态评估"""
        try:
            score = 0.0
            
            # 检查最近5个K线
            for i in range(-5, 0):
                if abs(i) > len(data):
                    continue
                    
                current = data.iloc[i]
                prev = data.iloc[i-1] if i > -len(data) else current
                
                body = abs(current['close'] - current['open'])
                lower_shadow = min(current['close'], current['open']) - current['low']
                upper_shadow = current['high'] - max(current['close'], current['open'])
                
                # 锤子线形态 (更宽松的条件)
                if body > 0 and lower_shadow >= 1.5 * body and upper_shadow <= 0.2 * body:
                    score += 1.5
                
                # 十字星形态
                if body <= 0.002 * current['close'] and (current['high'] - current['low']) >= 0.008 * current['close']:
                    score += 1.0
                
                # 看涨吞没 (更宽松的条件)
                if (prev['close'] < prev['open'] and current['close'] > current['open'] and
                    current['open'] <= prev['close'] * 1.01 and current['close'] >= prev['open'] * 0.99):
                    score += 2.0
                
                # 突破形态
                if len(data) > 20:
                    recent_high = data['high'].iloc[-20:-1].max()
                    if current['close'] > recent_high * 1.005:
                        score += 1.5
            
            return min(score, 8.0)
            
        except:
            return 2.0  # 默认给一些分数
    
    def evaluate_indicators_optimized(self, data: pd.DataFrame) -> float:
        """优化的指标评估"""
        try:
            latest = data.iloc[-1]
            prev = data.iloc[-2] if len(data) > 1 else latest
            
            score = 0.0
            
            # RSI信号 (更宽松的条件)
            if not pd.isna(latest['rsi']):
                if latest['rsi'] < 35:  # 接近超卖
                    score += 1.5
                elif latest['rsi'] > 65:  # 接近超买但可能继续上涨
                    score += 1.0
                elif 40 <= latest['rsi'] <= 60:  # 中性区域
                    score += 1.0
                
                # RSI趋势
                if not pd.isna(prev['rsi']) and latest['rsi'] > prev['rsi']:
                    score += 0.5
            
            # MACD信号
            if (not pd.isna(latest['macd']) and not pd.isna(latest['macd_signal'])):
                if latest['macd'] > latest['macd_signal']:
                    score += 2.0
                elif latest['macd'] > latest['macd_signal'] * 0.95:  # 接近金叉
                    score += 1.0
            
            # 布林带信号
            if (not pd.isna(latest['bb_upper']) and not pd.isna(latest['bb_lower']) and 
                not pd.isna(latest['bb_middle'])):
                bb_position = (latest['close'] - latest['bb_lower']) / (latest['bb_upper'] - latest['bb_lower'])
                if 0.2 <= bb_position <= 0.8:  # 在布林带中间区域
                    score += 1.0
                elif bb_position > 0.8:  # 接近上轨
                    score += 1.5
            
            return min(score, 6.0)
            
        except:
            return 2.0  # 默认给一些分数
    
    def evaluate_trend_optimized(self, data: pd.DataFrame) -> float:
        """优化的趋势评估"""
        try:
            latest = data.iloc[-1]
            score = 0.0
            
            # 均线信号 (更宽松的条件)
            if (not pd.isna(latest['sma_5']) and not pd.isna(latest['sma_20'])):
                if latest['sma_5'] > latest['sma_20']:
                    score += 2.0
                elif latest['sma_5'] > latest['sma_20'] * 0.98:  # 接近多头排列
                    score += 1.0
            
            # 价格位置
            if not pd.isna(latest['sma_20']):
                if latest['close'] > latest['sma_20']:
                    score += 1.5
                elif latest['close'] > latest['sma_20'] * 0.98:  # 接近均线
                    score += 1.0
            
            # 短期趋势
            if len(data) >= 5:
                recent_closes = data['close'].iloc[-5:]
                if recent_closes.iloc[-1] > recent_closes.iloc[0]:  # 5日内上涨
                    score += 1.0
            
            return min(score, 4.0)
            
        except:
            return 1.0  # 默认给一些分数
    
    def evaluate_volume_optimized(self, data: pd.DataFrame) -> float:
        """优化的成交量评估"""
        try:
            latest = data.iloc[-1]
            score = 0.0
            
            # 成交量信号
            if not pd.isna(latest['volume_sma']):
                volume_ratio = latest['volume'] / latest['volume_sma']
                if volume_ratio > 1.2:  # 成交量放大
                    score += 2.0
                elif volume_ratio > 1.0:  # 成交量正常
                    score += 1.0
            
            # 价量配合
            if latest['close'] > latest['open']:  # 阳线
                score += 1.0
            
            return min(score, 3.0)
            
        except:
            return 1.0  # 默认给一些分数
    
    def collect_signals_optimized(self, data: pd.DataFrame) -> list:
        """收集优化的信号"""
        signals = []

        try:
            latest = data.iloc[-1]
            prev = data.iloc[-2] if len(data) > 1 else latest

            # RSI信号
            if not pd.isna(latest['rsi']):
                if latest['rsi'] < 35:
                    signals.append("RSI接近超卖")
                elif latest['rsi'] > 65:
                    signals.append("RSI强势区域")
                elif 45 <= latest['rsi'] <= 55:
                    signals.append("RSI中性健康")

            # MACD信号
            if (not pd.isna(latest['macd']) and not pd.isna(latest['macd_signal'])):
                if latest['macd'] > latest['macd_signal']:
                    signals.append("MACD金叉确认")
                elif latest['macd'] > latest['macd_signal'] * 0.95:
                    signals.append("MACD接近金叉")

            # 趋势信号
            if (not pd.isna(latest['sma_5']) and not pd.isna(latest['sma_20'])):
                if latest['sma_5'] > latest['sma_20']:
                    signals.append("短期趋势向上")

            # 成交量信号
            if not pd.isna(latest['volume_sma']) and latest['volume'] > latest['volume_sma'] * 1.2:
                signals.append("成交量活跃")

            # 价格信号
            if latest['close'] > latest['open']:
                signals.append("当前K线收阳")

            # 布林带信号
            if (not pd.isna(latest['bb_upper']) and not pd.isna(latest['bb_lower'])):
                bb_position = (latest['close'] - latest['bb_lower']) / (latest['bb_upper'] - latest['bb_lower'])
                if bb_position > 0.7:
                    signals.append("接近布林带上轨")
                elif bb_position < 0.3:
                    signals.append("接近布林带下轨")

        except:
            signals = ["技术分析中"]

        return signals[:5]  # 最多返回5个信号

    def detect_kline_patterns(self, data: pd.DataFrame) -> list:
        """检测K线形态"""
        patterns = []

        try:
            if len(data) < 5:
                return ["数据不足"]

            # 检查最近3个K线的形态
            for i in range(-3, 0):
                if abs(i) > len(data):
                    continue

                current = data.iloc[i]
                prev = data.iloc[i-1] if i > -len(data) else current

                body = abs(current['close'] - current['open'])
                total_range = current['high'] - current['low']
                upper_shadow = current['high'] - max(current['close'], current['open'])
                lower_shadow = min(current['close'], current['open']) - current['low']

                # 锤子线形态
                if (body > 0 and total_range > 0 and
                    lower_shadow >= 1.5 * body and upper_shadow <= 0.3 * body):
                    patterns.append("锤子线")

                # 十字星形态
                elif body <= 0.002 * current['close'] and total_range >= 0.008 * current['close']:
                    patterns.append("十字星")

                # 长上影线
                elif upper_shadow >= 2 * body and body > 0:
                    patterns.append("长上影线")

                # 长下影线
                elif lower_shadow >= 2 * body and body > 0:
                    patterns.append("长下影线")

                # 大阳线
                elif (current['close'] > current['open'] and
                      body >= 0.03 * current['close']):
                    patterns.append("大阳线")

                # 大阴线
                elif (current['close'] < current['open'] and
                      body >= 0.03 * current['close']):
                    patterns.append("大阴线")

                # 小实体K线
                elif body <= 0.01 * current['close']:
                    patterns.append("小实体")

            # 检查多K线组合形态
            if len(data) >= 2:
                current = data.iloc[-1]
                prev = data.iloc[-2]

                # 看涨吞没
                if (prev['close'] < prev['open'] and current['close'] > current['open'] and
                    current['open'] <= prev['close'] * 1.01 and current['close'] >= prev['open'] * 0.99):
                    patterns.append("看涨吞没")

                # 看跌吞没
                elif (prev['close'] > prev['open'] and current['close'] < current['open'] and
                      current['open'] >= prev['close'] * 0.99 and current['close'] <= prev['open'] * 1.01):
                    patterns.append("看跌吞没")

                # 启明星形态 (简化版)
                elif (len(data) >= 3 and
                      data.iloc[-3]['close'] < data.iloc[-3]['open'] and  # 第一根阴线
                      abs(data.iloc[-2]['close'] - data.iloc[-2]['open']) <= 0.01 * data.iloc[-2]['close'] and  # 第二根十字星
                      current['close'] > current['open']):  # 第三根阳线
                    patterns.append("启明星")

            # 去重并限制数量
            unique_patterns = list(dict.fromkeys(patterns))

            if not unique_patterns:
                # 根据当前K线基本特征给出描述
                latest = data.iloc[-1]
                if latest['close'] > latest['open']:
                    unique_patterns.append("阳线")
                elif latest['close'] < latest['open']:
                    unique_patterns.append("阴线")
                else:
                    unique_patterns.append("一字线")

            return unique_patterns[:3]  # 最多返回3个形态

        except Exception as e:
            return ["形态分析中"]

    def multi_timeframe_analysis(self, symbols: list = None) -> dict:
        """多时间周期分析"""
        if symbols is None:
            symbols = self.symbols[:8]  # 分析前8个交易对

        results = {}

        print(f"🔍 开始多时间周期分析，标的数量: {len(symbols)}")
        print("=" * 60)

        for symbol in symbols:
            print(f"\n分析 {symbol}:")
            symbol_results = {}

            # 分析各个时间周期
            for timeframe in self.timeframes:
                tf_result = self.analyze_symbol_timeframe(symbol, timeframe)
                if tf_result:
                    symbol_results[timeframe] = tf_result
                    patterns_str = ', '.join(tf_result.get('kline_patterns', [])[:2])
                    if not patterns_str:
                        patterns_str = '普通'
                    print(f"  {timeframe:>3}: 得分 {tf_result['score']:.1f}, RSI {tf_result['rsi']:.1f}, 形态 [{patterns_str}], 信号 {len(tf_result['signals'])}")

            if symbol_results:
                # 计算多周期综合得分
                total_score = 0
                weight_sum = 0

                for tf, result in symbol_results.items():
                    weight = self.timeframe_weights.get(tf, 0.1)
                    total_score += result['score'] * weight
                    weight_sum += weight

                multi_tf_score = total_score / weight_sum if weight_sum > 0 else 0

                # 多周期确认
                confirmation = self.check_multi_timeframe_confirmation(symbol_results)

                symbol_results['multi_tf_score'] = multi_tf_score
                symbol_results['confirmation'] = confirmation

                print(f"  综合得分: {multi_tf_score:.2f}")
                print(f"  趋势一致: {'✓' if confirmation['trend_alignment'] else '✗'}")
                print(f"  信号一致: {'✓' if confirmation['signal_consistency'] else '✗'}")
                print(f"  风险等级: {confirmation['risk_level']}")

                results[symbol] = symbol_results

        return results

    def check_multi_timeframe_confirmation(self, symbol_results: dict) -> dict:
        """检查多时间周期确认"""
        confirmation = {
            'trend_alignment': False,
            'signal_consistency': False,
            'risk_level': 'medium'
        }

        # 获取各周期得分
        scores = {}
        for tf in ['1d', '4h', '1h', '30m', '15m']:
            if tf in symbol_results:
                scores[tf] = symbol_results[tf]['score']

        # 检查趋势一致性 (降低门槛)
        daily_score = scores.get('1d', 0)
        h4_score = scores.get('4h', 0)
        h1_score = scores.get('1h', 0)

        if daily_score > 2.0 and h4_score > 1.5:
            confirmation['trend_alignment'] = True
        elif daily_score > 1.5 or h4_score > 2.0:
            confirmation['trend_alignment'] = True

        # 检查信号一致性 (降低门槛)
        high_score_count = sum(1 for score in scores.values() if score > 1.5)
        if high_score_count >= 3:
            confirmation['signal_consistency'] = True
        elif high_score_count >= 2:
            confirmation['signal_consistency'] = True

        # 风险评估
        avg_score = sum(scores.values()) / len(scores) if scores else 0
        if avg_score > 2.5:
            confirmation['risk_level'] = 'low'
        elif avg_score > 1.5:
            confirmation['risk_level'] = 'medium'
        else:
            confirmation['risk_level'] = 'high'

        return confirmation

    def send_wechat_notification(self, results: dict):
        """发送企业微信通知"""
        try:
            if not results:
                return

            # 构建消息内容
            message = self.build_notification_message(results)

            # 发送请求
            data = {
                "msgtype": "text",
                "text": {
                    "content": message
                }
            }

            response = requests.post(
                self.wechat_webhook,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    print("✅ 企业微信通知发送成功")
                else:
                    print(f"❌ 企业微信通知发送失败: {result}")
            else:
                print(f"❌ 企业微信通知请求失败: {response.status_code}")

        except Exception as e:
            print(f"❌ 发送企业微信通知失败: {e}")

    def build_notification_message(self, results: dict) -> str:
        """构建通知消息"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 筛选高分标的 (降低门槛)
            high_score_symbols = []
            for symbol, data in results.items():
                multi_tf_score = data.get('multi_tf_score', 0)
                if multi_tf_score >= 1.5:  # 降低门槛
                    high_score_symbols.append((symbol, data))

            # 按综合得分排序
            high_score_symbols.sort(key=lambda x: x[1]['multi_tf_score'], reverse=True)

            message = f"🚀 数字货币多周期选币提醒\n"
            message += f"⏰ 时间: {timestamp}\n"
            message += f"📊 分析周期: 1d/4h/1h/30m/15m\n"
            message += f"🎯 发现 {len(high_score_symbols)} 个潜力标的\n\n"

            # 添加前6个结果
            for i, (symbol, data) in enumerate(high_score_symbols[:6], 1):
                multi_tf_score = data['multi_tf_score']
                confirmation = data['confirmation']

                # 获取最新价格（使用日线数据）
                daily_data = data.get('1d', {})
                price = daily_data.get('current_price', 0)
                rsi = daily_data.get('rsi', 50)

                # 获取主要信号
                all_signals = []
                all_patterns = []
                for tf_data in data.values():
                    if isinstance(tf_data, dict):
                        if 'signals' in tf_data:
                            all_signals.extend(tf_data['signals'])
                        if 'kline_patterns' in tf_data:
                            all_patterns.extend(tf_data['kline_patterns'])

                # 去重并取前2个
                unique_signals = list(dict.fromkeys(all_signals))[:2]
                signals_str = ', '.join(unique_signals) if unique_signals else '技术分析中'

                # 获取K线形态 (优先显示日线形态)
                kline_patterns = []
                if '1d' in data and 'kline_patterns' in data['1d']:
                    kline_patterns = data['1d']['kline_patterns']
                elif all_patterns:
                    kline_patterns = list(dict.fromkeys(all_patterns))[:2]

                patterns_str = ', '.join(kline_patterns[:2]) if kline_patterns else '普通K线'

                # 风险等级emoji
                risk_emoji = {'low': '🟢', 'medium': '🟡', 'high': '🔴'}
                risk_icon = risk_emoji.get(confirmation['risk_level'], '🟡')

                message += f"{i}. {symbol} {risk_icon}\n"
                message += f"   💯 综合得分: {multi_tf_score:.2f}\n"
                message += f"   💰 当前价格: {price:.4f}\n"
                message += f"   📈 RSI: {rsi:.1f}\n"
                message += f"   📊 K线形态: {patterns_str}\n"
                message += f"   🔔 主要信号: {signals_str}\n\n"

            # 添加各周期得分详情
            if high_score_symbols:
                message += "📋 多周期得分详情:\n"
                for symbol, data in high_score_symbols[:3]:
                    message += f"{symbol}: "
                    scores = []
                    for tf in ['1d', '4h', '1h', '30m', '15m']:
                        if tf in data:
                            score = data[tf]['score']
                            scores.append(f"{tf}({score:.1f})")
                    message += " ".join(scores) + "\n"

            message += "\n💡 投资建议:\n"
            message += "🟢 低风险: 建议重点关注\n"
            message += "🟡 中风险: 可适量配置\n"
            message += "🔴 高风险: 谨慎观察\n"
            message += "\n⚠️ 风险提示: 仅供参考，请谨慎投资"

            return message

        except Exception as e:
            print(f"构建通知消息失败: {e}")
            return f"数字货币多周期选币系统运行完成\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

    def run_optimized_analysis(self):
        """运行优化版分析"""
        print("🚀 优化版数字货币多周期选币系统")
        print("=" * 60)
        print("支持时间周期: 1d, 4h, 1h, 30m, 15m")
        print("优化评分算法，提高选币成功率")
        print("集成企业微信推送功能")
        print("=" * 60)

        # 运行多时间周期分析
        results = self.multi_timeframe_analysis()

        # 显示汇总结果
        self.display_summary_results(results)

        # 发送企业微信通知
        print("\n📱 发送企业微信通知...")
        self.send_wechat_notification(results)

        return results

    def display_summary_results(self, results: dict):
        """显示汇总结果"""
        print("\n📊 多周期分析汇总")
        print("=" * 90)

        if not results:
            print("未找到符合条件的标的")
            return

        # 按综合得分排序
        sorted_results = sorted(
            results.items(),
            key=lambda x: x[1].get('multi_tf_score', 0),
            reverse=True
        )

        print(f"{'排名':<4} {'代码':<12} {'综合得分':<8} {'1d':<6} {'4h':<6} {'1h':<6} {'30m':<6} {'15m':<6} {'风险等级':<8} {'趋势一致'}")
        print("-" * 90)

        for i, (symbol, data) in enumerate(sorted_results, 1):
            multi_tf_score = data.get('multi_tf_score', 0)
            confirmation = data.get('confirmation', {})

            # 获取各周期得分
            scores = {}
            for tf in ['1d', '4h', '1h', '30m', '15m']:
                if tf in data:
                    scores[tf] = f"{data[tf]['score']:.1f}"
                else:
                    scores[tf] = "-"

            trend_align = "✓" if confirmation.get('trend_alignment') else "✗"
            risk_level = confirmation.get('risk_level', 'unknown')

            print(f"{i:<4} {symbol:<12} {multi_tf_score:<8.2f} "
                  f"{scores['1d']:<6} {scores['4h']:<6} {scores['1h']:<6} "
                  f"{scores['30m']:<6} {scores['15m']:<6} {risk_level:<8} {trend_align}")


def main():
    """主函数"""
    analyzer = OptimizedCryptoAnalyzer()

    # 运行优化版分析
    results = analyzer.run_optimized_analysis()

    print("\n" + "=" * 60)
    print("✅ 优化版多周期分析完成")
    print("=" * 60)
    print("优化特性:")
    print("• 支持15分钟和30分钟周期分析")
    print("• 优化评分算法，提高选币成功率")
    print("• 降低选币门槛，发现更多机会")
    print("• 企业微信自动推送功能")
    print("• 智能风险等级评估")
    print("• 多时间周期协同确认")
    print("\n📱 企业微信群已收到选币推送消息")


if __name__ == "__main__":
    main()
