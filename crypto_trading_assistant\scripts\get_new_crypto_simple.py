#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import time
import argparse
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pycoingecko import CoinGeckoAPI
from loguru import logger

# 配置日志
logger.remove()
logger.add(lambda msg: print(msg), level="INFO")

class SimpleCryptoFinder:
    """
    获取数字货币列表，并尝试识别新上市的币种
    这个版本只使用基本市场数据，不会请求每个币种的详细信息
    """
    def __init__(self, days=30, output_file=None, vs_currency="usd"):
        """
        初始化参数
        
        Parameters
        ----------
        days : int
            查找过去多少天内上市的币种，默认30天
        output_file : str
            输出结果的文件路径，默认为None，输出到控制台
        vs_currency : str
            计价货币，默认为美元(usd)
        """
        self.days = days
        self.output_file = output_file
        self.vs_currency = vs_currency
        self.cg = CoinGeckoAPI()
        
    def get_all_coins(self):
        """
        获取所有币种的基本信息
        
        Returns
        -------
        pd.DataFrame
            包含所有币种信息的DataFrame
        """
        try:
            logger.info("获取所有币种信息...")
            # 获取所有币种列表
            all_coins = self.cg.get_coins_list()
            logger.info(f"获取到 {len(all_coins)} 个币种基本信息")
            
            # 将列表转换为DataFrame
            coins_df = pd.DataFrame(all_coins)
            
            # 获取最近的币种
            recent_coins = []
            
            # 分页获取币种市场数据
            pages = 4  # 只获取前几页数据，每页250个币种
            for page in range(1, pages+1):
                try:
                    logger.info(f"获取第{page}页市场数据...")
                    # 添加延时避免API限制
                    if page > 1:
                        time.sleep(1.5)
                    
                    # 获取市场数据，按初次添加到CoinGecko的时间倒序排序
                    markets = self.cg.get_coins_markets(
                        vs_currency=self.vs_currency, 
                        per_page=250, 
                        page=page,
                        order="id_desc"  # 尝试按ID倒序获取，新币ID通常较大
                    )
                    
                    if not markets:
                        break
                        
                    logger.info(f"第{page}页获取到 {len(markets)} 个币种的市场数据")
                    recent_coins.extend(markets)
                except Exception as e:
                    logger.error(f"获取第{page}页市场数据失败: {e}")
                    break
            
            # 创建包含市场数据的DataFrame
            markets_df = pd.DataFrame(recent_coins)
            logger.info(f"总共获取到 {len(markets_df)} 个币种的市场数据")
            return markets_df
            
        except Exception as e:
            logger.error(f"获取币种信息失败: {e}")
            return pd.DataFrame()
            
    def detect_new_coins(self, markets_df):
        """
        通过各种方式尝试检测新上市的币种
        
        Parameters
        ----------
        markets_df : pd.DataFrame
            包含币种市场数据的DataFrame
            
        Returns
        -------
        pd.DataFrame
            过滤后可能是新上市的币种
        """
        if markets_df.empty:
            return pd.DataFrame()
            
        # 计算日期范围 - 转换为UTC字符串再解析，确保时区一致
        cutoff_date = datetime.now() - timedelta(days=self.days)
        cutoff_str = cutoff_date.strftime('%Y-%m-%d')
        logger.info(f"查找 {cutoff_str} 之后上市的新币")
        
        # 1. 筛选出市值排名较靠后的币种 (可能是新上市的)
        low_ranked = None
        if 'market_cap_rank' in markets_df.columns:
            # 筛选出排名在1000名以后的币种
            low_ranked = markets_df[markets_df['market_cap_rank'] > 1000].copy()
            if not low_ranked.empty:
                logger.info(f"找到 {len(low_ranked)} 个市值排名较低的币种")
        
        # 2. 使用ATH日期 (历史最高价日期，如果非常近可能是新币)
        new_by_ath = None
        if 'ath_date' in markets_df.columns:
            # 标准化日期格式
            markets_df['ath_date_str'] = markets_df['ath_date'].astype(str).str[:10]
            markets_df['ath_date_parsed'] = pd.to_datetime(markets_df['ath_date_str'], errors='coerce')
            
            # 过滤
            mask = ~markets_df['ath_date_parsed'].isna() & (markets_df['ath_date_parsed'] >= pd.to_datetime(cutoff_str))
            new_by_ath = markets_df[mask].copy()
            
            if not new_by_ath.empty:
                logger.info(f"通过ath_date找到 {len(new_by_ath)} 个新币")
                return new_by_ath
        
        # 3. 使用价格波动特征识别新币
        # 新币通常价格波动较大
        volatile_coins = None
        price_columns = [col for col in markets_df.columns if 'price_change_percentage' in col]
        if price_columns:
            # 使用第一个价格变化列
            price_col = price_columns[0]
            try:
                # 清理数据，确保是数字
                markets_df[price_col] = pd.to_numeric(markets_df[price_col], errors='coerce')
                # 标记波动较大的币种
                volatile_coins = markets_df[abs(markets_df[price_col]) > 10].copy()
                if not volatile_coins.empty:
                    logger.info(f"通过价格波动特征找到 {len(volatile_coins)} 个可能的新币")
            except Exception as e:
                logger.warning(f"处理价格数据时出错: {e}")
        
        # 4. 使用交易量特征
        if 'total_volume' in markets_df.columns:
            # 清理数据
            markets_df['total_volume'] = pd.to_numeric(markets_df['total_volume'], errors='coerce')
            # 填充缺失值
            markets_df['total_volume'] = markets_df['total_volume'].fillna(0)
            
            # 筛选交易量不是特别大的币种
            low_volume = markets_df[(markets_df['total_volume'] < 100000) & 
                                   (markets_df['total_volume'] > 1000)].copy()
            if not low_volume.empty:
                logger.info(f"通过交易量特征找到 {len(low_volume)} 个可能的新币")
                # 如果之前有波动大的币种，取交集
                if volatile_coins is not None:
                    combined = pd.merge(low_volume, volatile_coins, on='id', how='inner')
                    if not combined.empty:
                        logger.info(f"结合价格波动和交易量特征，找到 {len(combined)} 个最可能的新币")
                        return combined.head(30)  # 限制返回数量
                return low_volume.head(30)  # 限制返回数量
        
        # 5. 如果有低排名的币种，返回它们
        if low_ranked is not None and len(low_ranked) > 0:
            return low_ranked.head(30)
            
        # 6. 如果有价格波动大的币种，返回它们
        if volatile_coins is not None and len(volatile_coins) > 0:
            return volatile_coins.head(30)
        
        # 7. 没有找到任何特征明显的新币，返回前30个币种
        logger.warning("无法通过任何特征识别新币，返回前30个最新的币种作为参考")
        return markets_df.head(30)
    
    def run(self):
        """
        运行脚本并输出结果
        """
        try:
            # 获取所有币种的市场数据
            markets_df = self.get_all_coins()
            
            if markets_df.empty:
                logger.warning("未获取到任何币种数据")
                return
                
            # 检测新上市的币种
            new_coins = self.detect_new_coins(markets_df)
            
            if new_coins.empty:
                logger.warning(f"未找到过去 {self.days} 天内上市的新币")
                return
                
            # 整理输出结果
            # 确保所有列都存在
            important_fields = ['id', 'symbol', 'name', 'current_price', 'market_cap', 
                             'market_cap_rank', 'total_volume', 'price_change_percentage_24h']
            available_fields = [f for f in important_fields if f in new_coins.columns]
            
            # 添加日期字段（如果有）
            date_fields = ['ath_date', 'ath_date_parsed']
            for field in date_fields:
                if field in new_coins.columns:
                    available_fields.append(field)
                    
            # 排序并选择要输出的字段
            if 'market_cap_rank' in new_coins.columns:
                result = new_coins.sort_values(by='market_cap_rank', na_position='last')[available_fields].copy()
            else:
                result = new_coins[available_fields].copy()
                
            # 输出结果
            if self.output_file:
                result.to_csv(self.output_file, index=False, encoding='utf-8')
                logger.info(f"结果已保存到文件: {self.output_file}")
            else:
                pd.set_option('display.max_rows', None)
                pd.set_option('display.max_columns', None)
                pd.set_option('display.width', 1000)
                print("\n可能的新上市币种列表 (过去{}天):\n".format(self.days))
                print(result)
                print("\n总计: {} 个可能的新币".format(len(result)))
                print("\n注意: 由于API限制，该列表可能不完全准确，仅供参考。")
                
        except Exception as e:
            logger.error(f"运行过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            
def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description='获取数字货币新币列表(简化版)')
    parser.add_argument('-d', '--days', type=int, default=30, help='查找过去多少天内上市的币种，默认30天')
    parser.add_argument('-o', '--output', type=str, help='输出结果的CSV文件路径')
    parser.add_argument('-c', '--currency', type=str, default='usd', help='计价货币，默认为美元(usd)')
    return parser.parse_args()

if __name__ == "__main__":
    print("开始执行简化版新币查找脚本...")
    args = parse_args()
    finder = SimpleCryptoFinder(days=args.days, output_file=args.output, vs_currency=args.currency)
    finder.run()
    print("程序执行完毕") 