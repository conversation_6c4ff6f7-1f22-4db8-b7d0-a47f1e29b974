#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试各数据源的实际连通性
"""

import requests
import time
from datetime import datetime

def log(message):
    """日志函数"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def test_url_connectivity(name, url, timeout=10):
    """测试URL连通性"""
    try:
        log(f"🔍 测试 {name}: {url}")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=timeout)
        
        if response.status_code == 200:
            log(f"✅ {name}: 连接成功 (状态码: {response.status_code})")
            return True
        else:
            log(f"⚠️ {name}: 连接异常 (状态码: {response.status_code})")
            return False
            
    except requests.exceptions.ConnectionError as e:
        log(f"❌ {name}: 连接失败 - 网络连接错误")
        return False
    except requests.exceptions.Timeout as e:
        log(f"❌ {name}: 连接失败 - 请求超时")
        return False
    except Exception as e:
        log(f"❌ {name}: 连接失败 - {e}")
        return False

def main():
    """主函数"""
    log("🚀 开始测试各数据源的实际连通性...")
    log("="*60)
    
    # 定义要测试的数据源
    data_sources = {
        '火币': 'https://api.huobi.pro/v1/common/symbols',
        'Gate.io': 'https://api.gateio.ws/api/v4/spot/tickers',
        'Binance': 'https://api.binance.com/api/v3/ticker/24hr',
        'CoinGecko': 'https://api.coingecko.com/api/v3/ping',
        'CoinMarketCap': 'https://pro-api.coinmarketcap.com/v1/cryptocurrency/listings/latest',
        'OKX': 'https://www.okx.com/api/v5/market/tickers?instType=SPOT',
        '币安中国': 'https://api.binance.com/api/v3/ping'
    }
    
    results = {}
    
    for name, url in data_sources.items():
        log(f"\n📡 测试 {name}...")
        results[name] = test_url_connectivity(name, url)
        time.sleep(1)  # 避免请求过快
    
    log("\n" + "="*60)
    log("📊 连通性测试结果汇总:")
    log("="*60)
    
    accessible_sources = []
    blocked_sources = []
    
    for name, success in results.items():
        if success:
            accessible_sources.append(name)
            log(f"✅ {name}: 可直连访问")
        else:
            blocked_sources.append(name)
            log(f"❌ {name}: 需要代理访问")
    
    log(f"\n📈 统计结果:")
    log(f"   可直连访问: {len(accessible_sources)} 个")
    log(f"   需要代理访问: {len(blocked_sources)} 个")
    
    if accessible_sources:
        log(f"\n🏠 建议直连模式主数据源:")
        for source in accessible_sources:
            log(f"   - {source}")
    
    if blocked_sources:
        log(f"\n🌐 建议代理模式数据源:")
        for source in blocked_sources:
            log(f"   - {source}")
    
    log(f"\n💡 建议配置:")
    if accessible_sources:
        log(f"   主数据源: {accessible_sources[0]} (直连可用)")
        log(f"   备用数据源: {', '.join(blocked_sources)} (需要代理)")
    else:
        log("   所有数据源都需要代理访问")
        log("   建议启用SSR代理后使用")

if __name__ == "__main__":
    main()
