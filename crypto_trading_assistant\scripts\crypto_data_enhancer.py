import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import json
import logging
import ccxt
import time
from typing import Dict, List, Optional
import gzip
import shutil

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CryptoDataEnhancer:
    def __init__(self, data_dir: str):
        self.data_dir = data_dir
        self.exchanges = {
            'okx': ccxt.okx({
                'proxies': {
                    'http': 'socks5h://127.0.0.1:1080',
                    'https': 'socks5h://127.0.0.1:1080'
                },
                'timeout': 30000,  # 增加超时时间
                'enableRateLimit': True  # 启用速率限制
            })
        }
        
    def compress_data(self):
        """压缩CSV文件"""
        for root, _, files in os.walk(self.data_dir):
            for file in files:
                if file.endswith('.csv'):
                    file_path = os.path.join(root, file)
                    gz_path = file_path + '.gz'
                    
                    try:
                        with open(file_path, 'rb') as f_in:
                            with gzip.open(gz_path, 'wb') as f_out:
                                shutil.copyfileobj(f_in, f_out)
                        
                        # 验证压缩文件
                        with gzip.open(gz_path, 'rb') as f:
                            pd.read_csv(f)
                        
                        # 如果验证成功，删除原文件
                        os.remove(file_path)
                        logger.info(f"已压缩并验证: {file_path}")
                    except Exception as e:
                        logger.error(f"压缩文件失败 {file_path}: {str(e)}")
                        if os.path.exists(gz_path):
                            os.remove(gz_path)
    
    def fill_time_gaps(self, df: pd.DataFrame) -> pd.DataFrame:
        """填充时间序列中的间隔"""
        df = df.sort_values('datetime')
        date_range = pd.date_range(df['datetime'].min(), df['datetime'].max(), freq='D')
        df_reindexed = df.set_index('datetime').reindex(date_range)
        
        # 使用前向填充处理缺失值
        df_reindexed = df_reindexed.ffill()  # 使用新的方法替代fillna(method='ffill')
        
        # 重置索引
        df_reindexed = df_reindexed.reset_index()
        df_reindexed = df_reindexed.rename(columns={'index': 'datetime'})
        
        return df_reindexed
    
    def detect_and_fix_anomalies(self, df: pd.DataFrame) -> pd.DataFrame:
        """检测并修复异常值"""
        df_fixed = df.copy()
        
        # 修复负值
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df_fixed.loc[df_fixed[col] < 0, col] = np.nan
        
        # 使用移动平均填充异常值
        for col in ['open', 'high', 'low', 'close', 'volume']:
            ma = df_fixed[col].rolling(window=5, min_periods=1).mean()
            df_fixed.loc[df_fixed[col].isna(), col] = ma[df_fixed[col].isna()]
        
        return df_fixed
    
    def discover_new_listings(self) -> List[Dict]:
        """发现新上市的币种"""
        new_listings = []
        
        for ex_id, exchange in self.exchanges.items():
            try:
                # 添加重试机制
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        markets = exchange.load_markets()
                        break
                    except Exception as e:
                        if attempt == max_retries - 1:
                            raise
                        logger.warning(f"Attempt {attempt + 1} failed, retrying...")
                        time.sleep(2 ** attempt)  # 指数退避
                
                # 获取所有USDT交易对
                usdt_pairs = [symbol for symbol in markets.keys() if symbol.endswith('USDT')]
                
                # 获取现有数据中的交易对
                existing_pairs = set()
                for root, _, files in os.walk(os.path.join(self.data_dir, ex_id, '1d')):
                    for file in files:
                        if file.endswith('.csv.gz'):
                            existing_pairs.add(file.replace('.csv.gz', ''))
                
                # 找出新的交易对
                new_pairs = set(usdt_pairs) - existing_pairs
                
                if new_pairs:
                    for pair in new_pairs:
                        new_listings.append({
                            'exchange': ex_id,
                            'symbol': pair,
                            'base_currency': pair.split('-')[0],
                            'discovery_date': datetime.now().strftime('%Y-%m-%d')
                        })
                
            except Exception as e:
                logger.error(f"Error discovering new listings for {ex_id}: {str(e)}")
        
        return new_listings
    
    def enhance_data(self):
        """增强数据质量"""
        for root, _, files in os.walk(self.data_dir):
            for file in files:
                if file.endswith('.csv.gz'):
                    file_path = os.path.join(root, file)
                    try:
                        # 读取压缩数据
                        with gzip.open(file_path, 'rt') as f:
                            df = pd.read_csv(f)
                        
                        if 'datetime' not in df.columns:
                            continue
                            
                        df['datetime'] = pd.to_datetime(df['datetime'])
                        
                        # 填充时间间隔
                        df = self.fill_time_gaps(df)
                        
                        # 检测并修复异常值
                        df = self.detect_and_fix_anomalies(df)
                        
                        # 保存增强后的数据
                        csv_path = file_path.replace('.gz', '')
                        df.to_csv(csv_path, index=False)
                        
                        # 重新压缩
                        with open(csv_path, 'rb') as f_in:
                            with gzip.open(file_path, 'wb') as f_out:
                                shutil.copyfileobj(f_in, f_out)
                        
                        os.remove(csv_path)
                        logger.info(f"已增强数据: {file_path}")
                        
                    except Exception as e:
                        logger.error(f"Error enhancing data for {file_path}: {str(e)}")
    
    def implement_incremental_update(self):
        """实现增量更新"""
        for root, _, files in os.walk(self.data_dir):
            for file in files:
                if file.endswith('.csv.gz') and '1d' in root:
                    file_path = os.path.join(root, file)
                    try:
                        # 读取压缩数据
                        with gzip.open(file_path, 'rt') as f:
                            df_existing = pd.read_csv(f)
                        
                        df_existing['datetime'] = pd.to_datetime(df_existing['datetime'])
                        last_timestamp = df_existing['datetime'].max()
                        
                        # 从交易所获取新数据
                        symbol = os.path.basename(file_path).replace('.csv.gz', '')
                        exchange_id = os.path.basename(os.path.dirname(os.path.dirname(root)))
                        
                        if exchange_id in self.exchanges:
                            exchange = self.exchanges[exchange_id]
                            
                            # 添加重试机制
                            max_retries = 3
                            for attempt in range(max_retries):
                                try:
                                    new_data = exchange.fetch_ohlcv(
                                        symbol,
                                        timeframe='1d',
                                        since=int(last_timestamp.timestamp() * 1000)
                                    )
                                    break
                                except Exception as e:
                                    if attempt == max_retries - 1:
                                        raise
                                    logger.warning(f"Attempt {attempt + 1} failed, retrying...")
                                    time.sleep(2 ** attempt)
                            
                            if new_data:
                                df_new = pd.DataFrame(
                                    new_data,
                                    columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']
                                )
                                df_new['datetime'] = pd.to_datetime(df_new['timestamp'], unit='ms')
                                
                                df_combined = pd.concat([df_existing, df_new])
                                df_combined = df_combined.drop_duplicates(subset=['datetime'])
                                df_combined = df_combined.sort_values('datetime')
                                
                                # 保存更新后的数据
                                csv_path = file_path.replace('.gz', '')
                                df_combined.to_csv(csv_path, index=False)
                                
                                # 重新压缩
                                with open(csv_path, 'rb') as f_in:
                                    with gzip.open(file_path, 'wb') as f_out:
                                        shutil.copyfileobj(f_in, f_out)
                                
                                os.remove(csv_path)
                                logger.info(f"已更新数据: {file_path}")
                    
                    except Exception as e:
                        logger.error(f"Error updating data for {file_path}: {str(e)}")

def main():
    # 设置数据目录
    data_dir = os.path.join('data', 'crypto')
    
    # 创建增强器实例
    enhancer = CryptoDataEnhancer(data_dir)
    
    # 执行数据增强
    logger.info("开始增强数据...")
    enhancer.enhance_data()
    
    # 实现增量更新
    logger.info("开始增量更新...")
    enhancer.implement_incremental_update()
    
    # 发现新上市币种
    logger.info("开始发现新上市币种...")
    new_listings = enhancer.discover_new_listings()
    if new_listings:
        logger.info(f"发现 {len(new_listings)} 个新上市币种:")
        for listing in new_listings:
            logger.info(f"  {listing['exchange']}: {listing['symbol']}")
    
    logger.info("数据增强完成")

if __name__ == "__main__":
    main() 