@echo off
chcp 65001 >nul
title 实际运行形态分析 - 完整流程
echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █                  🎯 实际运行形态分析                        █
echo █            数据下载 → 形态识别 → 微信推送 → 图表生成        █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.

cd /d "%~dp0"

echo 📍 当前目录: %CD%
echo 🐍 Python环境: D:\envs\tqsdk\python.exe
echo.

echo 🔧 检查环境...
if not exist "D:\envs\tqsdk\python.exe" (
    echo ❌ Python环境不存在
    pause
    exit /b 1
)

if not exist "advanced_crypto_scraper tuxing.py" (
    echo ❌ 主脚本文件不存在
    pause
    exit /b 1
)

if not exist "实际运行形态分析.py" (
    echo ❌ 分析脚本不存在
    pause
    exit /b 1
)

echo ✅ 环境检查通过
echo.

echo 💡 分析流程:
echo ┌─────────────────────────────────────────────────────────────┐
echo │ 🎯 完整形态分析流程                                        │
echo │                                                             │
echo │ 📊 1. 获取市场数据                                         │
echo │   • 从OKX/Gate.io/CoinGecko获取实时数据                    │
echo │   • 获取前100个币种的基础信息                              │
echo │                                                             │
echo │ 🔍 2. 形态分析                                             │
echo │   • 分析前50个币种的历史K线数据                            │
echo │   • 识别双长上影线形态                                     │
echo │   • 计算技术指标和综合评分                                 │
echo │                                                             │
echo │ 📱 3. 结果推送                                             │
echo │   • 自动推送分析结果到企业微信                             │
echo │   • 包含币种信息和交易建议                                 │
echo │                                                             │
echo │ 🎨 4. 图表验证                                             │
echo │   • 可选择生成K线图表                                      │
echo │   • 直观验证形态识别准确性                                 │
echo │   • 保存到pattern_charts目录                               │
echo └─────────────────────────────────────────────────────────────┘
echo.

echo ⚠️ 注意事项:
echo   • 分析过程需要网络连接
echo   • 完整分析可能需要5-10分钟
echo   • 建议选择直连模式以提高稳定性
echo   • 分析结果会自动推送到微信群
echo.

echo 🚀 开始实际形态分析...
echo ================================================================
echo.

D:\envs\tqsdk\python.exe "实际运行形态分析.py"

echo.
echo ================================================================
echo 📝 分析完成
echo.
pause
