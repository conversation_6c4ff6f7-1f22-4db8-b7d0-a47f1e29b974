#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版形态分析测试脚本 - 无emoji版本
"""

import sys
import os

def test_basic_imports():
    """测试基本导入"""
    try:
        import pandas as pd
        import numpy as np
        import requests
        from datetime import datetime
        print("基本库导入成功")
        return True
    except ImportError as e:
        print(f"导入失败: {e}")
        return False

def test_api_connection():
    """测试API连接"""
    try:
        url = "https://api.coingecko.com/api/v3/ping"
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            print("CoinGecko API连接成功")
            return True
        else:
            print(f"API连接失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"API连接异常: {e}")
        return False

def test_market_data():
    """测试市场数据获取"""
    try:
        url = "https://api.coingecko.com/api/v3/coins/markets"
        params = {
            'vs_currency': 'usd',
            'order': 'market_cap_desc',
            'per_page': 5,
            'page': 1,
            'sparkline': False
        }
        
        response = requests.get(url, params=params, timeout=10)
        data = response.json()
        
        if isinstance(data, list) and len(data) > 0:
            print(f"成功获取 {len(data)} 个币种数据")
            
            # 显示前3个币种信息
            for i, coin in enumerate(data[:3]):
                name = coin.get('name', 'Unknown')
                price = coin.get('current_price', 0)
                change_24h = coin.get('price_change_percentage_24h', 0)
                print(f"   {i+1}. {name}: ${price:.2f} ({change_24h:+.2f}%)")
            
            return True
        else:
            print("获取的数据格式不正确")
            return False
            
    except Exception as e:
        print(f"获取市场数据失败: {e}")
        return False

def test_wechat_webhook():
    """测试企业微信推送"""
    try:
        import requests
        from datetime import datetime
        
        webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=52d1b8a8-a88a-4d41-9664-f1268da20985"
        
        test_message = {
            "msgtype": "markdown",
            "markdown": {
                "content": f"""# 形态分析器测试
测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
测试状态: 系统功能测试中...

基本功能正常
API连接成功
数据获取正常

这是一条测试消息，请忽略。"""
            }
        }
        
        response = requests.post(
            webhook_url,
            json=test_message,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        result = response.json()
        if result.get('errcode') == 0:
            print("企业微信推送测试成功")
            return True
        else:
            print(f"企业微信推送失败: {result}")
            return False
            
    except Exception as e:
        print(f"企业微信推送异常: {e}")
        return False

def simple_pattern_analysis():
    """简单的形态分析示例"""
    try:
        import pandas as pd
        import numpy as np
        from datetime import datetime, timedelta
        
        print("\n开始简单形态分析...")
        
        # 模拟生成一些K线数据
        dates = pd.date_range(end=datetime.now(), periods=50, freq='D')
        np.random.seed(42)  # 确保结果可重复
        
        # 生成模拟价格数据
        base_price = 50000  # BTC基础价格
        returns = np.random.normal(0, 0.02, 50)  # 2%日波动率
        prices = [base_price]
        
        for ret in returns[1:]:
            new_price = prices[-1] * (1 + ret)
            prices.append(new_price)
        
        # 创建OHLCV数据
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            open_price = close * (1 + np.random.normal(0, 0.005))
            high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.01)))
            low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.01)))
            volume = np.random.uniform(1000000, 5000000)
            
            data.append({
                'date': date,
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        
        # 简单的形态识别
        latest = df.iloc[-1]
        prev = df.iloc[-2]
        
        patterns = []
        
        # 计算K线参数
        body = abs(latest['close'] - latest['open'])
        upper_shadow = latest['high'] - max(latest['open'], latest['close'])
        lower_shadow = min(latest['open'], latest['close']) - latest['low']
        total_range = latest['high'] - latest['low']
        
        # 形态判断
        if body < 0.1 * total_range:
            patterns.append('十字星')
        elif upper_shadow > 2 * body:
            patterns.append('长上影线')
        elif lower_shadow > 2 * body:
            patterns.append('锤子线')
        elif body > 0.6 * total_range:
            if latest['close'] > latest['open']:
                patterns.append('大阳线')
            else:
                patterns.append('大阴线')
        
        # 计算简单技术指标
        df['sma_5'] = df['close'].rolling(5).mean()
        df['sma_20'] = df['close'].rolling(20).mean()
        
        latest_sma5 = df['sma_5'].iloc[-1]
        latest_sma20 = df['sma_20'].iloc[-1]
        
        # 趋势判断
        if latest['close'] > latest_sma5 > latest_sma20:
            trend = "上升趋势"
        elif latest['close'] < latest_sma5 < latest_sma20:
            trend = "下降趋势"
        else:
            trend = "震荡趋势"
        
        # 显示分析结果
        print(f"\nBTCUSDT 模拟分析结果:")
        print(f"   当前价格: ${latest['close']:.2f}")
        print(f"   K线形态: {', '.join(patterns) if patterns else '无明显形态'}")
        print(f"   趋势判断: {trend}")
        print(f"   5日均线: ${latest_sma5:.2f}")
        print(f"   20日均线: ${latest_sma20:.2f}")
        
        return True
        
    except Exception as e:
        print(f"形态分析失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始形态分析器功能测试\n")
    
    tests = [
        ("基本库导入", test_basic_imports),
        ("API连接", test_api_connection),
        ("市场数据获取", test_market_data),
        ("企业微信推送", test_wechat_webhook),
        ("简单形态分析", simple_pattern_analysis)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
                print(f"{test_name} 通过")
            else:
                print(f"{test_name} 失败")
        except Exception as e:
            print(f"{test_name} 异常: {e}")
    
    print(f"\n测试总结:")
    print(f"   通过: {passed}/{total}")
    print(f"   成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n所有测试通过！系统功能正常。")
        return 0
    else:
        print(f"\n有 {total-passed} 个测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    exit(main())
