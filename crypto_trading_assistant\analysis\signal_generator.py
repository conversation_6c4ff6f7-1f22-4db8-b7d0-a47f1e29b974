"""
买点信号生成模块
基于技术指标和形态分析生成交易信号
"""

import pandas as pd
import numpy as np
import talib
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from loguru import logger

from .pattern_recognition import Pat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PatternResult, PatternType


class SignalType(Enum):
    """信号类型枚举"""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"


class SignalStrength(Enum):
    """信号强度枚举"""
    WEAK = "weak"
    MODERATE = "moderate"
    STRONG = "strong"
    VERY_STRONG = "very_strong"


@dataclass
class TradingSignal:
    """交易信号数据类"""
    symbol: str
    signal_type: SignalType
    strength: SignalStrength
    confidence: float  # 0-1
    entry_price: float
    stop_loss: float
    target_price: float
    timestamp: pd.Timestamp
    timeframe: str
    reasons: List[str]  # 信号产生的原因
    technical_indicators: Dict[str, float]
    pattern_info: Optional[PatternResult] = None
    risk_reward_ratio: Optional[float] = None


class SignalGenerator:
    """交易信号生成器"""
    
    def __init__(self, config: Dict):
        """
        初始化信号生成器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.signal_config = config.get('entry_signals', {})
        self.risk_config = config.get('risk_management', {})
        self.pattern_recognizer = PatternRecognizer(config)
        
    def generate_signals(self, df: pd.DataFrame, symbol: str, timeframe: str) -> List[TradingSignal]:
        """
        生成交易信号
        
        Args:
            df: OHLCV数据
            symbol: 交易对符号
            timeframe: 时间周期
            
        Returns:
            交易信号列表
        """
        signals = []
        
        try:
            # 计算技术指标
            indicators = self._calculate_indicators(df)
            
            # 识别形态
            patterns = self.pattern_recognizer.analyze_patterns(df, symbol)
            
            # 生成基于指标的信号
            indicator_signals = self._generate_indicator_signals(df, indicators, symbol, timeframe)
            signals.extend(indicator_signals)
            
            # 生成基于形态的信号
            pattern_signals = self._generate_pattern_signals(df, patterns, symbol, timeframe)
            signals.extend(pattern_signals)
            
            # 多时间周期确认
            if self.signal_config.get('multi_timeframe', {}).get('enabled', False):
                signals = self._apply_multi_timeframe_filter(signals, df, symbol)
            
            # 计算风险回报比
            for signal in signals:
                signal.risk_reward_ratio = self._calculate_risk_reward_ratio(signal)
            
            # 按置信度排序
            signals.sort(key=lambda x: x.confidence, reverse=True)
            
            logger.info(f"{symbol} {timeframe} 生成了 {len(signals)} 个交易信号")
            
        except Exception as e:
            logger.error(f"生成 {symbol} 交易信号时出错: {e}")
        
        return signals
    
    def _calculate_indicators(self, df: pd.DataFrame) -> Dict[str, np.ndarray]:
        """计算技术指标"""
        indicators = {}
        
        try:
            high = df['high'].values
            low = df['low'].values
            close = df['close'].values
            volume = df['volume'].values
            
            # 移动平均线
            indicators['sma_20'] = talib.SMA(close, timeperiod=20)
            indicators['sma_50'] = talib.SMA(close, timeperiod=50)
            indicators['ema_12'] = talib.EMA(close, timeperiod=12)
            indicators['ema_26'] = talib.EMA(close, timeperiod=26)
            
            # MACD
            indicators['macd'], indicators['macd_signal'], indicators['macd_hist'] = talib.MACD(close)
            
            # RSI
            indicators['rsi'] = talib.RSI(close, timeperiod=14)
            
            # 布林带
            indicators['bb_upper'], indicators['bb_middle'], indicators['bb_lower'] = talib.BBANDS(close)
            
            # 随机指标
            indicators['stoch_k'], indicators['stoch_d'] = talib.STOCH(high, low, close)
            
            # ATR
            indicators['atr'] = talib.ATR(high, low, close, timeperiod=14)
            
            # 成交量指标
            indicators['volume_sma'] = talib.SMA(volume, timeperiod=20)
            
            # 威廉指标
            indicators['williams_r'] = talib.WILLR(high, low, close, timeperiod=14)
            
            # CCI
            indicators['cci'] = talib.CCI(high, low, close, timeperiod=14)
            
        except Exception as e:
            logger.error(f"计算技术指标时出错: {e}")
        
        return indicators
    
    def _generate_indicator_signals(self, df: pd.DataFrame, indicators: Dict, 
                                   symbol: str, timeframe: str) -> List[TradingSignal]:
        """基于技术指标生成信号"""
        signals = []
        
        try:
            current_price = float(df['close'].iloc[-1])
            current_time = df.index[-1]
            
            # RSI超卖信号
            if self._check_rsi_oversold(indicators):
                signal = self._create_buy_signal(
                    symbol, current_price, current_time, timeframe,
                    "RSI超卖反弹", indicators, 0.7
                )
                signals.append(signal)
            
            # MACD金叉信号
            if self._check_macd_bullish_cross(indicators):
                signal = self._create_buy_signal(
                    symbol, current_price, current_time, timeframe,
                    "MACD金叉", indicators, 0.75
                )
                signals.append(signal)
            
            # 布林带下轨支撑信号
            if self._check_bollinger_support(df, indicators):
                signal = self._create_buy_signal(
                    symbol, current_price, current_time, timeframe,
                    "布林带下轨支撑", indicators, 0.65
                )
                signals.append(signal)
            
            # 多重指标汇聚信号
            confluence_score = self._calculate_indicator_confluence(indicators)
            if confluence_score >= 3:
                signal = self._create_buy_signal(
                    symbol, current_price, current_time, timeframe,
                    f"多重指标汇聚(得分:{confluence_score})", indicators, 0.85
                )
                signals.append(signal)
                
        except Exception as e:
            logger.error(f"生成指标信号时出错: {e}")
        
        return signals
    
    def _generate_pattern_signals(self, df: pd.DataFrame, patterns: List[PatternResult],
                                 symbol: str, timeframe: str) -> List[TradingSignal]:
        """基于形态分析生成信号"""
        signals = []
        
        try:
            current_price = float(df['close'].iloc[-1])
            current_time = df.index[-1]
            
            for pattern in patterns:
                if pattern.confidence > 0.6:  # 只考虑高置信度形态
                    if pattern.breakout_direction == 'up':
                        signal = TradingSignal(
                            symbol=symbol,
                            signal_type=SignalType.BUY,
                            strength=self._determine_signal_strength(pattern.confidence),
                            confidence=pattern.confidence,
                            entry_price=current_price,
                            stop_loss=pattern.stop_loss or current_price * 0.95,
                            target_price=pattern.target_price or current_price * 1.1,
                            timestamp=current_time,
                            timeframe=timeframe,
                            reasons=[f"{pattern.pattern_type.value}形态突破"],
                            technical_indicators={},
                            pattern_info=pattern
                        )
                        signals.append(signal)
                        
        except Exception as e:
            logger.error(f"生成形态信号时出错: {e}")
        
        return signals
    
    def _check_rsi_oversold(self, indicators: Dict) -> bool:
        """检查RSI超卖条件"""
        try:
            rsi = indicators.get('rsi')
            if rsi is not None and len(rsi) > 1:
                current_rsi = rsi[-1]
                prev_rsi = rsi[-2]
                return current_rsi < 30 and prev_rsi < current_rsi  # RSI < 30且开始回升
            return False
        except:
            return False
    
    def _check_macd_bullish_cross(self, indicators: Dict) -> bool:
        """检查MACD金叉"""
        try:
            macd = indicators.get('macd')
            macd_signal = indicators.get('macd_signal')
            if macd is not None and macd_signal is not None and len(macd) > 1:
                # 当前MACD > 信号线，前一期MACD < 信号线
                return (macd[-1] > macd_signal[-1] and 
                       macd[-2] <= macd_signal[-2])
            return False
        except:
            return False
    
    def _check_bollinger_support(self, df: pd.DataFrame, indicators: Dict) -> bool:
        """检查布林带下轨支撑"""
        try:
            bb_lower = indicators.get('bb_lower')
            if bb_lower is not None:
                current_low = float(df['low'].iloc[-1])
                current_close = float(df['close'].iloc[-1])
                lower_band = bb_lower[-1]
                
                # 价格触及下轨但收盘价回到下轨之上
                return current_low <= lower_band and current_close > lower_band
            return False
        except:
            return False
    
    def _calculate_indicator_confluence(self, indicators: Dict) -> int:
        """计算指标汇聚得分"""
        score = 0
        
        try:
            # RSI超卖
            if self._check_rsi_oversold(indicators):
                score += 1
            
            # MACD金叉
            if self._check_macd_bullish_cross(indicators):
                score += 1
            
            # 随机指标超卖
            stoch_k = indicators.get('stoch_k')
            if stoch_k is not None and stoch_k[-1] < 20:
                score += 1
            
            # 威廉指标超卖
            williams_r = indicators.get('williams_r')
            if williams_r is not None and williams_r[-1] < -80:
                score += 1
            
            # 价格在移动平均线上方
            sma_20 = indicators.get('sma_20')
            if sma_20 is not None:
                # 这里需要当前价格，暂时跳过
                pass
                
        except Exception as e:
            logger.error(f"计算指标汇聚得分时出错: {e}")
        
        return score

    def _create_buy_signal(self, symbol: str, price: float, timestamp: pd.Timestamp,
                          timeframe: str, reason: str, indicators: Dict, confidence: float) -> TradingSignal:
        """创建买入信号"""
        try:
            # 计算止损位
            atr = indicators.get('atr')
            if atr is not None and not np.isnan(atr[-1]):
                stop_loss = price - (atr[-1] * self.risk_config.get('stop_loss', {}).get('atr_multiplier', 2.0))
            else:
                stop_loss = price * (1 - self.risk_config.get('stop_loss', {}).get('default_percentage', 0.05))

            # 计算目标价位（风险回报比1:2）
            risk = price - stop_loss
            target_price = price + (risk * 2)

            return TradingSignal(
                symbol=symbol,
                signal_type=SignalType.BUY,
                strength=self._determine_signal_strength(confidence),
                confidence=confidence,
                entry_price=price,
                stop_loss=stop_loss,
                target_price=target_price,
                timestamp=timestamp,
                timeframe=timeframe,
                reasons=[reason],
                technical_indicators=self._extract_current_indicators(indicators)
            )
        except Exception as e:
            logger.error(f"创建买入信号时出错: {e}")
            return None

    def _determine_signal_strength(self, confidence: float) -> SignalStrength:
        """根据置信度确定信号强度"""
        if confidence >= 0.9:
            return SignalStrength.VERY_STRONG
        elif confidence >= 0.8:
            return SignalStrength.STRONG
        elif confidence >= 0.7:
            return SignalStrength.MODERATE
        else:
            return SignalStrength.WEAK

    def _extract_current_indicators(self, indicators: Dict) -> Dict[str, float]:
        """提取当前时刻的指标值"""
        current_indicators = {}

        try:
            for key, values in indicators.items():
                if values is not None and len(values) > 0:
                    current_value = values[-1]
                    if not np.isnan(current_value):
                        current_indicators[key] = float(current_value)
        except Exception as e:
            logger.error(f"提取当前指标值时出错: {e}")

        return current_indicators

    def _apply_multi_timeframe_filter(self, signals: List[TradingSignal],
                                     df: pd.DataFrame, symbol: str) -> List[TradingSignal]:
        """应用多时间周期过滤"""
        filtered_signals = []

        try:
            # 这里可以添加更高时间周期的确认逻辑
            # 暂时返回原始信号
            filtered_signals = signals

        except Exception as e:
            logger.error(f"应用多时间周期过滤时出错: {e}")
            filtered_signals = signals

        return filtered_signals

    def _calculate_risk_reward_ratio(self, signal: TradingSignal) -> float:
        """计算风险回报比"""
        try:
            if signal.signal_type == SignalType.BUY:
                risk = signal.entry_price - signal.stop_loss
                reward = signal.target_price - signal.entry_price

                if risk > 0:
                    return reward / risk

            return 0.0
        except:
            return 0.0


class MultiTimeframeAnalyzer:
    """多时间周期分析器"""

    def __init__(self, config: Dict):
        self.config = config
        self.timeframes = config.get('timeframes', {})

    def analyze_trend_alignment(self, data_dict: Dict[str, pd.DataFrame], symbol: str) -> Dict[str, str]:
        """分析多时间周期趋势一致性"""
        trends = {}

        try:
            for timeframe, df in data_dict.items():
                if len(df) >= 50:  # 确保有足够的数据
                    trend = self._determine_trend(df)
                    trends[timeframe] = trend

        except Exception as e:
            logger.error(f"分析 {symbol} 多时间周期趋势时出错: {e}")

        return trends

    def _determine_trend(self, df: pd.DataFrame) -> str:
        """确定单一时间周期的趋势"""
        try:
            close = np.array(df['close'].values, dtype=float)

            # 使用简单移动平均线判断趋势
            sma_20 = talib.SMA(close, timeperiod=20)
            sma_50 = talib.SMA(close, timeperiod=50)

            if len(sma_20) > 0 and len(sma_50) > 0:
                current_price = close[-1]
                sma_20_current = sma_20[-1]
                sma_50_current = sma_50[-1]

                if current_price > sma_20_current > sma_50_current:
                    return "uptrend"
                elif current_price < sma_20_current < sma_50_current:
                    return "downtrend"
                else:
                    return "sideways"

            return "unknown"

        except Exception as e:
            logger.error(f"确定趋势时出错: {e}")
            return "unknown"

    def get_trend_strength(self, trends: Dict[str, str]) -> float:
        """计算趋势强度得分"""
        try:
            uptrend_count = sum(1 for trend in trends.values() if trend == "uptrend")
            total_count = len(trends)

            if total_count > 0:
                return uptrend_count / total_count

            return 0.0

        except:
            return 0.0


class VolumeAnalyzer:
    """成交量分析器"""

    @staticmethod
    def analyze_volume_confirmation(df: pd.DataFrame, signal_type: SignalType) -> bool:
        """分析成交量确认"""
        try:
            if len(df) < 20:
                return False

            current_volume = df['volume'].iloc[-1]
            avg_volume = df['volume'].rolling(20).mean().iloc[-1]

            # 买入信号需要成交量放大确认
            if signal_type == SignalType.BUY:
                return current_volume > avg_volume * 1.2

            return True

        except:
            return False

    @staticmethod
    def calculate_volume_trend(df: pd.DataFrame, periods: int = 10) -> str:
        """计算成交量趋势"""
        try:
            if len(df) < periods * 2:
                return "unknown"

            recent_volume = df['volume'].tail(periods).mean()
            previous_volume = df['volume'].tail(periods * 2).head(periods).mean()

            if recent_volume > previous_volume * 1.1:
                return "increasing"
            elif recent_volume < previous_volume * 0.9:
                return "decreasing"
            else:
                return "stable"

        except:
            return "unknown"
