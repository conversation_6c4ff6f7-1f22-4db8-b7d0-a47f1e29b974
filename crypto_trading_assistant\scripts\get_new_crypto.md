# 数字货币新币列表获取工具

这个脚本用于获取最近一个月内上市的数字货币新币列表，通过CoinGecko API提取相关信息。

## 功能特点

- 自动获取最近指定天数内上市的数字货币
- 支持多种方式识别新币（创建日期、交易所上线时间等）
- 提供丰富的币种信息（价格、市值、交易量等）
- 支持CSV格式导出结果
- 可以自定义查询时间范围和计价货币

## 安装依赖

在使用此脚本前，请确保安装了所需的Python库：

```bash
pip install pandas pycoingecko loguru
```

## 使用方法

### 基本用法

获取最近30天内上市的数字货币列表：

```bash
python scripts/get_new_crypto.py
```

### 高级选项

- `-d, --days`：指定查找过去多少天内上市的币种（默认30天）
- `-o, --output`：指定输出CSV文件路径
- `-c, --currency`：指定计价货币（默认为USD）

### 示例

1. 获取过去两周内上市的新币：
```bash
python scripts/get_new_crypto.py -d 14
```

2. 获取过去一个月内上市的新币，并保存为CSV文件：
```bash
python scripts/get_new_crypto.py -o new_coins.csv
```

3. 获取过去90天内上市且以欧元计价的新币：
```bash
python scripts/get_new_crypto.py -d 90 -c eur
```

## 输出结果说明

脚本输出的数据包含以下字段：

- `id`：币种在CoinGecko中的唯一标识符
- `symbol`：币种的交易符号
- `name`：币种的完整名称
- `current_price`：当前价格（按指定计价货币）
- `market_cap`：市值
- `market_cap_rank`：市值排名
- `total_volume`：24小时交易量
- `price_change_percentage_24h`：24小时价格变化百分比
- `genesis_date`：创建日期

## 注意事项

- 由于API访问限制，脚本可能需要一些时间完成数据获取
- 对于大量请求，CoinGecko API可能会限制访问速率
- 对于一些新币，可能没有完整的信息（如创建日期）
- 当无法准确获取币种上市时间时，脚本会尝试通过其他方式识别新币

## 技术细节

脚本使用了以下主要技术：

- `pycoingecko`：与CoinGecko API交互
- `pandas`：处理和分析数据
- `loguru`：提供日志功能
- 重试机制：处理API请求失败的情况

## 数据来源

数据来自[CoinGecko API](https://www.coingecko.com/en/api)，该API提供了关于加密货币的全面市场数据。 