"""
高级数字货币选币系统
集成市场筛选、多周期分析、K线形态识别和企业微信推送
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import requests
import json
import warnings
warnings.filterwarnings('ignore')

from market_filter import CryptoMarketFilter


class AdvancedCryptoSelector:
    """高级数字货币选币器"""
    
    def __init__(self):
        """初始化选币器"""
        self.market_filter = CryptoMarketFilter()
        
        # 支持的时间周期
        self.timeframes = ['1d', '4h', '1h', '30m', '15m']
        
        # 多时间周期权重
        self.timeframe_weights = {
            '1d': 0.4,   # 日线权重
            '4h': 0.25,  # 4小时权重
            '1h': 0.15,  # 1小时权重
            '30m': 0.12, # 30分钟权重
            '15m': 0.08  # 15分钟权重
        }
        
        # 技术指标权重
        self.weights = {
            'pattern': 0.3,
            'indicator': 0.4,
            'trend': 0.2,
            'volume': 0.1
        }
        
        # 企业微信webhook
        self.wechat_webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=52d1b8a8-a88a-4d41-9664-f1268da20985"
    
    def display_selection_menu(self):
        """显示选币菜单"""
        print("🚀 高级数字货币选币系统")
        print("=" * 60)
        print("集成市场筛选、多周期分析、K线形态识别")
        print("=" * 60)
        
        print("\n📋 选币范围选择:")
        print("1. 🌍 全市场扫描 (所有币种)")
        print("2. 👑 主流币种 (市值>100亿美元)")
        print("3. 🔥 热门山寨币 (市值10-100亿美元)")
        print("4. 💎 小市值潜力币 (市值<10亿美元)")
        print("5. 🆕 新上市币种 (30天内)")
        print("6. 🆕 较新币种 (90天内)")
        print("7. 🏦 DeFi生态代币")
        print("8. ⛓️ Layer1公链代币")
        print("9. 🔗 Layer2扩容代币")
        print("10. 🐕 Meme币专区")
        print("11. 🤖 AI概念币")
        print("12. 📈 高交易量币种")
        print("13. 🔧 自定义筛选条件")
        print("14. 📊 查看市场概况")
        
        return input("\n请选择筛选范围 (1-14): ").strip()
    
    def get_symbols_by_choice(self, choice: str) -> Tuple[List[str], str]:
        """根据选择获取币种列表"""
        predefined_lists = self.market_filter.get_predefined_lists()
        
        choice_map = {
            '1': ('all_market', '全市场'),
            '2': ('mainstream', '主流币种'),
            '3': ('altcoins', '热门山寨币'),
            '4': ('small_caps', '小市值币'),
            '5': ('new_listings_30d', '新上市币种(30天)'),
            '6': ('new_listings_90d', '较新币种(90天)'),
            '7': ('defi_tokens', 'DeFi代币'),
            '8': ('layer1_chains', 'Layer1公链'),
            '9': ('layer2_solutions', 'Layer2扩容'),
            '10': ('meme_coins', 'Meme币'),
            '11': ('ai_tokens', 'AI概念币'),
            '12': ('high_volume', '高交易量币种')
        }
        
        if choice in choice_map:
            list_key, description = choice_map[choice]
            symbols = predefined_lists[list_key]
            return symbols, description
        elif choice == '13':
            symbols = self._custom_filter_menu()
            return symbols, '自定义筛选'
        elif choice == '14':
            self._display_market_overview()
            return [], ''
        else:
            print("❌ 无效选择，使用主流币种")
            return predefined_lists['mainstream'], '主流币种'
    
    def _custom_filter_menu(self) -> List[str]:
        """自定义筛选菜单"""
        print("\n🔧 自定义筛选条件设置")
        print("-" * 40)
        
        kwargs = {}
        
        # 市值筛选
        print("\n💰 市值筛选:")
        print("1. 超大市值 (>1000亿美元)")
        print("2. 大市值 (100-1000亿美元)")
        print("3. 中市值 (10-100亿美元)")
        print("4. 小市值 (1-10亿美元)")
        print("5. 微市值 (<1亿美元)")
        print("6. 自定义市值范围")
        print("7. 跳过市值筛选")
        
        cap_choice = input("选择市值范围 (1-7): ").strip()
        
        if cap_choice == '1':
            kwargs['min_market_cap'] = 100000000000
        elif cap_choice == '2':
            kwargs['min_market_cap'] = 10000000000
            kwargs['max_market_cap'] = 100000000000
        elif cap_choice == '3':
            kwargs['min_market_cap'] = 1000000000
            kwargs['max_market_cap'] = 10000000000
        elif cap_choice == '4':
            kwargs['min_market_cap'] = 100000000
            kwargs['max_market_cap'] = 1000000000
        elif cap_choice == '5':
            kwargs['max_market_cap'] = 100000000
        elif cap_choice == '6':
            try:
                min_cap = input("最小市值 (美元): ").strip()
                if min_cap:
                    kwargs['min_market_cap'] = float(min_cap)
                max_cap = input("最大市值 (美元): ").strip()
                if max_cap:
                    kwargs['max_market_cap'] = float(max_cap)
            except:
                print("❌ 输入格式错误，跳过市值筛选")
        
        # 上市时间筛选
        print("\n⏰ 上市时间筛选:")
        print("1. 最新币种 (<30天)")
        print("2. 新币种 (30-90天)")
        print("3. 较新币种 (90-180天)")
        print("4. 成熟币种 (1-3年)")
        print("5. 老牌币种 (>3年)")
        print("6. 跳过时间筛选")
        
        time_choice = input("选择时间范围 (1-6): ").strip()
        
        if time_choice == '1':
            kwargs['listing_days'] = 30
            kwargs['listing_comparison'] = 'less_than'
        elif time_choice == '2':
            # 需要组合筛选，这里简化为90天内
            kwargs['listing_days'] = 90
            kwargs['listing_comparison'] = 'less_than'
        elif time_choice == '3':
            kwargs['listing_days'] = 180
            kwargs['listing_comparison'] = 'less_than'
        elif time_choice == '4':
            kwargs['listing_days'] = 365
            kwargs['listing_comparison'] = 'greater_than'
        elif time_choice == '5':
            kwargs['listing_days'] = 1095
            kwargs['listing_comparison'] = 'greater_than'
        
        # 类别筛选
        print("\n🏷️ 类别筛选 (可多选，用逗号分隔):")
        print("Layer1, Layer2, DeFi, Exchange, Payment, Oracle, Meme, AI, New")
        categories_input = input("选择类别 (直接回车跳过): ").strip()
        if categories_input:
            kwargs['categories'] = [cat.strip() for cat in categories_input.split(',')]
        
        return self.market_filter.custom_filter(**kwargs)
    
    def _display_market_overview(self):
        """显示市场概况"""
        print("\n📊 数字货币市场概况")
        print("=" * 50)
        
        predefined_lists = self.market_filter.get_predefined_lists()
        
        overview_data = [
            ("全市场", len(predefined_lists['all_market'])),
            ("主流币 (>100亿)", len(predefined_lists['mainstream'])),
            ("山寨币 (10-100亿)", len(predefined_lists['altcoins'])),
            ("小市值 (<10亿)", len(predefined_lists['small_caps'])),
            ("新上市 (30天)", len(predefined_lists['new_listings_30d'])),
            ("DeFi代币", len(predefined_lists['defi_tokens'])),
            ("Layer1公链", len(predefined_lists['layer1_chains'])),
            ("Layer2扩容", len(predefined_lists['layer2_solutions'])),
            ("Meme币", len(predefined_lists['meme_coins'])),
            ("AI概念", len(predefined_lists['ai_tokens'])),
        ]
        
        for category, count in overview_data:
            print(f"{category:<15}: {count:>3} 个币种")
        
        print(f"\n📈 总计: {len(predefined_lists['all_market'])} 个可交易币种")
        
        # 显示一些具体例子
        print(f"\n💎 小市值潜力币示例:")
        small_caps = predefined_lists['small_caps'][:5]
        for symbol in small_caps:
            info = self.market_filter.get_symbol_info(symbol)
            if info:
                print(f"  {symbol}: {info['name']} (市值: ${info['market_cap']/1000000:.0f}M)")
        
        print(f"\n🆕 最新上市币种:")
        new_coins = predefined_lists['new_listings_30d']
        for symbol in new_coins:
            info = self.market_filter.get_symbol_info(symbol)
            if info:
                print(f"  {symbol}: {info['name']} (上市: {info['listing_date']})")
        
        input("\n按回车键继续...")
    
    def generate_mock_data(self, symbol: str, timeframe: str) -> pd.DataFrame:
        """生成模拟数据"""
        # 获取币种信息以调整基础价格
        info = self.market_filter.get_symbol_info(symbol)
        if info:
            # 根据市值调整基础价格
            if info['market_cap'] > 100000000000:  # 超大市值
                base_price = np.random.uniform(20000, 50000)
            elif info['market_cap'] > 10000000000:  # 大市值
                base_price = np.random.uniform(100, 5000)
            elif info['market_cap'] > 1000000000:  # 中市值
                base_price = np.random.uniform(1, 100)
            else:  # 小市值
                base_price = np.random.uniform(0.001, 10)
        else:
            base_price = 100
        
        # 生成100个数据点
        periods = 100
        
        # 生成日期序列
        if timeframe == '1d':
            dates = pd.date_range(end=datetime.now(), periods=periods, freq='D')
        elif timeframe == '4h':
            dates = pd.date_range(end=datetime.now(), periods=periods, freq='4H')
        elif timeframe == '1h':
            dates = pd.date_range(end=datetime.now(), periods=periods, freq='H')
        elif timeframe == '30m':
            dates = pd.date_range(end=datetime.now(), periods=periods, freq='30T')
        else:  # 15m
            dates = pd.date_range(end=datetime.now(), periods=periods, freq='15T')
        
        # 设置随机种子
        np.random.seed(hash(symbol + timeframe) % 1000)
        
        # 根据币种类别调整波动性
        if info and info['category'] == 'Meme':
            volatility = 0.05  # Meme币高波动
        elif info and info['category'] in ['Layer1', 'Exchange']:
            volatility = 0.02  # 主流币低波动
        else:
            volatility = 0.03  # 其他币种中等波动
        
        # 创建价格走势
        trend = np.linspace(0, 0.1, periods)
        noise = np.random.normal(0, volatility, periods)
        
        # 添加技术形态
        pattern_signals = np.zeros(periods)
        signal_positions = [20, 40, 60, 80, 95]
        for pos in signal_positions:
            if pos < periods:
                noise[pos-3:pos] = -0.03
                noise[pos:pos+2] = 0.02
                pattern_signals[pos] = 1
        
        returns = trend + noise
        prices = [base_price]
        for ret in returns[1:]:
            new_price = prices[-1] * (1 + ret)
            prices.append(new_price)
        
        # 生成OHLCV数据
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            vol_factor = 0.01 + 0.02 * pattern_signals[i]
            
            open_price = close * (1 + np.random.normal(0, 0.005))
            high = max(open_price, close) * (1 + vol_factor)
            low = min(open_price, close) * (1 - vol_factor)
            
            # 根据市值调整成交量
            if info:
                base_volume = info['volume_24h'] / 24  # 小时成交量
                volume = base_volume * (1 + 2 * pattern_signals[i]) * np.random.uniform(0.5, 1.5)
            else:
                volume = np.random.lognormal(10, 0.5) * 1000
            
            data.append({
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': max(volume, 1000)
            })
        
        df = pd.DataFrame(data, index=dates)
        return df
    
    def analyze_symbol_timeframe(self, symbol: str, timeframe: str) -> dict:
        """分析单个交易对的单个时间周期"""
        try:
            # 获取数据
            data = self.generate_mock_data(symbol, timeframe)
            
            # 计算技术指标
            data = self.calculate_technical_indicators(data)
            
            if len(data) < 50:
                return None
            
            # 评估各类信号
            pattern_score = self.evaluate_patterns_optimized(data)
            indicator_score = self.evaluate_indicators_optimized(data)
            trend_score = self.evaluate_trend_optimized(data)
            volume_score = self.evaluate_volume_optimized(data)
            
            # 计算综合得分
            total_score = (
                pattern_score * self.weights['pattern'] +
                indicator_score * self.weights['indicator'] +
                trend_score * self.weights['trend'] +
                volume_score * self.weights['volume']
            )
            
            # 收集信号和K线形态
            signals = self.collect_signals_optimized(data)
            kline_patterns = self.detect_kline_patterns(data)
            
            latest = data.iloc[-1]
            
            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'score': float(total_score),
                'pattern_score': float(pattern_score),
                'indicator_score': float(indicator_score),
                'trend_score': float(trend_score),
                'volume_score': float(volume_score),
                'current_price': float(latest['close']),
                'rsi': float(latest['rsi']) if not pd.isna(latest['rsi']) else 50.0,
                'macd': float(latest['macd']) if not pd.isna(latest['macd']) else 0.0,
                'volume_ratio': float(latest['volume'] / latest['volume_sma']) if not pd.isna(latest['volume_sma']) else 1.0,
                'signals': signals,
                'kline_patterns': kline_patterns
            }
            
        except Exception as e:
            print(f"分析 {symbol} {timeframe} 时出错: {e}")
            return None

    def calculate_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        df = data.copy()

        # RSI
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))

        # MACD
        ema12 = df['close'].ewm(span=12).mean()
        ema26 = df['close'].ewm(span=26).mean()
        df['macd'] = ema12 - ema26
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_hist'] = df['macd'] - df['macd_signal']

        # 布林带
        df['bb_middle'] = df['close'].rolling(20).mean()
        bb_std = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)

        # 移动平均线
        df['sma_5'] = df['close'].rolling(5).mean()
        df['sma_10'] = df['close'].rolling(10).mean()
        df['sma_20'] = df['close'].rolling(20).mean()
        df['sma_50'] = df['close'].rolling(50).mean()

        # 成交量均线
        df['volume_sma'] = df['volume'].rolling(20).mean()

        return df

    def evaluate_patterns_optimized(self, data: pd.DataFrame) -> float:
        """优化的形态评估"""
        try:
            score = 0.0

            for i in range(-5, 0):
                if abs(i) > len(data):
                    continue

                current = data.iloc[i]
                prev = data.iloc[i-1] if i > -len(data) else current

                body = abs(current['close'] - current['open'])
                lower_shadow = min(current['close'], current['open']) - current['low']
                upper_shadow = current['high'] - max(current['close'], current['open'])

                # 锤子线形态
                if body > 0 and lower_shadow >= 1.5 * body and upper_shadow <= 0.2 * body:
                    score += 1.5

                # 十字星形态
                if body <= 0.002 * current['close'] and (current['high'] - current['low']) >= 0.008 * current['close']:
                    score += 1.0

                # 看涨吞没
                if (prev['close'] < prev['open'] and current['close'] > current['open'] and
                    current['open'] <= prev['close'] * 1.01 and current['close'] >= prev['open'] * 0.99):
                    score += 2.0

                # 突破形态
                if len(data) > 20:
                    recent_high = data['high'].iloc[-20:-1].max()
                    if current['close'] > recent_high * 1.005:
                        score += 1.5

            return min(score, 8.0)

        except:
            return 2.0

    def evaluate_indicators_optimized(self, data: pd.DataFrame) -> float:
        """优化的指标评估"""
        try:
            latest = data.iloc[-1]
            prev = data.iloc[-2] if len(data) > 1 else latest

            score = 0.0

            # RSI信号
            if not pd.isna(latest['rsi']):
                if latest['rsi'] < 35:
                    score += 1.5
                elif latest['rsi'] > 65:
                    score += 1.0
                elif 40 <= latest['rsi'] <= 60:
                    score += 1.0

                if not pd.isna(prev['rsi']) and latest['rsi'] > prev['rsi']:
                    score += 0.5

            # MACD信号
            if (not pd.isna(latest['macd']) and not pd.isna(latest['macd_signal'])):
                if latest['macd'] > latest['macd_signal']:
                    score += 2.0
                elif latest['macd'] > latest['macd_signal'] * 0.95:
                    score += 1.0

            # 布林带信号
            if (not pd.isna(latest['bb_upper']) and not pd.isna(latest['bb_lower']) and
                not pd.isna(latest['bb_middle'])):
                bb_position = (latest['close'] - latest['bb_lower']) / (latest['bb_upper'] - latest['bb_lower'])
                if 0.2 <= bb_position <= 0.8:
                    score += 1.0
                elif bb_position > 0.8:
                    score += 1.5

            return min(score, 6.0)

        except:
            return 2.0

    def evaluate_trend_optimized(self, data: pd.DataFrame) -> float:
        """优化的趋势评估"""
        try:
            latest = data.iloc[-1]
            score = 0.0

            # 均线信号
            if (not pd.isna(latest['sma_5']) and not pd.isna(latest['sma_20'])):
                if latest['sma_5'] > latest['sma_20']:
                    score += 2.0
                elif latest['sma_5'] > latest['sma_20'] * 0.98:
                    score += 1.0

            # 价格位置
            if not pd.isna(latest['sma_20']):
                if latest['close'] > latest['sma_20']:
                    score += 1.5
                elif latest['close'] > latest['sma_20'] * 0.98:
                    score += 1.0

            # 短期趋势
            if len(data) >= 5:
                recent_closes = data['close'].iloc[-5:]
                if recent_closes.iloc[-1] > recent_closes.iloc[0]:
                    score += 1.0

            return min(score, 4.0)

        except:
            return 1.0

    def evaluate_volume_optimized(self, data: pd.DataFrame) -> float:
        """优化的成交量评估"""
        try:
            latest = data.iloc[-1]
            score = 0.0

            # 成交量信号
            if not pd.isna(latest['volume_sma']):
                volume_ratio = latest['volume'] / latest['volume_sma']
                if volume_ratio > 1.2:
                    score += 2.0
                elif volume_ratio > 1.0:
                    score += 1.0

            # 价量配合
            if latest['close'] > latest['open']:
                score += 1.0

            return min(score, 3.0)

        except:
            return 1.0

    def collect_signals_optimized(self, data: pd.DataFrame) -> list:
        """收集优化的信号"""
        signals = []

        try:
            latest = data.iloc[-1]
            prev = data.iloc[-2] if len(data) > 1 else latest

            # RSI信号
            if not pd.isna(latest['rsi']):
                if latest['rsi'] < 35:
                    signals.append("RSI接近超卖")
                elif latest['rsi'] > 65:
                    signals.append("RSI强势区域")
                elif 45 <= latest['rsi'] <= 55:
                    signals.append("RSI中性健康")

            # MACD信号
            if (not pd.isna(latest['macd']) and not pd.isna(latest['macd_signal'])):
                if latest['macd'] > latest['macd_signal']:
                    signals.append("MACD金叉确认")
                elif latest['macd'] > latest['macd_signal'] * 0.95:
                    signals.append("MACD接近金叉")

            # 趋势信号
            if (not pd.isna(latest['sma_5']) and not pd.isna(latest['sma_20'])):
                if latest['sma_5'] > latest['sma_20']:
                    signals.append("短期趋势向上")

            # 成交量信号
            if not pd.isna(latest['volume_sma']) and latest['volume'] > latest['volume_sma'] * 1.2:
                signals.append("成交量活跃")

            # 价格信号
            if latest['close'] > latest['open']:
                signals.append("当前K线收阳")

            # 布林带信号
            if (not pd.isna(latest['bb_upper']) and not pd.isna(latest['bb_lower'])):
                bb_position = (latest['close'] - latest['bb_lower']) / (latest['bb_upper'] - latest['bb_lower'])
                if bb_position > 0.7:
                    signals.append("接近布林带上轨")
                elif bb_position < 0.3:
                    signals.append("接近布林带下轨")

        except:
            signals = ["技术分析中"]

        return signals[:5]

    def detect_kline_patterns(self, data: pd.DataFrame) -> list:
        """检测K线形态"""
        patterns = []

        try:
            if len(data) < 5:
                return ["数据不足"]

            # 检查最近3个K线的形态
            for i in range(-3, 0):
                if abs(i) > len(data):
                    continue

                current = data.iloc[i]
                prev = data.iloc[i-1] if i > -len(data) else current

                body = abs(current['close'] - current['open'])
                total_range = current['high'] - current['low']
                upper_shadow = current['high'] - max(current['close'], current['open'])
                lower_shadow = min(current['close'], current['open']) - current['low']

                # 锤子线形态
                if (body > 0 and total_range > 0 and
                    lower_shadow >= 1.5 * body and upper_shadow <= 0.3 * body):
                    patterns.append("锤子线")

                # 十字星形态
                elif body <= 0.002 * current['close'] and total_range >= 0.008 * current['close']:
                    patterns.append("十字星")

                # 长上影线
                elif upper_shadow >= 2 * body and body > 0:
                    patterns.append("长上影线")

                # 长下影线
                elif lower_shadow >= 2 * body and body > 0:
                    patterns.append("长下影线")

                # 大阳线
                elif (current['close'] > current['open'] and
                      body >= 0.03 * current['close']):
                    patterns.append("大阳线")

                # 大阴线
                elif (current['close'] < current['open'] and
                      body >= 0.03 * current['close']):
                    patterns.append("大阴线")

                # 小实体K线
                elif body <= 0.01 * current['close']:
                    patterns.append("小实体")

            # 检查多K线组合形态
            if len(data) >= 2:
                current = data.iloc[-1]
                prev = data.iloc[-2]

                # 看涨吞没
                if (prev['close'] < prev['open'] and current['close'] > current['open'] and
                    current['open'] <= prev['close'] * 1.01 and current['close'] >= prev['open'] * 0.99):
                    patterns.append("看涨吞没")

                # 看跌吞没
                elif (prev['close'] > prev['open'] and current['close'] < current['open'] and
                      current['open'] >= prev['close'] * 0.99 and current['close'] <= prev['open'] * 1.01):
                    patterns.append("看跌吞没")

            # 去重并限制数量
            unique_patterns = list(dict.fromkeys(patterns))

            if not unique_patterns:
                latest = data.iloc[-1]
                if latest['close'] > latest['open']:
                    unique_patterns.append("阳线")
                elif latest['close'] < latest['open']:
                    unique_patterns.append("阴线")
                else:
                    unique_patterns.append("一字线")

            return unique_patterns[:3]

        except Exception as e:
            return ["形态分析中"]

    def multi_timeframe_analysis(self, symbols: list, description: str) -> dict:
        """多时间周期分析"""
        results = {}

        print(f"\n🔍 开始多时间周期分析")
        print(f"📊 筛选范围: {description}")
        print(f"🎯 分析标的: {len(symbols)} 个")
        print("=" * 60)

        for symbol in symbols:
            print(f"\n分析 {symbol}:")

            # 显示币种基本信息
            info = self.market_filter.get_symbol_info(symbol)
            if info:
                print(f"  📝 {info['name']} ({info['category']})")
                print(f"  💰 市值: ${info['market_cap']/1000000:.0f}M")
                print(f"  📅 上市: {info['listing_date']}")

            symbol_results = {}

            # 分析各个时间周期
            for timeframe in self.timeframes:
                tf_result = self.analyze_symbol_timeframe(symbol, timeframe)
                if tf_result:
                    symbol_results[timeframe] = tf_result
                    patterns_str = ', '.join(tf_result.get('kline_patterns', [])[:2])
                    if not patterns_str:
                        patterns_str = '普通'
                    print(f"  {timeframe:>3}: 得分 {tf_result['score']:.1f}, RSI {tf_result['rsi']:.1f}, 形态 [{patterns_str}], 信号 {len(tf_result['signals'])}")

            if symbol_results:
                # 计算多周期综合得分
                total_score = 0
                weight_sum = 0

                for tf, result in symbol_results.items():
                    weight = self.timeframe_weights.get(tf, 0.1)
                    total_score += result['score'] * weight
                    weight_sum += weight

                multi_tf_score = total_score / weight_sum if weight_sum > 0 else 0

                # 多周期确认
                confirmation = self.check_multi_timeframe_confirmation(symbol_results)

                symbol_results['multi_tf_score'] = multi_tf_score
                symbol_results['confirmation'] = confirmation

                print(f"  综合得分: {multi_tf_score:.2f}")
                print(f"  趋势一致: {'✓' if confirmation['trend_alignment'] else '✗'}")
                print(f"  信号一致: {'✓' if confirmation['signal_consistency'] else '✗'}")
                print(f"  风险等级: {confirmation['risk_level']}")

                results[symbol] = symbol_results

        return results

    def check_multi_timeframe_confirmation(self, symbol_results: dict) -> dict:
        """检查多时间周期确认"""
        confirmation = {
            'trend_alignment': False,
            'signal_consistency': False,
            'risk_level': 'medium'
        }

        # 获取各周期得分
        scores = {}
        for tf in ['1d', '4h', '1h', '30m', '15m']:
            if tf in symbol_results:
                scores[tf] = symbol_results[tf]['score']

        # 检查趋势一致性
        daily_score = scores.get('1d', 0)
        h4_score = scores.get('4h', 0)
        h1_score = scores.get('1h', 0)

        if daily_score > 2.0 and h4_score > 1.5:
            confirmation['trend_alignment'] = True
        elif daily_score > 1.5 or h4_score > 2.0:
            confirmation['trend_alignment'] = True

        # 检查信号一致性
        high_score_count = sum(1 for score in scores.values() if score > 1.5)
        if high_score_count >= 3:
            confirmation['signal_consistency'] = True
        elif high_score_count >= 2:
            confirmation['signal_consistency'] = True

        # 风险评估
        avg_score = sum(scores.values()) / len(scores) if scores else 0
        if avg_score > 2.5:
            confirmation['risk_level'] = 'low'
        elif avg_score > 1.5:
            confirmation['risk_level'] = 'medium'
        else:
            confirmation['risk_level'] = 'high'

        return confirmation

    def send_wechat_notification(self, results: dict, description: str):
        """发送企业微信通知"""
        try:
            if not results:
                return

            # 构建消息内容
            message = self.build_notification_message(results, description)

            # 发送请求
            data = {
                "msgtype": "text",
                "text": {
                    "content": message
                }
            }

            response = requests.post(
                self.wechat_webhook,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    print("✅ 企业微信通知发送成功")
                else:
                    print(f"❌ 企业微信通知发送失败: {result}")
            else:
                print(f"❌ 企业微信通知请求失败: {response.status_code}")

        except Exception as e:
            print(f"❌ 发送企业微信通知失败: {e}")

    def build_notification_message(self, results: dict, description: str) -> str:
        """构建通知消息"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 筛选高分标的
            high_score_symbols = []
            for symbol, data in results.items():
                multi_tf_score = data.get('multi_tf_score', 0)
                if multi_tf_score >= 1.5:
                    high_score_symbols.append((symbol, data))

            # 按综合得分排序
            high_score_symbols.sort(key=lambda x: x[1]['multi_tf_score'], reverse=True)

            message = f"🚀 数字货币高级选币提醒\n"
            message += f"⏰ 时间: {timestamp}\n"
            message += f"📊 筛选范围: {description}\n"
            message += f"🔍 分析周期: 1d/4h/1h/30m/15m\n"
            message += f"🎯 发现 {len(high_score_symbols)} 个潜力标的\n\n"

            # 添加前6个结果
            for i, (symbol, data) in enumerate(high_score_symbols[:6], 1):
                multi_tf_score = data['multi_tf_score']
                confirmation = data['confirmation']

                # 获取币种信息
                info = self.market_filter.get_symbol_info(symbol)
                market_cap_str = f"${info['market_cap']/1000000:.0f}M" if info else "未知"
                category_str = info['category'] if info else "未知"

                # 获取最新价格（使用日线数据）
                daily_data = data.get('1d', {})
                price = daily_data.get('current_price', 0)
                rsi = daily_data.get('rsi', 50)

                # 获取主要信号
                all_signals = []
                all_patterns = []
                for tf_data in data.values():
                    if isinstance(tf_data, dict):
                        if 'signals' in tf_data:
                            all_signals.extend(tf_data['signals'])
                        if 'kline_patterns' in tf_data:
                            all_patterns.extend(tf_data['kline_patterns'])

                # 去重并取前2个
                unique_signals = list(dict.fromkeys(all_signals))[:2]
                signals_str = ', '.join(unique_signals) if unique_signals else '技术分析中'

                # 获取K线形态
                kline_patterns = []
                if '1d' in data and 'kline_patterns' in data['1d']:
                    kline_patterns = data['1d']['kline_patterns']
                elif all_patterns:
                    kline_patterns = list(dict.fromkeys(all_patterns))[:2]

                patterns_str = ', '.join(kline_patterns[:2]) if kline_patterns else '普通K线'

                # 风险等级emoji
                risk_emoji = {'low': '🟢', 'medium': '🟡', 'high': '🔴'}
                risk_icon = risk_emoji.get(confirmation['risk_level'], '🟡')

                message += f"{i}. {symbol} {risk_icon}\n"
                message += f"   💯 综合得分: {multi_tf_score:.2f}\n"
                message += f"   🏷️ 类别: {category_str} (市值: {market_cap_str})\n"
                message += f"   💰 当前价格: {price:.4f}\n"
                message += f"   📈 RSI: {rsi:.1f}\n"
                message += f"   📊 K线形态: {patterns_str}\n"
                message += f"   🔔 主要信号: {signals_str}\n\n"

            # 添加筛选统计
            message += f"📋 筛选统计:\n"
            message += f"总分析: {len(results)} 个标的\n"
            message += f"优质标的: {len(high_score_symbols)} 个\n"

            # 按风险等级统计
            risk_stats = {'low': 0, 'medium': 0, 'high': 0}
            for _, data in high_score_symbols:
                risk_level = data['confirmation']['risk_level']
                risk_stats[risk_level] += 1

            message += f"🟢 低风险: {risk_stats['low']} 个\n"
            message += f"🟡 中风险: {risk_stats['medium']} 个\n"
            message += f"🔴 高风险: {risk_stats['high']} 个\n"

            message += f"\n💡 投资建议:\n"
            message += f"🟢 低风险: 建议重点关注\n"
            message += f"🟡 中风险: 可适量配置\n"
            message += f"🔴 高风险: 谨慎观察\n"
            message += f"\n⚠️ 风险提示: 仅供参考，请谨慎投资"

            return message

        except Exception as e:
            print(f"构建通知消息失败: {e}")
            return f"数字货币高级选币系统运行完成\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n筛选范围: {description}"

    def run_advanced_selection(self):
        """运行高级选币系统"""
        while True:
            try:
                choice = self.display_selection_menu()

                if choice == '14':  # 查看市场概况
                    continue
                elif choice.lower() in ['q', 'quit', 'exit']:
                    print("👋 感谢使用高级选币系统！")
                    break

                symbols, description = self.get_symbols_by_choice(choice)

                if not symbols:
                    print("❌ 未找到符合条件的币种，请重新选择")
                    continue

                print(f"\n✅ 已选择: {description}")
                print(f"📊 包含 {len(symbols)} 个币种")

                if len(symbols) > 15:
                    print(f"⚠️ 币种数量较多，将分析前15个: {', '.join(symbols[:15])}")
                    symbols = symbols[:15]
                else:
                    print(f"🎯 分析币种: {', '.join(symbols)}")

                # 确认是否继续
                confirm = input(f"\n是否开始分析? (y/n): ").strip().lower()
                if confirm not in ['y', 'yes', '是']:
                    continue

                # 运行多时间周期分析
                results = self.multi_timeframe_analysis(symbols, description)

                # 显示汇总结果
                self.display_summary_results(results, description)

                # 发送企业微信通知
                if results:
                    send_notification = input(f"\n是否发送企业微信通知? (y/n): ").strip().lower()
                    if send_notification in ['y', 'yes', '是']:
                        print("\n📱 发送企业微信通知...")
                        self.send_wechat_notification(results, description)

                # 是否继续
                continue_choice = input(f"\n是否继续选币? (y/n): ").strip().lower()
                if continue_choice not in ['y', 'yes', '是']:
                    print("👋 感谢使用高级选币系统！")
                    break

            except KeyboardInterrupt:
                print("\n\n👋 用户中断，感谢使用！")
                break
            except Exception as e:
                print(f"\n❌ 系统错误: {e}")
                continue

    def display_summary_results(self, results: dict, description: str):
        """显示汇总结果"""
        print(f"\n📊 {description} - 多周期分析汇总")
        print("=" * 100)

        if not results:
            print("未找到符合条件的标的")
            return

        # 按综合得分排序
        sorted_results = sorted(
            results.items(),
            key=lambda x: x[1].get('multi_tf_score', 0),
            reverse=True
        )

        print(f"{'排名':<4} {'代码':<15} {'类别':<8} {'市值':<8} {'综合得分':<8} {'1d':<6} {'4h':<6} {'1h':<6} {'30m':<6} {'15m':<6} {'风险':<6} {'趋势'}")
        print("-" * 100)

        for i, (symbol, data) in enumerate(sorted_results, 1):
            multi_tf_score = data.get('multi_tf_score', 0)
            confirmation = data.get('confirmation', {})

            # 获取币种信息
            info = self.market_filter.get_symbol_info(symbol)
            category = info['category'][:6] if info else 'Unknown'
            market_cap = f"{info['market_cap']/1000000:.0f}M" if info else 'Unknown'

            # 获取各周期得分
            scores = {}
            for tf in ['1d', '4h', '1h', '30m', '15m']:
                if tf in data:
                    scores[tf] = f"{data[tf]['score']:.1f}"
                else:
                    scores[tf] = "-"

            trend_align = "✓" if confirmation.get('trend_alignment') else "✗"
            risk_level = confirmation.get('risk_level', 'unknown')[:4]

            print(f"{i:<4} {symbol:<15} {category:<8} {market_cap:<8} {multi_tf_score:<8.2f} "
                  f"{scores['1d']:<6} {scores['4h']:<6} {scores['1h']:<6} "
                  f"{scores['30m']:<6} {scores['15m']:<6} {risk_level:<6} {trend_align}")


def main():
    """主函数"""
    selector = AdvancedCryptoSelector()
    selector.run_advanced_selection()


if __name__ == "__main__":
    main()
