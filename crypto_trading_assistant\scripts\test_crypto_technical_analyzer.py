import unittest
import pandas as pd
import numpy as np
from crypto_technical_analyzer import TechnicalAnalyzer # 假设可以直接导入

class TestTechnicalAnalyzer(unittest.TestCase):

    def setUp(self):
        """
        在每个测试方法执行前设置测试数据
        """
        # 创建一个包含足够数据的测试DataFrame
        data = {
            'open': np.random.randn(300).cumsum() + 10000,
            'high': np.random.randn(300).cumsum() + 10010,
            'low': np.random.randn(300).cumsum() + 9990,
            'close': np.random.randn(300).cumsum() + 10000,
            'volume': np.random.randint(100000, 1000000, 300)
        }
        self.test_df = pd.DataFrame(data)
        self.analyzer = TechnicalAnalyzer() # 使用默认SMTP参数初始化

    def test_calculate_indicators(self):
        """
        测试 calculate_indicators 方法
        """
        df_with_indicators = self.analyzer.calculate_indicators(self.test_df.copy())

        # 检查是否添加了预期的指标列
        expected_columns = [
            'sma_10', 'sma_20', 'sma_50', 'sma_200',
            'ema_12', 'ema_26', 'ema_50',
            'macd', 'macd_signal', 'macd_hist',
            'rsi_14', 'k', 'd', 'j',
            'bollinger_upper', 'bollinger_middle', 'bollinger_lower',
            'atr', 'vwap', 'volume_change',
            'adx', 'plus_di', 'minus_di', 'cci', 'obv',
            'trend_strength', 'fear_greed', 'historical_volatility',
            'liquidity_ratio', 'pivot', 'r1', 's1', 'r2', 's2',
            'psychological_levels', 'psychological_levels_50', 'psychological_levels_100',
            'volume_profile', 'volume_std', 'price_volatility',
            'support_strength', 'resistance_strength', 'order_book_depth',
            'large_trade_volume', 'large_trade_ratio', 'market_impact', 'liquidity_score',
            'daily_trend', 'weekly_trend', 'monthly_trend',
            'social_sentiment', 'news_sentiment', 'money_flow', 'market_sentiment'
        ]
        for col in expected_columns:
            self.assertIn(col, df_with_indicators.columns, f"缺少指标列: {col}")

        # 检查指标列是否包含NaN值 (预期在数据开头会有NaN)
        # 至少前200行应该有NaN，因为一些指标需要较长的窗口
        # 检查指标列是否包含NaN值 (预期在数据开头会有NaN)
        # 至少前200行应该有NaN，因为一些指标需要较长的窗口
        # 排除不需要NaN的列，如pivot
        no_nan_expected_cols = ['open', 'high', 'low', 'close', 'volume', 'pivot', 'r1', 's1', 'r2', 's2', 'psychological_levels', 'psychological_levels_50', 'psychological_levels_100']
        for col in expected_columns:
             if col not in no_nan_expected_cols:
                self.assertTrue(df_with_indicators[col].isnull().any(), f"指标列 {col} 没有NaN值")

        # 可以添加更具体的指标值检查，但这需要知道预期的计算结果
        # 例如：self.assertAlmostEqual(df_with_indicators['sma_10'].iloc[-1], expected_sma_value, places=4)


    def test_generate_signals(self):
        """
        测试 generate_signals 方法
        """
        # 创建能够触发不同信号的测试数据
        # TODO: 添加更全面的测试数据
        data = {
            'open': [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 109, 108, 107, 106, 105, 104, 103, 102, 101, 100],
            'high': [101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 110, 109, 108, 107, 106, 105, 104, 103, 102, 101],
            'low': [99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 108, 107, 106, 105, 104, 103, 102, 101, 100, 99],
            'close': [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 109, 108, 107, 106, 105, 104, 103, 102, 101, 100],
            'volume': [1000, 1200, 1100, 1300, 1400, 1500, 1600, 1700, 1800, 1900, 2000, 1800, 1700, 1600, 1500, 1400, 1300, 1200, 1100, 1000, 900]
        }
        test_df_signals = pd.DataFrame(data)
        # 为了计算所有指标，需要更多数据，这里只是一个示例
        # 在实际测试中，应该使用足够长的数据序列
        extended_data = {
             'open': np.random.randn(280).cumsum() + 90,
             'high': np.random.randn(280).cumsum() + 92,
             'low': np.random.randn(280).cumsum() + 88,
             'close': np.random.randn(280).cumsum() + 90,
             'volume': np.random.randint(500, 5000, 280)
        }
        extended_df = pd.DataFrame(extended_data)
        combined_df = pd.concat([extended_df, test_df_signals], ignore_index=True)


        df_with_indicators = self.analyzer.calculate_indicators(combined_df.copy())
        signal = self.analyzer.generate_signals(df_with_indicators)

        # TODO: 添加具体的断言来验证信号的action, strength, reasons, confidence, stop_loss, take_profit, risk_level

        # 示例断言 (需要根据测试数据和预期信号进行修改)
        # self.assertEqual(signal['action'], 'buy')
        # self.assertEqual(signal['strength'], 'strong')
        # self.assertIn('MACD金叉', signal['reasons'])
        # self.assertGreater(signal['confidence'], 0.5)
        # self.assertIsNotNone(signal['stop_loss'])
        # self.assertIsNotNone(signal['take_profit'])
        # self.assertIn(signal['risk_level'], ['low', 'medium', 'high'])


if __name__ == '__main__':
    unittest.main()