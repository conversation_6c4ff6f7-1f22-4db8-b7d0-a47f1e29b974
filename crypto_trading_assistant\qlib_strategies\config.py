"""
数字货币选币系统配置文件
"""

from typing import Dict, List, Any

# 基础配置
BASE_CONFIG = {
    # 选币参数
    'min_score': 3.0,
    'max_selections': 10,
    'timeframes': ['1d', '4h', '1h', '30m', '15m'],
    
    # 技术指标权重
    'weights': {
        'pattern': 0.3,      # K线形态权重
        'indicator': 0.4,    # 技术指标权重
        'trend': 0.2,        # 趋势权重
        'volume': 0.1        # 成交量权重
    },
    
    # 数据源配置
    'data_config': {
        'data_source': 'binance',  # 数据源: binance, okx, huobi, coinbase
        'api_key': '',             # API密钥 (如需要)
        'api_secret': '',          # API密钥 (如需要)
        'cache_timeout': 300,      # 缓存超时时间 (秒)
        'request_timeout': 10,     # 请求超时时间 (秒)
        'rate_limit': 0.1          # 请求间隔 (秒)
    },
    
    # 消息推送配置
    'notification_config': {
        'enable_wechat': True,
        'wechat_webhook': 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=69db19ba-d1af-422a-b0cf-19f21cd5b5fc',
        'enable_dingtalk': False,
        'dingtalk_webhook': '',
        'enable_email': False,
        'email_config': {
            'smtp_server': '',
            'smtp_port': 587,
            'username': '',
            'password': '',
            'to_emails': []
        }
    }
}

# 数字货币交易对列表
CRYPTO_SYMBOLS = [
    # 主流币
    'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'XRPUSDT',
    'SOLUSDT', 'DOTUSDT', 'AVAXUSDT', 'MATICUSDT', 'LINKUSDT',
    
    # 热门币
    'DOGEUSDT', 'SHIBUSDT', 'UNIUSDT', 'LTCUSDT', 'BCHUSDT',
    'ETCUSDT', 'XLMUSDT', 'FILUSDT', 'TRXUSDT', 'ATOMUSDT',
    
    # DeFi币
    'AAVEUSDT', 'COMPUSDT', 'MKRUSDT', 'YFIUSDT', 'SUSHIUSDT',
    'CAKEUSDT', '1INCHUSDT', 'CRVUSDT', 'SNXUSDT', 'ALPHAUSDT',
    
    # Layer2/新兴币
    'OPUSDT', 'ARBUSDT', 'APTUSDT', 'SUIUSDT', 'INJUSDT',
    'TRBUSDT', 'RNDRUSDT', 'FETUSDT', 'AGIXUSDT', 'OCEANUSDT'
]

# 技术指标参数配置
INDICATOR_PARAMS = {
    'rsi': {
        'period': 14,
        'oversold': 30,
        'overbought': 70,
        'recovery_threshold': 35
    },
    'macd': {
        'fast_period': 12,
        'slow_period': 26,
        'signal_period': 9
    },
    'bollinger_bands': {
        'period': 20,
        'std_dev': 2
    },
    'moving_averages': {
        'short_ma': 5,
        'medium_ma': 10,
        'long_ma': 20,
        'trend_ma': 50
    },
    'volume': {
        'volume_ma_period': 20,
        'volume_breakout_ratio': 1.5,
        'volume_confirm_ratio': 1.2
    },
    'atr': {
        'period': 14
    }
}

# K线形态识别参数
PATTERN_PARAMS = {
    'hammer': {
        'lower_shadow_ratio': 2.0,    # 下影线与实体比例
        'upper_shadow_ratio': 0.1,    # 上影线与实体比例
        'min_body_ratio': 0.001       # 最小实体比例
    },
    'doji': {
        'max_body_ratio': 0.001,      # 最大实体比例
        'min_range_ratio': 0.01,      # 最小波动比例
        'shadow_balance_ratio': 0.005  # 上下影线平衡比例
    },
    'engulfing': {
        'min_body_ratio': 1.0         # 吞没实体比例
    },
    'breakout': {
        'lookback_period': 20,        # 回看周期
        'min_breakout_ratio': 0.01,   # 最小突破幅度
        'volume_confirm_ratio': 1.5   # 成交量确认比例
    }
}

# 多时间周期权重配置
MULTI_TIMEFRAME_WEIGHTS = {
    '1d': 0.4,   # 日线权重
    '4h': 0.25,  # 4小时权重
    '1h': 0.15,  # 1小时权重
    '30m': 0.12, # 30分钟权重
    '15m': 0.08  # 15分钟权重
}

# 风险控制参数
RISK_PARAMS = {
    'max_rsi': 80,              # 最大RSI值
    'min_rsi': 20,              # 最小RSI值
    'max_price_change': 0.15,   # 最大单日涨幅
    'min_volume_ratio': 0.5,    # 最小成交量比例
    'max_volatility': 0.1,      # 最大波动率
    'min_market_cap': 1000000   # 最小市值 (如果有数据)
}

# 选币策略配置
STRATEGY_CONFIGS = {
    # 强势突破策略
    'strong_breakout': {
        'min_score': 4.0,
        'weights': {
            'pattern': 0.2,
            'indicator': 0.3,
            'trend': 0.3,
            'volume': 0.2
        },
        'filters': {
            'rsi_max': 75,
            'volume_min_ratio': 1.5,
            'price_change_max': 0.12
        }
    },
    
    # 超跌反弹策略
    'oversold_rebound': {
        'min_score': 3.5,
        'weights': {
            'pattern': 0.4,
            'indicator': 0.4,
            'trend': 0.1,
            'volume': 0.1
        },
        'filters': {
            'rsi_max': 40,
            'rsi_min': 25,
            'price_decline_min': -0.1
        }
    },
    
    # 趋势跟随策略
    'trend_following': {
        'min_score': 3.0,
        'weights': {
            'pattern': 0.2,
            'indicator': 0.3,
            'trend': 0.4,
            'volume': 0.1
        },
        'filters': {
            'rsi_min': 40,
            'rsi_max': 70,
            'ma_alignment': True
        }
    }
}

# 回测配置
BACKTEST_CONFIG = {
    'start_date': '2023-01-01',
    'end_date': '2024-01-01',
    'initial_capital': 100000,
    'commission_rate': 0.001,
    'slippage_rate': 0.0005,
    'rebalance_frequency': 'daily',
    'max_positions': 10,
    'position_size': 0.1,  # 每个仓位占总资金的比例
    'stop_loss': -0.05,    # 止损比例
    'take_profit': 0.15    # 止盈比例
}

# 实盘交易配置
LIVE_TRADING_CONFIG = {
    'enable_trading': False,    # 是否启用实盘交易
    'paper_trading': True,      # 是否为模拟交易
    'max_daily_trades': 5,      # 每日最大交易次数
    'position_size_ratio': 0.05, # 单笔交易占总资金比例
    'stop_loss_ratio': 0.03,    # 止损比例
    'take_profit_ratio': 0.08,  # 止盈比例
    'max_drawdown': 0.1,        # 最大回撤
    'risk_free_rate': 0.02      # 无风险利率
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_path': 'logs/crypto_selection.log',
    'max_file_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}


def get_config(strategy_name: str = 'default') -> Dict[str, Any]:
    """
    获取指定策略的配置
    
    Args:
        strategy_name: 策略名称
        
    Returns:
        配置字典
    """
    config = BASE_CONFIG.copy()
    
    if strategy_name in STRATEGY_CONFIGS:
        strategy_config = STRATEGY_CONFIGS[strategy_name]
        config.update(strategy_config)
    
    # 添加其他配置
    config['symbols'] = CRYPTO_SYMBOLS
    config['indicator_params'] = INDICATOR_PARAMS
    config['pattern_params'] = PATTERN_PARAMS
    config['multi_timeframe_weights'] = MULTI_TIMEFRAME_WEIGHTS
    config['risk_params'] = RISK_PARAMS
    config['backtest_config'] = BACKTEST_CONFIG
    config['live_trading_config'] = LIVE_TRADING_CONFIG
    config['logging_config'] = LOGGING_CONFIG
    
    return config


def get_symbols_by_category(category: str = 'all') -> List[str]:
    """
    根据类别获取交易对列表
    
    Args:
        category: 类别 ('mainstream', 'defi', 'layer2', 'all')
        
    Returns:
        交易对列表
    """
    categories = {
        'mainstream': CRYPTO_SYMBOLS[:10],
        'popular': CRYPTO_SYMBOLS[10:20],
        'defi': CRYPTO_SYMBOLS[20:30],
        'layer2': CRYPTO_SYMBOLS[30:],
        'all': CRYPTO_SYMBOLS
    }
    
    return categories.get(category, CRYPTO_SYMBOLS)
