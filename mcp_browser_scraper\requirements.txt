# MCP Browser Scraper Dependencies

# 核心浏览器自动化
playwright>=1.40.0
playwright-stealth>=1.0.6

# 异步支持
asyncio>=3.4.3
aiohttp>=3.9.0
aiofiles>=23.2.0

# 数据处理
pandas>=2.1.0
numpy>=1.24.0
beautifulsoup4>=4.12.0
lxml>=4.9.0
selectolax>=0.3.17

# 网络和代理
requests>=2.31.0
httpx>=0.25.0
pysocks>=1.7.1
fake-useragent>=1.4.0

# 配置管理
pyyaml>=6.0.1
python-dotenv>=1.0.0
configparser>=6.0.0

# 缓存和存储
redis>=5.0.0
sqlite3
diskcache>=5.6.3

# 日志和监控
loguru>=0.7.2
rich>=13.7.0
tqdm>=4.66.0

# 数据验证
pydantic>=2.5.0
jsonschema>=4.20.0

# 图像处理
pillow>=10.1.0
opencv-python>=4.8.0

# 加密和安全
cryptography>=41.0.0
hashlib

# 时间处理
python-dateutil>=2.8.2
pytz>=2023.3

# 任务调度
schedule>=1.2.0

# 正则表达式增强
regex>=2023.10.3

# 并发处理
concurrent-futures>=3.1.1
threading

# 系统工具
psutil>=5.9.6
platform

# 开发和测试
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.12.0
black>=23.11.0
flake8>=6.1.0

# 可选依赖 (根据需要安装)
# selenium>=4.15.0          # 备用浏览器驱动
# undetected-chromedriver   # Chrome反检测
# cloudscraper>=1.2.71      # Cloudflare绕过
# curl-cffi>=0.5.10         # HTTP/2支持
