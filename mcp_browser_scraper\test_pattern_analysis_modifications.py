#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试形态分析修改功能
验证双长上影线识别和图形验证功能
"""

import sys
import os
from datetime import datetime

def log(message):
    """日志函数"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def test_double_long_upper_shadow_detection():
    """测试双长上影线形态识别"""
    log("🔧 测试双长上影线形态识别...")
    
    try:
        # 导入修改后的类
        sys.path.append('.')
        import importlib.util
        spec = importlib.util.spec_from_file_location("advanced_crypto_scraper_tuxing",
                                                     "mcp_browser_scraper/advanced_crypto_scraper tuxing.py")
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        AdvancedCryptoScraper = module.AdvancedCryptoScraper
        
        # 创建实例
        scraper = AdvancedCryptoScraper(use_proxy=False)
        
        # 构造测试数据 - 模拟双长上影线形态
        test_historical_data = [
            # 第一根K线 - 长上影线
            {
                'date': '2025-06-20',
                'open_price': 100.0,
                'high_price': 120.0,  # 高点
                'low_price': 95.0,
                'close_price': 105.0,  # 实体较小
                'volume': 1000000
            },
            # 第二根K线 - 长上影线，高点递减
            {
                'date': '2025-06-21',
                'open_price': 105.0,
                'high_price': 115.0,  # 比第一根低
                'low_price': 100.0,
                'close_price': 108.0,  # 实体较小
                'volume': 1200000
            }
        ]
        
        log("📊 测试数据:")
        for i, candle in enumerate(test_historical_data, 1):
            log(f"   第{i}根K线: 开盘={candle['open_price']}, 最高={candle['high_price']}, "
                f"最低={candle['low_price']}, 收盘={candle['close_price']}")
        
        # 测试形态识别
        patterns = scraper._identify_patterns(test_historical_data)
        
        log(f"🎯 识别结果: {patterns}")
        
        if "双长上影线" in patterns:
            log("✅ 双长上影线形态识别成功！")
            return True
        else:
            log("❌ 双长上影线形态识别失败")
            return False
            
    except Exception as e:
        log(f"❌ 测试失败: {e}")
        import traceback
        log(f"错误详情: {traceback.format_exc()}")
        return False

def test_non_double_long_upper_shadow():
    """测试非双长上影线形态"""
    log("🔧 测试非双长上影线形态...")
    
    try:
        sys.path.append('mcp_browser_scraper')
        from advanced_crypto_scraper import AdvancedCryptoScraper
        
        scraper = AdvancedCryptoScraper(use_proxy=False)
        
        # 构造测试数据 - 不符合双长上影线条件
        test_historical_data = [
            # 第一根K线 - 普通K线
            {
                'date': '2025-06-20',
                'open_price': 100.0,
                'high_price': 105.0,  # 上影线很短
                'low_price': 95.0,
                'close_price': 103.0,
                'volume': 1000000
            },
            # 第二根K线 - 普通K线
            {
                'date': '2025-06-21',
                'open_price': 103.0,
                'high_price': 108.0,
                'low_price': 100.0,
                'close_price': 106.0,
                'volume': 1200000
            }
        ]
        
        patterns = scraper._identify_patterns(test_historical_data)
        
        log(f"🎯 识别结果: {patterns}")
        
        if "双长上影线" not in patterns:
            log("✅ 正确识别为非双长上影线形态")
            return True
        else:
            log("❌ 错误识别为双长上影线形态")
            return False
            
    except Exception as e:
        log(f"❌ 测试失败: {e}")
        return False

def test_chart_generation_availability():
    """测试图表生成功能可用性"""
    log("🔧 测试图表生成功能可用性...")
    
    try:
        # 测试matplotlib导入
        import matplotlib.pyplot as plt
        import matplotlib.dates as mdates
        from matplotlib.patches import Rectangle
        log("✅ matplotlib库导入成功")
        
        # 测试seaborn导入
        import seaborn as sns
        log("✅ seaborn库导入成功")
        
        # 测试基本图表创建
        fig, ax = plt.subplots(1, 1, figsize=(10, 6))
        ax.plot([1, 2, 3], [1, 4, 2])
        ax.set_title("测试图表")
        plt.close()
        log("✅ 基本图表创建成功")
        
        return True
        
    except ImportError as e:
        log(f"❌ 图表库导入失败: {e}")
        log("💡 请安装必要的库: pip install matplotlib seaborn")
        return False
    except Exception as e:
        log(f"❌ 图表功能测试失败: {e}")
        return False

def test_pattern_analysis_integration():
    """测试形态分析集成功能"""
    log("🔧 测试形态分析集成功能...")
    
    try:
        sys.path.append('mcp_browser_scraper')
        from advanced_crypto_scraper import AdvancedCryptoScraper
        
        scraper = AdvancedCryptoScraper(use_proxy=False)
        
        # 模拟加密货币数据
        test_crypto = {
            'id': 'bitcoin',
            'symbol': 'BTC',
            'name': 'Bitcoin',
            'current_price': 50000.0,
            'price_change_24h': 2.5,
            'market_cap': 1000000000000,
            'volume_24h': 50000000000
        }
        
        # 模拟历史数据
        test_historical_data = []
        base_price = 50000
        for i in range(25):  # 25天数据
            price_variation = (i % 5 - 2) * 1000  # 模拟价格波动
            test_historical_data.append({
                'date': f'2025-06-{i+1:02d}',
                'open_price': base_price + price_variation,
                'high_price': base_price + price_variation + 2000,
                'low_price': base_price + price_variation - 1000,
                'close_price': base_price + price_variation + 500,
                'volume': 1000000 + i * 10000
            })
        
        # 测试形态分析
        analysis_result = scraper._analyze_crypto_patterns(test_crypto, test_historical_data)
        
        if analysis_result:
            log("✅ 形态分析功能正常")
            log(f"   综合得分: {analysis_result.get('total_score', 0):.2f}")
            log(f"   识别形态: {analysis_result.get('patterns', [])}")
            return True
        else:
            log("❌ 形态分析返回空结果")
            return False
            
    except Exception as e:
        log(f"❌ 形态分析集成测试失败: {e}")
        import traceback
        log(f"错误详情: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    log("🚀 开始形态分析修改功能测试...")
    log("="*60)
    
    results = []
    
    # 测试1: 双长上影线形态识别
    results.append(test_double_long_upper_shadow_detection())
    log("="*60)
    
    # 测试2: 非双长上影线形态识别
    results.append(test_non_double_long_upper_shadow())
    log("="*60)
    
    # 测试3: 图表生成功能可用性
    results.append(test_chart_generation_availability())
    log("="*60)
    
    # 测试4: 形态分析集成功能
    results.append(test_pattern_analysis_integration())
    log("="*60)
    
    # 统计结果
    passed = sum(results)
    total = len(results)
    
    log(f"📊 测试结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        log("🎉 所有测试通过！")
        log("✅ 双长上影线形态识别功能正常")
        log("✅ 图表生成功能可用")
        log("✅ 形态分析集成功能正常")
        log("🎯 修改功能验证成功")
    else:
        log("⚠️ 部分测试失败，需要检查修改")
        
    log("\n💡 使用说明:")
    log("1. 运行形态分析功能")
    log("2. 在微信推送后会询问是否生成图表")
    log("3. 选择'y'将为每个识别的币种生成K线图")
    log("4. 图表保存在'pattern_charts'目录中")
    log("5. 可以通过图表验证双长上影线形态识别的准确性")

if __name__ == "__main__":
    main()
