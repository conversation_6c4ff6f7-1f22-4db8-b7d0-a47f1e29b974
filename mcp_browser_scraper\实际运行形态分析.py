#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
实际运行形态分析 - 完整的数据下载和分析流程
"""

import os
import sys
import time
from datetime import datetime

def log(message):
    """日志函数"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def run_real_pattern_analysis():
    """运行真实的形态分析"""
    try:
        print("🚀 实际运行形态分析功能")
        print("="*80)
        
        # 获取脚本路径
        script_dir = os.path.dirname(os.path.abspath(__file__))
        script_path = os.path.join(script_dir, "advanced_crypto_scraper tuxing.py")
        
        if not os.path.exists(script_path):
            print(f"❌ 主脚本文件不存在: {script_path}")
            return False
        
        # 导入模块
        print("🔧 加载主脚本模块...")
        import importlib.util
        spec = importlib.util.spec_from_file_location("advanced_crypto_scraper_tuxing", script_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        print("✅ 模块加载成功")
        
        # 询问用户选择代理模式
        print("\n🌐 网络连接模式选择:")
        print("1. 使用SSR代理 (访问更多国外数据源)")
        print("2. 直连模式 (仅访问国内可用数据源)")
        
        choice = input("请选择 (1/2, 默认2): ").strip() or "2"
        
        if choice == "1":
            print("🔗 启用SSR代理模式...")
            scraper = module.AdvancedCryptoScraper(use_proxy=True)
        else:
            print("🔗 启用直连模式...")
            scraper = module.AdvancedCryptoScraper(use_proxy=False)
        
        print("✅ 抓取器初始化完成")
        
        # 步骤1: 获取加密货币数据
        print("\n" + "="*80)
        print("📊 步骤1: 获取加密货币市场数据")
        print("="*80)
        
        print("🔍 正在获取加密货币列表...")
        cryptocurrencies = scraper.get_all_cryptocurrencies(limit=100)
        
        if not cryptocurrencies:
            print("❌ 无法获取加密货币数据，请检查网络连接")
            return False
        
        print(f"✅ 成功获取 {len(cryptocurrencies)} 个加密货币数据")
        print("\n📈 获取到的前10个币种:")
        for i, crypto in enumerate(cryptocurrencies[:10], 1):
            symbol = crypto.get('symbol', 'N/A')
            name = crypto.get('name', 'N/A')
            price = crypto.get('current_price', 0)
            volume = crypto.get('volume_24h', 0)
            source = crypto.get('data_source', 'N/A')
            print(f"   {i:2d}. {symbol:8s} - ${price:12.6f} - 成交量: ${volume:15,.0f} - 来源: {source}")
        
        # 步骤2: 执行形态分析
        print("\n" + "="*80)
        print("🎯 步骤2: 执行双长上影线形态分析")
        print("="*80)
        
        print("🔍 开始分析前50个币种的形态...")
        print("⏳ 这可能需要几分钟时间，请耐心等待...")
        
        # 调用形态分析筛选器
        filtered_results = scraper._filter_pattern_analysis(cryptocurrencies)
        
        # 步骤3: 显示分析结果
        print("\n" + "="*80)
        print("📊 步骤3: 形态分析结果")
        print("="*80)
        
        if filtered_results:
            print(f"🎉 发现 {len(filtered_results)} 个符合双长上影线形态的币种:")
            print("\n📈 分析结果详情:")
            for i, result in enumerate(filtered_results, 1):
                symbol = result.get('symbol', 'N/A')
                name = result.get('name', 'N/A')
                price = result.get('current_price', 0)
                change_24h = result.get('price_change_24h', 0)
                print(f"   {i}. {symbol} ({name})")
                print(f"      💰 当前价格: ${price:.6f}")
                print(f"      📊 24h涨跌: {change_24h:+.2f}%")
                print(f"      🎯 形态: 双长上影线 (上涨乏力，可能回调)")
                print()
        else:
            print("📊 当前市场中未发现符合双长上影线形态的币种")
            print("💡 这可能意味着:")
            print("   • 市场处于相对稳定状态")
            print("   • 大多数币种没有出现明显的反转信号")
            print("   • 可以继续观察市场变化")
        
        # 步骤4: 询问是否生成图表
        print("\n" + "="*80)
        print("🎨 步骤4: 图表验证")
        print("="*80)
        
        if filtered_results:
            generate_charts = input(f"是否为这 {len(filtered_results)} 个币种生成K线图表进行验证? (y/n, 默认n): ").strip().lower()
            
            if generate_charts == 'y':
                print("🎨 开始生成K线图表...")
                
                # 创建图表目录
                chart_dir = "pattern_charts"
                if not os.path.exists(chart_dir):
                    os.makedirs(chart_dir)
                    print(f"✅ 创建图表目录: {chart_dir}")
                
                for i, crypto in enumerate(filtered_results, 1):
                    try:
                        symbol = crypto.get('symbol', 'Unknown')
                        crypto_id = crypto.get('id', symbol.lower())
                        
                        print(f"📊 生成 {symbol} 的K线图表 ({i}/{len(filtered_results)})...")
                        
                        # 获取历史数据
                        historical_data = scraper.get_historical_data(crypto_id, days=30)
                        
                        if historical_data and len(historical_data) >= 20:
                            # 生成图表
                            chart_path = scraper._generate_pattern_chart(crypto, historical_data)
                            if chart_path:
                                print(f"✅ 图表已保存: {chart_path}")
                            else:
                                print(f"⚠️ {symbol} 图表生成失败")
                        else:
                            print(f"⚠️ {symbol} 历史数据不足，跳过图表生成")
                            
                    except Exception as e:
                        print(f"❌ 生成 {symbol} 图表时出错: {e}")
                        continue
                
                print(f"🎉 图表生成完成！请查看 {chart_dir} 目录")
            else:
                print("⏭️ 跳过图表生成")
        
        # 步骤5: 总结
        print("\n" + "="*80)
        print("📋 分析总结")
        print("="*80)
        
        print(f"✅ 数据获取: 成功获取 {len(cryptocurrencies)} 个币种数据")
        print(f"✅ 形态分析: 完成前50个币种的双长上影线分析")
        print(f"✅ 识别结果: 发现 {len(filtered_results)} 个符合条件的币种")
        
        if filtered_results:
            print(f"📱 微信推送: 分析结果已自动推送到企业微信")
            print(f"🎯 交易建议: 关注这些币种的后续走势，可能出现回调")
        
        print("\n💡 使用建议:")
        print("   1. 双长上影线是重要的反转信号")
        print("   2. 建议结合其他技术指标确认")
        print("   3. 注意风险管理，设置止损位")
        print("   4. 可以定期运行分析跟踪市场变化")
        
        return True
        
    except Exception as e:
        print(f"❌ 运行过程出错: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    try:
        print("🚀 启动实际形态分析...")
        print("="*80)
        
        # 检查当前目录
        current_dir = os.getcwd()
        print(f"📍 当前目录: {current_dir}")
        
        # 运行分析
        success = run_real_pattern_analysis()
        
        print("\n" + "="*80)
        if success:
            print("🎉 形态分析完成！")
            print("\n🔄 如需再次分析，可以:")
            print("   1. 重新运行此脚本")
            print("   2. 使用主脚本选择菜单选项15")
            print("   3. 运行 '直接启动形态分析.py'")
        else:
            print("⚠️ 分析过程中遇到问题")
            print("💡 请检查:")
            print("   1. 网络连接是否正常")
            print("   2. 数据源是否可访问")
            print("   3. Python环境是否完整")
        
    except KeyboardInterrupt:
        print("\n👋 用户中断分析")
    except Exception as e:
        print(f"❌ 程序失败: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
