#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
运行修改后的形态分析功能
"""

import sys
import os
from datetime import datetime

def log(message):
    """日志函数"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def main():
    """主函数"""
    log("🚀 启动修改后的形态分析功能...")
    
    try:
        # 检查文件是否存在
        script_path = "mcp_browser_scraper/advanced_crypto_scraper tuxing.py"
        if not os.path.exists(script_path):
            log(f"❌ 文件不存在: {script_path}")
            return
        
        log(f"✅ 找到脚本文件: {script_path}")
        
        # 导入并运行
        import importlib.util
        
        # 加载模块
        spec = importlib.util.spec_from_file_location("advanced_crypto_scraper_tuxing", script_path)
        if spec is None or spec.loader is None:
            log("❌ 无法加载模块")
            return
            
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        log("✅ 模块加载成功")
        
        # 创建实例
        scraper = module.AdvancedCryptoScraper(use_proxy=False)
        log("✅ 抓取器实例创建成功")
        
        # 显示菜单
        log("\n" + "="*60)
        log("🎯 修改后的形态分析功能已就绪")
        log("="*60)
        log("主要修改内容:")
        log("1. ✅ 重构了形态识别逻辑")
        log("   - 专门识别双长上影线形态")
        log("   - 详细的K线数据分析")
        log("   - 精确的形态判断条件")
        log("")
        log("2. ✅ 增加了图形验证功能")
        log("   - 形态分析后询问是否生成图表")
        log("   - K线图表显示技术指标")
        log("   - 形态标注和验证")
        log("")
        log("3. ✅ 保持了原有功能")
        log("   - 技术指标计算")
        log("   - 微信推送功能")
        log("   - 错误处理机制")
        log("="*60)
        
        # 启动主程序
        log("🚀 启动主程序...")
        scraper.show_selection_menu()
        
    except KeyboardInterrupt:
        log("\n👋 用户中断程序")
    except Exception as e:
        log(f"❌ 程序运行出错: {e}")
        import traceback
        log(f"错误详情: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
