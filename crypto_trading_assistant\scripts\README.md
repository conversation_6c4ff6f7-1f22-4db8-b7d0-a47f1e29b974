# 加密货币新币分析工具

这是一个用于分析最近上市加密货币的工具，它提供了基本市场数据和技术指标分析。

## 功能特点

- 自动获取最近上市的新币信息
- 分析币种的市场数据和价格趋势
- 计算技术指标（RSI、MACD等）
- 生成美观的HTML分析报告
- 支持导出CSV和JSON格式数据

## 安装依赖

在使用前，请确保安装了所需的依赖：

```bash
pip install -r requirements.txt
```

主要依赖包括：
- pandas
- numpy
- matplotlib
- pycoingecko
- loguru
- ta (Technical Analysis库)
- requests

## 使用方法

### 方式一：使用自动脚本（推荐）

Windows用户可以直接双击运行`run_crypto_analysis.bat`脚本，或在命令行中使用自定义参数：

```
run_crypto_analysis.bat [天数] [分析币种数量] [输出文件夹]
```

例如：
```
run_crypto_analysis.bat 20 5 reports
```
将分析最近20天内上市的前5个新币，并将结果保存到reports文件夹。

### 方式二：分步运行

1. 首先收集基本信息：
```
python crypto_collector_step1.py -d 30
```
这将收集最近30天上市的币种基本信息，并保存到`new_coins.json`中。

2. 然后进行详细分析：
```
python crypto_collector_step2.py -m 10 -o crypto_reports
```
这将分析前10个币种，并将报告保存到`crypto_reports`文件夹。

### 命令行参数

#### 第一步（crypto_collector_step1.py）
- `-d, --days`：分析过去多少天内上市的币种，默认为60天
- `-c, --currency`：计价货币，默认为usd
- `-p, --proxy`：是否使用代理
- `-o, --output`：CSV输出文件路径，默认为new_coins.csv
- `-j, --json`：JSON输出文件路径，默认为new_coins.json

#### 第二步（crypto_collector_step2.py）
- `-i, --input`：输入文件路径，默认为new_coins.json
- `-o, --output`：输出文件夹路径，默认为crypto_reports
- `-c, --currency`：计价货币，默认为usd
- `-l, --lang`：语言，默认为zh
- `-p, --proxy`：是否使用代理
- `-m, --max`：最多分析多少个币种，默认为20

## 工作原理

本工具分两步工作：

1. **第一步**：收集最近上市币种的基本信息
   - 从CoinGecko API获取币种列表
   - 通过ATH日期识别新上市的币种
   - 将结果保存为CSV和JSON格式

2. **第二步**：详细分析并生成报告
   - 从第一步生成的文件中读取币种信息
   - 获取详细市场数据
   - 计算技术指标
   - 生成HTML报告和JSON/CSV数据文件

## 优势

- **分步处理**：将数据收集和分析分离，避免因长时间运行导致API限制
- **灵活配置**：可自定义分析天数、币种数量等参数
- **美观报告**：生成美观易读的HTML报告
- **多格式导出**：支持CSV和JSON格式导出，便于进一步分析

## 注意事项

- CoinGecko API有请求频率限制，如果遇到错误可以稍后再试
- 如果需要使用代理，请确保代理服务器正常运行
- 分析结果仅供参考，不构成投资建议
