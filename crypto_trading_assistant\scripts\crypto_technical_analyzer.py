#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
加密货币技术分析模块 - 备份于 2024-12-19 23:00

负责计算技术指标和生成交易信号，包括：
- 趋势指标（MA、EMA、MACD等）
- 动量指标（RSI、KDJ等）
- 波动率指标（布林带、ATR等）
- 交易信号生成
- 邮件发送分析报告
"""

import pandas as pd
import numpy as np
from numpy import nan as NaN  # 修复 NaN 导入问题
from typing import Dict, List, Optional, Union
import logging
import pandas_ta as pta
from ta.trend import SMAIndicator, EMAIndicator, MACD
from ta.momentum import RSIIndicator, StochasticOscillator
from ta.volatility import BollingerBands
from ta.volume import VolumeWeightedAveragePrice
import smtplib
from email.mime.text import MIMEText
from datetime import datetime

class TechnicalAnalyzer:
    """
    技术分析器
    """
    def __init__(self, smtp_server: str = 'smtp.your.email.com', smtp_port: int = 587, smtp_user: str = '<EMAIL>', smtp_password: str = 'your_password'):
        """
        初始化技术分析器

        Args:
            smtp_server: SMTP服务器地址
            smtp_port: SMTP服务器端口
            smtp_user: SMTP登录用户名
            smtp_password: SMTP登录密码
        """
        # 配置日志 # Roo: 库不应该调用basicConfig，basicConfig应由主应用程序配置。
        # logging.basicConfig(
        #     level=logging.INFO,
        #     format='%(asctime)s - %(levelname)s - %(message)s',
        #     handlers=[
        #         logging.FileHandler('technical_analyzer.log'),
        #         logging.StreamHandler()
        #     ]
        # ) # Roo: 已移除
        self.logger = logging.getLogger(__name__) # 获取一个logger实例，它将继承根logger的配置
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.smtp_user = smtp_user
        self.smtp_password = smtp_password

    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算技术指标

        Args:
            df: 包含OHLCV数据的DataFrame

        Returns:
            添加了技术指标的DataFrame
        """
        try:
            # 确保数据格式正确且数据量足够
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            if not all(col in df.columns for col in required_columns):
                self.logger.error("DataFrame必须包含OHLCV数据: %s", required_columns)
                return pd.DataFrame() # 返回空DataFrame或原始DataFrame，取决于期望行为
            
            # 检查数据量，至少需要计算最长周期的指标所需的数据量
            # 例如，SMA(200) 需要至少200个数据点
            min_data_points = 200 # 根据最长周期指标确定
            if len(df) < min_data_points:
                 self.logger.warning("数据量不足 (%d 行)，无法计算所有指标 (至少需要 %d 行)。", len(df), min_data_points)
                 # 尝试计算部分指标，但可能返回大量NaN
                 # 或者直接返回原始df，取决于期望行为
                 # 为了演示，我们继续计算，但用户应注意结果可能不完整
                 pass # 继续执行，但会有NaN

            # 计算趋势指标
            df['sma_10'] = SMAIndicator(close=df['close'], window=10).sma_indicator()
            df['sma_20'] = SMAIndicator(close=df['close'], window=20).sma_indicator()
            df['sma_50'] = SMAIndicator(close=df['close'], window=50).sma_indicator()
            df['sma_200'] = SMAIndicator(close=df['close'], window=200).sma_indicator()

            df['ema_12'] = EMAIndicator(close=df['close'], window=12).ema_indicator()
            df['ema_26'] = EMAIndicator(close=df['close'], window=26).ema_indicator()
            df['ema_50'] = EMAIndicator(close=df['close'], window=50).ema_indicator()

            macd = MACD(close=df['close'])
            df['macd'] = macd.macd()
            df['macd_signal'] = macd.macd_signal()
            df['macd_hist'] = macd.macd_diff()

            # 计算动量指标
            df['rsi_14'] = RSIIndicator(close=df['close']).rsi()

            stoch = pta.stoch(high=df['high'], low=df['low'], close=df['close'], k=9, d=3, smooth_k=3)
            if stoch is not None and not stoch.empty:
                df['k'] = stoch[f'STOCHk_9_3_3']
                df['d'] = stoch[f'STOCHd_9_3_3']
                df['j'] = 3 * df['k'] - 2 * df['d']
            else:
                df['k'] = np.nan
                df['d'] = np.nan
                df['j'] = np.nan

            # 计算波动率指标
            bollinger = BollingerBands(close=df['close'])
            df['bollinger_upper'] = bollinger.bollinger_hband()
            df['bollinger_middle'] = bollinger.bollinger_mavg()
            df['bollinger_lower'] = bollinger.bollinger_lband()

            df['atr'] = self.calculate_atr(df) # 修正：传入df

            # 计算成交量指标
            vwap = VolumeWeightedAveragePrice(
                high=df['high'],
                low=df['low'],
                close=df['close'],
                volume=df['volume']
            )
            df['vwap'] = vwap.volume_weighted_average_price()

            df['volume_change'] = df['volume'].pct_change()

            # 添加新的技术指标
            adx_df = pta.adx(high=df['high'], low=df['low'], close=df['close'], length=14)
            if adx_df is not None and not adx_df.empty:
                df['adx'] = adx_df[f'ADX_14']
                df['plus_di'] = adx_df[f'DMP_14'] # DI+
                df['minus_di'] = adx_df[f'DMN_14'] # DI-
            else:
                df['adx'] = np.nan
                df['plus_di'] = np.nan
                df['minus_di'] = np.nan

            df['cci'] = pta.cci(high=df['high'], low=df['low'], close=df['close'], length=14)
            # OBV (使用 pandas_ta)
            df['obv'] = pta.obv(close=df['close'], volume=df['volume'])

            # 计算趋势强度
            df['trend_strength'] = abs(df['ema_12'] - df['ema_26']) / df['ema_26'] * 100

            # 添加市场情绪指标 (代理) - 简化计算，仅作为示例
            df['fear_greed'] = (df['rsi_14'] / 100 + (df['macd_hist'].apply(lambda x: 1 if x > 0 else (0 if x == 0 else -1)) + 1) / 2) / 2 # 简化示例

            # 计算历史波动率 (20日)
            df['historical_volatility'] = df['close'].pct_change().rolling(window=20).std() * np.sqrt(252)

            # 计算流动性指标 (代理) - 简化计算，仅作为示例
            df['liquidity_ratio'] = df['volume'] / df['volume'].rolling(window=20).mean() # 简化示例

            # 计算支撑阻力位 (Pivot Points)
            # 计算支撑阻力位 (Pivot Points)
            df.loc[:, 'pivot'] = (df['high'] + df['low'] + df['close']) / 3
            df.loc[:, 'r1'] = 2 * df['pivot'] - df['low']
            df.loc[:, 's1'] = 2 * df['pivot'] - df['high']
            df.loc[:, 'r2'] = df['pivot'] + (df['high'] - df['low'])
            df.loc[:, 's2'] = df['pivot'] - (df['high'] - df['low'])

            # 计算心理价位
            df['psychological_levels'] = df['close'].round(0)
            df['psychological_levels_50'] = (df['close'] / 50).round() * 50
            df['psychological_levels_100'] = (df['close'] / 100).round() * 100

            # 计算历史成交量分布
            df['volume_profile'] = df['volume'].rolling(window=20).mean()
            df['volume_std'] = df['volume'].rolling(window=20).std()

            # 计算价格波动率
            df['price_volatility'] = df['close'].pct_change().rolling(window=20).std()

            # 计算支撑阻力强度 (代理) - 简化计算，仅作为示例
            df['support_strength'] = df['volume'].rolling(window=20).mean() / df['price_volatility'].rolling(window=20).mean() # 简化示例
            df['resistance_strength'] = df['volume'].rolling(window=20).mean() / df['price_volatility'].rolling(window=20).mean() # 简化示例

            # 计算市场深度指标 (代理) - 简化计算，仅作为示例
            df['order_book_depth'] = df['volume'].rolling(window=20).mean() * df['close'].rolling(window=20).mean() # 简化示例

            # 大单交易分析 (代理) - 简化计算，仅作为示例
            df['large_trade_volume'] = df['volume'].rolling(window=20).apply(
                lambda x: x[x > x.mean() + 1.5 * x.std()].sum(), raw=True # 降低阈值以便测试
            )
            df['large_trade_ratio'] = df['large_trade_volume'] / df['volume'].rolling(window=20).sum() # 修正分母

            # 市场冲击成本 (代理) - 简化计算，仅作为示例
            df['market_impact'] = df['price_volatility'].rolling(window=20).mean() / df['order_book_depth'].rolling(window=20).mean() # 简化示例

            # 流动性评分 (代理) - 简化计算，仅作为示例
            df['liquidity_score'] = df['order_book_depth'].rolling(window=20).mean() / df['market_impact'].rolling(window=20).mean() # 简化示例

            # 计算时间周期指标
            df['daily_trend'] = df['close'].pct_change(periods=1)
            df['weekly_trend'] = df['close'].pct_change(periods=5)
            df['monthly_trend'] = df['close'].pct_change(periods=20)

            # 添加社交媒体情绪指标 (代理) - 简化计算，仅作为示例
            df['social_sentiment'] = df['volume'].rolling(window=20).mean() * (1 + df['rsi_14'].rolling(window=20).mean() / 100) # 简化示例

            # 添加新闻情绪指标 (代理) - 简化计算，仅作为示例
            df['news_sentiment'] = df['volume_change'].rolling(window=20).mean() * (1 + df['cci'].rolling(window=20).mean() / 100) # 简化示例

            # 添加市场资金流向指标 (代理) - 简化计算，仅作为示例
            df['money_flow'] = df['volume'].rolling(window=20).mean() * (df['close'].iloc[-1] - df['open'].iloc[-1]) # 简化示例

            # 计算综合情绪指标 (代理) - 简化计算，仅作为示例
            df['market_sentiment'] = (
                df['social_sentiment'].rolling(window=5).mean() * 0.3 +
                df['news_sentiment'].rolling(window=5).mean() * 0.3 +
                df['money_flow'].rolling(window=5).mean() * 0.4
            )


            return df

        except Exception as e:
            self.logger.error(f"计算技术指标失败: {str(e)}")
            return df

    def calculate_atr(self, df: pd.DataFrame, period=14) -> pd.Series:
        """计算ATR指标"""
        if df is None or len(df) < period:
            return pd.Series([np.nan] * len(df), index=df.index)

        high = df['high'].astype(float)
        low = df['low'].astype(float)
        close = df['close'].astype(float)

        # 计算TR
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

        # 计算ATR
        atr = tr.rolling(window=period).mean()
        return atr

    def _determine_overall_trend(self, df: pd.DataFrame) -> str:
        """确定整体趋势"""
        if len(df) < 50: # 需要至少50个周期来判断中长期趋势
            return "未知"
        latest = df.iloc[-1]
        # 使用EMA判断趋势，更平滑
        if latest['ema_12'] > latest['ema_26'] > latest['ema_50']:
            return "强势上涨"
        elif latest['ema_12'] < latest['ema_26'] < latest['ema_50']:
            return "强势下跌"
        elif latest['ema_26'] > latest['ema_50']:
            return "上涨"
        elif latest['ema_26'] < latest['ema_50']:
            return "下跌"
        else:
            return "盘整"

    def _identify_candlestick_pattern(self, df: pd.DataFrame) -> str:
        """识别K线形态 (基于最新两根K线)"""
        if len(df) < 2:
            return "未知"
        latest = df.iloc[-1]
        prev = df.iloc[-2]

        latest_body = abs(latest['close'] - latest['open'])
        latest_upper_shadow = latest['high'] - max(latest['open'], latest['close'])
        latest_lower_shadow = min(latest['open'], latest['close']) - latest['low']

        prev_body = abs(prev['close'] - prev['open'])

        # 判断形态
        if latest_body < 0.1 * (latest['high'] - latest['low']):
            return "十字星"
        elif latest_upper_shadow > 2 * latest_body and latest_lower_shadow < 0.5 * latest_body:
            return "长上影线"
        elif latest_lower_shadow > 2 * latest_body and latest_upper_shadow < 0.5 * latest_body:
            return "锤子线"
        elif latest['close'] > latest['open'] and prev['close'] < prev['open'] and \
             latest['close'] > prev['open'] and latest['open'] < prev['close']:
            return "看涨吞没"
        elif latest['close'] < latest['open'] and prev['close'] > prev['open'] and \
             latest['close'] < prev['open'] and latest['open'] > prev['close']:
            return "看跌吞没"
        elif latest['close'] > latest['open']:
            return "看涨实体"
        else:
            return "看跌实体"

    def _evaluate_risk(self, df: pd.DataFrame) -> Dict:
        """评估风险"""
        if len(df) < 20: # 需要至少20个周期计算波动率
             return {
                "risk_level": "未知",
                "volatility_risk": "未知",
                "liquidity_risk": "未知",
                "trend_risk": "未知"
            }

        volatility = df['close'].pct_change().iloc[-20:].std() * 100 # 使用最近20周期波动率
        volume_trend = df['volume'].pct_change().iloc[-20:].mean() # 使用最近20周期成交量变化趋势
        latest = df.iloc[-1]
        first = df.iloc[0]

        # 评估波动风险
        if volatility > 5:
            volatility_risk = "高"
        elif volatility > 2:
            volatility_risk = "中"
        else:
            volatility_risk = "低"

        # 评估流动性风险
        if volume_trend < -0.5:
            liquidity_risk = "高"
        elif volume_trend < -0.2:
            liquidity_risk = "中"
        else:
            liquidity_risk = "低"

        # 评估趋势风险
        # 使用EMA判断趋势稳定性
        trend_stability = abs(latest['ema_12'] - latest['ema_26']) / latest['ema_26']
        trend_risk = "高" if trend_stability < 0.01 else "中" if trend_stability < 0.05 else "低" # 趋势越不稳定风险越高

        # 综合风险等级
        risk_level = "高" if volatility_risk == "高" or liquidity_risk == "高" or trend_risk == "高" else \
                     "中" if volatility_risk == "中" or liquidity_risk == "中" or trend_risk == "中" else "低"

        return {
            "risk_level": risk_level,
            "volatility_risk": volatility_risk,
            "liquidity_risk": liquidity_risk,
            "trend_risk": trend_risk
        }


    def analyze_trend(self, df: pd.DataFrame) -> Dict:
        """
        分析趋势并返回结构化结果

        Args:
            df: 包含技术指标的DataFrame

        Returns:
            趋势分析结果字典
        """
        overall_trend_str = self._determine_overall_trend(df)
        trend_analysis = {"trend": "未知", "strength": "未知"}

        if overall_trend_str == "强势上涨":
            trend_analysis["trend"] = "上涨"
            trend_analysis["strength"] = "强"
        elif overall_trend_str == "上涨":
            trend_analysis["trend"] = "上涨"
            trend_analysis["strength"] = "中"
        elif overall_trend_str == "强势下跌":
            trend_analysis["trend"] = "下跌"
            trend_analysis["strength"] = "强"
        elif overall_trend_str == "下跌":
            trend_analysis["trend"] = "下跌"
            trend_analysis["strength"] = "中"
        elif overall_trend_str == "盘整":
            trend_analysis["trend"] = "盘整"
            trend_analysis["strength"] = "中"

        # 添加 RSI 和 MACD 到趋势分析报告部分
        latest = df.iloc[-1] if len(df) > 0 else {}
        trend_analysis['rsi'] = latest.get('rsi_14', np.nan)
        trend_analysis['macd'] = latest.get('macd', np.nan)
        trend_analysis['macd_signal'] = latest.get('macd_signal', np.nan)
        trend_analysis['macd_hist'] = latest.get('macd_hist', np.nan)

        return trend_analysis

    def generate_signals(self, df: pd.DataFrame) -> Dict:
        """
        生成交易信号 (优化版)

        Args:
            df: 包含技术指标的DataFrame

        Returns:
            交易信号
        """
        try:
            # 初始数据有效性检查 (例如，至少需要多少行数据来获取 latest 和 prev)
            if len(df) < 2: # 需要至少两行数据来比较 prev 和 latest
                self.logger.warning("数据不足 (少于2行)，无法生成有效信号。")
                return {
                    'action': 'hold', 'strength': 'neutral', 'reasons': ['数据不足 (少于2行)'],
                    'confidence': 0.0, 'stop_loss': None, 'take_profit': None, 'risk_level': 'unknown'
                }

            latest = df.iloc[-1]
            prev = df.iloc[-2]
            current_price = latest['close']
            atr = latest['atr']

            signal = {
                'action': 'hold',
                'strength': 'neutral',
                'reasons': [],
                'confidence': 0.0,
                'stop_loss': None,
                'take_profit': None,
                'risk_level': 'medium'
            }

            # 获取趋势分析结果
            overall_trend = self._determine_overall_trend(df)

            # 信号生成逻辑 (基于优化建议)

            # 1. 趋势跟随信号 (EMA) - 权重 0.3
            if overall_trend == '强势上涨':
                signal['reasons'].append('强势上升趋势')
                signal['action'] = 'buy'
                signal['confidence'] += 0.3
            elif overall_trend == '强势下跌':
                signal['reasons'].append('强势下降趋势')
                signal['action'] = 'sell'
                signal['confidence'] += 0.3

            # 2. MACD 信号 - 权重 0.2 (结合趋势)
            if pd.notna(latest['macd']) and pd.notna(latest['macd_signal']) and \
               pd.notna(prev['macd']) and pd.notna(prev['macd_signal']):
                if latest['macd'] > latest['macd_signal'] and prev['macd'] <= prev['macd_signal']:
                    if overall_trend in ['强势上涨', '上涨', '盘整'] or overall_trend == "未知":
                        signal['reasons'].append('MACD金叉')
                        signal['action'] = 'buy'
                        signal['confidence'] += 0.2 if overall_trend != "未知" else 0.1 # 趋势未知时降低权重
                    else: # 强势下跌或下跌 (逆势)
                         signal['reasons'].append('MACD金叉 (逆势)')
                         signal['confidence'] += 0.1
                elif latest['macd'] < latest['macd_signal'] and prev['macd'] >= prev['macd_signal']:
                     if overall_trend in ['强势下跌', '下跌', '盘整'] or overall_trend == "未知":
                        signal['reasons'].append('MACD死叉')
                        signal['action'] = 'sell'
                        signal['confidence'] += 0.2 if overall_trend != "未知" else 0.1 # 趋势未知时降低权重
                     else: # 强势上涨或上涨 (逆势)
                        signal['reasons'].append('MACD死叉 (逆势)')
                        signal['confidence'] += 0.1
            else:
                signal['reasons'].append('MACD指标数据不足')

            # 3. RSI 信号 - 权重 0.15 (结合趋势)
            if pd.notna(latest['rsi_14']):
                if latest['rsi_14'] < 30:
                    if overall_trend in ['强势上涨', '上涨', '盘整'] or overall_trend == "未知":
                        signal['reasons'].append('RSI超卖')
                        signal['action'] = 'buy'
                        signal['confidence'] += 0.15 if overall_trend != "未知" else 0.07
                    else: # 逆势
                        signal['reasons'].append('RSI超卖 (逆势)')
                        # 逆势超卖通常不直接作为买入信号，或权重极低
                elif latest['rsi_14'] > 70: # 添加RSI超买逻辑
                    if overall_trend in ['强势下跌', '下跌', '盘整'] or overall_trend == "未知":
                        signal['reasons'].append('RSI超买')
                        signal['action'] = 'sell'
                        signal['confidence'] += 0.15 if overall_trend != "未知" else 0.07
                    else: # 逆势
                        signal['reasons'].append('RSI超买 (逆势)')
            else:
                signal['reasons'].append('RSI指标数据不足')

            # 4. 布林带信号 - 权重 0.15 (结合趋势)
            if pd.notna(latest['bollinger_lower']) and pd.notna(latest['bollinger_upper']) and pd.notna(latest['close']):
                if latest['close'] < latest['bollinger_lower']:
                     if overall_trend in ['强势上涨', '上涨', '盘整'] or overall_trend == "未知":
                        signal['reasons'].append('价格触及布林带下轨')
                        signal['action'] = 'buy'
                        signal['confidence'] += 0.15 if overall_trend != "未知" else 0.07
                     else: # 逆势
                        signal['reasons'].append('价格触及布林带下轨 (逆势)')
                        signal['confidence'] += 0.05
                elif latest['close'] > latest['bollinger_upper']:
                     if overall_trend in ['强势下跌', '下跌', '盘整'] or overall_trend == "未知":
                        signal['reasons'].append('价格触及布林带上轨')
                        signal['action'] = 'sell'
                        signal['confidence'] += 0.15 if overall_trend != "未知" else 0.07
                     else: # 逆势
                        signal['reasons'].append('价格触及布林带上轨 (逆势)')
                        signal['confidence'] += 0.05
            else:
                signal['reasons'].append('布林带指标数据不足')

            # 5. KDJ 信号 - 权重 0.1 (结合趋势)
            if pd.notna(latest['k']) and pd.notna(latest['d']) and pd.notna(latest['j']):
                if latest['k'] < 20 and latest['d'] < 20 and latest['j'] < 20: # KDJ超卖区
                     if overall_trend in ['强势上涨', '上涨', '盘整'] or overall_trend == "未知":
                        signal['reasons'].append('KDJ超卖')
                        signal['action'] = 'buy'
                        signal['confidence'] += 0.1 if overall_trend != "未知" else 0.05
                     else: # 逆势
                        signal['reasons'].append('KDJ超卖 (逆势)')
                        signal['confidence'] += 0.03
                elif latest['k'] > 80 and latest['d'] > 80 and latest['j'] > 80: # KDJ超买区
                     if overall_trend in ['强势下跌', '下跌', '盘整'] or overall_trend == "未知":
                        signal['reasons'].append('KDJ超买')
                        signal['action'] = 'sell'
                        signal['confidence'] += 0.1 if overall_trend != "未知" else 0.05
                     else: # 逆势
                        signal['reasons'].append('KDJ超买 (逆势)')
                        signal['confidence'] += 0.03
            else:
                signal['reasons'].append('KDJ指标数据不足')

            # 6. DMI 信号 - 权重 0.1 (确认趋势)
            if pd.notna(latest['plus_di']) and pd.notna(latest['minus_di']) and pd.notna(latest['adx']):
                if latest['plus_di'] > latest['minus_di'] and latest['adx'] > 25:
                    signal['reasons'].append('DMI显示上升趋势 (ADX > 25)')
                    if signal['action'] == 'buy': # 确认买入信号
                        signal['confidence'] += 0.1
                    elif signal['action'] == 'hold': # 如果之前是观望，DMI可作为启动信号
                        signal['action'] = 'buy'
                        signal['confidence'] += 0.1
                elif latest['plus_di'] < latest['minus_di'] and latest['adx'] > 25:
                    signal['reasons'].append('DMI显示下降趋势 (ADX > 25)')
                    if signal['action'] == 'sell': # 确认卖出信号
                        signal['confidence'] += 0.1
                    elif signal['action'] == 'hold': # 如果之前是观望，DMI可作为启动信号
                        signal['action'] = 'sell'
                        signal['confidence'] += 0.1
                elif latest['adx'] < 20 : # ADX小于20，趋势不明朗
                    signal['reasons'].append('DMI显示趋势不明朗 (ADX < 20)')
                    signal['confidence'] -= 0.05 # 降低整体置信度
            else:
                signal['reasons'].append('DMI指标数据不足')

            # 7. 成交量确认 - 权重 0.05
            if pd.notna(latest['volume_change']):
                if latest['volume_change'] > 0.5:  # 成交量增加50%
                    if signal['action'] == 'buy':
                        signal['reasons'].append('放量上涨')
                        signal['confidence'] += 0.05
                    elif signal['action'] == 'sell':
                        signal['reasons'].append('放量下跌')
                        signal['confidence'] += 0.05
            else:
                signal['reasons'].append('成交量变化数据不足')

            # 8. 支撑阻力位分析 - 权重 0.1 (调整止损止盈)
            if pd.notna(latest['close']) and pd.notna(latest['s1']) and pd.notna(latest['s2']) and \
               pd.notna(latest['r1']) and pd.notna(latest['r2']):
                # 检查是否接近支撑位
                if latest['close'] < latest['s1'] * 1.01:
                    signal['reasons'].append('价格接近第一支撑位')
                    if signal['action'] == 'buy':
                        current_sl = signal.get('stop_loss') # 使用get避免KeyError
                        new_sl = latest['s1'] * 0.99
                        signal['stop_loss'] = min(current_sl, new_sl) if current_sl is not None else new_sl
                elif latest['close'] < latest['s2'] * 1.01:
                    signal['reasons'].append('价格接近第二支撑位')
                    if signal['action'] == 'buy':
                        current_sl = signal.get('stop_loss')
                        new_sl = latest['s2'] * 0.99
                        signal['stop_loss'] = min(current_sl, new_sl) if current_sl is not None else new_sl

                # 检查是否接近阻力位
                if latest['close'] > latest['r1'] * 0.99:
                    signal['reasons'].append('价格接近第一阻力位')
                    if signal['action'] == 'sell':
                        current_sl = signal.get('stop_loss')
                        new_sl = latest['r1'] * 1.01
                        signal['stop_loss'] = max(current_sl, new_sl) if current_sl is not None else new_sl
                elif latest['close'] > latest['r2'] * 0.99:
                    signal['reasons'].append('价格接近第二阻力位')
                    if signal['action'] == 'sell':
                        current_sl = signal.get('stop_loss')
                        new_sl = latest['r2'] * 1.01
                        signal['stop_loss'] = max(current_sl, new_sl) if current_sl is not None else new_sl
            else:
                signal['reasons'].append('支撑阻力位数据不足')

            # 9. 心理价位 - 权重 0.05 (辅助判断)
            if pd.notna(latest['close']) and pd.notna(latest['psychological_levels']) and \
               pd.notna(latest['psychological_levels_50']) and pd.notna(latest['psychological_levels_100']):
                if abs(latest['close'] - latest['psychological_levels']) / latest['close'] < 0.005:
                    signal['reasons'].append('价格接近整数心理价位')
                    signal['confidence'] += 0.05
                elif abs(latest['close'] - latest['psychological_levels_50']) / latest['close'] < 0.005:
                    signal['reasons'].append('价格接近50倍数心理价位')
                    signal['confidence'] += 0.05
                elif abs(latest['close'] - latest['psychological_levels_100']) / latest['close'] < 0.005:
                    signal['reasons'].append('价格接近100倍数心理价位')
                    signal['confidence'] += 0.05
            else:
                signal['reasons'].append('心理价位数据不足')

            # 10. 市场情绪和流动性 (代理) - 权重 0.1 (辅助判断)
            risk_analysis = self._evaluate_risk(df) # _evaluate_risk 内部有长度检查
            if risk_analysis['risk_level'] != "未知": # 确保风险评估有效
                if risk_analysis['liquidity_risk'] == '高': # 注意：原文是'低'，逻辑上应该是流动性高风险时降低置信度
                     signal['reasons'].append('市场流动性风险较高')
                     signal['confidence'] -= 0.1 # 流动性风险高则降低置信度
            else:
                signal['reasons'].append('风险评估数据不足')

            if pd.notna(latest['market_sentiment']) and pd.notna(df['market_sentiment'].mean()) and pd.notna(df['market_sentiment'].std()):
                if latest['market_sentiment'] > df['market_sentiment'].mean() + df['market_sentiment'].std():
                     signal['reasons'].append('市场情绪偏乐观')
                     if signal['action'] == 'buy': signal['confidence'] += 0.05
                elif latest['market_sentiment'] < df['market_sentiment'].mean() - df['market_sentiment'].std():
                     signal['reasons'].append('市场情绪偏悲观')
                     if signal['action'] == 'sell': signal['confidence'] += 0.05
            else:
                signal['reasons'].append('市场情绪指标数据不足')


            # 根据置信度确定信号强度
            if signal['confidence'] >= 0.8: # 提高强信号阈值
                signal['strength'] = 'strong'
            elif signal['confidence'] >= 0.5: # 提高中等信号阈值
                signal['strength'] = 'moderate'
            else:
                signal['strength'] = 'weak'

            # 如果置信度太低，保持观望
            if signal['confidence'] < 0.4: # 提高观望阈值
                signal['action'] = 'hold'
                signal['reasons'].append('信号强度不足，建议观望')

            # 评估风险等级
            signal['risk_level'] = risk_analysis['risk_level']

            # 根据风险等级调整止损止盈 (更精细化)
            if signal['action'] != 'hold' and atr is not None and not np.isnan(atr):
                if signal['risk_level'] == 'high':
                    # 收紧止损，扩大止盈
                    if signal['action'] == 'buy':
                        signal['stop_loss'] = max(signal['stop_loss'] if signal['stop_loss'] is not None else current_price - 1.5 * atr, current_price - 1.5 * atr)
                        signal['take_profit'] = min(signal['take_profit'] if signal['take_profit'] is not None else current_price + 4 * atr, current_price + 4 * atr)
                    else: # sell
                        signal['stop_loss'] = min(signal['stop_loss'] if signal['stop_loss'] is not None else current_price + 1.5 * atr, current_price + 1.5 * atr)
                        signal['take_profit'] = max(signal['take_profit'] if signal['take_profit'] is not None else current_price - 4 * atr, current_price - 4 * atr)
                elif signal['risk_level'] == 'low':
                    # 放宽止损，收紧止盈
                    if signal['action'] == 'buy':
                        signal['stop_loss'] = max(signal['stop_loss'] if signal['stop_loss'] is not None else current_price - 2.5 * atr, current_price - 2.5 * atr)
                        signal['take_profit'] = min(signal['take_profit'] if signal['take_profit'] is not None else current_price + 2 * atr, current_price + 2 * atr)
                    else: # sell
                        signal['stop_loss'] = min(signal['stop_loss'] if signal['stop_loss'] is not None else current_price + 2.5 * atr, current_price + 2.5 * atr)
                        signal['take_profit'] = max(signal['take_profit'] if signal['take_profit'] is not None else current_price - 2 * atr, current_price - 2 * atr)
                else: # medium risk
                     if signal['action'] == 'buy':
                        signal['stop_loss'] = max(signal['stop_loss'] if signal['stop_loss'] is not None else current_price - 2 * atr, current_price - 2 * atr)
                        signal['take_profit'] = min(signal['take_profit'] if signal['take_profit'] is not None else current_price + 3 * atr, current_price + 3 * atr)
                     else: # sell
                        signal['stop_loss'] = min(signal['stop_loss'] if signal['stop_loss'] is not None else current_price + 2 * atr, current_price + 2 * atr)
                        signal['take_profit'] = max(signal['take_profit'] if signal['take_profit'] is not None else current_price - 3 * atr, current_price - 3 * atr)


            return signal

        except Exception as e:
            self.logger.error(f"生成交易信号失败: {str(e)}")
            return {
                'action': 'hold',
                'strength': 'neutral',
                'reasons': [f'生成信号异常: {str(e)}'],
                'confidence': 0.0,
                'stop_loss': None,
                'take_profit': None,
                'risk_level': 'medium'
            }

    def send_analysis_report_email(self, report_content: str, recipients: List[str], subject: str = "加密货币技术分析报告"):
        """
        发送技术分析报告邮件

        Args:
            report_content: 报告内容
            recipients: 收件人邮箱列表
            subject: 邮件主题
        """
        try:
            msg = MIMEText(report_content, 'plain', 'utf-8')
            msg['Subject'] = subject
            msg['From'] = self.smtp_user
            msg['To'] = ", ".join(recipients)

            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls() # 启动TLS加密
                server.login(self.smtp_user, self.smtp_password)
                server.sendmail(self.smtp_user, recipients, msg.as_string())

            self.logger.info(f"分析报告已成功发送至: {', '.join(recipients)}")

        except Exception as e:
            self.logger.error(f"发送邮件失败: {str(e)}")


    # 保留形态识别方法，但不再在主流程中使用，可作为辅助分析
    def find_patterns(self, df: pd.DataFrame) -> List[str]:
        """
        识别价格形态 (辅助方法)

        Args:
            df: 包含OHLCV数据的DataFrame

        Returns:
            识别到的形态列表
        """
        try:
            patterns = []

            # 获取最近的数据
            recent_data = df.tail(100)

            # 识别双顶/双底
            if self._is_double_top(recent_data):
                patterns.append('double_top')
            if self._is_double_bottom(recent_data):
                patterns.append('double_bottom')

            # 识别头肩顶/底
            if self._is_head_and_shoulders(recent_data):
                patterns.append('head_and_shoulders')
            if self._is_inverse_head_and_shoulders(recent_data):
                patterns.append('inverse_head_and_shoulders')

            # 识别三角形整理
            triangle_type = self._is_triangle(recent_data)
            if triangle_type:
                 patterns.append(triangle_type)

            # 识别旗形
            if self._is_flag(recent_data):
                 patterns.append('flag')

            # 识别楔形
            if self._is_wedge(recent_data):
                 patterns.append('wedge')


            return patterns

        except Exception as e:
            self.logger.error(f"识别形态失败: {str(e)}")
            return []

    def _is_double_top(self, df: pd.DataFrame) -> bool:
        """
        判断是否形成双顶形态
        """
        try:
            # 获取局部高点
            highs = df['high'].values
            peaks = []
            for i in range(1, len(highs)-1):
                if highs[i] > highs[i-1] and highs[i] > highs[i+1]:
                    peaks.append((i, highs[i]))

            if len(peaks) < 2:
                return False

            # 检查最后两个峰值
            last_two_peaks = peaks[-2:]

            # 优化判断条件：
            # 1. 两个峰值之间的价格差异不超过1%
            # 2. 两个峰值之间至少间隔5个周期
            # 3. 两个峰值之间的最低点比峰值低至少2%
            if (abs(last_two_peaks[0][1] - last_two_peaks[1][1]) / last_two_peaks[0][1] < 0.01 and
                last_two_peaks[1][0] - last_two_peaks[0][0] >= 5):

                # 检查两个峰值之间的最低点
                min_between_peaks = min(highs[last_two_peaks[0][0]:last_two_peaks[1][0]])
                if (last_two_peaks[0][1] - min_between_peaks) / last_two_peaks[0][1] >= 0.02:
                    return True

            return False

        except Exception as e:
            self.logger.error(f"判断双顶形态失败: {str(e)}")
            return False

    def _is_double_bottom(self, df: pd.DataFrame) -> bool:
        """
        判断是否形成双底形态
        """
        try:
            # 获取局部低点
            lows = df['low'].values
            troughs = []
            for i in range(1, len(lows)-1):
                if lows[i] < lows[i-1] and lows[i] < lows[i+1]:
                    troughs.append((i, lows[i]))

            if len(troughs) < 2:
                return False

            # 检查最后两个谷值
            last_two_troughs = troughs[-2:]

            # 优化判断条件：
            # 1. 两个谷值之间的价格差异不超过1%
            # 2. 两个谷值之间至少间隔5个周期
            # 3. 两个谷值之间的最高点比谷值高至少2%
            if (abs(last_two_troughs[0][1] - last_two_troughs[1][1]) / last_two_troughs[0][1] < 0.01 and
                last_two_troughs[1][0] - last_two_troughs[0][0] >= 5):

                # 检查两个谷值之间的最高点
                max_between_troughs = max(lows[last_two_troughs[0][0]:last_two_troughs[1][0]])
                if (max_between_troughs - last_two_troughs[0][1]) / last_two_troughs[0][1] >= 0.02:
                    return True

            return False

        except Exception as e:
            self.logger.error(f"判断双底形态失败: {str(e)}")
            return False

    def _is_head_and_shoulders(self, df: pd.DataFrame) -> bool:
        """
        判断是否形成头肩顶形态
        """
        try:
            # 获取局部高点
            highs = df['high'].values
            peaks = []
            for i in range(1, len(highs)-1):
                if highs[i] > highs[i-1] and highs[i] > highs[i+1]:
                    peaks.append((i, highs[i]))

            if len(peaks) < 3:
                return False

            # 检查最后三个峰值
            last_three_peaks = peaks[-3:]
            if (last_three_peaks[1][1] > last_three_peaks[0][1] and
                last_three_peaks[1][1] > last_three_peaks[2][1] and
                abs(last_three_peaks[0][1] - last_three_peaks[2][1]) / last_three_peaks[0][1] < 0.02):
                return True

            return False

        except Exception as e:
            self.logger.error(f"判断头肩顶形态失败: {str(e)}")
            return False

    def _is_inverse_head_and_shoulders(self, df: pd.DataFrame) -> bool:
        """
        判断是否形成头肩底形态
        """
        try:
            # 获取局部低点
            lows = df['low'].values
            troughs = []
            for i in range(1, len(lows)-1):
                if lows[i] < lows[i-1] and lows[i] < lows[i+1]:
                    troughs.append((i, lows[i]))

            if len(troughs) < 3:
                return False

            # 检查最后三个谷值
            last_three_troughs = troughs[-3:]
            if (last_three_troughs[1][1] < last_three_troughs[0][1] and
                last_three_troughs[1][1] < last_three_troughs[2][1] and
                abs(last_three_troughs[0][1] - last_three_troughs[2][1]) / last_three_troughs[0][1] < 0.02):
                return True

            return False

        except Exception as e:
            self.logger.error(f"判断头肩底形态失败: {str(e)}")
            return False

    def _is_triangle(self, df: pd.DataFrame) -> str | bool:
        """
        判断是否形成三角形整理形态
        """
        try:
            # 获取局部高点和低点
            highs = df['high'].values
            lows = df['low'].values
            peaks = []
            troughs = []

            for i in range(1, len(highs)-1):
                if highs[i] > highs[i-1] and highs[i] > highs[i+1]:
                    peaks.append((i, highs[i]))
                if lows[i] < lows[i-1] and lows[i] < lows[i+1]:
                    troughs.append((i, lows[i]))

            if len(peaks) < 2 or len(troughs) < 2:
                return False

            # 检查最后两个峰值和谷值
            last_two_peaks = peaks[-2:]
            last_two_troughs = troughs[-2:]

            # 计算趋势线斜率
            # 确保分母不为零
            peak_slope = (last_two_peaks[1][1] - last_two_peaks[0][1]) / (last_two_peaks[1][0] - last_two_peaks[0][0]) if (last_two_peaks[1][0] - last_two_peaks[0][0]) != 0 else 0
            trough_slope = (last_two_troughs[1][1] - last_two_troughs[0][1]) / (last_two_troughs[1][0] - last_two_troughs[0][0]) if (last_two_troughs[1][0] - last_two_troughs[0][0]) != 0 else 0


            # 判断是否形成三角形 (斜率绝对值较小且方向相反或同向收敛)
            if abs(peak_slope) < 0.1 and abs(trough_slope) < 0.1:
                 # 判断三角形类型
                 if peak_slope < 0 and trough_slope > 0:
                     return 'descending_triangle' # 修正：高点下降，低点上升为收敛三角形，这里可能是笔误
                 elif peak_slope > 0 and trough_slope < 0:
                     return 'ascending_triangle' # 修正：高点上升，低点下降为扩散三角形，这里可能是笔误
                 elif peak_slope < 0 and trough_slope < 0 and abs(peak_slope) > abs(trough_slope):
                     return 'descending_triangle' # 高点下降更快
                 elif peak_slope > 0 and trough_slope > 0 and abs(trough_slope) > abs(peak_slope):
                     return 'ascending_triangle' # 低点上升更快
                 elif (peak_slope < 0 and trough_slope > 0) or (peak_slope > 0 and trough_slope < 0):
                     return 'symmetrical_triangle' # 收敛三角形

            return False

        except Exception as e:
            self.logger.error(f"判断三角形形态失败: {str(e)}")
            return False

    def _is_flag(self, df: pd.DataFrame) -> bool:
        """
        判断是否形成旗形整理形态
        """
        try:
            # 获取最近的数据
            recent_data = df.tail(20)
            highs = recent_data['high'].values
            lows = recent_data['low'].values

            if len(recent_data) < 20: return False # 数据不足

            # 计算旗杆（前期趋势）
            trend_start = df['close'].iloc[-20]
            trend_end = df['close'].iloc[-1]
            trend_direction = 1 if trend_end > trend_start else -1

            # 计算旗形区域的高点和低点
            flag_highs = []
            flag_lows = []
            # 简化查找局部极值
            for i in range(1, len(highs)-1):
                if highs[i] > highs[i-1] and highs[i] > highs[i+1]:
                    flag_highs.append((i, highs[i]))
                if lows[i] < lows[i-1] and lows[i] < lows[i+1]:
                    flag_lows.append((i, lows[i]))

            if len(flag_highs) < 2 or len(flag_lows) < 2:
                return False

            # 计算旗形区域的斜率
            # 确保分母不为零
            high_slope = (flag_highs[-1][1] - flag_highs[0][1]) / (flag_highs[-1][0] - flag_highs[0][0]) if (flag_highs[-1][0] - flag_highs[0][0]) != 0 else 0
            low_slope = (flag_lows[-1][1] - flag_lows[0][1]) / (flag_lows[-1][0] - flag_lows[0][0]) if (flag_lows[-1][0] - flag_lows[0][0]) != 0 else 0


            # 判断是否形成旗形 (平行通道且与前期趋势相反)
            if abs(high_slope - low_slope) < 0.01: # 斜率接近
                 if (trend_direction > 0 and high_slope < 0) or (trend_direction < 0 and high_slope > 0):
                     return True

            return False

        except Exception as e:
            self.logger.error(f"判断旗形形态失败: {str(e)}")
            return False

    def _is_wedge(self, df: pd.DataFrame) -> bool:
        """
        判断是否形成楔形整理形态
        """
        try:
            # 获取最近的数据
            recent_data = df.tail(30)
            highs = recent_data['high'].values
            lows = recent_data['low'].values

            if len(recent_data) < 30: return False # 数据不足

            # 计算楔形区域的高点和低点
            wedge_highs = []
            wedge_lows = []
            # 简化查找局部极值
            for i in range(1, len(highs)-1):
                if highs[i] > highs[i-1] and highs[i] > highs[i+1]:
                    wedge_highs.append((i, highs[i]))
                if lows[i] < lows[i-1] and lows[i] < lows[i+1]:
                    wedge_lows.append((i, lows[i]))

            if len(wedge_highs) < 2 or len(wedge_lows) < 2:
                return False

            # 计算楔形区域的斜率
            # 确保分母不为零
            high_slope = (wedge_highs[-1][1] - wedge_highs[0][1]) / (wedge_highs[-1][0] - wedge_highs[0][0]) if (wedge_highs[-1][0] - wedge_highs[0][0]) != 0 else 0
            low_slope = (wedge_lows[-1][1] - wedge_lows[0][1]) / (wedge_lows[-1][0] - wedge_lows[0][0]) if (wedge_lows[-1][0] - wedge_lows[0][0]) != 0 else 0


            # 判断是否形成楔形 (两条趋势线收敛)
            if (high_slope < 0 and low_slope > 0) or (high_slope > 0 and low_slope < 0):
                 return True

            return False

        except Exception as e:
            self.logger.error(f"判断楔形形态失败: {str(e)}")
            return False


    def _get_default_analysis(self) -> Dict:
        """返回默认的分析结果"""
        return {
            "price_change": {
                "current_price": 0,
                "price_change_24h": 0,
                "price_change_7d": 0,
                "price_change_30d": 0
            },
            "volume_change": {
                "volume_change_24h": 0,
                "volume_change_7d": 0
            },
            "trends": {
                "ma5_trend": "未知",
                "ma10_trend": "未知",
                "ma20_trend": "未知",
                "overall_trend": "未知"
            },
            "volatility": 0,
            "candlestick_pattern": "未知",
            "shadow_ratio": {
                "upper": 0,
                "lower": 0
            },
            "body_size": 0,
            "indicators": {
                "rsi": 0,
                "macd": "未知",
                "bollinger_position": "未知",
                "kdj": "未知"
            },
            "risk_level": {
                "risk_level": "未知",
                "volatility_risk": "未知",
                "liquidity_risk": "未知",
                "trend_risk": "未知"
            },
            "trade_signal": {
                "direction": "未知",
                "strength": "未知",
                "position_size": "未知",
                "stop_loss": 0,
                "take_profit": 0
            }
        }

    def analyze_and_report(self, df: pd.DataFrame, recipients: List[str]):
        """
        执行完整的分析并发送报告

        Args:
            df: 包含OHLCV数据的DataFrame
            recipients: 接收报告的邮箱列表
        """
        try:
            self.logger.info("开始计算技术指标...")
            df_with_indicators = self.calculate_indicators(df.copy()) # 使用副本避免修改原始df
            self.logger.info("技术指标计算完成。")

            self.logger.info("开始生成交易信号...")
            trade_signal = self.generate_signals(df_with_indicators)
            self.logger.info("交易信号生成完成。")

            self.logger.info("开始分析趋势...")
            trend_analysis = self.analyze_trend(df_with_indicators)
            self.logger.info("趋势分析完成。")

            self.logger.info("开始识别形态...")
            patterns = self.find_patterns(df_with_indicators)
            self.logger.info("形态识别完成。")

            self.logger.info("开始评估风险...")
            risk_analysis = self._evaluate_risk(df_with_indicators)
            self.logger.info("风险评估完成。")

            # 构建报告内容
            report_content = f"""
加密货币技术分析报告 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**交易信号:**
方向: {trade_signal['action']}
强度: {trade_signal['strength']}
置信度: {f"{trade_signal['confidence']:.2f}" if pd.notna(trade_signal['confidence']) else "NaN"}
原因: {', '.join(trade_signal['reasons'])}
止损: {f"{trade_signal['stop_loss']:.4f}" if trade_signal['stop_loss'] is not None else "N/A"}
止盈: {f"{trade_signal['take_profit']:.4f}" if trade_signal['take_profit'] is not None else "N/A"}
风险等级: {trade_signal['risk_level']}

**趋势分析:**
整体趋势: {trend_analysis['trend']} ({trend_analysis['strength']})
RSI: {f"{trend_analysis['rsi']:.2f}" if pd.notna(trend_analysis.get('rsi')) else "NaN"}
MACD: {f"{trend_analysis['macd']:.4f}" if pd.notna(trend_analysis.get('macd')) else "NaN"}, 信号线: {f"{trend_analysis['macd_signal']:.4f}" if pd.notna(trend_analysis.get('macd_signal')) else "NaN"}, 柱状图: {f"{trend_analysis['macd_hist']:.4f}" if pd.notna(trend_analysis.get('macd_hist')) else "NaN"}

**形态识别:**
识别到的形态: {', '.join(patterns) if patterns else '无'}

**风险评估:**
综合风险等级: {risk_analysis['risk_level']}
波动率风险: {risk_analysis['volatility_risk']}
流动性风险: {risk_analysis['liquidity_risk']}
趋势风险: {risk_analysis['trend_risk']}

**技术指标概览 (最新数据):**
收盘价: {f"{df_with_indicators['close'].iloc[-1]:.4f}" if pd.notna(df_with_indicators['close'].iloc[-1]) else "NaN"}
SMA(20): {f"{df_with_indicators['sma_20'].iloc[-1]:.4f}" if pd.notna(df_with_indicators['sma_20'].iloc[-1]) else "NaN"}
EMA(12): {f"{df_with_indicators['ema_12'].iloc[-1]:.4f}" if pd.notna(df_with_indicators['ema_12'].iloc[-1]) else "NaN"}
EMA(26): {f"{df_with_indicators['ema_26'].iloc[-1]:.4f}" if pd.notna(df_with_indicators['ema_26'].iloc[-1]) else "NaN"}
RSI(14): {f"{df_with_indicators['rsi_14'].iloc[-1]:.2f}" if pd.notna(df_with_indicators['rsi_14'].iloc[-1]) else "NaN"}
KDJ(K): {f"{df_with_indicators['k'].iloc[-1]:.2f}" if pd.notna(df_with_indicators['k'].iloc[-1]) else "NaN"}
KDJ(D): {f"{df_with_indicators['d'].iloc[-1]:.2f}" if pd.notna(df_with_indicators['d'].iloc[-1]) else "NaN"}
KDJ(J): {f"{df_with_indicators['j'].iloc[-1]:.2f}" if pd.notna(df_with_indicators['j'].iloc[-1]) else "NaN"}
布林带上轨: {f"{df_with_indicators['bollinger_upper'].iloc[-1]:.4f}" if pd.notna(df_with_indicators['bollinger_upper'].iloc[-1]) else "NaN"}
布林带中轨: {f"{df_with_indicators['bollinger_middle'].iloc[-1]:.4f}" if pd.notna(df_with_indicators['bollinger_middle'].iloc[-1]) else "NaN"}
布林带下轨: {f"{df_with_indicators['bollinger_lower'].iloc[-1]:.4f}" if pd.notna(df_with_indicators['bollinger_lower'].iloc[-1]) else "NaN"}
ATR(14): {f"{df_with_indicators['atr'].iloc[-1]:.4f}" if pd.notna(df_with_indicators['atr'].iloc[-1]) else "NaN"}
ADX(14): {f"{df_with_indicators['adx'].iloc[-1]:.2f}" if pd.notna(df_with_indicators['adx'].iloc[-1]) else "NaN"}
CCI(14): {f"{df_with_indicators['cci'].iloc[-1]:.2f}" if pd.notna(df_with_indicators['cci'].iloc[-1]) else "NaN"}
OBV: {f"{df_with_indicators['obv'].iloc[-1]:.2f}" if pd.notna(df_with_indicators['obv'].iloc[-1]) else "NaN"}
成交量变化: {f"{df_with_indicators['volume_change'].iloc[-1]*100:.2f}%" if pd.notna(df_with_indicators['volume_change'].iloc[-1]) else "NaN"}

"""
            self.logger.info("报告内容已生成。")

            # 发送邮件
            self.send_analysis_report_email(report_content, recipients)

        except Exception as e:
            self.logger.error(f"执行分析和报告发送失败: {str(e)}")


if __name__ == "__main__":
    # 测试代码
    analyzer = TechnicalAnalyzer(
        smtp_server='smtp.your.email.com', # 替换为您的SMTP服务器地址
        smtp_port=587, # 替换为您的SMTP服务器端口
        smtp_user='<EMAIL>', # 替换为您的邮箱地址
        smtp_password='your_password' # 替换为您的邮箱密码或授权码
    )

    # 创建测试数据 (请替换为实际的加密货币OHLCV数据)
    # 示例数据需要包含足够多的行来计算所有指标 (至少200行)
    test_data = pd.DataFrame({
        'open': np.random.randn(300).cumsum() + 10000,
        'high': np.random.randn(300).cumsum() + 10010,
        'low': np.random.randn(300).cumsum() + 9990,
        'close': np.random.randn(300).cumsum() + 10000,
        'volume': np.random.randint(100000, 1000000, 300)
    })
    # 添加时间戳索引
    test_data['timestamp'] = pd.to_datetime(pd.Series(range(len(test_data))), unit='s')
    test_data = test_data.set_index('timestamp')


    # 定义收件人列表
    recipients = ['<EMAIL>', '<EMAIL>']

    # 执行分析并发送报告
    analyzer.analyze_and_report(test_data, recipients)

    # 以下是原有的测试代码，可以根据需要保留或修改
    # 计算技术指标
    # df_with_indicators = analyzer.calculate_indicators(test_data)
    # print("\n技术指标:")
    # print(df_with_indicators.tail())

    # 分析趋势
    # trend_analysis = analyzer.analyze_trend(df_with_indicators)
    # print("\n趋势分析:")
    # print(trend_analysis)

    # 识别形态
    # patterns = analyzer.find_patterns(df_with_indicators)
    # print("\n识别到的形态:")
    # print(patterns)

    # 生成交易信号
    # signals = analyzer.generate_signals(df_with_indicators)
    # print("\n交易信号:")
    # print(signals)