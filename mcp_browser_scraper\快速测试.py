#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速测试双长上影线识别功能
"""

import os
import sys
from datetime import datetime

def log(message):
    """日志函数"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def main():
    """主函数"""
    try:
        print("🚀 快速测试双长上影线识别功能...")
        print("="*80)
        
        # 检查当前目录
        current_dir = os.getcwd()
        print(f"📍 当前目录: {current_dir}")
        
        # 列出当前目录的Python文件
        print("📁 当前目录Python文件:")
        py_files = [f for f in os.listdir('.') if f.endswith('.py')]
        for file in sorted(py_files):
            print(f"   ✓ {file}")
        
        # 检查主脚本文件
        main_script = "advanced_crypto_scraper tuxing.py"
        if os.path.exists(main_script):
            print(f"✅ 找到主脚本: {main_script}")
        else:
            print(f"❌ 未找到主脚本: {main_script}")
            print("💡 请确保在正确的目录中运行此脚本")
            input("按回车键退出...")
            return
        
        # 尝试导入和测试
        print("\n🔧 开始导入测试...")
        
        try:
            # 添加当前目录到Python路径
            if '.' not in sys.path:
                sys.path.insert(0, '.')
            
            # 动态导入主脚本
            import importlib.util
            spec = importlib.util.spec_from_file_location("advanced_crypto_scraper_tuxing", main_script)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            print("✅ 模块导入成功")
            
            # 创建测试实例
            print("🔧 创建测试实例...")
            scraper = module.AdvancedCryptoScraper(use_proxy=False)
            print("✅ 实例创建成功")
            
            # 准备测试数据
            print("\n📊 准备测试数据...")
            test_data = [
                {
                    'date': '2025-06-21',
                    'open_price': 100.0,
                    'high_price': 120.0,  # 长上影线
                    'low_price': 95.0,
                    'close_price': 105.0,  # 小实体
                    'volume': 1000000
                },
                {
                    'date': '2025-06-22',
                    'open_price': 105.0,
                    'high_price': 115.0,  # 高点递减
                    'low_price': 100.0,
                    'close_price': 108.0,  # 小实体
                    'volume': 1200000
                }
            ]
            
            print("📈 测试数据:")
            for i, candle in enumerate(test_data, 1):
                print(f"   第{i}根K线: 开盘={candle['open_price']}, 最高={candle['high_price']}, "
                      f"最低={candle['low_price']}, 收盘={candle['close_price']}")
            
            # 执行形态识别测试
            print("\n🔍 执行形态识别测试...")
            patterns = scraper._identify_patterns(test_data)
            
            print(f"\n🎯 识别结果: {patterns}")
            
            # 判断测试结果
            if "双长上影线" in patterns:
                print("🎉 ✅ 测试成功！正确识别双长上影线形态")
                print("📈 形态特征: 连续两根长上影线，高点递减")
                print("💡 技术含义: 上涨乏力，可能出现回调")
                success = True
            else:
                print("❌ 测试失败：未能识别双长上影线形态")
                print(f"实际识别结果: {patterns}")
                success = False
            
            print("\n" + "="*80)
            print("📊 测试总结:")
            print("="*80)
            
            if success:
                print("🎉 双长上影线识别功能正常工作！")
                print("✅ 算法逻辑正确")
                print("✅ 数据处理正常")
                print("✅ 形态判断准确")
            else:
                print("⚠️ 功能需要进一步检查")
            
            print("\n💡 双长上影线形态说明:")
            print("📈 识别条件:")
            print("   • 分析最近2根K线数据")
            print("   • 上影线长度 ≥ 整根K线长度的1/3")
            print("   • 实体部分长度 ≤ 整根K线长度的2/3")
            print("   • 第二根K线最高价 < 第一根K线最高价")
            print("📊 技术含义:")
            print("   • 上涨乏力，多头力量衰竭")
            print("   • 可能出现价格回调")
            print("   • 重要的反转信号")
            
        except Exception as e:
            print(f"❌ 测试过程出错: {e}")
            import traceback
            print(f"错误详情: {traceback.format_exc()}")
            success = False
        
        return success
        
    except KeyboardInterrupt:
        print("\n👋 用户中断测试")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()
