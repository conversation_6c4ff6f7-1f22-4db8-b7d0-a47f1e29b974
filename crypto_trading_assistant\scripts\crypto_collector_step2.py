#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
加密货币收集第二步：根据已收集的基本信息获取详细数据并分析

此脚本从第一步生成的文件读取币种信息，然后获取详细数据进行分析
"""

import os
import time
import json
import argparse
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from loguru import logger
import matplotlib.pyplot as plt
import matplotlib as mpl
from tqdm import tqdm
from pycoingecko import CoinGeckoAPI
import requests
from pathlib import Path

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体
plt.rcParams['axes.unicode_minus'] = False    # 解决保存图像是负号'-'显示为方块的问题

# 导入技术分析模块
from crypto_technical_analyzer import TechnicalAnalyzer

# 配置日志
logger.remove()
logger.add(lambda msg: print(msg), level="INFO")
logger.add("crypto_analyzer_step2.log", rotation="500 MB", level="DEBUG")

class DetailedCryptoAnalyzer:
    """
    详细加密货币分析器
    从基本信息文件读取币种列表，然后获取详细数据并分析
    """
    def __init__(self, input_file='new_coins.json', output_folder="crypto_reports", 
                vs_currency="usd", lang="zh", use_proxy=False, request_delay=5.0,
                api_keys_file="api_keys.json", max_coins=20):
        """
        初始化详细加密货币分析器
        
        Parameters
        ----------
        input_file : str
            输入文件路径，包含基本币种信息
        output_folder : str
            输出结果的文件夹路径
        vs_currency : str
            计价货币，默认为美元(usd)
        lang : str
            语言，默认为中文(zh)
        use_proxy : bool
            是否使用代理，默认为False
        request_delay : float
            API请求间隔时间（秒），默认5.0秒
        api_keys_file : str
            API密钥配置文件路径
        max_coins : int
            最多分析多少个币种，默认20个
        """
        self.input_file = input_file
        self.output_folder = output_folder
        self.vs_currency = vs_currency
        self.lang = lang
        self.use_proxy = use_proxy
        self.request_delay = request_delay
        self.max_coins = max_coins
        
        # 创建输出文件夹
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
            
        # 加载API密钥
        self.api_keys = self._load_api_keys(api_keys_file)
        
        # 设置代理
        self.proxies = None
        if use_proxy:
            self.proxies = {
                'http': 'http://127.0.0.1:7890',
                'https': 'http://127.0.0.1:7890'
            }
            session = requests.Session()
            session.proxies = self.proxies
            self.cg = CoinGeckoAPI(session=session)
        else:
            self.cg = CoinGeckoAPI()
            
        # 初始化技术分析器
        self.technical_analyzer = TechnicalAnalyzer(
            request_delay=request_delay,
            use_proxy=use_proxy
        )
        
        # 创建中文对应的字段名映射
        self.field_translation = {
            'id': '币种ID',
            'symbol': '交易符号',
            'name': '名称',
            'current_price': '当前价格',
            'market_cap': '市值',
            'market_cap_rank': '市值排名',
            'total_volume': '24h交易量',
            'high_24h': '24h最高价',
            'low_24h': '24h最低价',
            'price_change_24h': '24h价格变化',
            'price_change_percentage_24h': '24h价格变化百分比',
            'market_cap_change_24h': '24h市值变化',
            'market_cap_change_percentage_24h': '24h市值变化百分比',
            'circulating_supply': '流通供应量',
            'total_supply': '总供应量',
            'max_supply': '最大供应量',
            'ath': '历史最高价',
            'ath_change_percentage': '距历史最高价变化百分比',
            'ath_date': '历史最高价日期',
            'atl': '历史最低价',
            'atl_change_percentage': '距历史最低价变化百分比',
            'atl_date': '历史最低价日期',
            'roi': '投资回报率',
            'last_updated': '最后更新时间',
            'price_change_percentage_7d': '7d价格变化百分比',
            'price_change_percentage_14d': '14d价格变化百分比',
            'price_change_percentage_30d': '30d价格变化百分比',
            'first_price': '首次价格',
            'listing_date': '上市日期',
            'price_change_since_listing': '上市以来涨幅(%)',
            'price_change_since_listing_text': '上市以来涨幅',
            'rsi_14': 'RSI(14)',
            'macd': 'MACD',
            'macd_signal': 'MACD信号线',
            'macd_hist': 'MACD柱线',
            'ema_12': 'EMA(12)',
            'ema_26': 'EMA(26)',
            'sma_10': 'SMA(10)',
            'bollinger_upper': '布林上轨',
            'bollinger_middle': '布林中轨',
            'bollinger_lower': '布林下轨',
            'trend': '趋势',
            'strength': '强度',
            'volatility': '波动性',
            'trading_advice': '交易建议'
        }
    
    def _load_api_keys(self, api_keys_file):
        """
        加载API密钥
        如果文件不存在，则创建一个空的配置文件
        
        Parameters
        ----------
        api_keys_file : str
            API密钥配置文件路径
            
        Returns
        -------
        dict
            API密钥配置
        """
        if os.path.exists(api_keys_file):
            try:
                with open(api_keys_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"读取API密钥配置文件失败: {e}")
        
        # 创建空的配置文件模板
        default_config = {
            "huobi": {
                "access_key": "",
                "secret_key": ""
            }
        }
        
        try:
            with open(api_keys_file, 'w') as f:
                json.dump(default_config, f, indent=4)
            logger.info(f"已创建API密钥配置文件模板: {api_keys_file}")
        except Exception as e:
            logger.warning(f"创建API密钥配置文件失败: {e}")
            
        return default_config
    
    def load_coins_from_file(self):
        """
        从文件加载币种信息
        
        Returns
        -------
        pd.DataFrame
            币种基本信息
        """
        try:
            # 根据文件扩展名决定读取方式
            if self.input_file.endswith('.json'):
                with open(self.input_file, 'r', encoding='utf-8') as f:
                    coins_data = json.load(f)
                coins_df = pd.DataFrame(coins_data)
            elif self.input_file.endswith('.csv'):
                coins_df = pd.read_csv(self.input_file)
            else:
                logger.error(f"不支持的文件格式: {self.input_file}")
                return pd.DataFrame()
                
            logger.info(f"从文件加载了{len(coins_df)}个币种信息")
            
            # 限制最多分析的币种数量
            if len(coins_df) > self.max_coins:
                logger.info(f"币种数量超过限制，只分析前{self.max_coins}个币种")
                coins_df = coins_df.head(self.max_coins)
                
            return coins_df
            
        except Exception as e:
            logger.error(f"加载币种信息失败: {e}")
            return pd.DataFrame()
    
    def get_detailed_data(self, coins_df):
        """
        获取币种的详细数据
        
        Parameters
        ----------
        coins_df : pd.DataFrame
            币种基本信息
            
        Returns
        -------
        pd.DataFrame
            详细币种数据
        """
        logger.info("获取币种的详细数据...")
        
        detailed_data = []
        
        for i, coin in tqdm(coins_df.iterrows(), total=len(coins_df), desc="获取详细数据"):
            try:
                coin_id = coin['id']
                symbol = coin['symbol']
                
                logger.info(f"获取{symbol}({coin_id})的详细数据...")
                
                # 延迟请求，避免触发API限制
                time.sleep(self.request_delay)
                
                # 获取币种详细信息
                try:
                    coin_info = self.cg.get_coin_by_id(
                        id=coin_id,
                        localization=self.lang == 'zh',
                        tickers=False,
                        market_data=True,
                        community_data=False,
                        developer_data=False,
                        sparkline=False
                    )
                    
                    # 提取详细数据
                    coin_details = coin.to_dict()
                    
                    # 添加额外信息
                    if 'market_data' in coin_info:
                        market_data = coin_info['market_data']
                        
                        # 添加更多价格相关数据
                        if 'current_price' in market_data:
                            coin_details['current_price'] = market_data['current_price'].get(self.vs_currency)
                            
                        # 添加更多供应量数据
                        for field in ['circulating_supply', 'total_supply', 'max_supply']:
                            if field in market_data:
                                coin_details[field] = market_data[field]
                                
                        # 添加价格变化数据
                        for period in ['24h', '7d', '14d', '30d']:
                            field = f'price_change_percentage_{period}'
                            if field in market_data:
                                coin_details[field] = market_data[field]
                                
                    # 添加其他基础信息
                    for field in ['description', 'homepage', 'blockchain_site', 'genesis_date']:
                        if field in coin_info:
                            if field == 'description':
                                # 只保留中文或英文描述
                                if self.lang == 'zh' and 'zh' in coin_info[field]:
                                    coin_details[field] = coin_info[field]['zh']
                                else:
                                    coin_details[field] = coin_info[field].get('en', '')
                            else:
                                coin_details[field] = coin_info[field]
                    
                    # 尝试获取上市日期 - 优先使用多种来源
                    # 1. 首先检查是否已有listing_date
                    if 'listing_date' not in coin_details or not coin_details['listing_date']:
                        # 2. 使用genesis_date作为备选
                        if 'genesis_date' in coin_details and coin_details['genesis_date']:
                            coin_details['listing_date'] = coin_details['genesis_date']
                        # 3. 使用ath_date作为备选（假设ATH日期接近上市日期）
                        elif 'ath_date' in coin_details and coin_details['ath_date']:
                            coin_details['listing_date'] = coin_details['ath_date']
                        # 4. 尝试从市场数据中获取
                        elif 'market_data' in coin_info and 'first_price_date' in coin_info['market_data']:
                            coin_details['listing_date'] = coin_info['market_data']['first_price_date']
                        
                    detailed_data.append(coin_details)
                    
                except Exception as e:
                    logger.warning(f"获取{coin_id}的详细信息失败: {e}")
                    # 如果获取详细信息失败，仍使用基本信息
                    detailed_data.append(coin.to_dict())
                
            except Exception as e:
                logger.error(f"处理{coin_id if 'coin_id' in locals() else '未知币种'}时出错: {e}")
                
        # 转换为DataFrame
        detailed_df = pd.DataFrame(detailed_data)
        
        return detailed_df
    
    def analyze(self):
        """
        运行详细分析流程
        
        Returns
        -------
        str
            报告保存路径
        """
        try:
            logger.info("开始详细分析流程...")
            
            # 1. 从文件加载币种基本信息
            coins_df = self.load_coins_from_file()
            
            if coins_df.empty:
                logger.warning("未找到币种信息，无法进行分析")
                return None
                
            logger.info(f"加载了{len(coins_df)}个币种进行分析")
            
            # 2. 获取详细市场数据
            logger.info("获取详细的市场数据...")
            market_data = self.get_detailed_data(coins_df)
            
            # 3. 计算技术分析指标
            logger.info("计算技术分析指标...")
            technical_data = self.technical_analyzer.analyze(market_data, vs_currency=self.vs_currency)
            
            # 4. 整合所有数据
            logger.info("整合所有数据...")
            integrated_data = self._integrate_data(market_data, technical_data)
            
            # 5. 生成详细分析报告
            logger.info("生成分析报告...")
            report_path = self._generate_report(integrated_data)
            
            logger.info(f"分析完成，报告已生成: {report_path}")
            return report_path
            
        except Exception as e:
            logger.error(f"分析过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return None
            
    def _integrate_data(self, market_data, technical_data):
        """
        整合所有数据
        
        Parameters
        ----------
        market_data : pd.DataFrame
            市场数据
        technical_data : dict
            技术分析数据
            
        Returns
        -------
        pd.DataFrame
            整合后的数据
        """
        # 将技术分析数据转换为DataFrame
        tech_df = pd.DataFrame(technical_data)
        
        # 合并市场数据和技术分析数据
        # 以id作为合并依据
        if not tech_df.empty and 'id' in tech_df.columns:
            integrated = pd.merge(
                market_data,
                tech_df,
                on='id',
                how='left',
                suffixes=('', '_tech')
            )
        else:
            integrated = market_data.copy()
            
        # 计算其他有用的指标
        self._calculate_additional_metrics(integrated)
        
        return integrated
        
    def _calculate_additional_metrics(self, df):
        """
        计算额外的指标
        
        Parameters
        ----------
        df : pd.DataFrame
            币种数据
        """
        # 计算上市以来的价格变化
        if 'ath' in df.columns and 'current_price' in df.columns:
            # 假设ATH是上市初期价格的近似值(由于无法直接获取上市价格)
            df['price_change_since_listing'] = ((df['current_price'] - df['ath']) / df['ath'] * 100).apply(lambda x: round(x, 2))
            
            # 添加易读的价格变化文本
            def format_price_change(x):
                if pd.isna(x):
                    return "未知"
                if x > 0:
                    return f"上涨{abs(x)}%"
                elif x < 0:
                    return f"下跌{abs(x)}%"
                else:
                    return "无变化"
                    
            df['price_change_since_listing_text'] = df['price_change_since_listing'].apply(format_price_change)
            
        # 计算上市天数 - 改进日期处理
        if 'listing_date' in df.columns:
            # 尝试多种日期格式
            try:
                # 首先尝试标准格式
                df['listing_date'] = pd.to_datetime(df['listing_date'], errors='coerce')
                
                # 对于仍然为NaT的日期，尝试其他格式
                mask = df['listing_date'].isna()
                if mask.any():
                    # 尝试不同的日期格式
                    for date_format in ['%Y-%m-%d', '%Y/%m/%d', '%d-%m-%Y', '%d/%m/%Y', '%Y-%m-%dT%H:%M:%S.%fZ']:
                        try:
                            # 只处理NaT的日期
                            temp_dates = pd.to_datetime(df.loc[mask, 'listing_date'], format=date_format, errors='coerce')
                            # 更新成功转换的日期
                            df.loc[temp_dates.notna(), 'listing_date'] = temp_dates[temp_dates.notna()]
                            # 更新mask
                            mask = df['listing_date'].isna()
                            # 如果所有日期都已转换，跳出循环
                            if not mask.any():
                                break
                        except:
                            continue
                
                # 计算上市天数
                df['days_since_listing'] = (pd.Timestamp.now() - df['listing_date']).dt.days
                df['days_since_listing'] = df['days_since_listing'].apply(lambda x: max(0, x) if not pd.isna(x) else None)
                
                # 格式化日期为易读格式
                df['listing_date_formatted'] = df['listing_date'].apply(
                    lambda x: x.strftime('%Y-%m-%d') if pd.notna(x) else "未知"
                )
            except Exception as e:
                logger.warning(f"处理上市日期时出错: {e}")
                df['days_since_listing'] = None
                df['listing_date_formatted'] = "未知"
        else:
            # 如果没有listing_date，尝试使用ath_date
            if 'ath_date' in df.columns:
                try:
                    df['listing_date'] = pd.to_datetime(df['ath_date'], errors='coerce')
                    df['days_since_listing'] = (pd.Timestamp.now() - df['listing_date']).dt.days
                    df['days_since_listing'] = df['days_since_listing'].apply(lambda x: max(0, x) if not pd.isna(x) else None)
                    df['listing_date_formatted'] = df['listing_date'].apply(
                        lambda x: x.strftime('%Y-%m-%d') if pd.notna(x) else "未知"
                    )
                except:
                    df['days_since_listing'] = None
                    df['listing_date_formatted'] = "未知"
            else:
                df['days_since_listing'] = None
                df['listing_date_formatted'] = "未知"
            
        # 确定市场趋势（多头或空头）
        if 'trend' in df.columns:
            df['market_trend'] = df['trend'].apply(lambda x: '多头' if x == '上升' else '空头' if x == '下降' else '震荡')
        else:
            # 如果没有trend列，尝试根据价格变化判断
            if 'price_change_percentage_24h' in df.columns:
                df['market_trend'] = df['price_change_percentage_24h'].apply(
                    lambda x: '多头' if x > 5 else '空头' if x < -5 else '震荡'
                )
            else:
                df['market_trend'] = '未知'
                
        # 格式化较大的数字，使其更易读
        for col in ['market_cap', 'total_volume', 'circulating_supply', 'total_supply', 'max_supply']:
            if col in df.columns:
                df[f'{col}_text'] = df[col].apply(self._format_number)
                
    @staticmethod
    def _format_number(num):
        """
        格式化数字使其更易读
        
        Parameters
        ----------
        num : float
            要格式化的数字
            
        Returns
        -------
        str
            格式化后的字符串
        """
        if pd.isna(num):
            return "未知"
            
        if num >= 1_000_000_000:
            return f"{num/1_000_000_000:.2f}B"
        elif num >= 1_000_000:
            return f"{num/1_000_000:.2f}M"
        elif num >= 1_000:
            return f"{num/1_000:.2f}K"
        else:
            return f"{num:.2f}"
            
    def _format_datetime(dt):
        """格式化日期时间为字符串"""
        if isinstance(dt, (datetime, pd.Timestamp)):
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        return dt

    def _serialize_data(data):
        """序列化数据，处理日期时间对象"""
        if isinstance(data, dict):
            return {k: _serialize_data(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [_serialize_data(item) for item in data]
        elif isinstance(data, (datetime, pd.Timestamp)):
            return _format_datetime(data)
        return data

    def _generate_report(self, data):
        """生成分析报告"""
        try:
            # 序列化数据
            serialized_data = _serialize_data(data)
            
            # 生成报告文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_path = os.path.join(self.output_folder, f"crypto_analysis_{timestamp}.json")
            
            # 保存JSON报告
            with open(report_path, "w", encoding="utf-8") as f:
                json.dump(serialized_data, f, ensure_ascii=False, indent=4)
            
            logger.info(f"报告已生成: {report_path}")
            return report_path
        except Exception as e:
            logger.error(f"生成报告失败: {str(e)}")
            return None

def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description='加密货币详细分析工具 - 第二步')
    parser.add_argument('-i', '--input', type=str, default='new_coins.json', help='输入文件路径，包含基本币种信息')
    parser.add_argument('-o', '--output', type=str, default="crypto_reports", help='输出文件夹路径')
    parser.add_argument('-c', '--currency', type=str, default='usd', help='计价货币，默认为美元(usd)')
    parser.add_argument('-l', '--lang', type=str, default='zh', help='语言，默认为中文(zh)')
    parser.add_argument('-p', '--proxy', action='store_true', help='是否使用代理')
    parser.add_argument('-m', '--max', type=int, default=20, help='最多分析多少个币种，默认20个')
    return parser.parse_args()

def main():
    """
    主函数
    """
    args = parse_args()
    
    logger.info("=" * 80)
    logger.info("            加密货币详细分析工具 - 第二步")
    logger.info("=" * 80)
    
    analyzer = DetailedCryptoAnalyzer(
        input_file=args.input,
        output_folder=args.output,
        vs_currency=args.currency,
        lang=args.lang,
        use_proxy=args.proxy,
        max_coins=args.max
    )
    
    # 运行详细分析
    report_path = analyzer.analyze()
    
    if report_path:
        logger.info(f"分析报告已保存至: {report_path}")
        logger.info(f"可通过浏览器打开查看HTML报告")
    else:
        logger.error("分析失败，未生成报告")
    
if __name__ == "__main__":
    main() 