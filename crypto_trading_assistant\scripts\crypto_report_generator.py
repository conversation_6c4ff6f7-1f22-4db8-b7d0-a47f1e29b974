#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
加密货币分析报告生成模块

生成HTML和CSV格式的分析报告，包括：
- 新币一览表
- 详细市场数据
- 技术分析
- 交易所数据
- 数据可视化
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib as mpl
import seaborn as sns
import json
from typing import Dict, List, Optional, Union
import logging

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体
plt.rcParams['axes.unicode_minus'] = False    # 解决保存图像是负号'-'显示为方块的问题

class ReportGenerator:
    """
    报告生成器
    生成HTML和CSV格式的分析报告
    """
    def __init__(self, output_dir: str = 'reports'):
        """
        初始化报告生成器
        
        Args:
            output_dir: 输出目录，默认为reports
        """
        self.output_dir = output_dir
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('report_generator.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 创建图表文件夹
        self.charts_folder = os.path.join(output_dir, "charts")
        if not os.path.exists(self.charts_folder):
            os.makedirs(self.charts_folder)
            
        # 当前时间戳
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 中文字段名映射
        self.field_translation = {
            'id': '币种ID',
            'symbol': '交易符号',
            'name': '名称',
            'current_price': '当前价格',
            'market_cap': '市值',
            'market_cap_rank': '市值排名',
            'total_volume': '24h交易量',
            'high_24h': '24h最高价',
            'low_24h': '24h最低价',
            'price_change_24h': '24h价格变化',
            'price_change_percentage_24h': '24h价格变化百分比',
            'market_cap_change_24h': '24h市值变化',
            'market_cap_change_percentage_24h': '24h市值变化百分比',
            'circulating_supply': '流通供应量',
            'total_supply': '总供应量',
            'max_supply': '最大供应量',
            'ath': '历史最高价',
            'ath_change_percentage': '距历史最高价变化百分比',
            'ath_date': '历史最高价日期',
            'atl': '历史最低价',
            'atl_change_percentage': '距历史最低价变化百分比',
            'atl_date': '历史最低价日期',
            'roi': '投资回报率',
            'last_updated': '最后更新时间',
            'price_change_percentage_7d': '7d价格变化百分比',
            'price_change_percentage_14d': '14d价格变化百分比',
            'price_change_percentage_30d': '30d价格变化百分比',
            'first_price': '首次价格',
            'listing_date': '上市日期',
            'price_change_since_listing': '上市以来涨幅(%)',
            'price_change_since_listing_text': '上市以来涨幅',
            'total_volume_since_listing': '上市以来总交易量',
            'avg_volume_7d': '7日平均交易量',
            'turnover_rate': '换手率',
            'volume_ratio': '量比',
            'market_cap_text': '市值(易读)',
            'total_volume_text': '交易量(易读)',
            'long_short_ratio': '多空比',
            'funding_rate': '资金费率',
            'open_interest': '未平仓合约',
            'top_long_short_ratio': '大户多空比',
            'top_long_position': '大户多头持仓',
            'top_short_position': '大户空头持仓',
            'rsi_14': 'RSI(14)',
            'macd': 'MACD',
            'macd_signal': 'MACD信号线',
            'macd_hist': 'MACD柱线',
            'ema_12': 'EMA(12)',
            'ema_26': 'EMA(26)',
            'sma_10': 'SMA(10)',
            'bollinger_upper': '布林上轨',
            'bollinger_middle': '布林中轨',
            'bollinger_lower': '布林下轨',
            'trend': '趋势',
            'strength': '强度',
            'volatility': '波动性',
            'trading_advice': '交易建议'
        }
            
    def generate_backtest_report(self, backtest_results: Dict, 
                               strategy_name: str = 'Strategy') -> str:
        """
        生成回测报告
        
        Args:
            backtest_results: 回测结果
            strategy_name: 策略名称
            
        Returns:
            报告文件路径
        """
        try:
            # 创建报告目录
            report_dir = os.path.join(self.output_dir, 
                                    f"{strategy_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            if not os.path.exists(report_dir):
                os.makedirs(report_dir)
                
            # 生成图表
            self._plot_equity_curve(backtest_results, report_dir)
            self._plot_drawdown(backtest_results, report_dir)
            self._plot_monthly_returns(backtest_results, report_dir)
            self._plot_trade_distribution(backtest_results, report_dir)
        
        # 生成HTML报告
            html_path = self._generate_html_report(backtest_results, strategy_name, report_dir)
            
            # 保存原始数据
            json_path = os.path.join(report_dir, 'backtest_results.json')
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(backtest_results, f, indent=2, ensure_ascii=False)
                
        return html_path
        
        except Exception as e:
            self.logger.error(f"生成回测报告失败: {str(e)}")
            return ''
            
    def _plot_equity_curve(self, results: Dict, report_dir: str):
        """
        绘制权益曲线
        
        Args:
            results: 回测结果
            report_dir: 报告目录
        """
        try:
            equity_curve = pd.DataFrame(results['equity_curve'])
            equity_curve['date'] = pd.to_datetime(equity_curve['date'])
            equity_curve.set_index('date', inplace=True)
            
            plt.figure(figsize=(12, 6))
            plt.plot(equity_curve.index, equity_curve['equity'])
            plt.title('权益曲线')
            plt.xlabel('日期')
            plt.ylabel('权益')
            plt.grid(True)
            
            # 保存图表
            plt.savefig(os.path.join(report_dir, 'equity_curve.png'))
            plt.close()
            
        except Exception as e:
            self.logger.error(f"绘制权益曲线失败: {str(e)}")
            
    def _plot_drawdown(self, results: Dict, report_dir: str):
        """
        绘制回撤曲线
        
        Args:
            results: 回测结果
            report_dir: 报告目录
        """
        try:
            equity_curve = pd.DataFrame(results['equity_curve'])
            equity_curve['date'] = pd.to_datetime(equity_curve['date'])
            equity_curve.set_index('date', inplace=True)
            
            # 计算回撤
            equity_curve['peak'] = equity_curve['equity'].cummax()
            equity_curve['drawdown'] = (equity_curve['peak'] - equity_curve['equity']) / equity_curve['peak']
            
            plt.figure(figsize=(12, 6))
            plt.plot(equity_curve.index, equity_curve['drawdown'] * 100)
            plt.title('回撤曲线')
            plt.xlabel('日期')
            plt.ylabel('回撤(%)')
            plt.grid(True)
            
            # 保存图表
            plt.savefig(os.path.join(report_dir, 'drawdown.png'))
            plt.close()
            
        except Exception as e:
            self.logger.error(f"绘制回撤曲线失败: {str(e)}")
            
    def _plot_monthly_returns(self, results: Dict, report_dir: str):
        """
        绘制月度收益热力图
        
        Args:
            results: 回测结果
            report_dir: 报告目录
        """
        try:
            equity_curve = pd.DataFrame(results['equity_curve'])
            equity_curve['date'] = pd.to_datetime(equity_curve['date'])
            equity_curve.set_index('date', inplace=True)
            
            # 计算月度收益
            monthly_returns = equity_curve['equity'].resample('ME').last().pct_change()
            monthly_returns = monthly_returns.to_frame()
            monthly_returns.columns = ['returns']
            
            # 检查数据量是否足够生成热力图
            if len(monthly_returns) < 2:
                self.logger.warning("数据量不足，无法生成月度收益热力图")
                # 生成简单的月度收益柱状图
                plt.figure(figsize=(12, 6))
                monthly_returns['returns'].plot(kind='bar')
                plt.title('月度收益')
                plt.xlabel('月份')
                plt.ylabel('收益率(%)')
                plt.grid(True)
                plt.xticks(rotation=45)
            else:
                # 创建月度收益矩阵
                num_months = len(monthly_returns)
                num_years = (num_months + 11) // 12  # 向上取整
                
                # 填充数据
                returns_data = monthly_returns['returns'].values
                padding_size = num_years * 12 - len(returns_data)
                if padding_size > 0:
                    returns_data = np.pad(returns_data, (0, padding_size), 'constant', constant_values=np.nan)
                
                returns_matrix = returns_data.reshape(num_years, 12)
                returns_matrix = pd.DataFrame(returns_matrix)
                
                plt.figure(figsize=(12, 8))
                sns.heatmap(returns_matrix, annot=True, fmt='.2%', cmap='RdYlGn', 
                          mask=np.isnan(returns_matrix))
                plt.title('月度收益热力图')
                plt.xlabel('月份')
                plt.ylabel('年份')
                    
                    # 保存图表
            plt.savefig(os.path.join(report_dir, 'monthly_returns.png'))
                    plt.close()
                    
        except Exception as e:
            self.logger.error(f"绘制月度收益热力图失败: {str(e)}")
            
    def _plot_trade_distribution(self, results: Dict, report_dir: str):
        """
        绘制交易分布图
        
        Args:
            results: 回测结果
            report_dir: 报告目录
        """
        try:
            trades = pd.DataFrame(results['trades'])
            
            # 绘制收益分布直方图
            plt.figure(figsize=(12, 6))
            plt.hist(trades['pnl_pct'] * 100, bins=50)
            plt.title('交易收益分布')
            plt.xlabel('收益率(%)')
            plt.ylabel('频次')
            plt.grid(True)
                    
                    # 保存图表
            plt.savefig(os.path.join(report_dir, 'trade_distribution.png'))
                    plt.close()
            
        except Exception as e:
            self.logger.error(f"绘制交易分布图失败: {str(e)}")
            
    def _generate_html_report(self, results: Dict, strategy_name: str, 
                            report_dir: str) -> str:
        """
        生成HTML报告
        
        Args:
            results: 回测结果
            strategy_name: 策略名称
            report_dir: 报告目录
            
        Returns:
            HTML报告文件路径
        """
        try:
            metrics = results['metrics']
            
            # 生成HTML内容
            html_content = f"""
        <!DOCTYPE html>
            <html>
        <head>
            <meta charset="UTF-8">
                <title>{strategy_name} 回测报告</title>
            <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .container {{ max-width: 1200px; margin: 0 auto; }}
                    .section {{ margin-bottom: 30px; }}
                    .metric {{ margin-bottom: 10px; }}
                    .chart {{ margin: 20px 0; }}
                    table {{ border-collapse: collapse; width: 100%; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                    th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="container">
                    <h1>{strategy_name} 回测报告</h1>
                    <div class="section">
                        <h2>策略表现指标</h2>
                        <div class="metric">总收益率: {metrics['total_return']:.2%}</div>
                        <div class="metric">夏普比率: {metrics['sharpe_ratio']:.2f}</div>
                        <div class="metric">最大回撤: {metrics['max_drawdown']:.2%}</div>
                        <div class="metric">胜率: {metrics['win_rate']:.2%}</div>
                        <div class="metric">盈亏比: {metrics['profit_factor']:.2f}</div>
                        <div class="metric">总交易次数: {metrics['total_trades']}</div>
                    </div>
                    
                    <div class="section">
                        <h2>权益曲线</h2>
                        <div class="chart">
                            <img src="equity_curve.png" alt="权益曲线" style="width: 100%;">
                                </div>
                            </div>
                            
                    <div class="section">
                        <h2>回撤分析</h2>
                        <div class="chart">
                            <img src="drawdown.png" alt="回撤曲线" style="width: 100%;">
                                </div>
                            </div>
                            
                    <div class="section">
                        <h2>月度收益分析</h2>
                        <div class="chart">
                            <img src="monthly_returns.png" alt="月度收益热力图" style="width: 100%;">
                                </div>
                            </div>
                    
                    <div class="section">
                        <h2>交易分布</h2>
                        <div class="chart">
                            <img src="trade_distribution.png" alt="交易分布" style="width: 100%;">
                        </div>
                    </div>
                </div>
            </body>
            </html>
            """
            
            # 保存HTML文件
            html_path = os.path.join(report_dir, 'report.html')
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
                
            return html_path
            
        except Exception as e:
            self.logger.error(f"生成HTML报告失败: {str(e)}")
            return ''

if __name__ == "__main__":
    # 测试数据
    test_results = {
        'equity_curve': [
            {'date': '2024-01-01', 'equity': 10000},
            {'date': '2024-01-02', 'equity': 10500},
            {'date': '2024-01-03', 'equity': 10300},
            {'date': '2024-01-04', 'equity': 10800},
            {'date': '2024-01-05', 'equity': 11000}
        ],
        'trades': [
            {'entry_time': '2024-01-01', 'exit_time': '2024-01-02', 'pnl_pct': 0.05},
            {'entry_time': '2024-01-02', 'exit_time': '2024-01-03', 'pnl_pct': -0.02},
            {'entry_time': '2024-01-03', 'exit_time': '2024-01-04', 'pnl_pct': 0.048},
            {'entry_time': '2024-01-04', 'exit_time': '2024-01-05', 'pnl_pct': 0.019}
        ],
        'metrics': {
            'total_return': 0.10,
            'sharpe_ratio': 2.5,
            'max_drawdown': 0.02,
            'win_rate': 0.75,
            'profit_factor': 2.1,
            'total_trades': 4
        }
    }
    
    # 测试报告生成
    print("CRYPTO DATA COLLECTOR SCRIPT STARTED - TEST PRINT 123")
    generator = ReportGenerator()
    report_path = generator.generate_backtest_report(test_results, '测试策略')
    print(f"报告已生成: {report_path}") 