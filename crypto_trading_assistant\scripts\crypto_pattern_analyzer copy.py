"""
加密货币形态分析模块
专门用于分析新上市币种的放量上影线形态和反转特征
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import sys
import os
import time
from tqdm import tqdm
import requests
import ccxt
import argparse
import json
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich import box
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('crypto_pattern_analyzer.log', mode='w')
    ]
)

logger = logging.getLogger(__name__)

# 添加控制台处理器
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
logger.addHandler(console_handler)

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from scripts.crypto_technical_analyzer import TechnicalAnalyzer
from scripts.crypto_exchange_data import ExchangeDataCollector

@dataclass
class ExchangeConfig:
    """交易所配置"""
    name: str
    api_base_url: str
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    use_proxy: bool = False
    request_delay: float = 1.0

@dataclass
class PatternParameters:
    """形态识别参数"""
    volume_ma_period: int = 10  # 成交量均线周期
    volume_threshold: float = 1.2  # 放量倍数阈值
    shadow_ratio_threshold: float = 1.0  # 上影线比例阈值
    body_ratio_threshold: float = 0.1  # 实体比例阈值
    reversal_days: int = 10  # 反转观察天数
    reversal_threshold: float = 0.05  # 反转幅度阈值
    listing_days: int = 60  # 新上市天数阈值

class PatternAnalyzer:
    def __init__(self, 
                 api_keys: Optional[Dict] = None,
                 use_proxy: bool = False,
                 request_delay: float = 1.0):
        """
        初始化模式分析器
        
        Args:
            api_keys: API密钥字典
            use_proxy: 是否使用代理
            request_delay: 请求延迟（秒）
        """
        self.logger = logging.getLogger(__name__)
        self.use_proxy = use_proxy
        self.request_delay = request_delay
        
        # 初始化交易所API
        self.exchange = ccxt.gateio({
            'enableRateLimit': True,
            'timeout': 60000,  # 增加超时时间到60秒
            'options': {
                'defaultType': 'spot',
                'fetchOHLCV': {
                    'limit': 1000,
                    'timeout': 60000
                }
            },
            'rateLimit': 1000,  # 限制请求频率为每秒1次
            'verbose': True  # 启用详细日志
        })
        
        # 初始化代理设置
        if use_proxy:
            self.exchange.proxies = {
                'http': 'http://127.0.0.1:7890',
                'https': 'http://127.0.0.1:7890'
            }
        
        # 初始化技术分析器
        self.technical_analyzer = TechnicalAnalyzer()
        
        # 设置重试次数和延迟
        self.max_retries = 3
        self.retry_delay = 5  # 秒
        
        # 初始化rich控制台
        self.console = Console()
        
        # 创建结果保存目录
        self.results_dir = Path("analysis_results")
        self.results_dir.mkdir(exist_ok=True)
        
        # 加载SERVER酱配置
        try:
            with open('scripts/config.json', 'r') as f:
                self.config = json.load(f)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {str(e)}")
            self.config = {"wechat": {"send_keys": []}}

    def get_new_listings(self, max_listings: int = 0) -> Dict:
        """
        获取从60天前到今天的新上市币种信息
        
        Args:
            max_listings: 最大获取数量，大于0时获取到指定数量后立即返回，否则获取全部符合条件的币种
        
        Returns:
            新上市币种信息字典
        """
        try:
            self.logger.info("正在获取所有交易对信息...")
            # 获取所有交易对
            markets = self.exchange.load_markets()
            if not markets:
                self.logger.error("未能获取到交易对信息")
                return {}
            
            self.logger.info(f"成功获取{len(markets)}个交易对")
            
            # 获取新币上线时间
            new_listings = {}
            current_date = datetime.now()
            end_date = current_date
            start_date = end_date - timedelta(days=60)  # 60天前的日期
            
            self.logger.info(f"开始日期: {start_date.strftime('%Y-%m-%d')}")
            self.logger.info(f"结束日期: {end_date.strftime('%Y-%m-%d')}")
            
            # 只处理USDT交易对，并按上市时间排序
            usdt_markets = []
            for symbol, market in markets.items():
                if symbol.endswith('USDT'):
                    try:
                        # 从市场信息中获取上市时间
                        if 'info' in market and 'buy_start' in market['info']:
                            listing_time = int(market['info']['buy_start'])
                            if listing_time == 0:  # 如果buy_start为0，尝试使用sell_start
                                listing_time = int(market['info'].get('sell_start', 0))
                            
                            if listing_time > 0:  # 只处理有效的时间戳
                                listing_date = datetime.fromtimestamp(listing_time)
                                
                                # 只保留在时间范围内的币种
                                if start_date <= listing_date <= end_date:
                                    usdt_markets.append((symbol, market, listing_date))
                                    self.logger.info(f"找到符合条件的币种: {symbol}, 上市时间: {listing_date.strftime('%Y-%m-%d')}")
                    except Exception as e:
                        self.logger.warning(f"处理{symbol}的上市时间时出错: {str(e)}")
                        continue
            
            # 按上市时间排序
            usdt_markets.sort(key=lambda x: x[2], reverse=True)
            
            self.logger.info(f"找到 {len(usdt_markets)} 个符合条件的USDT交易对")
            
            # 处理符合条件的交易对
            for symbol, market, listing_date in usdt_markets:
                try:
                    self.logger.info(f"\n处理: {symbol}")
                    self.logger.info(f"上市时间: {listing_date.strftime('%Y-%m-%d')}")
                    
                    # 获取K线数据验证
                    try:
                        ohlcv = self.exchange.fetch_ohlcv(symbol, '1d', limit=1)
                        if not ohlcv or len(ohlcv) == 0:
                            self.logger.warning(f"{symbol} 没有K线数据，跳过")
                            continue
                            
                        # 进行形态分析
                        analysis_result = self.analyze_pattern(symbol, listing_date)
                        
                        new_listings[symbol] = {
                            'listing_date': listing_date.strftime('%Y-%m-%d'),
                            'base_currency': market['base'],
                            'quote_currency': market['quote'],
                            'days_since_listing': (current_date - listing_date).days,
                            'analysis': analysis_result
                        }
                        
                        # 打印分析结果
                        self.logger.info("\n形态分析结果:")
                        self.logger.info(f"24小时价格变化: {analysis_result.get('price_change_24h', 0):.2f}%")
                        self.logger.info(f"24小时成交量变化: {analysis_result.get('volume_change_24h', 0):.2f}%")
                        self.logger.info(f"MA5趋势: {analysis_result.get('ma5_trend', 'unknown')}")
                        self.logger.info(f"MA10趋势: {analysis_result.get('ma10_trend', 'unknown')}")
                        self.logger.info(f"成交量趋势: {analysis_result.get('volume_trend', 'unknown')}")
                        self.logger.info(f"波动率: {analysis_result.get('volatility', 0):.2f}%")
                        self.logger.info(f"上影线比例: {analysis_result.get('upper_shadow', 0):.2f}%")
                        self.logger.info(f"下影线比例: {analysis_result.get('lower_shadow', 0):.2f}%")
                        self.logger.info(f"实体大小: {analysis_result.get('body_size', 0):.2f}%")
                        self.logger.info(f"K线形态: {analysis_result.get('pattern', '无明显形态')}")
                        
                        # 如果设置了最大获取数量，且已达到该数量，则立即返回
                        if max_listings > 0 and len(new_listings) >= max_listings:
                            self.logger.info(f"已达到最大获取数量 {max_listings}，停止获取")
                            return new_listings
                            
                    except Exception as e:
                        self.logger.warning(f"获取{symbol}的K线数据失败: {str(e)}")
                        continue
                        
                    # 避免请求过于频繁
                    time.sleep(3)
                    
                except Exception as e:
                    self.logger.warning(f"处理{symbol}时发生错误: {str(e)}")
                    continue
                
            self.logger.info(f"处理完成，共找到 {len(new_listings)} 个新上市币种")
            return new_listings
            
        except Exception as e:
            self.logger.error(f"获取新上市币种信息失败: {str(e)}")
            return {}

    def _get_listing_date(self, market, current_date):
        """
        获取币种的上市日期
        
        Args:
            market: 交易对信息
            current_date: 当前日期
            
        Returns:
            上市日期
        """
        try:
            ohlcv = self.exchange.fetch_ohlcv(market['symbol'], '1d', limit=1)
            if ohlcv:
                return datetime.fromtimestamp(ohlcv[0][0] / 1000)
        except Exception:
            pass
        return current_date

    def backtest(self, symbol: str, start_date: str, end_date: str) -> Dict:
        """
        回测指定币种的交易策略
        
        Args:
            symbol: 交易对符号
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            回测结果字典
        """
        try:
            # 获取历史数据
            retries = 0
            while retries < self.max_retries:
                try:
                    # 获取K线数据
                    ohlcv = self.exchange.fetch_ohlcv(symbol, '1d', limit=200)
                    if not ohlcv:
                        raise Exception("未获取到K线数据")
                        
                    # 转换为DataFrame
                    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    
                    # 计算技术指标
                    df = self.technical_analyzer.calculate_indicators(df)
                    
                    # 识别上影线模式
                    signals = self.technical_analyzer.identify_upper_shadow(df)
                    
                    # 计算回测结果
                    results = self._calculate_backtest_results(df, signals)
                    
                    return results
                    
                except Exception as e:
                    retries += 1
                    self.logger.error(f"回测失败 (尝试 {retries}/{self.max_retries}): {str(e)}")
                    if retries < self.max_retries:
                        time.sleep(self.retry_delay)
                    else:
                        raise Exception("回测失败，已达到最大重试次数")
                        
        except Exception as e:
            self.logger.error(f"回测{symbol}失败: {str(e)}")
            raise

    def test_exchange_connection(self):
        """测试交易所API连接"""
        try:
            self.logger.info("正在测试交易所API连接...")
            
            # 测试获取交易对
            self.logger.info("1. 测试获取交易对...")
            markets = self.exchange.load_markets()
            self.logger.info(f"成功获取交易对，总数: {len(markets)}")
            
            # 测试获取K线数据
            self.logger.info("\n2. 测试获取K线数据...")
            symbol = "BTC/USDT"
            self.logger.info(f"使用测试交易对: {symbol}")
            ohlcv = self.exchange.fetch_ohlcv(symbol, '1d', limit=1)
            self.logger.info(f"成功获取K线数据: {ohlcv}")
            
            self.logger.info("\n交易所API连接测试成功!")
            return True
            
        except Exception as e:
            self.logger.error(f"交易所API连接测试失败: {str(e)}")
            self.logger.error(f"错误详情: {type(e).__name__}: {str(e)}")
            return False

    def save_analysis_results(self, results: Dict, symbol: str):
        """保存分析结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = self.results_dir / f"{symbol}_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            self.logger.info(f"分析结果已保存到: {filename}")
        except Exception as e:
            self.logger.error(f"保存分析结果失败: {str(e)}")

    def send_wechat_notification(self, title: str, content: str):
        """发送微信通知"""
        if not self.config.get("wechat", {}).get("send_keys"):
            self.logger.warning("未配置SERVER酱API密钥")
            return
            
        for send_key in self.config["wechat"]["send_keys"]:
            try:
                url = f"https://sctapi.ftqq.com/{send_key}.send"
                data = {
                    "title": title,
                    "desp": content
                }
                response = requests.post(url, data=data)
                if response.status_code == 200:
                    self.logger.info("微信通知发送成功")
                else:
                    self.logger.error(f"微信通知发送失败: {response.text}")
            except Exception as e:
                self.logger.error(f"发送微信通知时出错: {str(e)}")

    def format_analysis_report(self, symbol: str, analysis: Dict) -> str:
        """格式化分析报告"""
        # 创建rich表格
        table = Table(title=f"\n{symbol} 技术分析报告", box=box.ROUNDED)
        
        # 添加列
        table.add_column("指标", style="cyan")
        table.add_column("数值", style="green")
        
        # 添加基本信息
        table.add_row("当前价格", f"{analysis['当前价格']:.4f} USDT")
        table.add_row("24h涨跌幅", f"{analysis['24h涨跌幅']:.2f}%")
        table.add_row("24h成交量变化", f"{analysis['24h成交量变化']:.2f}%")
        
        # 添加趋势信息
        table.add_row("MA5趋势", analysis['MA5趋势'])
        table.add_row("MA10趋势", analysis['MA10趋势'])
        table.add_row("成交量趋势", analysis['成交量趋势'])
        
        # 添加技术指标
        table.add_row("波动率", f"{analysis['波动率']:.2f}%")
        table.add_row("上影线比例", f"{analysis['上影线比例']:.2f}%")
        table.add_row("下影线比例", f"{analysis['下影线比例']:.2f}%")
        table.add_row("实体大小", f"{analysis['实体大小']:.2f}%")
        
        # 添加形态和风险信息
        table.add_row("K线形态", analysis['K线形态'])
        table.add_row("风险等级", analysis['风险等级'])
        table.add_row("交易建议", analysis['交易建议'])
        
        # 使用rich渲染表格
        console = Console()
        report = console.render(table)
        
        # 添加时间戳
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        report = f"分析时间: {timestamp}\n{report}"
        
        return report

    def analyze_pattern(self, pair, listing_date):
        """
        分析指定交易对的K线形态
        :param pair: 交易对
        :param listing_date: 上市日期
        :return: 分析结果
        """
        try:
            # 获取最近1天的K线数据
            klines_1d = self.exchange.fetch_ohlcv(pair, '1d', limit=1)
            if not klines_1d:
                self.logger.warning(f"无法获取{pair}的K线数据")
                return self._get_default_analysis()

            # 获取最近30天的K线数据用于计算趋势
            klines_30d = self.exchange.fetch_ohlcv(pair, '1d', limit=30)
            
            # 将K线数据转换为DataFrame
            df = pd.DataFrame(klines_30d, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            # 计算基本指标
            current_price = float(df['close'].iloc[-1])
            prev_price = float(df['close'].iloc[-2]) if len(df) > 1 else current_price
            current_volume = float(df['volume'].iloc[-1])
            prev_volume = float(df['volume'].iloc[-2]) if len(df) > 1 else current_volume
            
            # 计算价格和成交量变化
            price_change = ((current_price - prev_price) / prev_price * 100) if prev_price != 0 else 0
            volume_change = ((current_volume - prev_volume) / prev_volume * 100) if prev_volume != 0 else 0
            
            # 计算移动平均线
            if len(df) >= 5:
                df['MA5'] = df['close'].rolling(window=5).mean()
                ma5_trend = self._determine_trend(df['MA5'].iloc[-2:])
            else:
                ma5_trend = "unknown"
                
            if len(df) >= 10:
                df['MA10'] = df['close'].rolling(window=10).mean()
                ma10_trend = self._determine_trend(df['MA10'].iloc[-2:])
            else:
                ma10_trend = "unknown"
                
            # 计算成交量趋势
            volume_trend = self._determine_trend(df['volume'].iloc[-2:]) if len(df) > 1 else "unknown"
            
            # 计算波动率
            latest_candle = df.iloc[-1]
            volatility = (latest_candle['high'] - latest_candle['low']) / latest_candle['low'] * 100
            
            # 计算影线比例和实体大小
            body_size = abs(latest_candle['close'] - latest_candle['open'])
            upper_shadow = latest_candle['high'] - max(latest_candle['open'], latest_candle['close'])
            lower_shadow = min(latest_candle['open'], latest_candle['close']) - latest_candle['low']
            
            upper_shadow_ratio = (upper_shadow / latest_candle['close'] * 100) if latest_candle['close'] != 0 else 0
            lower_shadow_ratio = (lower_shadow / latest_candle['close'] * 100) if latest_candle['close'] != 0 else 0
            body_size_ratio = (body_size / latest_candle['close'] * 100) if latest_candle['close'] != 0 else 0
            
            # 识别K线形态
            pattern = self._identify_candlestick_pattern(latest_candle)
            
            # 格式化分析结果
            analysis_result = {
                "当前价格": current_price,
                "24h涨跌幅": price_change,
                "24h成交量变化": volume_change,
                "MA5趋势": ma5_trend,
                "MA10趋势": ma10_trend,
                "成交量趋势": volume_trend,
                "波动率": volatility,
                "上影线比例": upper_shadow_ratio,
                "下影线比例": lower_shadow_ratio,
                "实体大小": body_size_ratio,
                "K线形态": pattern,
                "风险等级": self._evaluate_risk(volatility, volume_change, price_change),
                "交易建议": self._generate_trade_signal(pattern, price_change, volume_change, volatility)
            }
            
            # 保存分析结果
            self.save_analysis_results(analysis_result, pair)
            
            # 生成美化后的报告
            report = self.format_analysis_report(pair, analysis_result)
            
            # 发送微信通知
            self.send_wechat_notification(
                title=f"加密货币分析报告 - {pair}",
                content=report
            )
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"分析{pair}时发生错误: {str(e)}")
            return self._get_default_analysis()
            
    def _determine_trend(self, data):
        """
        判断趋势
        """
        if len(data) < 2:
            return "unknown"
        change = (data.iloc[-1] - data.iloc[0]) / data.iloc[0] * 100
        if change > 1:
            return "上涨"
        elif change < -1:
            return "下跌"
        else:
            return "横盘"
            
    def _identify_candlestick_pattern(self, candle):
        """
        识别K线形态
        """
        open_price = candle['open']
        close = candle['close']
        high = candle['high']
        low = candle['low']
        
        body = abs(close - open_price)
        upper_shadow = high - max(open_price, close)
        lower_shadow = min(open_price, close) - low
        
        # 判断是否为十字星
        if body / (high - low) < 0.1:
            return "十字星"
            
        # 判断是否为锤子线
        if lower_shadow > 2 * body and upper_shadow < body:
            return "锤子线"
            
        # 判断是否为上吊线
        if upper_shadow > 2 * body and lower_shadow < body:
            return "上吊线"
            
        # 判断是否为看涨实体
        if close > open_price and body > (high - low) * 0.6:
            return "看涨实体"
            
        # 判断是否为看跌实体
        if close < open_price and body > (high - low) * 0.6:
            return "看跌实体"
            
        return "无明显形态"
        
    def _evaluate_risk(self, volatility, volume_change, price_change):
        """
        评估风险等级
        """
        risk_score = 0
        
        # 根据波动率评估风险
        if volatility > 20:
            risk_score += 3
        elif volatility > 10:
            risk_score += 2
        elif volatility > 5:
            risk_score += 1
            
        # 根据成交量变化评估风险
        if abs(volume_change) > 100:
            risk_score += 2
        elif abs(volume_change) > 50:
            risk_score += 1
            
        # 根据价格变化评估风险
        if abs(price_change) > 10:
            risk_score += 2
        elif abs(price_change) > 5:
            risk_score += 1
            
        # 返回风险等级
        if risk_score >= 6:
            return "高风险"
        elif risk_score >= 3:
            return "中等风险"
        else:
            return "低风险"
            
    def _generate_trade_signal(self, pattern, price_change, volume_change, volatility):
        """
        生成交易信号
        """
        if pattern in ["锤子线", "看涨实体"] and price_change > 0 and volume_change > 0:
            return "可以考虑买入"
        elif pattern in ["上吊线", "看跌实体"] or (price_change < -5 and volume_change > 50):
            return "建议观望"
        elif volatility > 15:
            return "波动较大，建议谨慎"
        else:
            return "需要更多观察"
            
    def _get_default_analysis(self):
        """
        返回默认的分析结果
        """
        return {
            "当前价格": 0,
            "24h涨跌幅": 0,
            "24h成交量变化": 0,
            "MA5趋势": "unknown",
            "MA10趋势": "unknown",
            "成交量趋势": "unknown",
            "波动率": 0,
            "上影线比例": 0,
            "下影线比例": 0,
            "实体大小": 0,
            "K线形态": "无明显形态",
            "风险等级": "未知",
            "交易建议": "数据不足"
        }

    def format_summary_report(self, new_listings: Dict) -> str:
        """格式化汇总报告"""
        # 创建rich表格
        table = Table(title="\n新上市币种分析汇总", box=box.ROUNDED)
        
        # 添加列
        table.add_column("交易对", style="cyan")
        table.add_column("上市日期", style="green")
        table.add_column("当前价格", style="yellow")
        table.add_column("24h涨跌幅", style="red")
        table.add_column("风险等级", style="magenta")
        table.add_column("交易建议", style="blue")
        
        # 添加行
        for symbol, info in new_listings.items():
            analysis = info['analysis']
            table.add_row(
                symbol,
                info['listing_date'],
                f"{analysis['当前价格']:.4f}",
                f"{analysis['24h涨跌幅']:.2f}%",
                analysis['风险等级'],
                analysis['交易建议']
            )
        
        # 使用rich渲染表格
        console = Console()
        report = console.render(table)
        
        # 添加时间戳和统计信息
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        total_pairs = len(new_listings)
        report = f"""# 加密货币新上市币种分析报告
分析时间: {timestamp}
分析币种数量: {total_pairs}

{report}

## 风险提示
1. 新上市币种波动较大，请谨慎投资
2. 建议设置止损位，控制风险
3. 关注项目基本面和市场情绪变化
4. 杠杆代币风险较高，不建议长期持有

## 免责声明
本分析报告仅供参考，不构成投资建议。投资有风险，入市需谨慎。"""
        
        return report

def test_real_data(max_listings: int = 0):
    """使用真实数据进行测试"""
    try:
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('crypto_pattern_analyzer.log', mode='w')
            ]
        )
        logger = logging.getLogger(__name__)
        
        logger.info("开始运行加密货币形态分析...")
        
        # 加载API密钥
        try:
            import json
            with open('api_keys.json', 'r') as f:
                api_keys = json.load(f)
                logger.info("成功加载API密钥")
        except Exception as e:
            logger.warning(f"无法加载API密钥: {str(e)}")
            api_keys = {}
            
        # 创建分析器实例
        logger.info("初始化模式分析器...")
        analyzer = PatternAnalyzer(api_keys=api_keys)
        
        # 首先测试交易所API连接
        logger.info("测试交易所API连接...")
        if not analyzer.test_exchange_connection():
            logger.error("交易所API连接测试失败，退出程序")
            return
            
        logger.info("API连接测试成功，开始获取交易对信息...")
        
        # 获取新上市币种
        logger.info("\n开始获取新上市币种信息...")
        try:
            new_listings = analyzer.get_new_listings(max_listings=max_listings)
            
            if not new_listings:
                logger.warning("未找到新上市币种")
                return
            
            # 生成汇总报告
            summary_report = analyzer.format_summary_report(new_listings)
            
            # 打印汇总报告
            console = Console()
            console.print(summary_report)
            
            # 保存汇总结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            summary_file = Path("analysis_results") / f"summary_{timestamp}.json"
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(new_listings, f, ensure_ascii=False, indent=2)
            
            # 发送微信通知
            analyzer.send_wechat_notification(
                title=f"加密货币新上市币种分析报告 - {timestamp}",
                content=summary_report
            )
            
            logger.info(f"\n分析完成，汇总结果已保存到: {summary_file}")
            
        except Exception as e:
            logger.error(f"获取新上市币种时发生错误: {str(e)}")
            import traceback
            logger.error(f"错误详情:\n{traceback.format_exc()}")
            
    except Exception as e:
        logger.error(f"程序运行出错: {str(e)}")
        logger.error(f"错误详情: {type(e).__name__}: {str(e)}")
        import traceback
        logger.error(f"堆栈跟踪:\n{traceback.format_exc()}")

def print_results(results: Dict):
    """打印回测结果"""
    if results:
        print(f"总信号数: {results['total_signals']}")
        print(f"成功信号数: {results['successful_signals']}")
        print(f"胜率: {results['win_rate']:.2%}")
        print(f"平均收益: {results['avg_gain']:.2%}")
        print(f"平均亏损: {results['avg_loss']:.2%}")
        print(f"盈亏比: {results['profit_factor']:.2f}")
        print(f"平均风险收益比: {results['avg_risk_reward']:.2f}")
        
        # 显示最近的信号
        if results['signals']:
            print("\n最近的信号:")
            for signal in results['signals'][-3:]:  # 显示最后3个信号
                print(f"\n日期: {signal['date']}")
                print(f"价格: {signal['price']:.2f}")
                print(f"成交量比例: {signal['volume_ratio']:.2f}")
                print(f"上影线比例: {signal['shadow_ratio']:.2f}")
                print(f"信号强度: {signal['signal_strength']:.2f}")
                print(f"趋势: {signal['trend']:.2%}")
                print(f"波动率: {signal['volatility']:.2%}")
                if 'reversal_occurred' in signal:
                    print(f"是否发生反转: {'是' if signal['reversal_occurred'] else '否'}")
                    print(f"最大收益: {signal['max_gain']:.2%}")
                    print(f"最大亏损: {signal['max_loss']:.2%}")
                    print(f"风险收益比: {signal['risk_reward_ratio']:.2f}")
    else:
        print("回测失败")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='加密货币形态分析')
    parser.add_argument('--max-listings', type=int, default=0, help='最大获取新上市币种数量，0表示获取全部')
    args = parser.parse_args()
    test_real_data(max_listings=args.max_listings)