#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试主脚本的非交互运行
"""

import sys
import os
from datetime import datetime

def log(message):
    """日志函数"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def test_main_script():
    """测试主脚本"""
    log("🔍 开始测试主脚本...")
    
    try:
        # 添加路径
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # 导入主类
        from advanced_crypto_scraper import AdvancedCryptoScraper
        log("✅ AdvancedCryptoScraper 类导入成功")
        
        # 创建实例（不使用代理）
        log("🔄 创建实例...")
        scraper = AdvancedCryptoScraper(use_proxy=False)
        log("✅ 实例创建成功")
        
        # 测试一些基本方法
        log("🔄 测试基本方法...")
        
        # 测试速度模式显示
        scraper.show_speed_modes()
        log("✅ 速度模式显示正常")
        
        # 测试统计信息
        scraper.print_statistics()
        log("✅ 统计信息显示正常")
        
        # 测试连接测试
        scraper.test_connection()
        log("✅ 连接测试完成")
        
        log("✅ 主脚本测试完成")
        return True
        
    except Exception as e:
        log(f"❌ 主脚本测试失败: {e}")
        import traceback
        log(f"📍 错误详情: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    test_main_script()
