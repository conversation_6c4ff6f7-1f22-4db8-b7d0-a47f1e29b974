"""
数字货币选币系统使用示例
展示如何使用Qlib框架进行技术分析选币
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import qlib
from qlib.data import D
from qlib.config import REG_CN
import warnings
warnings.filterwarnings('ignore')

from crypto_stock_picker import CryptoStockPicker
from crypto_alpha_expressions import CRYPTO_ALPHA_EXPRESSIONS


class CryptoSelectionExample:
    """数字货币选币示例"""
    
    def __init__(self):
        """初始化"""
        # 初始化Qlib (需要根据实际情况配置)
        # qlib.init(provider_uri="path_to_your_data", region=REG_CN)
        
        # 选币器配置
        self.config = {
            'min_score': 3.0,
            'max_selections': 10,
            'timeframes': ['1d', '4h', '1h'],
            'weights': {
                'pattern': 0.3,
                'indicator': 0.4,
                'trend': 0.2,
                'volume': 0.1
            }
        }
        
        # 初始化选币器
        self.picker = CryptoStockPicker(self.config)
        
        # 数字货币代码列表 (示例)
        self.crypto_symbols = [
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'XRPUSDT',
            'SOLUSDT', 'DOTUSDT', 'DOGEUSDT', 'AVAXUSDT', 'SHIBUSDT',
            'MATICUSDT', 'LTCUSDT', 'UNIUSDT', 'LINKUSDT', 'ATOMUSDT',
            'ETCUSDT', 'XLMUSDT', 'BCHUSDT', 'FILUSDT', 'TRXUSDT'
        ]
    
    def run_daily_selection(self):
        """运行日常选币"""
        print("=" * 60)
        print("数字货币技术分析选币系统")
        print("=" * 60)
        
        try:
            # 1. 单时间周期选币 (日线)
            print("\n1. 日线选币结果:")
            print("-" * 40)
            
            daily_results = self.picker.select_stocks(
                symbols=self.crypto_symbols,
                timeframe='1d'
            )
            
            self._display_selection_results(daily_results)
            
            # 2. 多时间周期分析 (针对高分标的)
            if daily_results:
                print("\n2. 多时间周期确认分析:")
                print("-" * 40)
                
                top_symbols = [result['symbol'] for result in daily_results[:5]]
                
                for symbol in top_symbols:
                    print(f"\n分析 {symbol}:")
                    multi_tf_result = self.picker.multi_timeframe_analysis(symbol)
                    self._display_multi_timeframe_result(symbol, multi_tf_result)
            
            # 3. 生成交易建议
            print("\n3. 交易建议:")
            print("-" * 40)
            self._generate_trading_suggestions(daily_results)
            
        except Exception as e:
            print(f"选币过程出错: {e}")
    
    def _display_selection_results(self, results):
        """显示选股结果"""
        if not results:
            print("未找到符合条件的标的")
            return
        
        print(f"{'排名':<4} {'代码':<12} {'总分':<6} {'形态':<6} {'指标':<6} {'趋势':<6} {'成交量':<8} {'当前价格':<12} {'RSI':<6} {'信号'}")
        print("-" * 100)
        
        for i, result in enumerate(results, 1):
            signals_str = ', '.join(result['signals'][:2])  # 只显示前2个信号
            if len(result['signals']) > 2:
                signals_str += '...'
            
            print(f"{i:<4} {result['symbol']:<12} {result['score']:<6.1f} "
                  f"{result['pattern_score']:<6.1f} {result['indicator_score']:<6.1f} "
                  f"{result['trend_score']:<6.1f} {result['volume_score']:<8.1f} "
                  f"{result['current_price']:<12.4f} {result['rsi']:<6.1f} {signals_str}")
    
    def _display_multi_timeframe_result(self, symbol, result):
        """显示多时间周期结果"""
        if not result:
            print(f"  {symbol}: 无数据")
            return
        
        print(f"  综合得分: {result.get('multi_tf_score', 0):.2f}")
        
        for tf in ['1d', '4h', '1h']:
            if tf in result:
                tf_data = result[tf]
                print(f"    {tf:>3}: 得分 {tf_data.get('score', 0):.1f}, "
                      f"RSI {tf_data.get('rsi', 0):.1f}, "
                      f"信号 {len(tf_data.get('signals', []))}")
        
        confirmation = result.get('confirmation', {})
        print(f"  趋势一致: {'✓' if confirmation.get('trend_alignment') else '✗'}, "
              f"信号一致: {'✓' if confirmation.get('signal_consistency') else '✗'}, "
              f"风险等级: {confirmation.get('risk_level', 'unknown')}")
    
    def _generate_trading_suggestions(self, results):
        """生成交易建议"""
        if not results:
            print("暂无交易建议")
            return
        
        # 按风险等级分类
        high_confidence = []
        medium_confidence = []
        low_confidence = []
        
        for result in results:
            score = result['score']
            rsi = result['rsi']
            
            # 简单的风险评估逻辑
            if score >= 5 and 30 <= rsi <= 70:
                high_confidence.append(result)
            elif score >= 3.5:
                medium_confidence.append(result)
            else:
                low_confidence.append(result)
        
        if high_confidence:
            print("\n高信心度标的 (建议重点关注):")
            for result in high_confidence[:3]:
                print(f"  {result['symbol']}: 得分 {result['score']:.1f}, "
                      f"当前价 {result['current_price']:.4f}, "
                      f"主要信号: {', '.join(result['signals'][:2])}")
        
        if medium_confidence:
            print("\n中等信心度标的 (可适量配置):")
            for result in medium_confidence[:3]:
                print(f"  {result['symbol']}: 得分 {result['score']:.1f}, "
                      f"当前价 {result['current_price']:.4f}")
        
        # 风险提示
        print("\n风险提示:")
        print("- 技术分析仅供参考，不构成投资建议")
        print("- 数字货币市场波动较大，请控制仓位")
        print("- 建议结合基本面分析和市场情绪")
        print("- 设置止损位，严格执行风险管理")
    
    def demonstrate_alpha_expressions(self):
        """演示Alpha表达式的使用"""
        print("\n" + "=" * 60)
        print("Qlib Alpha表达式示例")
        print("=" * 60)
        
        # 展示一些关键的Alpha表达式
        key_expressions = {
            '锤子线形态': CRYPTO_ALPHA_EXPRESSIONS['hammer_pattern'],
            'RSI超卖回升': CRYPTO_ALPHA_EXPRESSIONS['rsi_oversold_recovery'],
            'MACD金叉': CRYPTO_ALPHA_EXPRESSIONS['macd_golden_cross'],
            '强势突破': CRYPTO_ALPHA_EXPRESSIONS['strong_breakout_stock']
        }
        
        for name, expression in key_expressions.items():
            print(f"\n{name}:")
            print("-" * 30)
            # 清理表达式格式以便显示
            clean_expr = expression.strip().replace('\n', ' ').replace('  ', ' ')
            if len(clean_expr) > 100:
                clean_expr = clean_expr[:100] + "..."
            print(f"表达式: {clean_expr}")
            
            # 这里可以添加实际的表达式计算示例
            # result = D.features(instruments, [expression], start_time, end_time)
            print("用途: 用于识别特定的技术分析信号")
    
    def backtest_strategy_example(self):
        """回测策略示例"""
        print("\n" + "=" * 60)
        print("选币策略回测示例")
        print("=" * 60)
        
        # 这里展示如何进行回测
        print("回测参数:")
        print(f"- 选币数量: {self.config['max_selections']}")
        print(f"- 最低得分: {self.config['min_score']}")
        print(f"- 权重配置: {self.config['weights']}")
        
        print("\n回测流程:")
        print("1. 每日运行选币算法")
        print("2. 选择得分最高的标的")
        print("3. 模拟买入并持有")
        print("4. 根据止损/止盈条件卖出")
        print("5. 计算收益率和风险指标")
        
        print("\n注意事项:")
        print("- 需要准备充足的历史数据")
        print("- 考虑交易成本和滑点")
        print("- 避免过度拟合")
        print("- 进行样本外测试")


def main():
    """主函数"""
    # 创建示例实例
    example = CryptoSelectionExample()
    
    # 运行选币示例
    example.run_daily_selection()
    
    # 演示Alpha表达式
    example.demonstrate_alpha_expressions()
    
    # 回测示例
    example.backtest_strategy_example()


if __name__ == "__main__":
    main()
