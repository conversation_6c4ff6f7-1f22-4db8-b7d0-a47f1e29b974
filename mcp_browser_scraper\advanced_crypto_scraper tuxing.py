#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
高级加密货币数据抓取器
整合历史数据抓取和火币API功能，提供丰富的选币范围选择
支持多种数据源和智能筛选条件
支持形态分析和企业微信推送功能
"""

import requests
import sqlite3
import pandas as pd
import time
import json
import hmac
import hashlib
import base64
import urllib.parse
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union
import asyncio
import aiohttp
import traceback
import numpy as np
# import talib  # 暂时注释掉
from scipy.signal import find_peaks
from scipy.stats import linregress
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import seaborn as sns

def log(message):
    """增强的日志函数"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

class AdvancedCryptoScraper:
    """高级加密货币数据抓取器"""

    def __init__(self, huobi_api_key: str = None, huobi_secret_key: str = None, use_proxy: bool = True):
        """初始化抓取器"""
        print("🔧 开始初始化抓取器...")
        self.huobi_api_key = huobi_api_key
        self.huobi_secret_key = huobi_secret_key
        self.db_path = "advanced_crypto_data.db"
        self.use_proxy = use_proxy
        print(f"✅ 基本参数设置完成，代理模式: {'启用' if use_proxy else '禁用'}")

        # 速度控制配置
        self.speed_modes = {
            'unlimited': {'delay': 0, 'name': '不限制', 'desc': '无延迟，最快速度'},
            'fast': {'delay': 0.3, 'name': '快速', 'desc': '300ms延迟，适合批量获取'},
            'normal': {'delay': 0.8, 'name': '正常', 'desc': '800ms延迟，推荐使用'},
            'safe': {'delay': 1.5, 'name': '安全', 'desc': '1.5秒延迟，最安全'},
            'conservative': {'delay': 3.0, 'name': '保守', 'desc': '3秒延迟，极度安全'}
        }
        self.current_speed_mode = 'normal'

        # 数据源配置
        self.data_sources = {
            'okx': {
                'base_url': 'https://www.okx.com/api/v5',
                'name': 'OKX(欧易)',
                'free': True,
                'rate_limit': 600,  # 每分钟请求数
                'priority': 1  # 优先级最高
            },
            'gate': {
                'base_url': 'https://api.gateio.ws/api/v4',
                'name': 'Gate.io',
                'free': True,
                'rate_limit': 900,
                'priority': 2
            },
            'coingecko': {
                'base_url': 'https://api.coingecko.com/api/v3',
                'name': 'CoinGecko',
                'free': True,
                'rate_limit': 50,
                'priority': 3
            },
            'huobi': {
                'base_url': 'https://api.huobi.pro',
                'name': '火币',
                'free': False,
                'rate_limit': 100,
                'priority': 4
            },
            'coinmarketcap': {
                'base_url': 'https://pro-api.coinmarketcap.com/v1',
                'name': 'CoinMarketCap',
                'free': False,
                'rate_limit': 333,
                'priority': 5
            }
        }

        # 设置默认数据源为OKX，如果网络有问题则自动切换
        self.current_data_source = 'okx'
        self.auto_fallback = True  # 自动降级到可用的数据源

        # 代理配置 - 支持多种代理类型
        self.proxy_configs = [
            {'http': 'socks5://127.0.0.1:1082', 'https': 'socks5://127.0.0.1:1082'},
            {'http': 'http://127.0.0.1:1080', 'https': 'http://127.0.0.1:1080'},
            {'http': 'socks5://127.0.0.1:1081', 'https': 'socks5://127.0.0.1:1081'}
        ]
        self.current_proxy_index = 0

        # 初始化请求会话
        print("🔧 初始化网络会话...")
        self._init_session()
        print("✅ 网络会话初始化完成")

        # 选币范围配置
        print("🔧 配置选币范围...")
        self.selection_ranges = {
            '1': {'name': '🌍 全市场扫描', 'desc': '所有币种', 'filter': self._filter_all_market},
            '2': {'name': '👑 主流币种', 'desc': '市值>100亿美元', 'filter': self._filter_mainstream},
            '3': {'name': '🔥 热门山寨币', 'desc': '市值10-100亿美元', 'filter': self._filter_hot_altcoins},
            '4': {'name': '💎 小市值潜力币', 'desc': '市值<10亿美元', 'filter': self._filter_small_cap},
            '5': {'name': '🆕 新上市币种', 'desc': '30天内', 'filter': self._filter_new_listings_30d},
            '6': {'name': '🆕 较新币种', 'desc': '90天内', 'filter': self._filter_new_listings_90d},
            '7': {'name': '🏦 DeFi生态代币', 'desc': 'DeFi相关项目', 'filter': self._filter_defi_tokens},
            '8': {'name': '⛓️ Layer1公链代币', 'desc': 'Layer1区块链', 'filter': self._filter_layer1_tokens},
            '9': {'name': '🔗 Layer2扩容代币', 'desc': 'Layer2解决方案', 'filter': self._filter_layer2_tokens},
            '10': {'name': '🐕 Meme币专区', 'desc': 'Meme概念币', 'filter': self._filter_meme_tokens},
            '11': {'name': '🤖 AI概念币', 'desc': 'AI人工智能', 'filter': self._filter_ai_tokens},
            '12': {'name': '📈 高交易量币种', 'desc': '24h交易量前100', 'filter': self._filter_high_volume},
            '13': {'name': '🔧 自定义筛选条件', 'desc': '用户自定义', 'filter': self._filter_custom},
            '14': {'name': '📊 查看市场概况', 'desc': '市场统计信息', 'filter': None},
            '15': {'name': '🎯 形态分析', 'desc': '技术形态识别', 'filter': self._filter_pattern_analysis}
        }

        # 请求统计
        self.request_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'last_request_time': None
        }

        # 企业微信推送配置
        print("🔧 配置企业微信推送...")
        self.wechat_webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=69db19ba-d1af-422a-b0cf-19f21cd5b5fc"
        print("✅ 企业微信推送配置完成")

        print("🔧 初始化数据库...")
        self._init_database()
        print("✅ 数据库初始化完成")

        log("高级加密货币数据抓取器初始化完成")
        log(f"当前速度模式: {self.speed_modes[self.current_speed_mode]['name']}")
        log(f"代理状态: {'启用' if self.use_proxy else '禁用'}")
        log("✅ 已集成形态分析和企业微信推送功能")

    def _init_session(self):
        """初始化请求会话"""
        self.session = requests.Session()

        # 设置代理
        if self.use_proxy and self.proxy_configs:
            current_proxy = self.proxy_configs[self.current_proxy_index]
            self.session.proxies.update(current_proxy)
            log(f"使用代理: {current_proxy}")

        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        })

        # 设置超时
        self.session.timeout = 30

    def _init_database(self):
        """初始化数据库"""
        try:
            # 确保数据库目录存在
            db_dir = os.path.dirname(self.db_path) if os.path.dirname(self.db_path) else '.'
            os.makedirs(db_dir, exist_ok=True)

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 创建币种信息表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS crypto_info (
                    id TEXT PRIMARY KEY,
                    symbol TEXT NOT NULL,
                    name TEXT NOT NULL,
                    market_cap REAL DEFAULT 0,
                    market_cap_rank INTEGER DEFAULT 0,
                    current_price REAL DEFAULT 0,
                    price_change_24h REAL DEFAULT 0,
                    volume_24h REAL DEFAULT 0,
                    circulating_supply REAL DEFAULT 0,
                    total_supply REAL DEFAULT 0,
                    max_supply REAL DEFAULT 0,
                    ath REAL DEFAULT 0,
                    ath_date TEXT DEFAULT '',
                    atl REAL DEFAULT 0,
                    atl_date TEXT DEFAULT '',
                    categories TEXT DEFAULT '',
                    description TEXT DEFAULT '',
                    genesis_date TEXT DEFAULT '',
                    homepage TEXT DEFAULT '',
                    blockchain_site TEXT DEFAULT '',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建历史价格表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS price_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    crypto_id TEXT NOT NULL,
                    date DATE NOT NULL,
                    open_price REAL DEFAULT 0,
                    high_price REAL DEFAULT 0,
                    low_price REAL DEFAULT 0,
                    close_price REAL DEFAULT 0,
                    volume REAL DEFAULT 0,
                    market_cap REAL DEFAULT 0,
                    data_source TEXT DEFAULT 'coingecko',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (crypto_id) REFERENCES crypto_info (id),
                    UNIQUE(crypto_id, date, data_source)
                )
            ''')

            # 创建筛选结果表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS selection_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    selection_type TEXT NOT NULL,
                    crypto_id TEXT NOT NULL,
                    selection_score REAL DEFAULT 0,
                    selection_reason TEXT DEFAULT '',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (crypto_id) REFERENCES crypto_info (id)
                )
            ''')

            # 创建市场概况表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS market_overview (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    total_market_cap REAL DEFAULT 0,
                    total_volume_24h REAL DEFAULT 0,
                    bitcoin_dominance REAL DEFAULT 0,
                    ethereum_dominance REAL DEFAULT 0,
                    active_cryptocurrencies INTEGER DEFAULT 0,
                    upcoming_icos INTEGER DEFAULT 0,
                    ongoing_icos INTEGER DEFAULT 0,
                    ended_icos INTEGER DEFAULT 0,
                    markets INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建索引以提高查询性能
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_crypto_symbol ON crypto_info(symbol)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_crypto_rank ON crypto_info(market_cap_rank)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_price_crypto_date ON price_history(crypto_id, date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_selection_type ON selection_results(selection_type)')

            conn.commit()
            conn.close()
            log("数据库初始化完成")

        except Exception as e:
            log(f"数据库初始化失败: {e}")
            log(f"错误详情: {traceback.format_exc()}")

    def apply_rate_limit(self):
        """应用速率限制"""
        delay = self.speed_modes[self.current_speed_mode]['delay']
        if delay > 0:
            time.sleep(delay)

        # 更新请求统计
        self.request_stats['last_request_time'] = datetime.now()

    def set_speed_mode(self, mode: str):
        """设置速度模式"""
        if mode in self.speed_modes:
            self.current_speed_mode = mode
            log(f"速度模式已设置为: {self.speed_modes[mode]['name']} ({self.speed_modes[mode]['desc']})")
        else:
            log(f"❌ 无效的速度模式: {mode}")

    def show_speed_modes(self):
        """显示所有速度模式"""
        log("\n📊 可用速度模式:")
        log("="*60)
        for key, mode in self.speed_modes.items():
            current = " ⭐ 当前" if key == self.current_speed_mode else ""
            log(f"{key}: {mode['name']} - {mode['desc']} (延迟: {mode['delay']}s){current}")
        log("="*60)

    def test_proxy(self):
        """测试代理连接"""
        test_urls = [
            'https://httpbin.org/ip',
            'https://api.ipify.org?format=json',
            'https://ifconfig.me/ip'
        ]

        for url in test_urls:
            try:
                log(f"测试代理连接: {url}")
                response = self.session.get(url, timeout=10)

                if response.status_code == 200:
                    if 'httpbin.org' in url:
                        ip_info = response.json()
                        current_ip = ip_info.get('origin', 'Unknown')
                    elif 'ipify.org' in url:
                        ip_info = response.json()
                        current_ip = ip_info.get('ip', 'Unknown')
                    else:
                        current_ip = response.text.strip()

                    log(f"✅ 代理工作正常，当前IP: {current_ip}")
                    self.request_stats['successful_requests'] += 1
                    return True
                else:
                    log(f"❌ 代理测试失败，状态码: {response.status_code}")

            except Exception as e:
                log(f"❌ 代理测试失败 ({url}): {e}")
                continue

        # 如果所有测试都失败，尝试切换代理
        if self.use_proxy and len(self.proxy_configs) > 1:
            self._switch_proxy()
            return self.test_proxy()  # 递归测试新代理

        self.request_stats['failed_requests'] += 1
        return False

    def _switch_proxy(self):
        """切换代理"""
        if not self.use_proxy or not self.proxy_configs:
            return

        self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxy_configs)
        new_proxy = self.proxy_configs[self.current_proxy_index]
        self.session.proxies.update(new_proxy)
        log(f"🔄 切换到新代理: {new_proxy}")

    def test_connection(self):
        """测试网络连接"""
        log("🔍 开始网络连接测试...")

        # 测试基本连接
        basic_test = self._test_basic_connection()

        # 测试代理连接
        proxy_test = self.test_proxy() if self.use_proxy else True

        # 测试API连接
        api_test = self._test_api_connection()

        # 显示测试结果
        log("\n📊 网络连接测试结果:")
        log("="*50)
        log(f"基本连接: {'✅ 正常' if basic_test else '❌ 失败'}")
        log(f"代理连接: {'✅ 正常' if proxy_test else '❌ 失败'}")
        log(f"API连接: {'✅ 正常' if api_test else '❌ 失败'}")
        log("="*50)

        return basic_test and proxy_test and api_test

    def _test_basic_connection(self):
        """测试基本网络连接"""
        try:
            # 临时禁用代理测试基本连接
            temp_session = requests.Session()
            temp_session.timeout = 10
            response = temp_session.get('https://www.google.com', timeout=10)
            return response.status_code == 200
        except:
            return False

    def _test_api_connection(self):
        """测试API连接"""
        try:
            # 优先测试OKX API
            if self.current_data_source == 'okx':
                url = f"{self.data_sources['okx']['base_url']}/market/tickers?instType=SPOT"
                response = self.session.get(url, timeout=10)
                if response.status_code == 200:
                    log("✅ OKX API连接正常")
                    return True

            # 测试Gate.io API
            elif self.current_data_source == 'gate':
                url = f"{self.data_sources['gate']['base_url']}/spot/currencies"
                response = self.session.get(url, timeout=10)
                if response.status_code == 200:
                    log("✅ Gate.io API连接正常")
                    return True

            # 备用测试CoinGecko
            url = f"{self.data_sources['coingecko']['base_url']}/ping"
            response = self.session.get(url, timeout=10)
            return response.status_code == 200
        except:
            return False

    def get_market_overview(self, retry_count: int = 3):
        """获取市场概况"""
        for attempt in range(retry_count):
            try:
                log(f"获取市场概况... (尝试 {attempt + 1}/{retry_count})")

                url = f"{self.data_sources['coingecko']['base_url']}/global"
                response = self.session.get(url, timeout=30)
                self.apply_rate_limit()

                # 更新请求统计
                self.request_stats['total_requests'] += 1

                if response.status_code == 200:
                    data = response.json()['data']

                    overview = {
                        'total_market_cap': data['total_market_cap'].get('usd', 0),
                        'total_volume_24h': data['total_volume'].get('usd', 0),
                        'bitcoin_dominance': data['market_cap_percentage'].get('btc', 0),
                        'ethereum_dominance': data['market_cap_percentage'].get('eth', 0),
                        'active_cryptocurrencies': data.get('active_cryptocurrencies', 0),
                        'upcoming_icos': data.get('upcoming_icos', 0),
                        'ongoing_icos': data.get('ongoing_icos', 0),
                        'ended_icos': data.get('ended_icos', 0),
                        'markets': data.get('markets', 0)
                    }

                    # 保存到数据库
                    self._save_market_overview(overview)

                    self.request_stats['successful_requests'] += 1
                    log("✅ 市场概况获取成功")
                    return overview

                elif response.status_code == 429:  # 速率限制
                    log(f"⚠️ 遇到速率限制，等待后重试...")
                    time.sleep(60)  # 等待1分钟
                    continue

                else:
                    log(f"❌ 获取市场概况失败，状态码: {response.status_code}")
                    if attempt < retry_count - 1:
                        log(f"等待 {(attempt + 1) * 5} 秒后重试...")
                        time.sleep((attempt + 1) * 5)
                        continue

            except requests.exceptions.ProxyError as e:
                log(f"❌ 代理错误: {e}")
                if self.use_proxy and attempt < retry_count - 1:
                    self._switch_proxy()
                    continue

            except requests.exceptions.Timeout as e:
                log(f"❌ 请求超时: {e}")
                if attempt < retry_count - 1:
                    log(f"等待 {(attempt + 1) * 10} 秒后重试...")
                    time.sleep((attempt + 1) * 10)
                    continue

            except Exception as e:
                log(f"❌ 获取市场概况失败: {e}")
                if attempt < retry_count - 1:
                    log(f"等待 {(attempt + 1) * 5} 秒后重试...")
                    time.sleep((attempt + 1) * 5)
                    continue

        self.request_stats['failed_requests'] += 1
        return None

    def _save_market_overview(self, overview: Dict):
        """保存市场概况到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO market_overview
                (total_market_cap, total_volume_24h, bitcoin_dominance, ethereum_dominance,
                 active_cryptocurrencies, upcoming_icos, ongoing_icos, ended_icos, markets)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                overview['total_market_cap'], overview['total_volume_24h'],
                overview['bitcoin_dominance'], overview['ethereum_dominance'],
                overview['active_cryptocurrencies'], overview['upcoming_icos'],
                overview['ongoing_icos'], overview['ended_icos'], overview['markets']
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            log(f"保存市场概况失败: {e}")

    def display_market_overview(self, overview: Dict):
        """显示市场概况"""
        if not overview:
            log("❌ 无市场概况数据")
            return

        log("\n" + "="*80)
        log("📊 全球加密货币市场概况")
        log("="*80)
        log(f"💰 总市值: ${overview['total_market_cap']:,.0f}")
        log(f"📈 24小时总交易量: ${overview['total_volume_24h']:,.0f}")
        log(f"🟠 比特币市值占比: {overview['bitcoin_dominance']:.2f}%")
        log(f"🔵 以太坊市值占比: {overview['ethereum_dominance']:.2f}%")
        log(f"🪙 活跃加密货币数量: {overview['active_cryptocurrencies']:,}")
        log(f"🏪 交易市场数量: {overview['markets']:,}")
        log(f"🚀 即将ICO项目: {overview['upcoming_icos']}")
        log(f"🔥 进行中ICO项目: {overview['ongoing_icos']}")
        log(f"✅ 已结束ICO项目: {overview['ended_icos']}")
        log("="*80)

    def get_all_cryptocurrencies(self, limit: int = 250, page: int = 1, retry_count: int = 3):
        """获取所有加密货币列表"""
        # 验证参数
        limit = min(max(limit, 1), 250)  # 限制在1-250之间
        page = max(page, 1)  # 页面至少为1

        # 优先使用OKX API
        if self.current_data_source == 'okx':
            return self._get_cryptocurrencies_from_okx(limit, page, retry_count)
        elif self.current_data_source == 'gate':
            return self._get_cryptocurrencies_from_gate(limit, page, retry_count)
        else:
            return self._get_cryptocurrencies_from_coingecko(limit, page, retry_count)

    def _get_cryptocurrencies_from_okx(self, limit: int = 250, page: int = 1, retry_count: int = 3):
        """从OKX获取加密货币列表"""
        for attempt in range(retry_count):
            try:
                log(f"从OKX获取加密货币列表 (尝试 {attempt + 1}/{retry_count})...")

                # 获取所有现货交易对的行情数据
                url = f"{self.data_sources['okx']['base_url']}/market/tickers"
                params = {
                    'instType': 'SPOT'  # 现货交易
                }

                response = self.session.get(url, params=params, timeout=30)
                self.apply_rate_limit()

                # 更新请求统计
                self.request_stats['total_requests'] += 1

                if response.status_code == 200:
                    result = response.json()

                    if result.get('code') != '0':
                        log(f"⚠️ OKX API返回错误: {result.get('msg', 'Unknown error')}")
                        return []

                    tickers_data = result.get('data', [])

                    if not tickers_data:
                        log("⚠️ OKX返回的数据为空")
                        return []

                    # 过滤USDT交易对并按交易量排序
                    usdt_pairs = []
                    for ticker in tickers_data:
                        try:
                            inst_id = ticker.get('instId', '')
                            if inst_id.endswith('-USDT'):
                                symbol = inst_id.replace('-USDT', '')

                                # 跳过一些不需要的币种
                                if symbol in ['USDT', 'USDC', 'BUSD', 'DAI']:
                                    continue

                                last_price = self._safe_float(ticker.get('last'))
                                vol_24h = self._safe_float(ticker.get('volCcy24h'))  # 24h成交量(计价货币)
                                change_24h = self._safe_float(ticker.get('chgUtc0'))  # 24h涨跌幅

                                if last_price > 0 and vol_24h > 0:
                                    crypto = {
                                        'id': symbol.lower(),
                                        'symbol': symbol,
                                        'name': symbol,  # OKX API不提供全名，使用symbol
                                        'market_cap': vol_24h * last_price * 365,  # 估算市值
                                        'market_cap_rank': 0,  # OKX不提供排名
                                        'current_price': last_price,
                                        'price_change_24h': change_24h * 100,  # 转换为百分比
                                        'volume_24h': vol_24h,
                                        'circulating_supply': 0,
                                        'total_supply': 0,
                                        'max_supply': 0,
                                        'ath': 0,
                                        'ath_date': '',
                                        'atl': 0,
                                        'atl_date': '',
                                        'image': '',
                                        'last_updated': '',
                                        'data_source': 'okx'
                                    }
                                    usdt_pairs.append(crypto)

                        except Exception as e:
                            log(f"⚠️ 处理OKX数据时出错: {e}")
                            continue

                    # 按交易量排序
                    usdt_pairs.sort(key=lambda x: x['volume_24h'], reverse=True)

                    # 限制返回数量
                    result = usdt_pairs[:limit]

                    # 添加市值排名
                    for i, crypto in enumerate(result, 1):
                        crypto['market_cap_rank'] = i

                    self.request_stats['successful_requests'] += 1
                    log(f"✅ 从OKX成功获取 {len(result)} 个加密货币")
                    return result

                elif response.status_code == 429:  # 速率限制
                    log(f"⚠️ OKX遇到速率限制，等待后重试...")
                    time.sleep(60)
                    continue

                else:
                    log(f"❌ OKX获取失败，状态码: {response.status_code}")
                    if attempt < retry_count - 1:
                        log(f"等待 {(attempt + 1) * 5} 秒后重试...")
                        time.sleep((attempt + 1) * 5)
                        continue

            except requests.exceptions.Timeout as e:
                log(f"❌ OKX请求超时: {e}")
                if attempt < retry_count - 1:
                    log(f"等待 {(attempt + 1) * 10} 秒后重试...")
                    time.sleep((attempt + 1) * 10)
                    continue

            except Exception as e:
                log(f"❌ OKX获取失败: {e}")
                if attempt < retry_count - 1:
                    log(f"等待 {(attempt + 1) * 5} 秒后重试...")
                    time.sleep((attempt + 1) * 5)
                    continue

        # 如果OKX失败，尝试Gate.io
        log("⚠️ OKX获取失败，尝试使用Gate.io...")
        return self._get_cryptocurrencies_from_gate(limit, page, retry_count)

    def _get_cryptocurrencies_from_gate(self, limit: int = 250, page: int = 1, retry_count: int = 3):
        """从Gate.io获取加密货币列表"""
        for attempt in range(retry_count):
            try:
                log(f"从Gate.io获取加密货币列表 (尝试 {attempt + 1}/{retry_count})...")

                # 获取所有交易对
                url = f"{self.data_sources['gate']['base_url']}/spot/tickers"
                response = self.session.get(url, timeout=30)
                self.apply_rate_limit()

                # 更新请求统计
                self.request_stats['total_requests'] += 1

                if response.status_code == 200:
                    tickers_data = response.json()

                    if not tickers_data:
                        log("⚠️ Gate.io返回的数据为空")
                        return []

                    # 过滤USDT交易对并按交易量排序
                    usdt_pairs = []
                    for ticker in tickers_data:
                        try:
                            currency_pair = ticker.get('currency_pair', '')
                            if currency_pair.endswith('_USDT'):
                                symbol = currency_pair.replace('_USDT', '')

                                # 跳过一些不需要的币种
                                if symbol in ['USDT', 'USDC', 'BUSD', 'DAI']:
                                    continue

                                volume_24h = self._safe_float(ticker.get('quote_volume'))
                                current_price = self._safe_float(ticker.get('last'))
                                price_change_24h = self._safe_float(ticker.get('change_percentage'))

                                if current_price > 0 and volume_24h > 0:
                                    crypto = {
                                        'id': symbol.lower(),
                                        'symbol': symbol,
                                        'name': symbol,  # Gate.io API不提供全名，使用symbol
                                        'market_cap': volume_24h * current_price * 365,  # 估算市值
                                        'market_cap_rank': 0,  # Gate.io不提供排名
                                        'current_price': current_price,
                                        'price_change_24h': price_change_24h,
                                        'volume_24h': volume_24h,
                                        'circulating_supply': 0,
                                        'total_supply': 0,
                                        'max_supply': 0,
                                        'ath': 0,
                                        'ath_date': '',
                                        'atl': 0,
                                        'atl_date': '',
                                        'image': '',
                                        'last_updated': '',
                                        'data_source': 'gate'
                                    }
                                    usdt_pairs.append(crypto)

                        except Exception as e:
                            log(f"⚠️ 处理Gate.io数据时出错: {e}")
                            continue

                    # 按交易量排序
                    usdt_pairs.sort(key=lambda x: x['volume_24h'], reverse=True)

                    # 限制返回数量
                    result = usdt_pairs[:limit]

                    # 添加市值排名
                    for i, crypto in enumerate(result, 1):
                        crypto['market_cap_rank'] = i

                    self.request_stats['successful_requests'] += 1
                    log(f"✅ 从Gate.io成功获取 {len(result)} 个加密货币")
                    return result

                elif response.status_code == 429:  # 速率限制
                    log(f"⚠️ Gate.io遇到速率限制，等待后重试...")
                    time.sleep(60)
                    continue

                else:
                    log(f"❌ Gate.io获取失败，状态码: {response.status_code}")
                    if attempt < retry_count - 1:
                        log(f"等待 {(attempt + 1) * 5} 秒后重试...")
                        time.sleep((attempt + 1) * 5)
                        continue

            except requests.exceptions.Timeout as e:
                log(f"❌ Gate.io请求超时: {e}")
                if attempt < retry_count - 1:
                    log(f"等待 {(attempt + 1) * 10} 秒后重试...")
                    time.sleep((attempt + 1) * 10)
                    continue

            except Exception as e:
                log(f"❌ Gate.io获取失败: {e}")
                if attempt < retry_count - 1:
                    log(f"等待 {(attempt + 1) * 5} 秒后重试...")
                    time.sleep((attempt + 1) * 5)
                    continue

        # 如果Gate.io失败，尝试CoinGecko
        log("⚠️ Gate.io获取失败，尝试使用CoinGecko...")
        return self._get_cryptocurrencies_from_coingecko(limit, page, retry_count)

    def _get_cryptocurrencies_from_coingecko(self, limit: int = 250, page: int = 1, retry_count: int = 3):
        """从CoinGecko获取加密货币列表"""
        for attempt in range(retry_count):
            try:
                log(f"从CoinGecko获取加密货币列表 (页面 {page}, 每页 {limit} 个, 尝试 {attempt + 1}/{retry_count})...")

                url = f"{self.data_sources['coingecko']['base_url']}/coins/markets"
                params = {
                    'vs_currency': 'usd',
                    'order': 'market_cap_desc',
                    'per_page': limit,
                    'page': page,
                    'sparkline': False,
                    'price_change_percentage': '24h,7d,30d'
                }

                response = self.session.get(url, params=params, timeout=30)
                self.apply_rate_limit()

                # 更新请求统计
                self.request_stats['total_requests'] += 1

                if response.status_code == 200:
                    coins_data = response.json()

                    if not coins_data:
                        log("⚠️ CoinGecko返回的数据为空")
                        return []

                    cryptocurrencies = []
                    for coin in coins_data:
                        try:
                            crypto = {
                                'id': coin.get('id', ''),
                                'symbol': coin.get('symbol', '').upper(),
                                'name': coin.get('name', ''),
                                'market_cap': self._safe_float(coin.get('market_cap')),
                                'market_cap_rank': self._safe_int(coin.get('market_cap_rank')),
                                'current_price': self._safe_float(coin.get('current_price')),
                                'price_change_24h': self._safe_float(coin.get('price_change_percentage_24h')),
                                'volume_24h': self._safe_float(coin.get('total_volume')),
                                'circulating_supply': self._safe_float(coin.get('circulating_supply')),
                                'total_supply': self._safe_float(coin.get('total_supply')),
                                'max_supply': self._safe_float(coin.get('max_supply')),
                                'ath': self._safe_float(coin.get('ath')),
                                'ath_date': coin.get('ath_date', ''),
                                'atl': self._safe_float(coin.get('atl')),
                                'atl_date': coin.get('atl_date', ''),
                                'image': coin.get('image', ''),
                                'last_updated': coin.get('last_updated', ''),
                                'data_source': 'coingecko'
                            }

                            # 验证必要字段
                            if crypto['id'] and crypto['symbol'] and crypto['name']:
                                cryptocurrencies.append(crypto)
                            else:
                                log(f"⚠️ 跳过无效币种数据: {coin}")

                        except Exception as e:
                            log(f"⚠️ 处理币种数据时出错: {e}, 数据: {coin}")
                            continue

                    self.request_stats['successful_requests'] += 1
                    log(f"✅ 从CoinGecko成功获取 {len(cryptocurrencies)} 个加密货币")
                    return cryptocurrencies

                elif response.status_code == 429:  # 速率限制
                    log(f"⚠️ 遇到速率限制，等待后重试...")
                    time.sleep(60)
                    continue

                else:
                    log(f"❌ 获取加密货币列表失败，状态码: {response.status_code}")
                    if response.text:
                        log(f"响应内容: {response.text[:200]}...")

                    if attempt < retry_count - 1:
                        log(f"等待 {(attempt + 1) * 5} 秒后重试...")
                        time.sleep((attempt + 1) * 5)
                        continue

            except requests.exceptions.ProxyError as e:
                log(f"❌ 代理错误: {e}")
                if self.use_proxy and attempt < retry_count - 1:
                    self._switch_proxy()
                    continue

            except requests.exceptions.Timeout as e:
                log(f"❌ 请求超时: {e}")
                if attempt < retry_count - 1:
                    log(f"等待 {(attempt + 1) * 10} 秒后重试...")
                    time.sleep((attempt + 1) * 10)
                    continue

            except Exception as e:
                log(f"❌ 获取加密货币列表失败: {e}")
                log(f"错误详情: {traceback.format_exc()}")
                if attempt < retry_count - 1:
                    log(f"等待 {(attempt + 1) * 5} 秒后重试...")
                    time.sleep((attempt + 1) * 5)
                    continue

        self.request_stats['failed_requests'] += 1
        return []

    def _safe_float(self, value):
        """安全转换为浮点数"""
        try:
            return float(value) if value is not None else 0.0
        except (ValueError, TypeError):
            return 0.0

    def _safe_int(self, value):
        """安全转换为整数"""
        try:
            return int(value) if value is not None else 0
        except (ValueError, TypeError):
            return 0

    # ==================== 筛选功能实现 ====================

    def _filter_all_market(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """全市场扫描 - 返回所有币种"""
        log("🌍 应用全市场扫描筛选...")
        return cryptocurrencies

    def _filter_mainstream(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """主流币种 - 市值>100亿美元"""
        log("👑 应用主流币种筛选 (市值>100亿美元)...")
        return [crypto for crypto in cryptocurrencies
                if crypto.get('market_cap', 0) > 10_000_000_000]

    def _filter_hot_altcoins(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """热门山寨币 - 市值10-100亿美元"""
        log("🔥 应用热门山寨币筛选 (市值10-100亿美元)...")
        return [crypto for crypto in cryptocurrencies
                if 1_000_000_000 <= crypto.get('market_cap', 0) <= 10_000_000_000]

    def _filter_small_cap(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """小市值潜力币 - 市值<10亿美元"""
        log("💎 应用小市值潜力币筛选 (市值<10亿美元)...")
        return [crypto for crypto in cryptocurrencies
                if crypto.get('market_cap', 0) < 1_000_000_000 and crypto.get('market_cap', 0) > 0]

    def _filter_new_listings_30d(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """新上市币种 - 30天内"""
        log("🆕 应用新上市币种筛选 (30天内)...")
        # 这里需要额外的API调用来获取上市时间
        # 暂时使用市值排名作为近似筛选
        return [crypto for crypto in cryptocurrencies
                if crypto.get('market_cap_rank', 0) > 500]

    def _filter_new_listings_90d(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """较新币种 - 90天内"""
        log("🆕 应用较新币种筛选 (90天内)...")
        return [crypto for crypto in cryptocurrencies
                if crypto.get('market_cap_rank', 0) > 300]

    def _filter_defi_tokens(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """DeFi生态代币"""
        log("🏦 应用DeFi生态代币筛选...")
        defi_keywords = ['defi', 'dex', 'swap', 'yield', 'farm', 'lending', 'protocol',
                        'finance', 'liquidity', 'compound', 'aave', 'uniswap', 'sushi']

        filtered = []
        for crypto in cryptocurrencies:
            name_lower = crypto.get('name', '').lower()
            symbol_lower = crypto.get('symbol', '').lower()

            if any(keyword in name_lower or keyword in symbol_lower for keyword in defi_keywords):
                filtered.append(crypto)

        return filtered

    def _filter_layer1_tokens(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """Layer1公链代币"""
        log("⛓️ 应用Layer1公链代币筛选...")
        layer1_symbols = ['BTC', 'ETH', 'BNB', 'ADA', 'SOL', 'DOT', 'AVAX', 'MATIC',
                         'ATOM', 'NEAR', 'FTM', 'ALGO', 'EGLD', 'HBAR', 'VET', 'ICP',
                         'FLOW', 'TEZOS', 'EOS', 'TRX', 'XLM', 'XRP', 'LTC']

        return [crypto for crypto in cryptocurrencies
                if crypto.get('symbol', '') in layer1_symbols]

    def _filter_layer2_tokens(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """Layer2扩容代币"""
        log("🔗 应用Layer2扩容代币筛选...")
        layer2_keywords = ['layer2', 'l2', 'scaling', 'rollup', 'optimism', 'arbitrum',
                          'polygon', 'loopring', 'immutable', 'starknet']
        layer2_symbols = ['MATIC', 'LRC', 'IMX', 'OP', 'ARB']

        filtered = []
        for crypto in cryptocurrencies:
            name_lower = crypto.get('name', '').lower()
            symbol = crypto.get('symbol', '')

            if (symbol in layer2_symbols or
                any(keyword in name_lower for keyword in layer2_keywords)):
                filtered.append(crypto)

        return filtered

    def _filter_meme_tokens(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """Meme币专区"""
        log("🐕 应用Meme币筛选...")
        meme_keywords = ['meme', 'dog', 'cat', 'shib', 'doge', 'pepe', 'floki', 'baby', 'inu']
        meme_symbols = ['DOGE', 'SHIB', 'PEPE', 'FLOKI', 'BABYDOGE', 'ELON']

        filtered = []
        for crypto in cryptocurrencies:
            name_lower = crypto.get('name', '').lower()
            symbol = crypto.get('symbol', '')

            if (symbol in meme_symbols or
                any(keyword in name_lower for keyword in meme_keywords)):
                filtered.append(crypto)

        return filtered

    def _filter_ai_tokens(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """AI概念币"""
        log("🤖 应用AI概念币筛选...")
        ai_keywords = ['ai', 'artificial', 'intelligence', 'machine', 'learning', 'neural',
                      'deep', 'algorithm', 'data', 'compute', 'gpu', 'render']
        ai_symbols = ['FET', 'AGIX', 'OCEAN', 'RLC', 'GRT', 'RNDR']

        filtered = []
        for crypto in cryptocurrencies:
            name_lower = crypto.get('name', '').lower()
            symbol = crypto.get('symbol', '')

            if (symbol in ai_symbols or
                any(keyword in name_lower for keyword in ai_keywords)):
                filtered.append(crypto)

        return filtered

    def _filter_high_volume(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """高交易量币种 - 24h交易量前100"""
        log("📈 应用高交易量币种筛选 (24h交易量前100)...")
        # 按24小时交易量排序，取前100
        sorted_cryptos = sorted(cryptocurrencies,
                               key=lambda x: x.get('volume_24h', 0),
                               reverse=True)
        return sorted_cryptos[:100]

    def _filter_custom(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """自定义筛选条件"""
        log("🔧 应用自定义筛选条件...")

        print("\n🔧 自定义筛选条件设置:")
        print("1. 市值范围筛选")
        print("2. 价格范围筛选")
        print("3. 涨跌幅筛选")
        print("4. 交易量筛选")
        print("5. 关键词筛选")
        print("6. 组合筛选")

        choice = input("请选择筛选类型 (1-6): ").strip()

        if choice == '1':
            return self._custom_market_cap_filter(cryptocurrencies)
        elif choice == '2':
            return self._custom_price_filter(cryptocurrencies)
        elif choice == '3':
            return self._custom_change_filter(cryptocurrencies)
        elif choice == '4':
            return self._custom_volume_filter(cryptocurrencies)
        elif choice == '5':
            return self._custom_keyword_filter(cryptocurrencies)
        elif choice == '6':
            return self._custom_combined_filter(cryptocurrencies)
        else:
            log("❌ 无效选择，返回所有币种")
            return cryptocurrencies

    def _custom_market_cap_filter(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """自定义市值范围筛选"""
        try:
            min_cap = float(input("请输入最小市值 (美元，例如: 1000000): ") or "0")
            max_cap = float(input("请输入最大市值 (美元，留空表示无上限): ") or "999999999999")

            filtered = [crypto for crypto in cryptocurrencies
                       if min_cap <= crypto.get('market_cap', 0) <= max_cap]

            log(f"✅ 市值筛选完成: ${min_cap:,.0f} - ${max_cap:,.0f}, 筛选出 {len(filtered)} 个币种")
            return filtered

        except ValueError:
            log("❌ 输入格式错误，返回所有币种")
            return cryptocurrencies

    def _custom_price_filter(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """自定义价格范围筛选"""
        try:
            min_price = float(input("请输入最小价格 (美元): ") or "0")
            max_price = float(input("请输入最大价格 (美元，留空表示无上限): ") or "999999")

            filtered = [crypto for crypto in cryptocurrencies
                       if min_price <= crypto.get('current_price', 0) <= max_price]

            log(f"✅ 价格筛选完成: ${min_price} - ${max_price}, 筛选出 {len(filtered)} 个币种")
            return filtered

        except ValueError:
            log("❌ 输入格式错误，返回所有币种")
            return cryptocurrencies

    def _custom_change_filter(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """自定义涨跌幅筛选"""
        try:
            min_change = float(input("请输入最小24h涨跌幅 (%, 例如: -10): ") or "-999")
            max_change = float(input("请输入最大24h涨跌幅 (%, 例如: 50): ") or "999")

            filtered = [crypto for crypto in cryptocurrencies
                       if min_change <= crypto.get('price_change_24h', 0) <= max_change]

            log(f"✅ 涨跌幅筛选完成: {min_change}% - {max_change}%, 筛选出 {len(filtered)} 个币种")
            return filtered

        except ValueError:
            log("❌ 输入格式错误，返回所有币种")
            return cryptocurrencies

    def _custom_volume_filter(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """自定义交易量筛选"""
        try:
            min_volume = float(input("请输入最小24h交易量 (美元): ") or "0")

            filtered = [crypto for crypto in cryptocurrencies
                       if crypto.get('volume_24h', 0) >= min_volume]

            log(f"✅ 交易量筛选完成: 最小交易量 ${min_volume:,.0f}, 筛选出 {len(filtered)} 个币种")
            return filtered

        except ValueError:
            log("❌ 输入格式错误，返回所有币种")
            return cryptocurrencies

    def _custom_keyword_filter(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """自定义关键词筛选"""
        keywords_input = input("请输入关键词 (多个关键词用逗号分隔): ").strip()
        if not keywords_input:
            log("❌ 未输入关键词，返回所有币种")
            return cryptocurrencies

        keywords = [kw.strip().lower() for kw in keywords_input.split(',')]

        filtered = []
        for crypto in cryptocurrencies:
            name_lower = crypto.get('name', '').lower()
            symbol_lower = crypto.get('symbol', '').lower()

            if any(keyword in name_lower or keyword in symbol_lower for keyword in keywords):
                filtered.append(crypto)

        log(f"✅ 关键词筛选完成: {keywords}, 筛选出 {len(filtered)} 个币种")
        return filtered

    def _custom_combined_filter(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """自定义组合筛选"""
        log("🔧 组合筛选 - 可以设置多个条件")

        filtered = cryptocurrencies

        # 市值筛选
        if input("是否设置市值筛选? (y/n): ").lower() == 'y':
            filtered = self._custom_market_cap_filter(filtered)

        # 价格筛选
        if input("是否设置价格筛选? (y/n): ").lower() == 'y':
            filtered = self._custom_price_filter(filtered)

        # 涨跌幅筛选
        if input("是否设置涨跌幅筛选? (y/n): ").lower() == 'y':
            filtered = self._custom_change_filter(filtered)

        # 交易量筛选
        if input("是否设置交易量筛选? (y/n): ").lower() == 'y':
            filtered = self._custom_volume_filter(filtered)

        # 关键词筛选
        if input("是否设置关键词筛选? (y/n): ").lower() == 'y':
            filtered = self._custom_keyword_filter(filtered)

        log(f"✅ 组合筛选完成，最终筛选出 {len(filtered)} 个币种")
        return filtered

    # ==================== 数据保存功能 ====================

    def save_cryptocurrencies(self, cryptocurrencies: List[Dict]):
        """保存加密货币信息到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            saved_count = 0
            for crypto in cryptocurrencies:
                try:
                    cursor.execute('''
                        INSERT OR REPLACE INTO crypto_info
                        (id, symbol, name, market_cap, market_cap_rank, current_price,
                         price_change_24h, volume_24h, circulating_supply, total_supply,
                         max_supply, ath, ath_date, atl, atl_date, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        crypto.get('id', ''),
                        crypto.get('symbol', ''),
                        crypto.get('name', ''),
                        crypto.get('market_cap', 0),
                        crypto.get('market_cap_rank', 0),
                        crypto.get('current_price', 0),
                        crypto.get('price_change_24h', 0),
                        crypto.get('volume_24h', 0),
                        crypto.get('circulating_supply', 0),
                        crypto.get('total_supply', 0),
                        crypto.get('max_supply', 0),
                        crypto.get('ath', 0),
                        crypto.get('ath_date', ''),
                        crypto.get('atl', 0),
                        crypto.get('atl_date', ''),
                        datetime.now().isoformat()
                    ))
                    saved_count += 1
                except Exception as e:
                    log(f"⚠️ 保存单个币种失败: {crypto.get('symbol', 'Unknown')} - {e}")
                    continue

            conn.commit()
            conn.close()
            log(f"✅ 成功保存 {saved_count}/{len(cryptocurrencies)} 个加密货币信息")

        except Exception as e:
            log(f"❌ 保存加密货币信息失败: {e}")
            log(f"错误详情: {traceback.format_exc()}")

    def save_selection_results(self, selection_type: str, cryptocurrencies: List[Dict]):
        """保存筛选结果到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 清除之前的筛选结果
            cursor.execute('DELETE FROM selection_results WHERE selection_type = ?', (selection_type,))

            for i, crypto in enumerate(cryptocurrencies, 1):
                cursor.execute('''
                    INSERT INTO selection_results
                    (selection_type, crypto_id, selection_score, selection_reason)
                    VALUES (?, ?, ?, ?)
                ''', (
                    selection_type, crypto['id'], i,
                    f"排名第{i}，市值: ${crypto.get('market_cap', 0):,.0f}"
                ))

            conn.commit()
            conn.close()
            log(f"✅ 筛选结果已保存: {selection_type}")

        except Exception as e:
            log(f"❌ 保存筛选结果失败: {e}")

    def get_historical_data(self, crypto_id: str, days: int = 365, retry_count: int = 3):
        """获取历史价格数据"""
        # 优先使用OKX API
        if self.current_data_source == 'okx':
            return self._get_historical_data_from_okx(crypto_id, days, retry_count)
        elif self.current_data_source == 'gate':
            return self._get_historical_data_from_gate(crypto_id, days, retry_count)
        else:
            return self._get_historical_data_from_coingecko(crypto_id, days, retry_count)

    def _get_historical_data_from_okx(self, crypto_id: str, days: int = 365, retry_count: int = 3):
        """从OKX获取历史价格数据"""
        for attempt in range(retry_count):
            try:
                # OKX使用symbol而不是id，需要转换
                symbol = crypto_id.upper()
                if not symbol.endswith('-USDT'):
                    symbol = f"{symbol}-USDT"

                log(f"从OKX获取 {symbol} 的历史数据 ({days}天, 尝试 {attempt + 1}/{retry_count})...")

                # OKX K线数据API
                url = f"{self.data_sources['okx']['base_url']}/market/history-candles"

                # 计算时间范围 (OKX需要毫秒时间戳)
                from datetime import datetime, timedelta
                end_time = datetime.now()
                start_time = end_time - timedelta(days=days)

                params = {
                    'instId': symbol,
                    'bar': '1D',  # 日线
                    'before': str(int(start_time.timestamp() * 1000)),  # 开始时间(毫秒)
                    'after': str(int(end_time.timestamp() * 1000)),     # 结束时间(毫秒)
                    'limit': str(min(days, 100))  # 限制数量
                }

                response = self.session.get(url, params=params, timeout=30)
                self.apply_rate_limit()

                # 更新请求统计
                self.request_stats['total_requests'] += 1

                if response.status_code == 200:
                    result = response.json()

                    if result.get('code') != '0':
                        log(f"⚠️ OKX API返回错误: {result.get('msg', 'Unknown error')}")
                        return []

                    data = result.get('data', [])

                    if not data:
                        log(f"⚠️ OKX {symbol} 没有历史价格数据")
                        return []

                    historical_data = []

                    # OKX返回的K线数据格式: [timestamp, open, high, low, close, volume, volCcy, volCcyQuote, confirm]
                    for candle in data:
                        try:
                            timestamp = float(candle[0]) / 1000  # 转换为秒时间戳
                            open_price = float(candle[1])        # 开盘价
                            high_price = float(candle[2])        # 最高价
                            low_price = float(candle[3])         # 最低价
                            close_price = float(candle[4])       # 收盘价
                            volume = float(candle[5])            # 成交量

                            # 转换时间戳为日期
                            date = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d')

                            historical_data.append({
                                'crypto_id': crypto_id,
                                'date': date,
                                'open_price': open_price,
                                'high_price': high_price,
                                'low_price': low_price,
                                'close_price': close_price,
                                'volume': volume,
                                'market_cap': 0,  # OKX不提供历史市值
                                'data_source': 'okx'
                            })

                        except (IndexError, ValueError, TypeError) as e:
                            log(f"⚠️ 处理OKX K线数据时出错: {e}")
                            continue

                    # 按日期排序（从旧到新）
                    historical_data.sort(key=lambda x: x['date'])

                    self.request_stats['successful_requests'] += 1
                    log(f"✅ 从OKX成功获取 {crypto_id} 的 {len(historical_data)} 天历史数据")
                    return historical_data

                elif response.status_code == 429:  # 速率限制
                    log(f"⚠️ OKX遇到速率限制，等待后重试...")
                    time.sleep(60)
                    continue

                else:
                    log(f"❌ OKX获取失败，状态码: {response.status_code}")
                    if attempt < retry_count - 1:
                        log(f"等待 {(attempt + 1) * 5} 秒后重试...")
                        time.sleep((attempt + 1) * 5)
                        continue

            except requests.exceptions.Timeout as e:
                log(f"❌ OKX请求超时: {e}")
                if attempt < retry_count - 1:
                    log(f"等待 {(attempt + 1) * 10} 秒后重试...")
                    time.sleep((attempt + 1) * 10)
                    continue

            except Exception as e:
                log(f"❌ OKX获取失败: {e}")
                if attempt < retry_count - 1:
                    log(f"等待 {(attempt + 1) * 5} 秒后重试...")
                    time.sleep((attempt + 1) * 5)
                    continue

        # 如果OKX失败，尝试Gate.io
        log("⚠️ OKX获取历史数据失败，尝试使用Gate.io...")
        return self._get_historical_data_from_gate(crypto_id, days, retry_count)

    def _get_historical_data_from_gate(self, crypto_id: str, days: int = 365, retry_count: int = 3):
        """从Gate.io获取历史价格数据"""
        for attempt in range(retry_count):
            try:
                # Gate.io使用symbol而不是id，需要转换
                symbol = crypto_id.upper()
                if not symbol.endswith('_USDT'):
                    symbol = f"{symbol}_USDT"

                log(f"从Gate.io获取 {symbol} 的历史数据 ({days}天, 尝试 {attempt + 1}/{retry_count})...")

                # Gate.io K线数据API
                url = f"{self.data_sources['gate']['base_url']}/spot/candlesticks"

                # 计算时间范围
                from datetime import datetime, timedelta
                end_time = datetime.now()
                start_time = end_time - timedelta(days=days)

                params = {
                    'currency_pair': symbol,
                    'interval': '1d',  # 日线
                    'from': int(start_time.timestamp()),
                    'to': int(end_time.timestamp())
                }

                response = self.session.get(url, params=params, timeout=30)
                self.apply_rate_limit()

                # 更新请求统计
                self.request_stats['total_requests'] += 1

                if response.status_code == 200:
                    data = response.json()

                    if not data:
                        log(f"⚠️ Gate.io {symbol} 没有历史价格数据")
                        return []

                    historical_data = []

                    # Gate.io返回的K线数据格式: [timestamp, volume, close, high, low, open]
                    for candle in data:
                        try:
                            timestamp = float(candle[0])  # 时间戳
                            volume = float(candle[1])     # 成交量
                            close_price = float(candle[2])  # 收盘价
                            high_price = float(candle[3])   # 最高价
                            low_price = float(candle[4])    # 最低价
                            open_price = float(candle[5])   # 开盘价

                            # 转换时间戳为日期
                            date = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d')

                            historical_data.append({
                                'crypto_id': crypto_id,
                                'date': date,
                                'open_price': open_price,
                                'high_price': high_price,
                                'low_price': low_price,
                                'close_price': close_price,
                                'volume': volume,
                                'market_cap': 0,  # Gate.io不提供历史市值
                                'data_source': 'gate'
                            })

                        except (IndexError, ValueError, TypeError) as e:
                            log(f"⚠️ 处理Gate.io K线数据时出错: {e}")
                            continue

                    self.request_stats['successful_requests'] += 1
                    log(f"✅ 从Gate.io成功获取 {crypto_id} 的 {len(historical_data)} 天历史数据")
                    return historical_data

                elif response.status_code == 429:  # 速率限制
                    log(f"⚠️ 遇到速率限制，等待后重试...")
                    time.sleep(60)
                    continue

                else:
                    log(f"❌ 获取 {crypto_id} 历史数据失败，状态码: {response.status_code}")
                    if attempt < retry_count - 1:
                        log(f"等待 {(attempt + 1) * 5} 秒后重试...")
                        time.sleep((attempt + 1) * 5)
                        continue

            except requests.exceptions.ProxyError as e:
                log(f"❌ 代理错误: {e}")
                if self.use_proxy and attempt < retry_count - 1:
                    self._switch_proxy()
                    continue

            except requests.exceptions.Timeout as e:
                log(f"❌ 请求超时: {e}")
                if attempt < retry_count - 1:
                    log(f"等待 {(attempt + 1) * 10} 秒后重试...")
                    time.sleep((attempt + 1) * 10)
                    continue

            except Exception as e:
                log(f"❌ 获取 {crypto_id} 历史数据失败: {e}")
                if attempt < retry_count - 1:
                    log(f"等待 {(attempt + 1) * 5} 秒后重试...")
                    time.sleep((attempt + 1) * 5)
                    continue

        # 如果Gate.io失败，尝试CoinGecko
        log("⚠️ Gate.io获取历史数据失败，尝试使用CoinGecko...")
        return self._get_historical_data_from_coingecko(crypto_id, days, retry_count)

    def _get_historical_data_from_coingecko(self, crypto_id: str, days: int = 365, retry_count: int = 3):
        """从CoinGecko获取历史价格数据"""
        for attempt in range(retry_count):
            try:
                log(f"从CoinGecko获取 {crypto_id} 的历史数据 ({days}天, 尝试 {attempt + 1}/{retry_count})...")

                url = f"{self.data_sources['coingecko']['base_url']}/coins/{crypto_id}/market_chart"
                params = {
                    'vs_currency': 'usd',
                    'days': days,
                    'interval': 'daily'
                }

                response = self.session.get(url, params=params, timeout=30)
                self.apply_rate_limit()

                # 更新请求统计
                self.request_stats['total_requests'] += 1

                if response.status_code == 200:
                    data = response.json()

                    if 'prices' not in data or not data['prices']:
                        log(f"⚠️ CoinGecko {crypto_id} 没有历史价格数据")
                        return []

                    historical_data = []
                    prices = data.get('prices', [])
                    volumes = data.get('total_volumes', [])
                    market_caps = data.get('market_caps', [])

                    # 确保有足够的数据
                    if len(prices) < 2:
                        log(f"⚠️ {crypto_id} 历史数据不足")
                        return []

                    # 处理价格和成交量数据
                    for i, price_point in enumerate(prices):
                        try:
                            timestamp = price_point[0]  # 毫秒时间戳
                            price = price_point[1]

                            # 获取对应的成交量数据
                            volume = 0
                            if i < len(volumes) and volumes[i]:
                                volume = volumes[i][1]

                            # 获取对应的市值数据
                            market_cap = 0
                            if i < len(market_caps) and market_caps[i]:
                                market_cap = market_caps[i][1]

                            # 转换时间戳为日期
                            date = datetime.fromtimestamp(timestamp / 1000).strftime('%Y-%m-%d')

                            # 计算OHLC数据（简化处理，使用价格作为所有值）
                            historical_data.append({
                                'crypto_id': crypto_id,
                                'date': date,
                                'open_price': price,
                                'high_price': price,
                                'low_price': price,
                                'close_price': price,
                                'volume': volume,
                                'market_cap': market_cap,
                                'data_source': 'coingecko'
                            })

                        except (IndexError, ValueError, TypeError) as e:
                            log(f"⚠️ 处理价格数据时出错: {e}")
                            continue

                    self.request_stats['successful_requests'] += 1
                    log(f"✅ 从CoinGecko成功获取 {crypto_id} 的 {len(historical_data)} 天历史数据")
                    return historical_data

                elif response.status_code == 429:  # 速率限制
                    log(f"⚠️ CoinGecko遇到速率限制，等待后重试...")
                    time.sleep(60)
                    continue

                else:
                    log(f"❌ CoinGecko获取失败，状态码: {response.status_code}")
                    if attempt < retry_count - 1:
                        log(f"等待 {(attempt + 1) * 5} 秒后重试...")
                        time.sleep((attempt + 1) * 5)
                        continue

            except requests.exceptions.Timeout as e:
                log(f"❌ CoinGecko请求超时: {e}")
                if attempt < retry_count - 1:
                    log(f"等待 {(attempt + 1) * 10} 秒后重试...")
                    time.sleep((attempt + 1) * 10)
                    continue

            except Exception as e:
                log(f"❌ CoinGecko获取失败: {e}")
                if attempt < retry_count - 1:
                    log(f"等待 {(attempt + 1) * 5} 秒后重试...")
                    time.sleep((attempt + 1) * 5)
                    continue

        self.request_stats['failed_requests'] += 1
        return []

    def save_historical_data(self, historical_data: List[Dict]):
        """保存历史数据到数据库"""
        if not historical_data:
            return

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            saved_count = 0
            for data in historical_data:
                try:
                    cursor.execute('''
                        INSERT OR REPLACE INTO price_history
                        (crypto_id, date, open_price, high_price, low_price, close_price,
                         volume, market_cap, data_source)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        data['crypto_id'], data['date'], data['open_price'],
                        data['high_price'], data['low_price'], data['close_price'],
                        data['volume'], data['market_cap'], data['data_source']
                    ))
                    saved_count += 1
                except Exception as e:
                    log(f"⚠️ 保存单条历史数据失败: {e}")
                    continue

            conn.commit()
            conn.close()
            log(f"✅ 成功保存 {saved_count} 条历史数据")

        except Exception as e:
            log(f"❌ 保存历史数据失败: {e}")

    def display_selection_results(self, cryptocurrencies: List[Dict], selection_name: str):
        """显示筛选结果"""
        if not cryptocurrencies:
            log(f"❌ {selection_name} 筛选结果为空")
            return

        log(f"\n🎯 {selection_name} 筛选结果 (共 {len(cryptocurrencies)} 个币种)")
        log("="*100)
        log(f"{'排名':<4} {'代码':<8} {'名称':<20} {'价格':<12} {'市值':<15} {'24h涨跌':<10} {'交易量':<15}")
        log("="*100)

        for i, crypto in enumerate(cryptocurrencies[:50], 1):  # 只显示前50个
            symbol = crypto.get('symbol', '')[:7]
            name = crypto.get('name', '')[:18]
            price = crypto.get('current_price', 0)
            market_cap = crypto.get('market_cap', 0)
            change_24h = crypto.get('price_change_24h', 0)
            volume_24h = crypto.get('volume_24h', 0)

            price_str = f"${price:.6f}" if price < 1 else f"${price:.2f}"
            market_cap_str = f"${market_cap/1e9:.2f}B" if market_cap > 1e9 else f"${market_cap/1e6:.1f}M"
            change_str = f"{change_24h:+.2f}%"
            volume_str = f"${volume_24h/1e6:.1f}M" if volume_24h > 1e6 else f"${volume_24h/1e3:.1f}K"

            log(f"{i:<4} {symbol:<8} {name:<20} {price_str:<12} {market_cap_str:<15} {change_str:<10} {volume_str:<15}")

        if len(cryptocurrencies) > 50:
            log(f"... 还有 {len(cryptocurrencies) - 50} 个币种未显示")

        log("="*100)

    def print_statistics(self):
        """打印统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取各种统计数据
            cursor.execute("SELECT COUNT(*) FROM crypto_info")
            crypto_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM price_history")
            history_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(DISTINCT selection_type) FROM selection_results")
            selection_types = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM market_overview")
            overview_count = cursor.fetchone()[0]

            # 获取最新更新时间
            cursor.execute("SELECT MAX(updated_at) FROM crypto_info")
            last_update = cursor.fetchone()[0]

            conn.close()

            log("\n📊 数据库统计信息")
            log("="*60)
            log(f"💰 币种信息: {crypto_count:,} 个")
            log(f"📈 历史记录: {history_count:,} 条")
            log(f"🎯 筛选类型: {selection_types} 种")
            log(f"🌍 市场概况: {overview_count} 条")
            log(f"🕒 最后更新: {last_update or '无'}")
            log("="*60)

            # 显示请求统计
            log("\n📡 请求统计信息")
            log("="*60)
            log(f"总请求数: {self.request_stats['total_requests']}")
            log(f"成功请求: {self.request_stats['successful_requests']}")
            log(f"失败请求: {self.request_stats['failed_requests']}")
            if self.request_stats['total_requests'] > 0:
                success_rate = (self.request_stats['successful_requests'] / self.request_stats['total_requests']) * 100
                log(f"成功率: {success_rate:.1f}%")
            log(f"最后请求: {self.request_stats['last_request_time'] or '无'}")
            log("="*60)

        except Exception as e:
            log(f"❌ 获取统计信息失败: {e}")

    def get_statistics(self):
        """获取数据库统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT COUNT(*) FROM crypto_info')
            total_cryptos = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM price_history')
            total_price_records = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(DISTINCT selection_type) FROM selection_results')
            total_selections = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM market_overview')
            total_market_records = cursor.fetchone()[0]

            cursor.execute('''
                SELECT MIN(date), MAX(date), COUNT(DISTINCT crypto_id)
                FROM price_history
            ''')

            price_stats = cursor.fetchone()
            conn.close()

            return {
                'total_cryptos': total_cryptos,
                'total_price_records': total_price_records,
                'total_selections': total_selections,
                'total_market_records': total_market_records,
                'earliest_date': price_stats[0],
                'latest_date': price_stats[1],
                'cryptos_with_price_data': price_stats[2]
            }

        except Exception as e:
            log(f"获取统计信息失败: {e}")
            return {}

    def print_statistics(self):
        """打印统计信息"""
        stats = self.get_statistics()

        if not stats:
            log("无法获取统计信息")
            return

        log("\n" + "="*60)
        log("📊 高级加密货币数据抓取器统计")
        log("="*60)
        log(f"💰 加密货币总数: {stats['total_cryptos']:,}")
        log(f"📈 历史价格记录: {stats['total_price_records']:,}")
        log(f"🔍 筛选方案数: {stats['total_selections']}")
        log(f"📊 市场概况记录: {stats['total_market_records']}")
        log(f"📅 有价格数据币种: {stats['cryptos_with_price_data']}")

        if stats['earliest_date'] and stats['latest_date']:
            log(f"📅 数据时间范围: {stats['earliest_date']} 到 {stats['latest_date']}")

        # 数据库大小
        import os
        if os.path.exists(self.db_path):
            db_size = os.path.getsize(self.db_path) / (1024 * 1024)
            log(f"💾 数据库大小: {db_size:.1f} MB")

        log("="*60)

    def export_data(self, table_name: str = 'crypto_info', limit: int = 1000):
        """导出数据到CSV"""
        try:
            conn = sqlite3.connect(self.db_path)

            if table_name == 'crypto_info':
                query = '''
                    SELECT symbol, name, market_cap, market_cap_rank, current_price,
                           price_change_24h, volume_24h, updated_at
                    FROM crypto_info
                    ORDER BY market_cap_rank
                    LIMIT ?
                '''
            elif table_name == 'selection_results':
                query = '''
                    SELECT sr.selection_type, ci.symbol, ci.name, sr.selection_score,
                           ci.current_price, ci.market_cap, sr.created_at
                    FROM selection_results sr
                    JOIN crypto_info ci ON sr.crypto_id = ci.id
                    ORDER BY sr.selection_type, sr.selection_score
                    LIMIT ?
                '''
            else:
                log(f"❌ 不支持的表名: {table_name}")
                return

            df = pd.read_sql_query(query, conn, params=(limit,))
            conn.close()

            if not df.empty:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"advanced_crypto_{table_name}_{timestamp}.csv"
                df.to_csv(filename, index=False, encoding='utf-8-sig')
                log(f"✅ 数据已导出: {filename}")

                log(f"\n数据预览 (前10行):")
                print(df.head(10).to_string())
            else:
                log("❌ 没有数据可导出")

        except Exception as e:
            log(f"❌ 导出数据失败: {e}")

    # ==================== 主要业务流程 ====================

    def run_selection_process(self, selection_key: str):
        """运行选币流程"""
        if selection_key not in self.selection_ranges:
            log(f"❌ 无效的选择: {selection_key}")
            return

        selection_info = self.selection_ranges[selection_key]
        selection_name = selection_info['name']

        if selection_key == '14':  # 查看市场概况
            overview = self.get_market_overview()
            if overview:
                self.display_market_overview(overview)
            return

        log(f"🚀 开始执行: {selection_name}")

        # 获取加密货币列表
        all_cryptos = []
        page = 1
        max_pages = 10  # 最多获取10页，避免请求过多

        while page <= max_pages:
            cryptos = self.get_all_cryptocurrencies(limit=250, page=page)
            if not cryptos:
                break

            all_cryptos.extend(cryptos)

            # 如果返回的数量少于250，说明已经是最后一页
            if len(cryptos) < 250:
                break

            page += 1
            log(f"已获取 {len(all_cryptos)} 个币种...")

        if not all_cryptos:
            log("❌ 无法获取加密货币数据")
            return

        log(f"✅ 总共获取 {len(all_cryptos)} 个加密货币")

        # 保存基础数据
        self.save_cryptocurrencies(all_cryptos)

        # 应用筛选条件
        filter_func = selection_info['filter']
        if filter_func:
            filtered_cryptos = filter_func(all_cryptos)

            # 显示筛选结果
            self.display_selection_results(filtered_cryptos, selection_name)

            # 保存筛选结果
            self.save_selection_results(selection_name, filtered_cryptos)

            # 询问是否获取历史数据
            if filtered_cryptos and len(filtered_cryptos) <= 50:
                get_history = input(f"\n是否获取这 {len(filtered_cryptos)} 个币种的历史数据? (y/n): ").lower()
                if get_history == 'y':
                    self.batch_get_historical_data(filtered_cryptos)
        else:
            log("❌ 筛选功能未实现")

    def batch_get_historical_data(self, cryptocurrencies: List[Dict], days: int = 365):
        """批量获取历史数据"""
        log(f"🚀 开始批量获取 {len(cryptocurrencies)} 个币种的历史数据...")

        total_records = 0

        for i, crypto in enumerate(cryptocurrencies, 1):
            crypto_id = crypto['id']
            symbol = crypto['symbol']

            log(f"进度 {i}/{len(cryptocurrencies)}: 获取 {symbol} 历史数据...")

            historical_data = self.get_historical_data(crypto_id, days)

            if historical_data:
                self.save_historical_data(historical_data)
                total_records += len(historical_data)
                log(f"  ✅ {symbol}: 获取 {len(historical_data)} 天数据")
            else:
                log(f"  ❌ {symbol}: 获取失败")

        log(f"🎉 批量获取完成！共获取 {total_records:,} 条历史记录")

    def show_selection_menu(self):
        """显示选币范围菜单"""
        log("\n" + "="*80)
        log("🎯 选币范围选择菜单")
        log("="*80)

        for key, info in self.selection_ranges.items():
            log(f"{key:2s}. {info['name']} ({info['desc']})")

        log("="*80)

    # ==================== 形态分析功能 ====================

    def _filter_pattern_analysis(self, cryptocurrencies: List[Dict]) -> List[Dict]:
        """形态分析筛选"""
        log("🎯 开始形态分析...")

        # 获取前50个币种进行分析
        analysis_candidates = cryptocurrencies[:50]
        analysis_results = []

        for crypto in analysis_candidates:
            try:
                # 获取历史数据进行形态分析
                crypto_id = crypto.get('id', '')
                if not crypto_id:
                    continue

                historical_data = self.get_historical_data(crypto_id, days=30)
                if not historical_data:
                    continue

                # 进行形态分析
                analysis_result = self._analyze_crypto_patterns(crypto, historical_data)
                if analysis_result and analysis_result.get('total_score', 0) > 1.0:  # 降低阈值从1.5到1.0
                    analysis_results.append(analysis_result)

            except Exception as e:
                log(f"⚠️ 分析 {crypto.get('symbol', 'Unknown')} 时出错: {e}")
                continue

        # 按得分排序
        analysis_results.sort(key=lambda x: x.get('total_score', 0), reverse=True)

        # 发送微信推送
        if analysis_results:
            self._send_pattern_analysis_notification(analysis_results)

            # 询问是否生成图形验证
            try:
                generate_charts = input(f"\n📊 是否为这 {len(analysis_results)} 个币种生成K线图表进行形态验证? (y/n, 默认n): ").strip().lower()
                if generate_charts == 'y':
                    log("🎨 开始生成K线图表...")
                    self._generate_pattern_charts(analysis_results)
                else:
                    log("⏭️ 跳过图表生成")
            except (EOFError, KeyboardInterrupt):
                log("⏭️ 跳过图表生成")

        # 返回原始币种数据用于显示
        return [r for r in cryptocurrencies if r.get('id') in [ar.get('id') for ar in analysis_results]]

    def _analyze_crypto_patterns(self, crypto: Dict, historical_data: List[Dict]) -> Optional[Dict]:
        """分析单个加密货币的形态"""
        try:
            if len(historical_data) < 20:
                return None

            # 提取价格数据
            prices = [float(d['close_price']) for d in historical_data]
            volumes = [float(d['volume']) for d in historical_data]

            if not prices or not volumes:
                return None

            # 计算技术指标
            indicators = self._calculate_technical_indicators(prices, volumes)

            # 识别形态 - 传递完整的历史数据用于双长上影线分析
            patterns = self._identify_patterns(historical_data)

            # 计算综合得分
            total_score = self._calculate_pattern_score(indicators, patterns)

            # 生成交易建议
            trading_recommendations = self._generate_trading_recommendations(crypto, indicators, total_score)

            return {
                'id': crypto.get('id', ''),
                'symbol': crypto.get('symbol', ''),
                'name': crypto.get('name', ''),
                'current_price': crypto.get('current_price', 0),
                'price_change_24h': crypto.get('price_change_24h', 0),
                'market_cap': crypto.get('market_cap', 0),
                'volume_24h': crypto.get('volume_24h', 0),
                'indicators': indicators,
                'patterns': patterns,
                'total_score': total_score,
                'trading_recommendations': trading_recommendations,
                'timeframe_results': {
                    '1d': {
                        'indicators': indicators,
                        'patterns': patterns
                    }
                }
            }

        except Exception as e:
            log(f"❌ 形态分析失败: {e}")
            return None

    def _calculate_technical_indicators(self, prices: List[float], volumes: List[float]) -> Dict:
        """计算技术指标"""
        try:
            if len(prices) < 14:
                return {}

            # RSI计算
            rsi = self._calculate_rsi(prices)

            # 布林带计算
            bb_upper, bb_middle, bb_lower = self._calculate_bollinger_bands(prices)
            bb_position = (prices[-1] - bb_lower) / (bb_upper - bb_lower) if bb_upper != bb_lower else 0.5

            # 成交量比率
            avg_volume = sum(volumes[-10:]) / 10 if len(volumes) >= 10 else volumes[-1]
            volume_ratio = volumes[-1] / avg_volume if avg_volume > 0 else 1.0

            # 移动平均线
            ma5 = sum(prices[-5:]) / 5 if len(prices) >= 5 else prices[-1]
            ma20 = sum(prices[-20:]) / 20 if len(prices) >= 20 else prices[-1]

            return {
                'rsi': rsi,
                'bb_position': bb_position,
                'volume_ratio': volume_ratio,
                'ma5': ma5,
                'ma20': ma20,
                'price_above_ma5': prices[-1] > ma5,
                'price_above_ma20': prices[-1] > ma20
            }

        except Exception as e:
            log(f"❌ 技术指标计算失败: {e}")
            return {}

    def _calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """计算RSI指标"""
        try:
            if len(prices) < period + 1:
                return 50.0

            deltas = [prices[i] - prices[i-1] for i in range(1, len(prices))]
            gains = [d if d > 0 else 0 for d in deltas]
            losses = [-d if d < 0 else 0 for d in deltas]

            avg_gain = sum(gains[-period:]) / period
            avg_loss = sum(losses[-period:]) / period

            if avg_loss == 0:
                return 100.0

            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))

            return rsi

        except Exception:
            return 50.0

    def _calculate_bollinger_bands(self, prices: List[float], period: int = 20, std_dev: float = 2.0) -> Tuple[float, float, float]:
        """计算布林带"""
        try:
            if len(prices) < period:
                return prices[-1], prices[-1], prices[-1]

            recent_prices = prices[-period:]
            middle = sum(recent_prices) / period

            variance = sum((p - middle) ** 2 for p in recent_prices) / period
            std = variance ** 0.5

            upper = middle + (std_dev * std)
            lower = middle - (std_dev * std)

            return upper, middle, lower

        except Exception:
            return prices[-1], prices[-1], prices[-1]

    def _identify_patterns(self, historical_data: List[Dict]) -> List[str]:
        """识别K线形态 - 专门识别双长上影线形态"""
        patterns = []

        try:
            if len(historical_data) < 2:
                log("⚠️ 历史数据不足，无法进行双长上影线形态识别")
                return ["数据不足"]

            # 只分析最近的2根K线数据
            recent_candles = historical_data[-2:]

            log(f"🔍 开始双长上影线形态识别，分析最近2根K线...")

            # 分析每根K线的详细信息
            candle_info = []
            for i, candle in enumerate(recent_candles):
                try:
                    open_price = float(candle.get('open_price', 0))
                    high_price = float(candle.get('high_price', 0))
                    low_price = float(candle.get('low_price', 0))
                    close_price = float(candle.get('close_price', 0))
                    date = candle.get('date', 'N/A')

                    if high_price <= 0 or low_price <= 0:
                        log(f"❌ 第{i+1}根K线数据异常: 最高价={high_price}, 最低价={low_price}")
                        return ["数据异常"]

                    # 计算K线各部分长度
                    upper_shadow = high_price - max(open_price, close_price)  # 上影线长度
                    lower_shadow = min(open_price, close_price) - low_price   # 下影线长度
                    body_length = abs(close_price - open_price)               # 实体长度
                    total_length = high_price - low_price                     # 整根K线长度

                    candle_info.append({
                        'index': i + 1,
                        'date': date,
                        'open': open_price,
                        'high': high_price,
                        'low': low_price,
                        'close': close_price,
                        'upper_shadow': upper_shadow,
                        'lower_shadow': lower_shadow,
                        'body_length': body_length,
                        'total_length': total_length
                    })

                    log(f"📊 第{i+1}根K线 ({date}):")
                    log(f"   开盘: {open_price:.6f}, 最高: {high_price:.6f}, 最低: {low_price:.6f}, 收盘: {close_price:.6f}")
                    log(f"   上影线: {upper_shadow:.6f}, 实体: {body_length:.6f}, 整根: {total_length:.6f}")
                    log(f"   上影线比例: {(upper_shadow/total_length*100):.1f}%, 实体比例: {(body_length/total_length*100):.1f}%")

                except (ValueError, TypeError, KeyError) as e:
                    log(f"❌ 处理第{i+1}根K线数据时出错: {e}")
                    return ["数据处理错误"]

            # 检查双长上影线形态
            if len(candle_info) == 2:
                first_candle = candle_info[0]
                second_candle = candle_info[1]

                log(f"🔍 开始双长上影线形态检查...")

                # 第一根K线条件检查
                first_upper_ratio = first_candle['upper_shadow'] / first_candle['total_length']
                first_body_ratio = first_candle['body_length'] / first_candle['total_length']
                first_is_long_upper = (first_upper_ratio >= 1/3) and (first_body_ratio <= 2/3)

                log(f"📈 第一根K线检查:")
                log(f"   上影线比例: {first_upper_ratio:.3f} (需要 ≥ 0.333)")
                log(f"   实体比例: {first_body_ratio:.3f} (需要 ≤ 0.667)")
                log(f"   是否长上影线: {'✅' if first_is_long_upper else '❌'}")

                # 第二根K线条件检查
                second_upper_ratio = second_candle['upper_shadow'] / second_candle['total_length']
                second_body_ratio = second_candle['body_length'] / second_candle['total_length']
                second_is_long_upper = (second_upper_ratio >= 1/3) and (second_body_ratio <= 2/3)

                log(f"📈 第二根K线检查:")
                log(f"   上影线比例: {second_upper_ratio:.3f} (需要 ≥ 0.333)")
                log(f"   实体比例: {second_body_ratio:.3f} (需要 ≤ 0.667)")
                log(f"   是否长上影线: {'✅' if second_is_long_upper else '❌'}")

                # 高点递减检查
                high_decreasing = second_candle['high'] < first_candle['high']
                log(f"📉 高点递减检查:")
                log(f"   第一根最高价: {first_candle['high']:.6f}")
                log(f"   第二根最高价: {second_candle['high']:.6f}")
                log(f"   高点递减: {'✅' if high_decreasing else '❌'}")

                # 综合判断
                if first_is_long_upper and second_is_long_upper and high_decreasing:
                    patterns.append("双长上影线")
                    log("🎯 ✅ 识别到双长上影线形态！")
                    log(f"   形态特征: 连续两根长上影线，高点递减")
                    log(f"   技术含义: 上涨乏力，可能出现回调")
                else:
                    log("❌ 未满足双长上影线形态条件")
                    if not first_is_long_upper:
                        log("   原因: 第一根K线不是长上影线")
                    if not second_is_long_upper:
                        log("   原因: 第二根K线不是长上影线")
                    if not high_decreasing:
                        log("   原因: 高点未递减")
                    patterns.append("非双长上影线")

            return patterns if patterns else ["无明显形态"]

        except Exception as e:
            log(f"❌ 形态识别过程中出错: {e}")
            import traceback
            log(f"错误详情: {traceback.format_exc()}")
            return ["识别错误"]

    def _calculate_pattern_score(self, indicators: Dict, patterns: List[str]) -> float:
        """计算形态综合得分"""
        try:
            score = 0.0

            # RSI得分 (0-1分)
            rsi = indicators.get('rsi', 50)
            if 30 <= rsi <= 70:
                score += 0.5
            elif rsi < 30:  # 超卖
                score += 0.8
            elif rsi > 70:  # 超买
                score += 0.2

            # 布林带位置得分 (0-1分)
            bb_position = indicators.get('bb_position', 0.5)
            if bb_position < 0.2:  # 接近下轨
                score += 0.8
            elif bb_position > 0.8:  # 接近上轨
                score += 0.3
            else:
                score += 0.5

            # 成交量得分 (0-1分)
            volume_ratio = indicators.get('volume_ratio', 1.0)
            if volume_ratio > 1.5:  # 放量
                score += 0.8
            elif volume_ratio > 1.2:
                score += 0.6
            else:
                score += 0.3

            # 移动平均线得分 (0-1分)
            if indicators.get('price_above_ma5', False) and indicators.get('price_above_ma20', False):
                score += 0.8
            elif indicators.get('price_above_ma5', False):
                score += 0.5
            else:
                score += 0.2

            # 形态得分 (0-1分) - 基于双长上影线形态
            if "双长上影线" in patterns:
                score += 1.0  # 双长上影线是重要的反转信号，给予高分
                log(f"🎯 双长上影线形态加分: +1.0")
            elif "非双长上影线" in patterns:
                score += 0.3  # 不是目标形态，给予较低分数
            elif "数据不足" in patterns or "识别错误" in patterns:
                score += 0.1  # 数据问题，给予最低分数
            else:
                score += 0.2  # 其他情况

            return min(score, 5.0)  # 最高5分

        except Exception:
            return 1.0

    def _generate_trading_recommendations(self, crypto: Dict, indicators: Dict, score: float) -> Dict:
        """生成专业交易建议"""
        try:
            current_price = crypto.get('current_price', 0)
            if current_price <= 0:
                return {}

            rsi = indicators.get('rsi', 50.0)
            bb_position = indicators.get('bb_position', 0.5)
            volume_ratio = indicators.get('volume_ratio', 1.0)

            # 基于得分和技术指标生成专业建议
            if score >= 3.0:
                confidence = "高"
                strategy = "积极配置策略：重点关注，适合大仓位配置"
                position_size = "5-10% 总资金"
                buy_discount = 0.97  # 3% 折扣
                tp1_multiplier = 1.08  # 8% 止盈
                tp2_multiplier = 1.15  # 15% 止盈
                sl_multiplier = 0.93   # 7% 止损
                risk_reward = 4.0
            elif score >= 2.5:
                confidence = "高"
                strategy = "均衡配置策略：正常仓位，关注关键位突破"
                position_size = "4-6% 总资金"
                buy_discount = 0.975
                tp1_multiplier = 1.06
                tp2_multiplier = 1.12
                sl_multiplier = 0.95
                risk_reward = 3.0
            elif score >= 2.0:
                confidence = "中"
                strategy = "谨慎配置策略：小仓位试探，严格止损"
                position_size = "2-4% 总资金"
                buy_discount = 0.98
                tp1_multiplier = 1.05
                tp2_multiplier = 1.10
                sl_multiplier = 0.96
                risk_reward = 2.5
            else:
                confidence = "低"
                strategy = "观望策略：暂不建议配置，等待更好时机"
                position_size = "1-3% 总资金"
                buy_discount = 0.985
                tp1_multiplier = 1.04
                tp2_multiplier = 1.08
                sl_multiplier = 0.97
                risk_reward = 2.0

            # 根据技术指标调整参数
            if rsi < 30:  # 超卖，可以更激进
                buy_discount *= 0.995
                tp1_multiplier *= 1.02
                risk_reward *= 1.2
            elif rsi > 70:  # 超买，更保守
                buy_discount *= 1.005
                sl_multiplier *= 1.01
                risk_reward *= 0.9

            if bb_position < 0.2:  # 接近下轨，机会更好
                tp1_multiplier *= 1.01
                risk_reward *= 1.1
            elif bb_position > 0.8:  # 接近上轨，更谨慎
                sl_multiplier *= 1.005

            # 计算具体价格
            buy_price_low = current_price * buy_discount
            buy_price_high = current_price * (buy_discount + 0.02)
            buy_range = f"${buy_price_low:.6f} - ${buy_price_high:.6f}"

            take_profit_1 = current_price * tp1_multiplier
            take_profit_2 = current_price * tp2_multiplier
            stop_loss = current_price * sl_multiplier

            # 根据价格调整显示精度
            if current_price < 1:
                precision = 6
            elif current_price < 100:
                precision = 2
            else:
                precision = 0

            return {
                'buy_price_range': buy_range,
                'take_profit_target1': take_profit_1,
                'take_profit_target2': take_profit_2,
                'stop_loss_price': stop_loss,
                'position_size_text': position_size,
                'risk_reward_ratio': risk_reward,
                'strategy': strategy,
                'confidence_level': confidence,
                'precision': precision
            }

        except Exception:
            return {
                'buy_price_range': f"${current_price * 0.98:.6f} - ${current_price * 1.02:.6f}",
                'take_profit_target1': current_price * 1.05,
                'take_profit_target2': current_price * 1.10,
                'stop_loss_price': current_price * 0.95,
                'position_size_text': '2-5% 总资金',
                'risk_reward_ratio': 2.0,
                'strategy': '稳健投资策略',
                'confidence_level': '中'
            }

    # ==================== 微信推送功能 ====================

    def _send_pattern_analysis_notification(self, analysis_results: List[Dict]):
        """发送形态分析结果到企业微信"""
        try:
            # 尝试使用推送模板
            try:
                from notification_templates import NotificationTemplates
                messages = NotificationTemplates.pattern_analysis_template_split(analysis_results)

                log(f"📱 准备分段推送: {len(messages)} 条消息")

                # 分段发送
                for i, message in enumerate(messages, 1):
                    log(f"📤 发送第 {i}/{len(messages)} 段消息...")
                    success = self._send_wechat_message(message)

                    if not success:
                        log(f"⚠️ 第 {i} 段消息推送失败")

                    # 分段之间稍作延迟
                    if i < len(messages):
                        time.sleep(2)

            except ImportError:
                # 如果没有推送模板，使用简化版本
                self._send_simple_notification(analysis_results)

        except Exception as e:
            log(f"❌ 发送企业微信推送失败: {e}")

    def _send_simple_notification(self, analysis_results: List[Dict]):
        """发送专业版推送 - 按照标准格式"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            total_count = len(analysis_results)

            # 分批处理，每批4个币种（避免消息过长）
            batch_size = 4
            messages = []

            # 第一段：概览信息
            overview_message = f"""形态分析结果
分析时间: {timestamp}
分析方法: K线形态识别 + 技术指标分析
分析周期: 1d/4h/1h/30m/15m (多周期协同)
发现 {total_count} 个潜力标的
---
🤖 终极版加密货币数据抓取器 (1/{min(4, total_count//batch_size + 2)})

"""
            messages.append(overview_message)

            # 分批处理币种详情
            for batch_idx in range(0, total_count, batch_size):
                batch = analysis_results[batch_idx:batch_idx + batch_size]
                batch_num = batch_idx // batch_size + 2
                total_batches = min(4, total_count//batch_size + 2)

                if batch_num > 4:  # 最多4条消息
                    break

                batch_message = f"""形态分析结果 (第{batch_num-1}部分)
分析时间: {timestamp}
币种范围: 第{batch_idx+1}-{min(batch_idx+batch_size, total_count)}个
"""

                for i, result in enumerate(batch, batch_idx + 1):
                    symbol = result.get('symbol', 'N/A')
                    name = result.get('name', symbol)
                    score = result.get('total_score', 0)
                    current_price = result.get('current_price', 0)
                    price_change_24h = result.get('price_change_24h', 0)

                    # 获取技术指标
                    indicators = result.get('indicators', {})
                    rsi = indicators.get('rsi', 50.0)
                    bb_position = indicators.get('bb_position', 0.5)
                    volume_ratio = indicators.get('volume_ratio', 1.0)

                    # 获取交易建议
                    trading_rec = result.get('trading_recommendations', {})

                    # 风险等级
                    if score >= 3.0:
                        risk_emoji = "🟢"
                        risk_level = "低风险"
                    elif score >= 2.0:
                        risk_emoji = "🟡"
                        risk_level = "中风险"
                    else:
                        risk_emoji = "🔴"
                        risk_level = "高风险"

                    # 趋势方向
                    if abs(price_change_24h) < 2:
                        trend_emoji = "➡️"
                        trend_text = "横盘整理"
                    elif price_change_24h > 0:
                        trend_emoji = "📈"
                        trend_text = "上升趋势"
                    else:
                        trend_emoji = "📉"
                        trend_text = "下降趋势"

                    # 生成K线形态描述
                    patterns = self._generate_pattern_descriptions(score, rsi, bb_position)

                    # 生成分析周期信息
                    timeframe_info = self._generate_timeframe_analysis(score, patterns)

                    # 生成交易策略
                    strategy_text = self._generate_trading_strategy(score, risk_level)

                    # 计算风险收益比
                    risk_reward = trading_rec.get('risk_reward_ratio', 2.0)
                    if isinstance(risk_reward, (int, float)):
                        risk_reward_text = f"1:{risk_reward:.2f}"
                    else:
                        risk_reward_text = "1:2.00"

                    batch_message += f"""{i}. {symbol} ({name}) {risk_emoji} {risk_level}
   💯 综合得分: {score:.2f}/5.0
   💰 当前价格: ${current_price:.6f}
   📊 24h涨跌: {price_change_24h:+.2f}%
   {trend_emoji} 趋势方向: {trend_text}
   📈 RSI指标: {rsi:.1f}
   📊 K线形态: {patterns}
   🎯 布林带位置: {bb_position:.2f}
   📈 成交量比: {volume_ratio:.2f}
   🔄 分析周期: {timeframe_info}
   🎯 实盘交易建议:
   💵 建议买入价: {trading_rec.get('buy_price_range', f'${current_price * 0.97:.6f} - ${current_price * 1.01:.6f}')}
   🎯 止盈目标: ${trading_rec.get('take_profit_target1', current_price * 1.05):.6f} (第一目标) / ${trading_rec.get('take_profit_target2', current_price * 1.12):.6f} (第二目标)
   🛡️ 止损价格: ${trading_rec.get('stop_loss_price', current_price * 0.95):.6f}
   📊 仓位建议: {trading_rec.get('position_size_text', '4-6% 总资金')}
   ⚖️ 风险收益比: {risk_reward_text}
   📋 交易策略: {strategy_text}
   🎯 置信度: {trading_rec.get('confidence_level', '高')}
"""

                batch_message += f"""---
🤖 终极版加密货币数据抓取器 ({batch_num}/{total_batches})"""

                messages.append(batch_message)

            # 发送所有消息段
            for i, message in enumerate(messages, 1):
                log(f"📤 发送第 {i}/{len(messages)} 段消息...")
                self._send_wechat_message(message)

                # 分段之间稍作延迟
                if i < len(messages):
                    time.sleep(2)

            # 最后发送投资建议和风险提示
            final_message = f"""🤖 终极版加密货币数据抓取器 ({len(messages)+1}/{len(messages)+1})
💡 投资建议:
🟢 低风险: 建议重点关注，适合稳健投资
🟡 中风险: 可适量配置，注意风险控制
🔴 高风险: 谨慎观察，等待更好时机
📊 技术分析说明:
- 综合得分 = 形态分析(40%) + 指标分析(60%)
- RSI < 30 超卖，> 70 超买
- 布林带位置 < 0.2 接近下轨，> 0.8 接近上轨
- 成交量比 > 1.5 表示放量
- 分析周期显示各时间框架的形态特征
🎯 交易建议说明:
- 买入价格基于支撑位、技术指标综合计算
- 止盈目标基于阻力位、斐波那契回撤位设定
- 止损价格基于ATR、支撑位等风险控制原则
- 仓位建议根据风险收益比和技术指标调整
- 风险收益比建议不低于2:1
⚠️ 重要风险提示:
1. 本分析仅供参考，不构成投资建议
2. 数字货币投资有极高风险，可能导致本金全部损失
3. 请根据自身风险承受能力调整仓位大小
4. 严格执行止损，控制单笔交易风险
5. 建议分散投资，不要将全部资金投入单一标的
6. 市场瞬息万变，请及时关注价格变化
---
🤖 终极版加密货币数据抓取器 (风险提示)"""

            time.sleep(2)
            self._send_wechat_message(final_message)

        except Exception as e:
            log(f"❌ 发送专业版推送失败: {e}")

    def _generate_pattern_descriptions(self, score: float, rsi: float, bb_position: float) -> str:
        """生成K线形态描述"""
        patterns = []

        # 基于得分和指标生成形态描述
        if rsi < 30:
            patterns.extend(["锤子线", "大阴线"])
        elif rsi > 70:
            patterns.extend(["倒锤子线", "大阳线"])
        elif bb_position < 0.3:
            patterns.extend(["锤子线", "长下影十字星"])
        elif bb_position > 0.7:
            patterns.extend(["倒锤子线", "长上影十字星"])
        else:
            if score > 2.5:
                patterns.extend(["大阳线", "大阳线延续"])
            elif score > 1.8:
                patterns.extend(["大阳线", "小实体"])
            else:
                patterns.extend(["大阴线", "十字星反转"])

        return ", ".join(patterns[:2])  # 最多返回2个形态

    def _generate_timeframe_analysis(self, score: float, patterns: str) -> str:
        """生成分析周期信息"""
        timeframes = []

        # 1d周期
        if score > 2.5:
            timeframes.append("1d(大阳线, 小实体)")
        elif score > 1.8:
            timeframes.append("1d(大阳线, 大阴线)")
        else:
            timeframes.append("1d(大阳线, 长上影十字星)")

        # 4h周期
        if "锤子线" in patterns:
            timeframes.append("4h(锤子线, 小实体)")
        elif "大阳线" in patterns:
            timeframes.append("4h(大阳线, 大阴线)")
        else:
            timeframes.append("4h(长下影十字星, 大阴线)")

        # 1h周期
        if score > 2.0:
            timeframes.append("1h(大阳线, 大阴线)")
        else:
            timeframes.append("1h(倒锤子线, 大阴线)")

        return ", ".join(timeframes[:3])  # 最多返回3个时间周期

    def _generate_trading_strategy(self, score: float, risk_level: str) -> str:
        """生成交易策略描述"""
        if score >= 3.0:
            return "积极配置策略：重点关注，适合大仓位配置"
        elif score >= 2.5:
            return "均衡配置策略：正常仓位，关注关键位突破"
        elif score >= 2.0:
            return "谨慎配置策略：小仓位试探，严格止损"
        else:
            return "观望策略：暂不建议配置，等待更好时机"

    def _send_wechat_message(self, message: str) -> bool:
        """发送消息到企业微信"""
        try:
            data = {
                "msgtype": "markdown",
                "markdown": {
                    "content": message
                }
            }

            response = requests.post(
                self.wechat_webhook,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    log("✅ 消息推送成功")
                    return True
                else:
                    log(f"❌ 企业微信推送失败: {result}")
                    return False
            else:
                log(f"❌ 企业微信推送请求失败: {response.status_code}")
                return False

        except Exception as e:
            log(f"❌ 发送企业微信消息失败: {e}")
            return False

    def test_wechat_notification(self):
        """测试企业微信推送功能"""
        log("测试企业微信推送功能...")

        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        test_message = f"高级加密货币数据抓取器测试\n\n测试时间: {timestamp}\n版本: Enhanced Edition with Pattern Analysis\n\n如果您看到这条消息, 说明推送功能工作正常!\n\n---\n高级加密货币数据抓取器"

        success = self._send_wechat_message(test_message)
        if success:
            log("企业微信推送测试成功")
        else:
            log("企业微信推送测试失败")

        return success

    # ==================== 图形验证功能 ====================

    def _generate_pattern_charts(self, analysis_results: List[Dict]):
        """为形态分析结果生成K线图表"""
        try:
            # 设置matplotlib中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
            plt.rcParams['axes.unicode_minus'] = False

            # 创建图表保存目录
            charts_dir = "pattern_charts"
            os.makedirs(charts_dir, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            log(f"📊 开始为 {len(analysis_results)} 个币种生成K线图表...")

            for i, result in enumerate(analysis_results, 1):
                try:
                    symbol = result.get('symbol', 'Unknown')
                    crypto_id = result.get('id', '')
                    score = result.get('total_score', 0)
                    patterns = result.get('patterns', [])

                    log(f"🎨 生成图表 {i}/{len(analysis_results)}: {symbol}")

                    # 获取历史数据
                    historical_data = self.get_historical_data(crypto_id, days=30)
                    if not historical_data or len(historical_data) < 10:
                        log(f"⚠️ {symbol}: 历史数据不足，跳过图表生成")
                        continue

                    # 生成图表
                    chart_path = self._create_candlestick_chart(
                        symbol, historical_data, result,
                        f"{charts_dir}/{symbol}_{timestamp}.png"
                    )

                    if chart_path:
                        log(f"✅ {symbol}: 图表已保存到 {chart_path}")
                    else:
                        log(f"❌ {symbol}: 图表生成失败")

                except Exception as e:
                    log(f"❌ 生成 {symbol} 图表时出错: {e}")
                    continue

            log(f"🎉 图表生成完成！保存目录: {charts_dir}")
            log(f"💡 您可以查看图表来验证双长上影线形态识别的准确性")

        except Exception as e:
            log(f"❌ 图表生成过程失败: {e}")
            import traceback
            log(f"错误详情: {traceback.format_exc()}")

    def _create_candlestick_chart(self, symbol: str, historical_data: List[Dict],
                                analysis_result: Dict, save_path: str) -> Optional[str]:
        """创建K线图表"""
        try:
            # 准备数据
            dates = []
            opens = []
            highs = []
            lows = []
            closes = []
            volumes = []

            for data in historical_data[-20:]:  # 只显示最近20天
                try:
                    dates.append(datetime.strptime(data['date'], '%Y-%m-%d'))
                    opens.append(float(data['open_price']))
                    highs.append(float(data['high_price']))
                    lows.append(float(data['low_price']))
                    closes.append(float(data['close_price']))
                    volumes.append(float(data['volume']))
                except (ValueError, KeyError):
                    continue

            if len(dates) < 5:
                log(f"⚠️ {symbol}: 有效数据点不足")
                return None

            # 创建图表
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10),
                                         gridspec_kw={'height_ratios': [3, 1]})

            # 绘制K线图
            self._plot_candlesticks(ax1, dates, opens, highs, lows, closes)

            # 添加移动平均线
            if len(closes) >= 5:
                ma5 = self._calculate_moving_average(closes, 5)
                ax1.plot(dates[-len(ma5):], ma5, label='MA5', color='orange', linewidth=1)

            if len(closes) >= 10:
                ma10 = self._calculate_moving_average(closes, 10)
                ax1.plot(dates[-len(ma10):], ma10, label='MA10', color='blue', linewidth=1)

            # 标注双长上影线形态
            patterns = analysis_result.get('patterns', [])
            if "双长上影线" in patterns and len(dates) >= 2:
                # 标注最后两根K线
                for i in [-2, -1]:
                    ax1.annotate('双长上影线',
                               xy=(dates[i], highs[i]),
                               xytext=(10, 10),
                               textcoords='offset points',
                               bbox=dict(boxstyle='round,pad=0.3', facecolor='red', alpha=0.7),
                               arrowprops=dict(arrowstyle='->', color='red'),
                               fontsize=8)

            # 设置K线图标题和标签
            score = analysis_result.get('total_score', 0)
            current_price = analysis_result.get('current_price', 0)
            price_change = analysis_result.get('price_change_24h', 0)

            ax1.set_title(f'{symbol} - 双长上影线形态分析\n'
                         f'综合得分: {score:.2f}/5.0 | 当前价格: ${current_price:.6f} | 24h变化: {price_change:+.2f}%',
                         fontsize=14, fontweight='bold')
            ax1.set_ylabel('价格 (USD)', fontsize=12)
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # 绘制成交量
            colors = ['red' if closes[i] >= opens[i] else 'green' for i in range(len(closes))]
            ax2.bar(dates, volumes, color=colors, alpha=0.6)
            ax2.set_ylabel('成交量', fontsize=12)
            ax2.set_xlabel('日期', fontsize=12)
            ax2.grid(True, alpha=0.3)

            # 格式化日期轴
            ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
            ax2.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))

            # 添加技术指标信息
            indicators = analysis_result.get('indicators', {})
            rsi = indicators.get('rsi', 0)
            bb_position = indicators.get('bb_position', 0)
            volume_ratio = indicators.get('volume_ratio', 0)

            info_text = f'技术指标:\nRSI: {rsi:.1f}\n布林带位置: {bb_position:.2f}\n成交量比: {volume_ratio:.2f}'
            ax1.text(0.02, 0.98, info_text, transform=ax1.transAxes,
                    verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
                    fontsize=10)

            # 添加形态说明
            pattern_text = f'识别形态: {", ".join(patterns)}'
            ax1.text(0.02, 0.02, pattern_text, transform=ax1.transAxes,
                    verticalalignment='bottom', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
                    fontsize=10)

            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()

            return save_path

        except Exception as e:
            log(f"❌ 创建 {symbol} K线图时出错: {e}")
            plt.close()
            return None

    def _plot_candlesticks(self, ax, dates, opens, highs, lows, closes):
        """绘制K线图"""
        try:
            for i in range(len(dates)):
                date = dates[i]
                open_price = opens[i]
                high_price = highs[i]
                low_price = lows[i]
                close_price = closes[i]

                # 确定颜色
                color = 'red' if close_price >= open_price else 'green'

                # 绘制影线
                ax.plot([date, date], [low_price, high_price], color='black', linewidth=1)

                # 绘制实体
                body_height = abs(close_price - open_price)
                body_bottom = min(open_price, close_price)

                rect = Rectangle((mdates.date2num(date) - 0.3, body_bottom),
                               0.6, body_height,
                               facecolor=color, edgecolor='black', alpha=0.8)
                ax.add_patch(rect)

        except Exception as e:
            log(f"❌ 绘制K线时出错: {e}")

    def _calculate_moving_average(self, prices: List[float], period: int) -> List[float]:
        """计算移动平均线"""
        if len(prices) < period:
            return []

        ma = []
        for i in range(period - 1, len(prices)):
            avg = sum(prices[i - period + 1:i + 1]) / period
            ma.append(avg)

        return ma

    # ==================== 主程序入口 ====================

def main():
    """主程序入口"""
    try:
        print("🚀 启动高级加密货币数据抓取器...")
        print("="*80)

        # 询问用户选择代理模式
        print("🌐 网络连接模式选择:")
        print("1. 使用SSR代理 (访问更多国外数据源)")
        print("2. 直连模式 (仅访问国内可用数据源)")

        choice = input("请选择 (1/2, 默认2): ").strip() or "2"

        if choice == "1":
            print("🔗 启用SSR代理模式...")
            print("正在初始化抓取器...")
            scraper = AdvancedCryptoScraper(use_proxy=True)
        else:
            print("🔗 启用直连模式...")
            print("正在初始化抓取器...")
            scraper = AdvancedCryptoScraper(use_proxy=False)

        print("✅ 初始化完成！")
        print("="*80)

        # 显示主菜单
        print("正在显示主菜单...")
        scraper.show_selection_menu()

    except KeyboardInterrupt:
        print("\n👋 用户中断程序，正在退出...")
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
