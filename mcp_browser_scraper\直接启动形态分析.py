#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
直接启动形态分析功能
"""

import sys
import os
from datetime import datetime
import importlib.util

def log(message):
    """日志函数"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def main():
    """主函数"""
    try:
        print("🚀 直接启动形态分析功能...")
        print("="*80)
        
        # 检查文件是否存在
        script_path = "advanced_crypto_scraper tuxing.py"
        if not os.path.exists(script_path):
            print(f"❌ 文件不存在: {script_path}")
            input("按回车键退出...")
            return
        
        print(f"✅ 找到脚本文件: {script_path}")
        
        # 导入模块
        print("🔧 加载模块...")
        spec = importlib.util.spec_from_file_location("advanced_crypto_scraper_tuxing", script_path)
        if spec is None or spec.loader is None:
            print("❌ 无法加载模块")
            input("按回车键退出...")
            return
            
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        print("✅ 模块加载成功")
        
        # 询问用户选择代理模式
        print("\n🌐 网络连接模式选择:")
        print("1. 使用SSR代理 (访问更多国外数据源)")
        print("2. 直连模式 (仅访问国内可用数据源)")
        
        choice = input("请选择 (1/2, 默认2): ").strip() or "2"
        
        if choice == "1":
            print("🔗 启用SSR代理模式...")
            scraper = module.AdvancedCryptoScraper(use_proxy=True)
        else:
            print("🔗 启用直连模式...")
            scraper = module.AdvancedCryptoScraper(use_proxy=False)
        
        print("✅ 初始化完成！")
        print("="*80)
        
        # 直接调用形态分析功能
        print("🎯 开始执行形态分析...")
        print("="*80)
        
        # 获取加密货币列表
        print("📊 获取加密货币数据...")
        cryptocurrencies = scraper.get_all_cryptocurrencies(limit=100)
        
        if not cryptocurrencies:
            print("❌ 无法获取加密货币数据")
            input("按回车键退出...")
            return
        
        print(f"✅ 成功获取 {len(cryptocurrencies)} 个加密货币数据")
        
        # 执行形态分析
        print("🔍 开始形态分析...")
        filtered_results = scraper._filter_pattern_analysis(cryptocurrencies)
        
        if filtered_results:
            print(f"🎉 形态分析完成！找到 {len(filtered_results)} 个符合条件的币种")
        else:
            print("📊 形态分析完成，未找到符合双长上影线形态的币种")
        
        print("="*80)
        print("✅ 形态分析任务完成！")
        
    except KeyboardInterrupt:
        print("\n👋 用户中断程序")
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
