#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
设置加密货币交易所API密钥的辅助脚本
"""

import os
import json
import getpass

def setup_api_keys():
    """设置API密钥"""
    print("=" * 60)
    print("            加密货币交易所API密钥设置")
    print("=" * 60)
    print("这个脚本将帮助您设置交易所API密钥，以便获取更全面的交易所数据。")
    print("您的密钥将保存在本地文件中，不会上传到任何地方。")
    print()
    
    # 检查是否已存在API密钥文件
    api_keys_file = "api_keys.json"
    existing_keys = {}
    
    if os.path.exists(api_keys_file):
        try:
            with open(api_keys_file, 'r') as f:
                existing_keys = json.load(f)
            print(f"发现已有API密钥配置文件: {api_keys_file}")
        except Exception as e:
            print(f"无法读取现有API密钥文件: {e}")
            existing_keys = {}
    
    # 初始化密钥结构
    api_keys = {
        "binance": {
            "api_key": "",
            "api_secret": ""
        },
        "okx": {
            "api_key": "",
            "api_secret": "",
            "passphrase": ""
        }
    }
    
    # 合并现有密钥
    for exchange in existing_keys:
        if exchange in api_keys:
            api_keys[exchange] = existing_keys[exchange]
    
    # 币安API密钥
    print("\n--- 币安(Binance)API密钥设置 ---")
    if api_keys["binance"]["api_key"]:
        print(f"当前币安API Key: {api_keys['binance']['api_key'][:5]}*****")
        change = input("是否修改币安API密钥? (y/n): ").lower()
        if change == 'y':
            api_keys["binance"]["api_key"] = input("请输入币安API Key: ").strip()
            api_keys["binance"]["api_secret"] = getpass.getpass("请输入币安API Secret: ").strip()
    else:
        setup = input("是否设置币安API密钥? (y/n): ").lower()
        if setup == 'y':
            api_keys["binance"]["api_key"] = input("请输入币安API Key: ").strip()
            api_keys["binance"]["api_secret"] = getpass.getpass("请输入币安API Secret: ").strip()
    
    # OKX API密钥
    print("\n--- OKX API密钥设置 ---")
    if api_keys["okx"]["api_key"]:
        print(f"当前OKX API Key: {api_keys['okx']['api_key'][:5]}*****")
        change = input("是否修改OKX API密钥? (y/n): ").lower()
        if change == 'y':
            api_keys["okx"]["api_key"] = input("请输入OKX API Key: ").strip()
            api_keys["okx"]["api_secret"] = getpass.getpass("请输入OKX API Secret: ").strip()
            api_keys["okx"]["passphrase"] = getpass.getpass("请输入OKX Passphrase: ").strip()
    else:
        setup = input("是否设置OKX API密钥? (y/n): ").lower()
        if setup == 'y':
            api_keys["okx"]["api_key"] = input("请输入OKX API Key: ").strip()
            api_keys["okx"]["api_secret"] = getpass.getpass("请输入OKX API Secret: ").strip()
            api_keys["okx"]["passphrase"] = getpass.getpass("请输入OKX Passphrase: ").strip()
    
    # 保存API密钥
    try:
        with open(api_keys_file, 'w') as f:
            json.dump(api_keys, f, indent=4)
        print(f"\nAPI密钥已保存到: {api_keys_file}")
        print("注意: 请确保该文件的安全，不要分享给他人。")
    except Exception as e:
        print(f"保存API密钥时出错: {e}")
    
    print("\n设置完成！")

if __name__ == "__main__":
    setup_api_keys() 