"""
数字货币数据适配器
用于连接各种数字货币数据源并转换为Qlib格式
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import requests
import time
import warnings
warnings.filterwarnings('ignore')


class CryptoDataAdapter:
    """数字货币数据适配器"""
    
    def __init__(self, config: Dict):
        """
        初始化数据适配器
        
        Args:
            config: 配置字典，包含数据源信息
        """
        self.config = config
        self.data_source = config.get('data_source', 'binance')
        self.api_key = config.get('api_key', '')
        self.api_secret = config.get('api_secret', '')
        self.base_url = self._get_base_url()
        
        # 数据缓存
        self.data_cache = {}
        self.cache_timeout = config.get('cache_timeout', 300)  # 5分钟缓存
        
    def _get_base_url(self) -> str:
        """获取API基础URL"""
        urls = {
            'binance': 'https://api.binance.com',
            'okx': 'https://www.okx.com',
            'huobi': 'https://api.huobi.pro',
            'coinbase': 'https://api.exchange.coinbase.com'
        }
        return urls.get(self.data_source, urls['binance'])
    
    def get_kline_data(self, symbol: str, timeframe: str, 
                      start_time: Optional[datetime] = None,
                      end_time: Optional[datetime] = None,
                      limit: int = 1000) -> Optional[pd.DataFrame]:
        """
        获取K线数据
        
        Args:
            symbol: 交易对符号
            timeframe: 时间周期 ('1m', '5m', '15m', '1h', '4h', '1d')
            start_time: 开始时间
            end_time: 结束时间
            limit: 数据条数限制
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        try:
            # 检查缓存
            cache_key = f"{symbol}_{timeframe}_{start_time}_{end_time}"
            if self._is_cache_valid(cache_key):
                return self.data_cache[cache_key]['data']
            
            # 根据数据源获取数据
            if self.data_source == 'binance':
                data = self._get_binance_klines(symbol, timeframe, start_time, end_time, limit)
            elif self.data_source == 'okx':
                data = self._get_okx_klines(symbol, timeframe, start_time, end_time, limit)
            else:
                # 默认使用模拟数据
                data = self._generate_mock_data(symbol, timeframe, start_time, end_time, limit)
            
            if data is not None:
                # 缓存数据
                self.data_cache[cache_key] = {
                    'data': data,
                    'timestamp': time.time()
                }
            
            return data
            
        except Exception as e:
            print(f"获取 {symbol} 数据失败: {e}")
            return None
    
    def _get_binance_klines(self, symbol: str, timeframe: str,
                           start_time: Optional[datetime] = None,
                           end_time: Optional[datetime] = None,
                           limit: int = 1000) -> Optional[pd.DataFrame]:
        """获取币安K线数据"""
        try:
            url = f"{self.base_url}/api/v3/klines"
            
            # 时间周期映射
            interval_map = {
                '1m': '1m', '5m': '5m', '15m': '15m', '30m': '30m',
                '1h': '1h', '4h': '4h', '1d': '1d', '1w': '1w'
            }
            
            params = {
                'symbol': symbol,
                'interval': interval_map.get(timeframe, '1d'),
                'limit': min(limit, 1000)
            }
            
            if start_time:
                params['startTime'] = int(start_time.timestamp() * 1000)
            if end_time:
                params['endTime'] = int(end_time.timestamp() * 1000)
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if not data:
                return None
            
            # 转换为DataFrame
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'count', 'taker_buy_volume',
                'taker_buy_quote_volume', 'ignore'
            ])
            
            # 数据类型转换
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 设置索引
            df.set_index('timestamp', inplace=True)
            
            # 只保留需要的列
            df = df[['open', 'high', 'low', 'close', 'volume']]
            
            return df
            
        except Exception as e:
            print(f"获取币安数据失败: {e}")
            return None
    
    def _get_okx_klines(self, symbol: str, timeframe: str,
                       start_time: Optional[datetime] = None,
                       end_time: Optional[datetime] = None,
                       limit: int = 1000) -> Optional[pd.DataFrame]:
        """获取OKX K线数据"""
        try:
            url = f"{self.base_url}/api/v5/market/candles"
            
            # 时间周期映射
            bar_map = {
                '1m': '1m', '5m': '5m', '15m': '15m', '30m': '30m',
                '1h': '1H', '4h': '4H', '1d': '1D', '1w': '1W'
            }
            
            params = {
                'instId': symbol,
                'bar': bar_map.get(timeframe, '1D'),
                'limit': str(min(limit, 300))
            }
            
            if start_time:
                params['after'] = str(int(start_time.timestamp() * 1000))
            if end_time:
                params['before'] = str(int(end_time.timestamp() * 1000))
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            
            if result['code'] != '0' or not result['data']:
                return None
            
            data = result['data']
            
            # 转换为DataFrame
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'volCcy', 'volCcyQuote', 'confirm'
            ])
            
            # 数据类型转换
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 设置索引并排序
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
            
            # 只保留需要的列
            df = df[['open', 'high', 'low', 'close', 'volume']]
            
            return df
            
        except Exception as e:
            print(f"获取OKX数据失败: {e}")
            return None
    
    def _generate_mock_data(self, symbol: str, timeframe: str,
                           start_time: Optional[datetime] = None,
                           end_time: Optional[datetime] = None,
                           limit: int = 1000) -> pd.DataFrame:
        """生成模拟数据用于测试"""
        try:
            if end_time is None:
                end_time = datetime.now()
            if start_time is None:
                start_time = end_time - timedelta(days=100)
            
            # 时间周期映射
            freq_map = {
                '1m': '1T', '5m': '5T', '15m': '15T', '30m': '30T',
                '1h': '1H', '4h': '4H', '1d': '1D', '1w': '1W'
            }
            
            freq = freq_map.get(timeframe, '1D')
            
            # 生成时间序列
            date_range = pd.date_range(start=start_time, end=end_time, freq=freq)
            
            if len(date_range) > limit:
                date_range = date_range[-limit:]
            
            # 生成模拟价格数据
            np.random.seed(42)  # 固定随机种子以便复现
            
            # 基础价格 (根据symbol设置不同的基础价格)
            base_prices = {
                'BTCUSDT': 45000, 'ETHUSDT': 3000, 'BNBUSDT': 400,
                'ADAUSDT': 1.2, 'XRPUSDT': 0.6, 'SOLUSDT': 100
            }
            base_price = base_prices.get(symbol, 100)
            
            # 生成价格走势
            returns = np.random.normal(0, 0.02, len(date_range))  # 2%的日波动率
            prices = [base_price]
            
            for ret in returns[1:]:
                new_price = prices[-1] * (1 + ret)
                prices.append(new_price)
            
            prices = np.array(prices)
            
            # 生成OHLC数据
            data = []
            for i, (timestamp, close) in enumerate(zip(date_range, prices)):
                # 生成当日高低开收
                volatility = abs(np.random.normal(0, 0.01))  # 日内波动
                
                open_price = close * (1 + np.random.normal(0, 0.005))
                high = max(open_price, close) * (1 + volatility)
                low = min(open_price, close) * (1 - volatility)
                
                # 生成成交量
                volume = np.random.lognormal(10, 1) * 1000
                
                data.append({
                    'open': open_price,
                    'high': high,
                    'low': low,
                    'close': close,
                    'volume': volume
                })
            
            df = pd.DataFrame(data, index=date_range)
            return df
            
        except Exception as e:
            print(f"生成模拟数据失败: {e}")
            return pd.DataFrame()
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.data_cache:
            return False
        
        cache_time = self.data_cache[cache_key]['timestamp']
        return (time.time() - cache_time) < self.cache_timeout
    
    def get_multiple_symbols_data(self, symbols: List[str], timeframe: str,
                                 start_time: Optional[datetime] = None,
                                 end_time: Optional[datetime] = None) -> Dict[str, pd.DataFrame]:
        """
        批量获取多个交易对的数据
        
        Args:
            symbols: 交易对列表
            timeframe: 时间周期
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            字典，键为交易对，值为对应的DataFrame
        """
        results = {}
        
        for symbol in symbols:
            try:
                data = self.get_kline_data(symbol, timeframe, start_time, end_time)
                if data is not None and len(data) > 0:
                    results[symbol] = data
                
                # 添加延时避免API限制
                time.sleep(0.1)
                
            except Exception as e:
                print(f"获取 {symbol} 数据失败: {e}")
                continue
        
        return results
    
    def clear_cache(self):
        """清空缓存"""
        self.data_cache.clear()
    
    def get_cache_info(self) -> Dict:
        """获取缓存信息"""
        return {
            'cache_size': len(self.data_cache),
            'cache_keys': list(self.data_cache.keys())
        }
