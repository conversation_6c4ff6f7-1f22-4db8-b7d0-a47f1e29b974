import requests

# 使用与主数据收集脚本相同的代理配置
PROXIES = {
    'http': 'socks5h://127.0.0.1:1082',
    'https': 'socks5h://127.0.0.1:1082',
}

# 备选的IP查询服务，如果一个不行可以尝试另一个
IP_CHECK_SERVICES = [
    'https://httpbin.org/ip',
    'https://api.ipify.org?format=json',
    'https://ipinfo.io/json'
]

print("Attempting to fetch public IP address using SOCKS5 proxy (127.0.0.1:1082)...")

for service_url in IP_CHECK_SERVICES:
    print(f"\nTrying service: {service_url}")
    try:
        response = requests.get(service_url, proxies=PROXIES, timeout=15)
        response.raise_for_status()
        print(f"Successfully connected to: {service_url}")
        print("Response JSON:")
        # 尝试打印JSON，如果不是JSON则打印文本
        try:
            ip_data = response.json()
            print(ip_data)
            # 如果是 httpbin.org 或 api.ipify.org，它们会直接返回IP
            if 'origin' in ip_data: # httpbin.org format
                print(f"===> IP address seen by {service_url}: {ip_data['origin']}")
                break 
            elif 'ip' in ip_data: # api.ipify.org or ipinfo.io format
                print(f"===> IP address seen by {service_url}: {ip_data['ip']}")
                break
        except requests.exceptions.JSONDecodeError:
            print("Response was not JSON. Raw text:")
            print(response.text)
            # 如果是纯IP地址文本 (api.ipify.org 不带 format=json 时会返回纯文本)
            # 我们这里用的是 format=json, 但作为备用检查
            if not response.text.startswith('{') and '.' in response.text: # Basic IP format check
                 print(f"===> IP address seen by {service_url}: {response.text.strip()}")
                 break

    except requests.exceptions.ProxyError as pe:
        print(f"Proxy Error while connecting to {service_url}: {pe}")
        print("Please ensure your SOCKS5 proxy (SSR local proxy) is running on 127.0.0.1:1082 and is correctly configured.")
        break # 如果代理本身有问题，尝试其他服务也可能失败
    except requests.exceptions.ConnectTimeout as ct:
        print(f"Connection Timeout while connecting to {service_url} (via proxy): {ct}")
    except requests.exceptions.HTTPError as he:
        print(f"HTTP Error from {service_url}: {he.response.status_code} - {he.response.text[:200]}")
    except requests.exceptions.RequestException as e:
        print(f"General Request Error for {service_url}: {e}")
    except Exception as ex:
        print(f"An unexpected error occurred with {service_url}: {ex}")

print("\nIP check finished.") 