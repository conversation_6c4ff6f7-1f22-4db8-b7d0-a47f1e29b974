#!/bin/bash

# 加密货币新币分析启动脚本

# 检查Python版本
python3 --version &> /dev/null
if [ $? -ne 0 ]; then
    echo "错误: 未找到Python3，请确保已安装Python 3.8或更高版本"
    exit 1
fi

# 检查依赖是否安装
echo "检查依赖..."
pip install -r requirements.txt &> /dev/null

# 默认参数
DAYS=30
OUTPUT_DIR="crypto_reports"
CURRENCY="usd"
LANG="zh"
USE_PROXY=0

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--days)
            DAYS="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -c|--currency)
            CURRENCY="$2"
            shift 2
            ;;
        -l|--lang)
            LANG="$2"
            shift 2
            ;;
        -p|--proxy)
            USE_PROXY=1
            shift
            ;;
        *)
            echo "未知参数: $1"
            echo "用法: $0 [-d 天数] [-o 输出目录] [-c 货币] [-l 语言] [-p]"
            exit 1
            ;;
    esac
done

# 构建命令
CMD="python3 crypto_analyzer.py -d $DAYS -o $OUTPUT_DIR -c $CURRENCY -l $LANG"
if [ $USE_PROXY -eq 1 ]; then
    CMD="$CMD -p"
fi

# 显示配置
echo "==============================================="
echo "       加密货币新币全面分析工具"
echo "==============================================="
echo "分析配置:"
echo "- 分析时间范围: 过去 $DAYS 天"
echo "- 输出目录: $OUTPUT_DIR"
echo "- 计价货币: $CURRENCY"
echo "- 显示语言: $LANG"
if [ $USE_PROXY -eq 1 ]; then
    echo "- 使用代理: 是"
else
    echo "- 使用代理: 否"
fi
echo "==============================================="

# 运行分析
echo "开始分析，请稍候..."
eval $CMD

# 检查是否成功
if [ $? -eq 0 ]; then
    echo "分析完成！报告已保存到 $OUTPUT_DIR 目录。"
else
    echo "分析过程中出现错误，请查看日志了解详情。"
fi 