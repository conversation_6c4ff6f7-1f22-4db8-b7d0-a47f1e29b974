#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
依赖安装脚本
自动安装 advanced_crypto_scraper.py 所需的依赖包
"""

import subprocess
import sys
import os
from datetime import datetime

def log(message):
    """日志函数"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def install_package(package):
    """安装单个包"""
    try:
        log(f"正在安装 {package}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            log(f"✅ {package} 安装成功")
            return True
        else:
            log(f"❌ {package} 安装失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        log(f"❌ {package} 安装超时")
        return False
    except Exception as e:
        log(f"❌ {package} 安装异常: {e}")
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def main():
    """主函数"""
    log("🚀 开始安装依赖包...")
    
    # 必需的包列表
    required_packages = [
        "requests",
        "pandas", 
        "numpy",
        "scipy",
        "pysocks",
        "pyyaml",
        "schedule",
        "aiohttp",
        "python-dateutil"
    ]
    
    # 检查和安装
    success_count = 0
    total_count = len(required_packages)
    
    for package in required_packages:
        # 特殊处理包名映射
        import_name = package
        if package == "pyyaml":
            import_name = "yaml"
        elif package == "python-dateutil":
            import_name = "dateutil"
        
        if check_package(import_name):
            log(f"✅ {package} 已安装")
            success_count += 1
        else:
            if install_package(package):
                success_count += 1
    
    log(f"\n📊 安装结果: {success_count}/{total_count} 个包安装成功")
    
    if success_count == total_count:
        log("🎉 所有依赖包安装完成！")
        log("💡 现在可以运行 advanced_crypto_scraper.py 了")
    else:
        log("⚠️ 部分依赖包安装失败，请手动安装")
        log("💡 可以尝试运行: pip install -r requirements_minimal.txt")

if __name__ == "__main__":
    main()
