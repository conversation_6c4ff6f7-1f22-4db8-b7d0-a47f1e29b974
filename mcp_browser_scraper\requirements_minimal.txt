# 加密货币数据抓取器最小依赖
# 适用于 advanced_crypto_scraper.py

# 核心网络请求
requests>=2.31.0
aiohttp>=3.9.0

# 数据处理
pandas>=2.1.0
numpy>=1.24.0

# 科学计算
scipy>=1.11.0

# 网络代理
pysocks>=1.7.1

# 配置文件
pyyaml>=6.0.1

# 任务调度
schedule>=1.2.0

# 数据库（内置，无需安装）
# sqlite3

# 时间处理
python-dateutil>=2.8.2

# 类型提示（Python 3.8+内置）
# typing

# 异步支持（Python 3.7+内置）
# asyncio

# 其他内置模块
# json, os, sys, time, datetime, traceback, warnings
# hmac, hashlib, base64, urllib, subprocess, socket
# signal, threading, collections
