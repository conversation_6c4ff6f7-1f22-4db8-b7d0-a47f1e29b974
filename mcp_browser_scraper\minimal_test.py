#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最小化测试脚本
"""

print("开始最小化测试...")

try:
    print("测试基本导入...")
    
    # 逐个测试导入
    print("导入 requests...")
    import requests
    print("✅ requests OK")
    
    print("导入 sqlite3...")
    import sqlite3
    print("✅ sqlite3 OK")
    
    print("导入 pandas...")
    import pandas as pd
    print("✅ pandas OK")
    
    print("导入 time...")
    import time
    print("✅ time OK")
    
    print("导入 json...")
    import json
    print("✅ json OK")
    
    print("导入 os...")
    import os
    print("✅ os OK")
    
    print("导入 sys...")
    import sys
    print("✅ sys OK")
    
    print("导入 datetime...")
    from datetime import datetime, timedelta
    print("✅ datetime OK")
    
    print("导入 typing...")
    from typing import Dict, List, Optional, Tuple, Union
    print("✅ typing OK")
    
    print("导入 numpy...")
    import numpy as np
    print("✅ numpy OK")
    
    print("导入 scipy...")
    from scipy.signal import find_peaks
    from scipy.stats import linregress
    print("✅ scipy OK")
    
    print("导入 matplotlib...")
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from matplotlib.patches import Rectangle
    print("✅ matplotlib OK")
    
    print("导入 seaborn...")
    import seaborn as sns
    print("✅ seaborn OK")
    
    print("所有导入测试通过！")
    
    # 测试简单的类定义
    print("测试类定义...")
    
    class TestClass:
        def __init__(self):
            print("TestClass 初始化")
            self.test_var = "test"
        
        def test_method(self):
            print("TestClass 方法调用")
            return "success"
    
    print("创建测试实例...")
    test_obj = TestClass()
    result = test_obj.test_method()
    print(f"测试结果: {result}")
    
    print("✅ 所有测试通过！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    print(f"错误详情: {traceback.format_exc()}")

print("测试完成")
