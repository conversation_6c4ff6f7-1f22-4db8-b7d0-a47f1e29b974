#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试启动问题
"""

import sys
import os
from datetime import datetime

def log(message):
    """日志函数"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")
    sys.stdout.flush()  # 强制刷新输出

def main():
    """主函数"""
    log("🔧 开始调试启动问题...")
    
    try:
        # 检查文件是否存在
        script_path = "advanced_crypto_scraper tuxing.py"
        if not os.path.exists(script_path):
            log(f"❌ 文件不存在: {script_path}")
            return
        
        log(f"✅ 文件存在: {script_path}")
        
        # 逐步导入测试
        log("🔧 测试基础导入...")
        
        import requests
        log("✅ requests 导入成功")
        
        import sqlite3
        log("✅ sqlite3 导入成功")
        
        import pandas as pd
        log("✅ pandas 导入成功")
        
        import numpy as np
        log("✅ numpy 导入成功")
        
        try:
            from scipy.signal import find_peaks
            from scipy.stats import linregress
            log("✅ scipy 导入成功")
        except ImportError as e:
            log(f"⚠️ scipy 导入失败: {e}")
        
        try:
            import matplotlib.pyplot as plt
            import matplotlib.dates as mdates
            from matplotlib.patches import Rectangle
            log("✅ matplotlib 导入成功")
        except ImportError as e:
            log(f"⚠️ matplotlib 导入失败: {e}")
        
        try:
            import seaborn as sns
            log("✅ seaborn 导入成功")
        except ImportError as e:
            log(f"⚠️ seaborn 导入失败: {e}")
        
        log("🔧 开始读取脚本文件...")
        
        # 读取脚本内容
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        log(f"✅ 脚本文件读取成功，长度: {len(content)} 字符")
        
        # 检查是否有明显的语法问题
        log("🔧 检查语法...")
        try:
            compile(content, script_path, 'exec')
            log("✅ 语法检查通过")
        except SyntaxError as e:
            log(f"❌ 语法错误: {e}")
            log(f"   行号: {e.lineno}")
            log(f"   错误内容: {e.text}")
            return
        
        # 尝试执行脚本
        log("🔧 尝试执行脚本...")
        
        # 创建一个新的命名空间来执行脚本
        script_globals = {
            '__name__': '__main__',
            '__file__': script_path
        }
        
        # 执行脚本
        exec(content, script_globals)
        
        log("✅ 脚本执行成功")
        
    except KeyboardInterrupt:
        log("\n👋 用户中断")
    except Exception as e:
        log(f"❌ 调试过程出错: {e}")
        import traceback
        log(f"错误详情: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
