"""
真实数据提供器
使用免费API获取真实的加密货币数据
"""

import pandas as pd
import numpy as np
import requests
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import warnings
warnings.filterwarnings('ignore')


class RealDataProvider:
    """真实数据提供器"""
    
    def __init__(self):
        """初始化数据提供器"""
        # 使用免费的CoinGecko API
        self.coingecko_base_url = "https://api.coingecko.com/api/v3"
        
        # 使用免费的Binance API (无需API密钥)
        self.binance_base_url = "https://api.binance.com/api/v3"
        
        # 币种映射 (CoinGecko ID -> Binance Symbol)
        self.symbol_mapping = {
            'bitcoin': 'BTCUSDT',
            'ethereum': 'ETHUSDT', 
            'binancecoin': 'BNBUSDT',
            'cardano': 'ADAUSDT',
            'ripple': 'XRPUSDT',
            'solana': 'SOLUSDT',
            'polkadot': 'DOTUSDT',
            'dogecoin': 'DOGEUSDT',
            'avalanche-2': 'AVAXUSDT',
            'polygon': 'MATICUSDT',
            'chainlink': 'LINKUSDT',
            'uniswap': 'UNIUSDT',
            'litecoin': 'LTCUSDT',
            'bitcoin-cash': 'BCHUSDT',
            'ethereum-classic': 'ETCUSDT'
        }
        
        # 反向映射
        self.reverse_mapping = {v: k for k, v in self.symbol_mapping.items()}
        
        # 时间周期映射
        self.timeframe_mapping = {
            '1d': '1d',
            '4h': '4h', 
            '1h': '1h',
            '30m': '30m',
            '15m': '15m',
            '5m': '5m',
            '1m': '1m'
        }
        
        # 请求间隔 (避免API限制)
        self.request_delay = 0.2
        
    def get_market_data(self) -> Dict:
        """获取市场概况数据"""
        try:
            url = f"{self.coingecko_base_url}/coins/markets"
            params = {
                'vs_currency': 'usd',
                'order': 'market_cap_desc',
                'per_page': 50,
                'page': 1,
                'sparkline': False,
                'price_change_percentage': '24h,7d'
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            market_data = {}
            for coin in data:
                if coin['symbol'].upper() + 'USDT' in self.symbol_mapping.values():
                    symbol = coin['symbol'].upper() + 'USDT'
                    market_data[symbol] = {
                        'name': coin['name'],
                        'symbol': symbol,
                        'current_price': coin['current_price'],
                        'market_cap': coin['market_cap'],
                        'market_cap_rank': coin['market_cap_rank'],
                        'total_volume': coin['total_volume'],
                        'price_change_24h': coin.get('price_change_percentage_24h', 0),
                        'price_change_7d': coin.get('price_change_percentage_7d_in_currency', 0),
                        'last_updated': coin['last_updated']
                    }
            
            time.sleep(self.request_delay)
            return market_data
            
        except Exception as e:
            print(f"获取市场数据失败: {e}")
            return {}
    
    def get_kline_data(self, symbol: str, timeframe: str, limit: int = 100) -> Optional[pd.DataFrame]:
        """
        获取K线数据
        
        Args:
            symbol: 交易对符号 (如 BTCUSDT)
            timeframe: 时间周期 (1d, 4h, 1h, 30m, 15m)
            limit: 数据条数
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        try:
            # 使用Binance API获取K线数据
            url = f"{self.binance_base_url}/klines"
            
            # 时间周期映射
            interval_map = {
                '1d': '1d',
                '4h': '4h',
                '1h': '1h', 
                '30m': '30m',
                '15m': '15m',
                '5m': '5m',
                '1m': '1m'
            }
            
            interval = interval_map.get(timeframe, '1d')
            
            params = {
                'symbol': symbol,
                'interval': interval,
                'limit': min(limit, 1000)  # Binance限制最多1000条
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if not data:
                print(f"未获取到 {symbol} 的数据")
                return None
            
            # 转换为DataFrame
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # 数据类型转换
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df['open'] = df['open'].astype(float)
            df['high'] = df['high'].astype(float)
            df['low'] = df['low'].astype(float)
            df['close'] = df['close'].astype(float)
            df['volume'] = df['volume'].astype(float)
            
            # 设置索引
            df.set_index('timestamp', inplace=True)
            
            # 只保留需要的列
            df = df[['open', 'high', 'low', 'close', 'volume']]
            
            time.sleep(self.request_delay)
            return df
            
        except Exception as e:
            print(f"获取 {symbol} K线数据失败: {e}")
            return None
    
    def get_multiple_symbols_data(self, symbols: List[str], timeframe: str, limit: int = 100) -> Dict[str, pd.DataFrame]:
        """
        批量获取多个交易对的K线数据
        
        Args:
            symbols: 交易对列表
            timeframe: 时间周期
            limit: 数据条数
            
        Returns:
            字典，键为交易对，值为DataFrame
        """
        results = {}
        
        print(f"📊 开始获取真实数据...")
        print(f"🎯 交易对数量: {len(symbols)}")
        print(f"⏰ 时间周期: {timeframe}")
        print(f"📈 数据条数: {limit}")
        
        for i, symbol in enumerate(symbols, 1):
            print(f"正在获取 {symbol} 数据... ({i}/{len(symbols)})")
            
            data = self.get_kline_data(symbol, timeframe, limit)
            if data is not None and not data.empty:
                results[symbol] = data
                print(f"✅ {symbol} 数据获取成功，共 {len(data)} 条记录")
            else:
                print(f"❌ {symbol} 数据获取失败")
            
            # 避免API限制
            if i < len(symbols):
                time.sleep(self.request_delay)
        
        print(f"\n📊 数据获取完成，成功获取 {len(results)}/{len(symbols)} 个交易对的数据")
        return results
    
    def validate_data_quality(self, data: pd.DataFrame, symbol: str) -> Dict:
        """
        验证数据质量
        
        Args:
            data: K线数据
            symbol: 交易对符号
            
        Returns:
            数据质量报告
        """
        report = {
            'symbol': symbol,
            'total_records': len(data),
            'missing_values': data.isnull().sum().to_dict(),
            'data_range': {
                'start': data.index.min().strftime('%Y-%m-%d %H:%M:%S'),
                'end': data.index.max().strftime('%Y-%m-%d %H:%M:%S')
            },
            'price_stats': {
                'min_price': data['close'].min(),
                'max_price': data['close'].max(),
                'avg_price': data['close'].mean(),
                'price_volatility': data['close'].pct_change().std()
            },
            'volume_stats': {
                'min_volume': data['volume'].min(),
                'max_volume': data['volume'].max(),
                'avg_volume': data['volume'].mean()
            },
            'data_integrity': {
                'no_missing_values': data.isnull().sum().sum() == 0,
                'positive_prices': (data[['open', 'high', 'low', 'close']] > 0).all().all(),
                'valid_ohlc': (data['high'] >= data[['open', 'close']].max(axis=1)).all() and 
                              (data['low'] <= data[['open', 'close']].min(axis=1)).all(),
                'positive_volume': (data['volume'] >= 0).all()
            }
        }
        
        return report
    
    def get_supported_symbols(self) -> List[str]:
        """获取支持的交易对列表"""
        return list(self.symbol_mapping.values())
    
    def test_api_connectivity(self) -> Dict:
        """测试API连接性"""
        results = {}
        
        # 测试Binance API
        try:
            url = f"{self.binance_base_url}/ping"
            response = requests.get(url, timeout=5)
            results['binance'] = {
                'status': 'success' if response.status_code == 200 else 'failed',
                'response_time': response.elapsed.total_seconds(),
                'status_code': response.status_code
            }
        except Exception as e:
            results['binance'] = {
                'status': 'failed',
                'error': str(e)
            }
        
        # 测试CoinGecko API
        try:
            url = f"{self.coingecko_base_url}/ping"
            response = requests.get(url, timeout=5)
            results['coingecko'] = {
                'status': 'success' if response.status_code == 200 else 'failed',
                'response_time': response.elapsed.total_seconds(),
                'status_code': response.status_code
            }
        except Exception as e:
            results['coingecko'] = {
                'status': 'failed',
                'error': str(e)
            }
        
        return results


def test_real_data_provider():
    """测试真实数据提供器"""
    print("🧪 测试真实数据提供器")
    print("=" * 50)
    
    provider = RealDataProvider()
    
    # 测试API连接
    print("1. 测试API连接性...")
    connectivity = provider.test_api_connectivity()
    for api, result in connectivity.items():
        status = result['status']
        print(f"   {api}: {status}")
        if status == 'success':
            print(f"      响应时间: {result.get('response_time', 0):.2f}秒")
    
    # 测试获取单个交易对数据
    print("\n2. 测试获取单个交易对数据...")
    test_symbol = 'BTCUSDT'
    data = provider.get_kline_data(test_symbol, '1d', 50)
    
    if data is not None:
        print(f"✅ {test_symbol} 数据获取成功")
        print(f"   数据条数: {len(data)}")
        print(f"   时间范围: {data.index.min()} 到 {data.index.max()}")
        print(f"   最新价格: {data['close'].iloc[-1]:.2f}")
        
        # 数据质量验证
        quality_report = provider.validate_data_quality(data, test_symbol)
        print(f"   数据完整性: {'✅' if quality_report['data_integrity']['no_missing_values'] else '❌'}")
        print(f"   价格有效性: {'✅' if quality_report['data_integrity']['positive_prices'] else '❌'}")
        print(f"   OHLC逻辑: {'✅' if quality_report['data_integrity']['valid_ohlc'] else '❌'}")
    else:
        print(f"❌ {test_symbol} 数据获取失败")
    
    # 测试获取市场数据
    print("\n3. 测试获取市场概况...")
    market_data = provider.get_market_data()
    
    if market_data:
        print(f"✅ 市场数据获取成功，包含 {len(market_data)} 个交易对")
        for symbol, info in list(market_data.items())[:3]:
            print(f"   {symbol}: {info['name']}, 价格: ${info['current_price']:.2f}, 市值排名: {info['market_cap_rank']}")
    else:
        print("❌ 市场数据获取失败")
    
    return data is not None and len(market_data) > 0


if __name__ == "__main__":
    test_real_data_provider()
