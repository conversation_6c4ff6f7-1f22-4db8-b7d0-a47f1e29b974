#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速形态分析器
结合两个文件的核心功能，快速测试版本
"""

import pandas as pd
import numpy as np
import requests
import time
from datetime import datetime

class QuickPatternAnalyzer:
    """快速形态分析器"""
    
    def __init__(self):
        """初始化"""
        self.wechat_webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=52d1b8a8-a88a-4d41-9664-f1268da20985"
        self.symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT']
        print("快速形态分析器初始化完成")
    
    def get_market_data(self):
        """获取市场数据"""
        try:
            url = "https://api.coingecko.com/api/v3/coins/markets"
            params = {
                'vs_currency': 'usd',
                'order': 'market_cap_desc',
                'per_page': 10,
                'page': 1,
                'sparkline': False,
                'price_change_percentage': '24h'
            }
            
            response = requests.get(url, params=params, timeout=10)
            data = response.json()
            
            market_data = {}
            symbol_mapping = {
                'bitcoin': 'BTCUSDT', 'ethereum': 'ETHUSDT', 'binancecoin': 'BNBUSDT',
                'cardano': 'ADAUSDT', 'solana': 'SOLUSDT'
            }
            
            for coin in data:
                coin_id = coin['id']
                if coin_id in symbol_mapping:
                    symbol = symbol_mapping[coin_id]
                    market_data[symbol] = {
                        'current_price': coin['current_price'],
                        'price_change_24h': coin.get('price_change_percentage_24h', 0),
                        'name': coin['name']
                    }
            
            print(f"获取到 {len(market_data)} 个币种数据")
            return market_data
            
        except Exception as e:
            print(f"获取市场数据失败: {e}")
            return {}
    
    def generate_simple_kline(self, symbol, market_data):
        """生成简单K线数据"""
        if symbol not in market_data:
            return None
        
        real_data = market_data[symbol]
        current_price = real_data['current_price']
        
        # 生成50个周期的模拟数据
        periods = 50
        dates = pd.date_range(end=datetime.now(), periods=periods, freq='D')
        
        # 基于真实价格变化生成历史数据
        price_change = real_data['price_change_24h'] / 100
        np.random.seed(hash(symbol) % 10000)
        
        # 生成价格序列
        prices = []
        base_price = current_price
        
        for i in range(periods):
            if i == periods - 1:
                price = current_price
            else:
                # 反向计算历史价格
                days_back = periods - 1 - i
                trend = price_change / periods * days_back
                noise = np.random.normal(0, 0.02)
                price = current_price / (1 + trend + noise)
                price = max(price, 0.0001)
            prices.append(price)
        
        # 生成OHLCV数据
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            open_price = close * (1 + np.random.normal(0, 0.005))
            high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.01)))
            low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.01)))
            volume = np.random.uniform(1000000, 5000000)
            
            data.append({
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume
            })
        
        return pd.DataFrame(data, index=dates)
    
    def identify_patterns(self, df):
        """识别K线形态"""
        if len(df) < 2:
            return {'patterns': [], 'score': 0.0}
        
        latest = df.iloc[-1]
        prev = df.iloc[-2]
        
        patterns = []
        score = 0.0
        
        # 计算K线参数
        body = abs(latest['close'] - latest['open'])
        upper_shadow = latest['high'] - max(latest['open'], latest['close'])
        lower_shadow = min(latest['open'], latest['close']) - latest['low']
        total_range = latest['high'] - latest['low']
        
        # 形态识别
        if total_range > 0:
            if body < 0.1 * total_range:
                patterns.append('十字星')
                score += 1.5
            elif upper_shadow > 2 * body and lower_shadow < 0.5 * body:
                patterns.append('长上影线')
                score += 1.0
            elif lower_shadow > 2 * body and upper_shadow < 0.5 * body:
                patterns.append('锤子线')
                score += 2.0
            elif body > 0.6 * total_range:
                if latest['close'] > latest['open']:
                    patterns.append('大阳线')
                    score += 1.5
                else:
                    patterns.append('大阴线')
                    score -= 0.5
        
        # 组合形态
        if (latest['close'] > latest['open'] and prev['close'] < prev['open'] and
            latest['close'] > prev['open'] and latest['open'] < prev['close']):
            patterns.append('看涨吞没')
            score += 2.5
        
        return {'patterns': patterns, 'score': max(min(score, 5.0), 0.0)}
    
    def calculate_indicators(self, df):
        """计算技术指标"""
        data = df.copy()
        
        # RSI
        delta = data['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = (-delta.where(delta < 0, 0))
        
        avg_gain = gain.rolling(14).mean()
        avg_loss = loss.rolling(14).mean()
        rs = avg_gain / avg_loss
        data['rsi'] = 100 - (100 / (1 + rs))
        
        # 移动平均线
        data['sma_5'] = data['close'].rolling(5).mean()
        data['sma_20'] = data['close'].rolling(20).mean()
        
        # MACD
        ema12 = data['close'].ewm(span=12).mean()
        ema26 = data['close'].ewm(span=26).mean()
        data['macd'] = ema12 - ema26
        data['macd_signal'] = data['macd'].ewm(span=9).mean()
        
        return data
    
    def evaluate_indicators(self, data):
        """评估技术指标"""
        if len(data) < 20:
            return {'score': 0.0, 'details': {}}
        
        latest = data.iloc[-1]
        score = 0.0
        
        # RSI评分
        rsi = latest.get('rsi', 50)
        if not pd.isna(rsi):
            if rsi < 30:
                score += 2.0  # 超卖
            elif rsi < 40:
                score += 1.0
            elif rsi > 70:
                score -= 1.0  # 超买
        
        # 移动平均线
        if (not pd.isna(latest['sma_5']) and not pd.isna(latest['sma_20'])):
            if latest['close'] > latest['sma_5'] > latest['sma_20']:
                score += 2.0  # 多头排列
            elif latest['close'] > latest['sma_20']:
                score += 1.0
        
        # MACD
        if (not pd.isna(latest['macd']) and not pd.isna(latest['macd_signal'])):
            if latest['macd'] > latest['macd_signal']:
                score += 1.0
        
        return {
            'score': max(min(score, 5.0), 0.0),
            'details': {
                'rsi': rsi if not pd.isna(rsi) else 50,
                'macd': latest.get('macd', 0),
                'price': latest['close']
            }
        }
    
    def analyze_symbol(self, symbol, market_data):
        """分析单个交易对"""
        try:
            # 生成K线数据
            df = self.generate_simple_kline(symbol, market_data)
            if df is None:
                return None
            
            # 计算指标
            df = self.calculate_indicators(df)
            
            # 形态分析
            pattern_result = self.identify_patterns(df)
            
            # 指标分析
            indicator_result = self.evaluate_indicators(df)
            
            # 综合评分
            total_score = (pattern_result['score'] * 0.4 + 
                          indicator_result['score'] * 0.6)
            
            return {
                'symbol': symbol,
                'total_score': total_score,
                'pattern_score': pattern_result['score'],
                'indicator_score': indicator_result['score'],
                'patterns': pattern_result['patterns'],
                'rsi': indicator_result['details']['rsi'],
                'current_price': indicator_result['details']['price'],
                'name': market_data[symbol]['name']
            }
            
        except Exception as e:
            print(f"分析 {symbol} 失败: {e}")
            return None
    
    def send_wechat_message(self, message):
        """发送企业微信消息"""
        try:
            data = {
                "msgtype": "markdown",
                "markdown": {
                    "content": message
                }
            }
            
            response = requests.post(
                self.wechat_webhook,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            result = response.json()
            return result.get('errcode') == 0
            
        except Exception as e:
            print(f"发送微信消息失败: {e}")
            return False
    
    def format_message(self, results):
        """格式化消息"""
        if not results:
            return "# 快速形态分析\n\n暂无符合条件的交易信号"
        
        message = f"""# 快速形态分析结果
分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
发现 {len(results)} 个潜力标的

"""
        
        for i, result in enumerate(results, 1):
            patterns_str = ', '.join(result['patterns']) if result['patterns'] else '无明显形态'
            
            message += f"""{i}. {result['symbol']} ({result['name']})
   综合得分: {result['total_score']:.2f}
   当前价格: ${result['current_price']:.6f}
   RSI: {result['rsi']:.1f}
   K线形态: {patterns_str}

"""
        
        message += "风险提示: 仅供参考，请谨慎投资"
        return message
    
    def run(self):
        """运行分析"""
        print("开始快速形态分析...")
        
        # 获取市场数据
        market_data = self.get_market_data()
        if not market_data:
            print("无法获取市场数据")
            return []
        
        results = []
        
        for symbol in self.symbols:
            if symbol in market_data:
                print(f"分析 {symbol}...")
                result = self.analyze_symbol(symbol, market_data)
                if result and result['total_score'] > 1.5:
                    results.append(result)
                    print(f"✓ {symbol} 完成，得分: {result['total_score']:.2f}")
                else:
                    print(f"○ {symbol} 得分较低")
        
        # 排序
        results.sort(key=lambda x: x['total_score'], reverse=True)
        
        # 显示结果
        if results:
            print("\n" + "="*50)
            print("分析结果:")
            for i, result in enumerate(results, 1):
                print(f"{i}. {result['symbol']}: {result['total_score']:.2f}")
            
            # 发送微信推送
            message = self.format_message(results)
            if self.send_wechat_message(message):
                print("企业微信推送成功")
            else:
                print("企业微信推送失败")
        else:
            print("未发现符合条件的标的")
        
        return results


def main():
    """主程序"""
    print("启动快速形态分析器")
    
    try:
        analyzer = QuickPatternAnalyzer()
        results = analyzer.run()
        print("分析完成")
        return 0
    except Exception as e:
        print(f"程序运行失败: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
