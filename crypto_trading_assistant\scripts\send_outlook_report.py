# -*- coding: utf-8 -*-
"""
通过 Outlook邮箱 发送加密货币形态分析报告的独立脚本。
此脚本会查找最新的已生成报告文件进行发送。
"""

import smtplib
import json
import os
import logging
import sys
import re
import glob
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.application import MIMEApplication
from datetime import datetime
from pathlib import Path

# 将项目根目录（scripts的上级目录）添加到sys.path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    # 我们仍然需要 PatternAnalyzer 来格式化从JSON加载的数据
    from scripts.crypto_pattern_analyzer import PatternAnalyzer, _serialize_data # _serialize_data 可能在加载json时用到
except ImportError as e:
    print(f"无法导入 PatternAnalyzer: {e}")
    print("请确保脚本从项目根目录运行，或者项目结构正确。")
    sys.exit(1)

# 配置日志
log_file_path = Path(project_root) / 'logs' / 'send_outlook_report.log'
log_file_path.parent.mkdir(parents=True, exist_ok=True) # 创建logs目录

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(log_file_path, mode='a', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def load_config():
    """加载配置文件 config.json"""
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
            logger.info(f"配置文件 {config_path} 加载成功。")
            return config_data
    except FileNotFoundError:
        logger.error(f"配置文件 {config_path} 未找到。")
    except json.JSONDecodeError:
        logger.error(f"配置文件 {config_path} 格式错误。")
    except Exception as e:
        logger.error(f"加载配置文件 {config_path} 失败: {str(e)}")
    return {}

def send_email_outlook(subject, body_html, recipients, config, attachment_path=None):
    """通过Outlook SMTP发送邮件"""
    smtp_config = config.get("outlook_smtp", {})
    smtp_server = smtp_config.get("server")
    smtp_port = int(smtp_config.get("port", 587))
    smtp_user = smtp_config.get("user")
    smtp_password = smtp_config.get("password")
    sender_email = smtp_config.get("sender_email", smtp_user)

    if not all([smtp_server, smtp_port, smtp_user, smtp_password, sender_email]):
        logger.error("Outlook SMTP 配置不完整，无法发送邮件。请检查 config.json 中的 outlook_smtp 部分。")
        return False

    msg = MIMEMultipart()
    msg['From'] = sender_email
    msg['To'] = ", ".join(recipients)
    msg['Subject'] = subject
    msg.attach(MIMEText(body_html, 'html', 'utf-8'))

    if attachment_path and os.path.exists(attachment_path):
        try:
            with open(attachment_path, "rb") as attachment_file:
                part = MIMEApplication(attachment_file.read(), Name=os.path.basename(attachment_path))
            part['Content-Disposition'] = f'attachment; filename="{os.path.basename(attachment_path)}"'
            msg.attach(part)
            logger.info(f"已附加文件: {attachment_path}")
        except Exception as e:
            logger.error(f"附加文件 {attachment_path} 失败: {str(e)}")
    elif attachment_path:
        logger.warning(f"附件路径 {attachment_path} 不存在或未提供，邮件将不含附件。")

    try:
        logger.info(f"尝试连接到 SMTP 服务器: {smtp_server}:{smtp_port}")
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.ehlo()
            server.starttls()
            server.ehlo()
            logger.info("尝试使用提供的凭据登录...")
            server.login(smtp_user, smtp_password)
            logger.info("SMTP 登录成功。")
            server.sendmail(sender_email, recipients, msg.as_string())
        logger.info(f"邮件已成功发送至: {', '.join(recipients)}")
        return True
    except smtplib.SMTPAuthenticationError:
        logger.error("SMTP认证失败。请检查Outlook邮箱的用户名和密码，并确保启用了SMTP访问或使用了应用密码（如果启用了两步验证）。")
    except smtplib.SMTPServerDisconnected:
        logger.error("SMTP服务器意外断开连接。")
    except smtplib.SMTPConnectError as e:
        logger.error(f"无法连接到SMTP服务器 {smtp_server}:{smtp_port}。错误: {e}")
    except Exception as e:
        logger.error(f"发送邮件失败: {type(e).__name__} - {str(e)}")
        import traceback
        logger.debug(traceback.format_exc())
    return False

def find_latest_report_files(results_dir_path: Path):
    """在指定目录查找最新的 summary_*.json 和 summary_*.csv 文件"""
    json_pattern = str(results_dir_path / "summary_*.json")
    csv_pattern = str(results_dir_path / "summary_*.csv")

    list_of_json_files = glob.glob(json_pattern)
    list_of_csv_files = glob.glob(csv_pattern)

    if not list_of_json_files:
        logger.warning(f"在 {results_dir_path} 未找到JSON报告文件 (模式: summary_*.json)。")
        return None, None

    latest_json_file = max(list_of_json_files, key=os.path.getctime)
    logger.info(f"找到最新的JSON报告文件: {latest_json_file}")

    # 尝试找到与最新JSON文件时间戳匹配的CSV文件
    timestamp_match = re.search(r"summary_(\d{8}_\d{6})\.json$", os.path.basename(latest_json_file))
    latest_csv_file = None
    if timestamp_match:
        timestamp_str = timestamp_match.group(1)
        expected_csv_filename = f"summary_{timestamp_str}.csv"
        expected_csv_filepath = results_dir_path / expected_csv_filename
        if expected_csv_filepath.exists():
            latest_csv_file = str(expected_csv_filepath)
            logger.info(f"找到对应的CSV报告文件: {latest_csv_file}")
        else:
            logger.warning(f"未找到与最新JSON时间戳匹配的CSV文件: {expected_csv_filename}。将尝试查找最新的CSV文件。")

    if not latest_csv_file and list_of_csv_files:
        latest_csv_file = max(list_of_csv_files, key=os.path.getctime)
        logger.info(f"找到最新的CSV报告文件 (可能与JSON时间戳不完全匹配): {latest_csv_file}")
    elif not list_of_csv_files:
         logger.warning(f"在 {results_dir_path} 未找到CSV报告文件 (模式: summary_*.csv)。")


    return latest_json_file, latest_csv_file


def format_report_from_json(json_filepath: str, analyzer_instance: PatternAnalyzer):
    """从JSON文件加载数据并使用PatternAnalyzer格式化报告"""
    try:
        with open(json_filepath, 'r', encoding='utf-8') as f:
            # 注意：crypto_pattern_analyzer.py 中的 _serialize_data 用于转储，
            # json.load 通常不需要特殊的反序列化器，除非有自定义对象。
            # datetime 会被转储为 ISO 格式字符串，pandas NaT 为 null。
            # PatternAnalyzer.format_summary_report 应该能处理这些。
            new_listings_data = json.load(f)
        logger.info(f"成功从 {json_filepath} 加载数据。")

        # 使用 PatternAnalyzer 的 format_summary_report 方法
        summary_report_text_raw = analyzer_instance.format_summary_report(new_listings_data)
        ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
        summary_report_text_clean = ansi_escape.sub('', summary_report_text_raw)
        summary_report_html = f"<pre>{summary_report_text_clean}</pre>"
        logger.info("报告内容已根据加载的JSON数据格式化。")
        return summary_report_html
    except FileNotFoundError:
        logger.error(f"JSON报告文件未找到: {json_filepath}")
    except json.JSONDecodeError:
        logger.error(f"JSON报告文件格式错误: {json_filepath}")
    except Exception as e:
        logger.error(f"从JSON文件格式化报告时出错 ({json_filepath}): {str(e)}")
        import traceback
        logger.debug(traceback.format_exc())
    return None


def main():
    """主执行函数"""
    logger.info("--- 开始执行邮件报告脚本 (查找最新报告模式) ---")
    config = load_config()
    if not config:
        logger.error("无法加载配置，程序退出。")
        sys.exit(1)

    # PatternAnalyzer 实例用于格式化报告，它本身不进行新的分析
    # 它会使用其内部的配置 (如config.json中的微信键等，尽管在此脚本中不直接使用)
    analyzer_api_keys = config.get("gateio_api_keys")
    analyzer_use_proxy = config.get("use_proxy", False)
    analyzer_request_delay = float(config.get("request_delay", 1.0))

    try:
        logger.info("正在初始化 PatternAnalyzer (仅用于报告格式化)...")
        # PatternAnalyzer 的 __init__ 会尝试加载 scripts/config.json
        analyzer = PatternAnalyzer(
            api_keys=analyzer_api_keys,
            use_proxy=analyzer_use_proxy,
            request_delay=analyzer_request_delay
        )
        logger.info("PatternAnalyzer 初始化成功。")
    except Exception as e:
        logger.error(f"初始化 PatternAnalyzer 失败: {str(e)}")
        sys.exit(1)

    # 确定报告结果目录
    # crypto_pattern_analyzer.py 将结果保存在其自身的 results_dir，
    # 通常是项目根目录下的 "analysis_results"
    results_dir = Path(project_root) / "analysis_results"
    if hasattr(analyzer, 'results_dir') and isinstance(analyzer.results_dir, Path):
        results_dir = analyzer.results_dir # 使用 PatternAnalyzer 中定义的路径
    results_dir.mkdir(parents=True, exist_ok=True) #确保目录存在

    logger.info(f"将从目录 {results_dir} 查找最新的报告文件。")
    latest_json_file, latest_csv_file = find_latest_report_files(results_dir)

    if not latest_json_file:
        logger.error("未能找到最新的JSON报告文件，无法发送邮件。请先运行 crypto_pattern_analyzer.py 生成报告。")
        sys.exit(1)

    report_html = format_report_from_json(latest_json_file, analyzer)

    if not report_html:
        logger.error("未能从JSON文件格式化报告内容，邮件未发送。")
        sys.exit(1)

    # 提取时间戳用于邮件主题
    report_timestamp = "未知时间"
    timestamp_match_subject = re.search(r"summary_(\d{8}_\d{6})\.json$", os.path.basename(latest_json_file))
    if timestamp_match_subject:
        try:
            dt_obj = datetime.strptime(timestamp_match_subject.group(1), "%Y%m%d_%H%M%S")
            report_timestamp = dt_obj.strftime("%Y-%m-%d %H:%M:%S")
        except ValueError:
            report_timestamp = timestamp_match_subject.group(1) # 使用原始时间戳字符串

    # 发送邮件
    email_recipients = config.get("email_recipients", [])
    if not isinstance(email_recipients, list) or not email_recipients:
        logger.warning("未在config.json中正确配置收件人列表 (email_recipients应为非空列表)，邮件未发送。")
        sys.exit(1)

    email_subject = f"加密货币新上市币种分析报告 - {report_timestamp}"

    logger.info(f"准备发送邮件，主题: '{email_subject}', 收件人: {email_recipients}")
    success = send_email_outlook(
        subject=email_subject,
        body_html=report_html,
        recipients=email_recipients,
        config=config,
        attachment_path=latest_csv_file # latest_csv_file 可能是 None
    )

    if success:
        logger.info("邮件报告流程成功完成。")
    else:
        logger.error("邮件报告流程执行失败。")
    logger.info("--- 邮件报告脚本执行完毕 ---")

if __name__ == "__main__":
    main()