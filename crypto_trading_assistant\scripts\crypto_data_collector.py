#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
加密货币数据收集模块

负责从交易所获取市场数据，包括：
- 新币信息
- 历史K线数据
- 市场深度数据
- 交易对信息
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional, Union
import ccxt
import requests
from pycoingecko import CoinGeckoAPI
import time
import json

class CryptoDataCollector:
    """
    加密货币数据收集器
    """
    def __init__(self, exchange_id: str = 'gateio'):
        """
        初始化数据收集器
        
        Args:
            exchange_id: 交易所ID，默认为gateio
        """
        # 初始化交易所API
        self.exchange = getattr(ccxt, exchange_id)({
            'enableRateLimit': True,
            'timeout': 30000,
            'options': {
                'defaultType': 'spot'
            }
        })
        
        # 初始化CoinGecko API
            self.cg = CoinGeckoAPI()
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('data_collector.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 创建数据目录
        self.data_dir = 'data'
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
            
        # 创建缓存目录
        self.cache_dir = os.path.join(self.data_dir, 'cache')
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
            
    def get_new_coins(self, days: int = 60) -> pd.DataFrame:
        """
        获取新上市的币种信息
        
        Args:
            days: 获取最近多少天内上市的币种，默认60天
            
        Returns:
            包含新币信息的DataFrame
        """
        try:
            # 获取所有交易对
            markets = self.exchange.load_markets()
            
            # 获取币种信息
            coins_info = []
            for symbol, market in markets.items():
                try:
                    # 获取币种详细信息
                    coin_id = market['base'].lower()
                    coin_info = self.cg.get_coin_by_id(coin_id)
                    
                    if not coin_info:
                        continue
                        
                    # 获取上市日期
                    genesis_date = coin_info.get('genesis_date')
                    if not genesis_date:
                        continue
                        
                    genesis_date = datetime.strptime(genesis_date, '%Y-%m-%d')
                    if (datetime.now() - genesis_date).days > days:
                        continue
            
                    # 获取当前价格和交易量
                    ticker = self.exchange.fetch_ticker(symbol)
                    
                    coin_data = {
                        'symbol': symbol,
                        'name': coin_info['name'],
                        'first_trade_date': genesis_date.strftime('%Y-%m-%d'),
                        'current_price': ticker['last'],
                        'volume_24h': ticker['quoteVolume'],
                        'price_change_24h': ticker['percentage'],
                        'market_cap': coin_info.get('market_data', {}).get('market_cap', {}).get('usd'),
                        'total_volume': coin_info.get('market_data', {}).get('total_volume', {}).get('usd'),
                        'high_24h': ticker['high'],
                        'low_24h': ticker['low']
                    }
                    
                    coins_info.append(coin_data)
                    
                except Exception as e:
                    self.logger.warning(f"获取{symbol}信息失败: {str(e)}")
                    continue
                    
            return pd.DataFrame(coins_info)

        except Exception as e:
            self.logger.error(f"获取新币信息失败: {str(e)}")
            return pd.DataFrame()
            
    def get_historical_data(self, symbol: str, timeframe: str = '1d',
                          limit: int = 1000) -> pd.DataFrame:
        """
        获取历史K线数据
        
        Args:
            symbol: 交易对符号
            timeframe: 时间周期，默认1d
            limit: 获取的K线数量，默认1000
            
        Returns:
            包含历史K线数据的DataFrame
        """
        try:
            # 检查缓存
            cache_file = os.path.join(self.cache_dir, f"{symbol.replace('/', '_')}_{timeframe}.csv")
            if os.path.exists(cache_file):
                df = pd.read_csv(cache_file, index_col=0, parse_dates=True)
                if len(df) >= limit:
                    return df.tail(limit)
                    
            # 获取K线数据
            ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            
            # 转换为DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            # 保存到缓存
            df.to_csv(cache_file)
            
            return df
            
        except Exception as e:
            self.logger.error(f"获取{symbol}历史数据失败: {str(e)}")
            return pd.DataFrame()
            
    def get_market_depth(self, symbol: str, limit: int = 20) -> Dict:
        """
        获取市场深度数据
        
        Args:
            symbol: 交易对符号
            limit: 获取的深度级别，默认20
            
        Returns:
            包含市场深度数据的字典
        """
        try:
            # 获取订单簿
            order_book = self.exchange.fetch_order_book(symbol, limit)
            
            # 计算买卖盘压力
            bid_volume = sum(bid[1] for bid in order_book['bids'])
            ask_volume = sum(ask[1] for ask in order_book['asks'])
            
            # 计算价格压力
            bid_pressure = sum(bid[0] * bid[1] for bid in order_book['bids'])
            ask_pressure = sum(ask[0] * ask[1] for ask in order_book['asks'])
            
            return {
                'bids': order_book['bids'],
                'asks': order_book['asks'],
                'bid_volume': bid_volume,
                'ask_volume': ask_volume,
                'bid_pressure': bid_pressure,
                'ask_pressure': ask_pressure,
                'spread': order_book['asks'][0][0] - order_book['bids'][0][0],
                'spread_percentage': (order_book['asks'][0][0] - order_book['bids'][0][0]) / order_book['bids'][0][0] * 100
            }
            
        except Exception as e:
            self.logger.error(f"获取{symbol}市场深度数据失败: {str(e)}")
            return {}
            
    def get_trading_pairs(self) -> pd.DataFrame:
        """
        获取所有交易对信息
        
        Returns:
            包含交易对信息的DataFrame
        """
        try:
            # 获取所有交易对
            markets = self.exchange.load_markets()
            
            # 提取交易对信息
            pairs_info = []
            for symbol, market in markets.items():
                try:
                    pair_info = {
                        'symbol': symbol,
                        'base': market['base'],
                        'quote': market['quote'],
                        'active': market['active'],
                        'precision': market['precision'],
                        'limits': market['limits'],
                        'info': market['info']
                    }
                    pairs_info.append(pair_info)
                except Exception as e:
                    self.logger.warning(f"获取{symbol}交易对信息失败: {str(e)}")
                    continue
                    
            return pd.DataFrame(pairs_info)
            
        except Exception as e:
            self.logger.error(f"获取交易对信息失败: {str(e)}")
            return pd.DataFrame()
            
    def get_market_summary(self) -> pd.DataFrame:
        """
        获取市场概况
        
        Returns:
            包含市场概况的DataFrame
        """
        try:
            # 获取所有交易对
            markets = self.exchange.load_markets()
            
            # 获取市场概况
            summary = []
            for symbol, market in markets.items():
                try:
                    ticker = self.exchange.fetch_ticker(symbol)
                    
                    market_data = {
                        'symbol': symbol,
                        'last_price': ticker['last'],
                        'volume_24h': ticker['quoteVolume'],
                        'price_change_24h': ticker['percentage'],
                        'high_24h': ticker['high'],
                        'low_24h': ticker['low'],
                        'bid': ticker['bid'],
                        'ask': ticker['ask']
                    }
                    
                    summary.append(market_data)
                    
                except Exception as e:
                    self.logger.warning(f"获取{symbol}市场概况失败: {str(e)}")
                    continue
        
            return pd.DataFrame(summary)
            
        except Exception as e:
            self.logger.error(f"获取市场概况失败: {str(e)}")
            return pd.DataFrame()

if __name__ == "__main__":
    # 测试代码
    print("CRYPTO DATA COLLECTOR SCRIPT STARTED - TEST PRINT 123")
    collector = CryptoDataCollector()
    
    # 获取新币信息
    new_coins = collector.get_new_coins(days=60)
    print("\n新币信息:")
    print(new_coins)
    
    # 获取历史数据
    historical_data = collector.get_historical_data('BTC/USDT', timeframe='1d', limit=100)
    print("\n历史数据:")
    print(historical_data)
    
    # 获取市场深度
    market_depth = collector.get_market_depth('BTC/USDT')
    print("\n市场深度:")
    print(json.dumps(market_depth, indent=2))
    
    # 获取交易对信息
    trading_pairs = collector.get_trading_pairs()
    print("\n交易对信息:")
    print(trading_pairs)
    
    # 获取市场概况
    market_summary = collector.get_market_summary()
    print("\n市场概况:")
    print(market_summary)