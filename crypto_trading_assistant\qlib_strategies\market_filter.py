"""
数字货币市场筛选器
支持按市值、上市时间、交易量等条件筛选标的
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import requests
import json


class CryptoMarketFilter:
    """数字货币市场筛选器"""
    
    def __init__(self):
        """初始化筛选器"""
        # 全市场数字货币列表 (模拟数据，实际应从交易所API获取)
        self.all_symbols = self._initialize_market_data()
        
    def _initialize_market_data(self) -> List[Dict]:
        """初始化市场数据"""
        # 模拟市场数据，实际应从CoinGecko、CoinMarketCap等API获取
        market_data = [
            # 主流币 (大市值)
            {'symbol': 'BTCUSDT', 'name': 'Bitcoin', 'market_cap': 850000000000, 'listing_date': '2017-09-13', 'category': 'Layer1', 'volume_24h': 15000000000},
            {'symbol': 'ETHUSDT', 'name': 'Ethereum', 'market_cap': 280000000000, 'listing_date': '2017-08-17', 'category': 'Layer1', 'volume_24h': 8000000000},
            {'symbol': 'BNBUSDT', 'name': 'BNB', 'market_cap': 65000000000, 'listing_date': '2017-07-25', 'category': 'Exchange', 'volume_24h': 1200000000},
            {'symbol': 'XRPUSDT', 'name': 'XRP', 'market_cap': 35000000000, 'listing_date': '2018-05-16', 'category': 'Payment', 'volume_24h': 1800000000},
            {'symbol': 'SOLUSDT', 'name': 'Solana', 'market_cap': 45000000000, 'listing_date': '2020-08-11', 'category': 'Layer1', 'volume_24h': 2500000000},
            
            # 中等市值币
            {'symbol': 'ADAUSDT', 'name': 'Cardano', 'market_cap': 18000000000, 'listing_date': '2018-04-17', 'category': 'Layer1', 'volume_24h': 800000000},
            {'symbol': 'DOTUSDT', 'name': 'Polkadot', 'market_cap': 12000000000, 'listing_date': '2020-08-19', 'category': 'Layer0', 'volume_24h': 600000000},
            {'symbol': 'LINKUSDT', 'name': 'Chainlink', 'market_cap': 8500000000, 'listing_date': '2019-06-20', 'category': 'Oracle', 'volume_24h': 450000000},
            {'symbol': 'MATICUSDT', 'name': 'Polygon', 'market_cap': 7200000000, 'listing_date': '2019-04-28', 'category': 'Layer2', 'volume_24h': 380000000},
            {'symbol': 'AVAXUSDT', 'name': 'Avalanche', 'market_cap': 15000000000, 'listing_date': '2020-09-22', 'category': 'Layer1', 'volume_24h': 700000000},
            
            # DeFi币
            {'symbol': 'UNIUSDT', 'name': 'Uniswap', 'market_cap': 5200000000, 'listing_date': '2020-09-17', 'category': 'DeFi', 'volume_24h': 180000000},
            {'symbol': 'AAVEUSDT', 'name': 'Aave', 'market_cap': 2800000000, 'listing_date': '2020-10-02', 'category': 'DeFi', 'volume_24h': 120000000},
            {'symbol': 'COMPUSDT', 'name': 'Compound', 'market_cap': 850000000, 'listing_date': '2020-06-16', 'category': 'DeFi', 'volume_24h': 45000000},
            {'symbol': 'MKRUSDT', 'name': 'Maker', 'market_cap': 1200000000, 'listing_date': '2019-11-18', 'category': 'DeFi', 'volume_24h': 65000000},
            {'symbol': 'CRVUSDT', 'name': 'Curve', 'market_cap': 680000000, 'listing_date': '2020-08-13', 'category': 'DeFi', 'volume_24h': 35000000},
            
            # 小市值/新币
            {'symbol': 'APTUSDT', 'name': 'Aptos', 'market_cap': 3500000000, 'listing_date': '2022-10-19', 'category': 'Layer1', 'volume_24h': 250000000},
            {'symbol': 'SUIUSDT', 'name': 'Sui', 'market_cap': 2100000000, 'listing_date': '2023-05-03', 'category': 'Layer1', 'volume_24h': 180000000},
            {'symbol': 'OPUSDT', 'name': 'Optimism', 'market_cap': 2800000000, 'listing_date': '2022-05-31', 'category': 'Layer2', 'volume_24h': 150000000},
            {'symbol': 'ARBUSDT', 'name': 'Arbitrum', 'market_cap': 2200000000, 'listing_date': '2023-03-23', 'category': 'Layer2', 'volume_24h': 200000000},
            {'symbol': 'INJUSDT', 'name': 'Injective', 'market_cap': 1800000000, 'listing_date': '2021-10-19', 'category': 'DeFi', 'volume_24h': 85000000},
            
            # Meme币
            {'symbol': 'DOGEUSDT', 'name': 'Dogecoin', 'market_cap': 12000000000, 'listing_date': '2019-07-05', 'category': 'Meme', 'volume_24h': 800000000},
            {'symbol': 'SHIBUSDT', 'name': 'Shiba Inu', 'market_cap': 5500000000, 'listing_date': '2021-05-10', 'category': 'Meme', 'volume_24h': 350000000},
            {'symbol': 'PEPEUSDT', 'name': 'Pepe', 'market_cap': 850000000, 'listing_date': '2023-05-05', 'category': 'Meme', 'volume_24h': 120000000},
            
            # AI/新兴概念
            {'symbol': 'FETUSDT', 'name': 'Fetch.ai', 'market_cap': 1200000000, 'listing_date': '2019-02-28', 'category': 'AI', 'volume_24h': 65000000},
            {'symbol': 'AGIXUSDT', 'name': 'SingularityNET', 'market_cap': 680000000, 'listing_date': '2018-01-19', 'category': 'AI', 'volume_24h': 35000000},
            {'symbol': 'OCEANUSDT', 'name': 'Ocean Protocol', 'market_cap': 420000000, 'listing_date': '2019-05-06', 'category': 'AI', 'volume_24h': 25000000},
            
            # 最新上市 (模拟)
            {'symbol': 'NEWCOIN1USDT', 'name': 'NewCoin1', 'market_cap': 150000000, 'listing_date': '2024-12-01', 'category': 'New', 'volume_24h': 15000000},
            {'symbol': 'NEWCOIN2USDT', 'name': 'NewCoin2', 'market_cap': 85000000, 'listing_date': '2024-11-28', 'category': 'New', 'volume_24h': 8000000},
            {'symbol': 'NEWCOIN3USDT', 'name': 'NewCoin3', 'market_cap': 220000000, 'listing_date': '2024-12-10', 'category': 'New', 'volume_24h': 25000000},
        ]
        
        return market_data
    
    def get_filter_options(self) -> Dict:
        """获取筛选选项"""
        return {
            'market_cap_ranges': {
                'mega_cap': {'min': 100000000000, 'max': None, 'name': '超大市值 (>1000亿美元)'},
                'large_cap': {'min': 10000000000, 'max': 100000000000, 'name': '大市值 (100-1000亿美元)'},
                'mid_cap': {'min': 1000000000, 'max': 10000000000, 'name': '中市值 (10-100亿美元)'},
                'small_cap': {'min': 100000000, 'max': 1000000000, 'name': '小市值 (1-10亿美元)'},
                'micro_cap': {'min': 0, 'max': 100000000, 'name': '微市值 (<1亿美元)'}
            },
            'listing_periods': {
                'veteran': {'days': 1825, 'name': '老牌币种 (>5年)'},
                'mature': {'days': 1095, 'name': '成熟币种 (3-5年)'},
                'established': {'days': 365, 'name': '稳定币种 (1-3年)'},
                'recent': {'days': 180, 'name': '较新币种 (6个月-1年)'},
                'new': {'days': 90, 'name': '新币种 (3-6个月)'},
                'very_new': {'days': 30, 'name': '最新币种 (<3个月)'}
            },
            'categories': {
                'Layer1': 'Layer1公链',
                'Layer2': 'Layer2扩容',
                'DeFi': 'DeFi生态',
                'Exchange': '交易所代币',
                'Payment': '支付类',
                'Oracle': '预言机',
                'Meme': 'Meme币',
                'AI': 'AI概念',
                'New': '新上市',
                'Layer0': '跨链协议'
            },
            'volume_ranges': {
                'high_volume': {'min': 1000000000, 'name': '高交易量 (>10亿美元)'},
                'medium_volume': {'min': 100000000, 'max': 1000000000, 'name': '中等交易量 (1-10亿美元)'},
                'low_volume': {'min': 10000000, 'max': 100000000, 'name': '低交易量 (1000万-1亿美元)'},
                'very_low_volume': {'min': 0, 'max': 10000000, 'name': '极低交易量 (<1000万美元)'}
            }
        }
    
    def filter_by_market_cap(self, min_cap: Optional[float] = None, 
                           max_cap: Optional[float] = None) -> List[str]:
        """按市值筛选"""
        filtered = []
        for coin in self.all_symbols:
            market_cap = coin['market_cap']
            if min_cap is not None and market_cap < min_cap:
                continue
            if max_cap is not None and market_cap > max_cap:
                continue
            filtered.append(coin['symbol'])
        return filtered
    
    def filter_by_listing_date(self, days_since_listing: int, 
                             comparison: str = 'less_than') -> List[str]:
        """按上市时间筛选"""
        filtered = []
        cutoff_date = datetime.now() - timedelta(days=days_since_listing)
        
        for coin in self.all_symbols:
            listing_date = datetime.strptime(coin['listing_date'], '%Y-%m-%d')
            
            if comparison == 'less_than':  # 新上市
                if listing_date > cutoff_date:
                    filtered.append(coin['symbol'])
            elif comparison == 'greater_than':  # 老币种
                if listing_date <= cutoff_date:
                    filtered.append(coin['symbol'])
        
        return filtered
    
    def filter_by_category(self, categories: List[str]) -> List[str]:
        """按类别筛选"""
        filtered = []
        for coin in self.all_symbols:
            if coin['category'] in categories:
                filtered.append(coin['symbol'])
        return filtered
    
    def filter_by_volume(self, min_volume: Optional[float] = None,
                        max_volume: Optional[float] = None) -> List[str]:
        """按交易量筛选"""
        filtered = []
        for coin in self.all_symbols:
            volume = coin['volume_24h']
            if min_volume is not None and volume < min_volume:
                continue
            if max_volume is not None and volume > max_volume:
                continue
            filtered.append(coin['symbol'])
        return filtered
    
    def get_predefined_lists(self) -> Dict[str, List[str]]:
        """获取预定义的选币列表"""
        return {
            'all_market': [coin['symbol'] for coin in self.all_symbols],
            'top_10': [coin['symbol'] for coin in sorted(self.all_symbols, key=lambda x: x['market_cap'], reverse=True)[:10]],
            'top_20': [coin['symbol'] for coin in sorted(self.all_symbols, key=lambda x: x['market_cap'], reverse=True)[:20]],
            'mainstream': self.filter_by_market_cap(min_cap=10000000000),  # >100亿
            'altcoins': self.filter_by_market_cap(min_cap=1000000000, max_cap=10000000000),  # 10-100亿
            'small_caps': self.filter_by_market_cap(max_cap=1000000000),  # <10亿
            'new_listings_30d': self.filter_by_listing_date(30, 'less_than'),
            'new_listings_90d': self.filter_by_listing_date(90, 'less_than'),
            'new_listings_180d': self.filter_by_listing_date(180, 'less_than'),
            'defi_tokens': self.filter_by_category(['DeFi']),
            'layer1_chains': self.filter_by_category(['Layer1']),
            'layer2_solutions': self.filter_by_category(['Layer2']),
            'meme_coins': self.filter_by_category(['Meme']),
            'ai_tokens': self.filter_by_category(['AI']),
            'high_volume': self.filter_by_volume(min_volume=500000000),  # >5亿交易量
        }
    
    def custom_filter(self, **kwargs) -> List[str]:
        """自定义筛选"""
        symbols = [coin['symbol'] for coin in self.all_symbols]
        
        # 市值筛选
        if 'min_market_cap' in kwargs or 'max_market_cap' in kwargs:
            symbols = list(set(symbols) & set(self.filter_by_market_cap(
                kwargs.get('min_market_cap'), 
                kwargs.get('max_market_cap')
            )))
        
        # 上市时间筛选
        if 'listing_days' in kwargs:
            comparison = kwargs.get('listing_comparison', 'less_than')
            symbols = list(set(symbols) & set(self.filter_by_listing_date(
                kwargs['listing_days'], comparison
            )))
        
        # 类别筛选
        if 'categories' in kwargs:
            symbols = list(set(symbols) & set(self.filter_by_category(kwargs['categories'])))
        
        # 交易量筛选
        if 'min_volume' in kwargs or 'max_volume' in kwargs:
            symbols = list(set(symbols) & set(self.filter_by_volume(
                kwargs.get('min_volume'),
                kwargs.get('max_volume')
            )))
        
        return symbols
    
    def get_symbol_info(self, symbol: str) -> Optional[Dict]:
        """获取单个币种信息"""
        for coin in self.all_symbols:
            if coin['symbol'] == symbol:
                return coin
        return None
    
    def display_filter_menu(self) -> str:
        """显示筛选菜单"""
        menu = """
🎯 数字货币选币范围筛选器
========================================

📊 预定义列表:
1. 全市场 (所有币种)
2. Top 10 (市值前10)
3. Top 20 (市值前20)
4. 主流币 (市值>100亿美元)
5. 山寨币 (市值10-100亿美元)
6. 小市值币 (市值<10亿美元)

⏰ 按上市时间:
7. 最新上市 (30天内)
8. 新币种 (90天内)
9. 较新币种 (180天内)

🏷️ 按类别筛选:
10. DeFi代币
11. Layer1公链
12. Layer2扩容
13. Meme币
14. AI概念币

📈 按交易量:
15. 高交易量 (>5亿美元)

🔧 自定义筛选:
16. 自定义条件组合

请输入选择 (1-16): """
        
        return menu
    
    def interactive_filter(self) -> List[str]:
        """交互式筛选"""
        print(self.display_filter_menu())
        
        try:
            choice = input().strip()
            predefined_lists = self.get_predefined_lists()
            
            choice_map = {
                '1': 'all_market',
                '2': 'top_10', 
                '3': 'top_20',
                '4': 'mainstream',
                '5': 'altcoins',
                '6': 'small_caps',
                '7': 'new_listings_30d',
                '8': 'new_listings_90d',
                '9': 'new_listings_180d',
                '10': 'defi_tokens',
                '11': 'layer1_chains',
                '12': 'layer2_solutions',
                '13': 'meme_coins',
                '14': 'ai_tokens',
                '15': 'high_volume'
            }
            
            if choice in choice_map:
                selected_list = choice_map[choice]
                symbols = predefined_lists[selected_list]
                print(f"\n✅ 已选择: {selected_list}")
                print(f"📊 包含 {len(symbols)} 个币种: {', '.join(symbols[:10])}{'...' if len(symbols) > 10 else ''}")
                return symbols
            
            elif choice == '16':
                return self._custom_filter_interactive()
            
            else:
                print("❌ 无效选择，使用默认主流币列表")
                return predefined_lists['mainstream']
                
        except Exception as e:
            print(f"❌ 输入错误: {e}，使用默认主流币列表")
            return self.get_predefined_lists()['mainstream']
    
    def _custom_filter_interactive(self) -> List[str]:
        """交互式自定义筛选"""
        print("\n🔧 自定义筛选条件:")
        
        kwargs = {}
        
        # 市值筛选
        print("\n💰 市值筛选 (可选，直接回车跳过):")
        try:
            min_cap = input("最小市值 (美元，如: 1000000000): ").strip()
            if min_cap:
                kwargs['min_market_cap'] = float(min_cap)
            
            max_cap = input("最大市值 (美元，如: 100000000000): ").strip()
            if max_cap:
                kwargs['max_market_cap'] = float(max_cap)
        except:
            pass
        
        # 上市时间筛选
        print("\n⏰ 上市时间筛选 (可选):")
        try:
            days = input("上市天数 (如: 30表示30天内上市): ").strip()
            if days:
                kwargs['listing_days'] = int(days)
                kwargs['listing_comparison'] = 'less_than'
        except:
            pass
        
        # 类别筛选
        print("\n🏷️ 类别筛选 (可选，多个用逗号分隔):")
        print("可选: Layer1, Layer2, DeFi, Exchange, Payment, Oracle, Meme, AI, New")
        try:
            categories = input("选择类别: ").strip()
            if categories:
                kwargs['categories'] = [cat.strip() for cat in categories.split(',')]
        except:
            pass
        
        symbols = self.custom_filter(**kwargs)
        print(f"\n✅ 自定义筛选完成")
        print(f"📊 找到 {len(symbols)} 个符合条件的币种")
        
        return symbols
