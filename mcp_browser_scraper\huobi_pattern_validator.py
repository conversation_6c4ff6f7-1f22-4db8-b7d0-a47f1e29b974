#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
火币网形态对比验证模块
使用Selenium自动化抓取火币网K线图并进行形态对比分析
"""

import os
import sys
import time
import cv2
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import base64
from io import BytesIO
from PIL import Image
import requests

# 尝试导入Selenium相关模块
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    print("⚠️ Selenium未安装，火币网对比功能将不可用")

# 尝试导入图像处理模块
try:
    from skimage.metrics import structural_similarity as ssim
    SKIMAGE_AVAILABLE = True
except ImportError:
    SKIMAGE_AVAILABLE = False
    print("⚠️ scikit-image未安装，将使用基础图像对比")

def log(message):
    """日志函数"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

class HuobiPatternValidator:
    """火币网形态对比验证器"""
    
    def __init__(self, use_proxy: bool = True):
        """初始化验证器"""
        self.use_proxy = use_proxy
        self.driver = None
        self.huobi_base_url = "https://www.huobi.com"
        self.screenshots_dir = "huobi_screenshots"
        self.comparison_dir = "pattern_comparisons"
        
        # 创建必要的目录
        os.makedirs(self.screenshots_dir, exist_ok=True)
        os.makedirs(self.comparison_dir, exist_ok=True)
        
        # 代理配置
        self.proxy_configs = [
            "socks5://127.0.0.1:1082",
            "socks5://127.0.0.1:1080", 
            "socks5://127.0.0.1:1081"
        ]
        
        log("🔧 火币网形态验证器初始化完成")

    def _setup_chrome_driver(self) -> bool:
        """设置Chrome WebDriver"""
        if not SELENIUM_AVAILABLE:
            log("❌ Selenium未安装，无法启动浏览器")
            return False
        
        try:
            chrome_options = Options()
            
            # 基本设置
            chrome_options.add_argument('--headless')  # 无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 设置代理
            if self.use_proxy and self.proxy_configs:
                proxy = self.proxy_configs[0]  # 使用第一个代理
                chrome_options.add_argument(f'--proxy-server={proxy}')
                log(f"🔗 使用代理: {proxy}")
            
            # 设置用户代理
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
            
            # 尝试启动Chrome
            try:
                self.driver = webdriver.Chrome(options=chrome_options)
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                log("✅ Chrome WebDriver启动成功")
                return True
            except Exception as e:
                log(f"❌ Chrome WebDriver启动失败: {e}")
                return False
                
        except Exception as e:
            log(f"❌ 设置Chrome WebDriver失败: {e}")
            return False

    def capture_huobi_chart(self, symbol: str, timeframe: str = "1day") -> Optional[str]:
        """抓取火币网K线图"""
        if not self.driver:
            if not self._setup_chrome_driver():
                return None
        
        try:
            # 构建火币网K线图URL
            symbol_formatted = symbol.lower() + "usdt"
            chart_url = f"{self.huobi_base_url}/zh-cn/exchange/{symbol_formatted}/"
            
            log(f"🌐 访问火币网: {chart_url}")
            self.driver.get(chart_url)
            
            # 等待页面加载
            time.sleep(5)
            
            # 等待K线图加载
            try:
                chart_element = WebDriverWait(self.driver, 20).until(
                    EC.presence_of_element_located((By.CLASS_NAME, "tradingview-widget-container"))
                )
                log("✅ K线图容器找到")
            except TimeoutException:
                log("⚠️ 未找到TradingView容器，尝试其他选择器")
                try:
                    chart_element = WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.ID, "tv_chart_container"))
                    )
                    log("✅ 找到图表容器")
                except TimeoutException:
                    log("❌ 无法找到K线图容器")
                    return None
            
            # 等待图表完全加载
            time.sleep(8)
            
            # 截取整个页面
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_path = os.path.join(self.screenshots_dir, f"{symbol}_{timeframe}_{timestamp}.png")
            
            self.driver.save_screenshot(screenshot_path)
            log(f"📸 截图保存: {screenshot_path}")
            
            return screenshot_path
            
        except Exception as e:
            log(f"❌ 抓取火币网图表失败: {e}")
            return None

    def extract_chart_region(self, screenshot_path: str) -> Optional[str]:
        """从截图中提取K线图区域"""
        try:
            # 读取截图
            image = cv2.imread(screenshot_path)
            if image is None:
                log("❌ 无法读取截图文件")
                return None
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 使用边缘检测找到图表区域
            edges = cv2.Canny(gray, 50, 150)
            
            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 找到最大的矩形区域（可能是图表）
            largest_area = 0
            best_rect = None
            
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                area = w * h
                
                # 过滤太小或形状不合理的区域
                if area > largest_area and w > 300 and h > 200 and w/h > 1.5:
                    largest_area = area
                    best_rect = (x, y, w, h)
            
            if best_rect:
                x, y, w, h = best_rect
                # 提取图表区域
                chart_region = image[y:y+h, x:x+w]
                
                # 保存提取的图表区域
                chart_path = screenshot_path.replace('.png', '_chart.png')
                cv2.imwrite(chart_path, chart_region)
                log(f"📊 图表区域提取完成: {chart_path}")
                return chart_path
            else:
                log("⚠️ 未找到合适的图表区域，使用原始截图")
                return screenshot_path
                
        except Exception as e:
            log(f"❌ 提取图表区域失败: {e}")
            return screenshot_path

    def compare_patterns(self, system_chart_path: str, huobi_chart_path: str) -> Dict:
        """对比系统生成的图表和火币网图表"""
        comparison_result = {
            'similarity_score': 0.0,
            'comparison_method': 'basic',
            'details': {},
            'comparison_image_path': None,
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            # 读取两个图像
            system_img = cv2.imread(system_chart_path)
            huobi_img = cv2.imread(huobi_chart_path)
            
            if system_img is None or huobi_img is None:
                log("❌ 无法读取对比图像")
                return comparison_result
            
            # 调整图像大小到相同尺寸
            target_size = (800, 600)
            system_resized = cv2.resize(system_img, target_size)
            huobi_resized = cv2.resize(huobi_img, target_size)
            
            # 转换为灰度图
            system_gray = cv2.cvtColor(system_resized, cv2.COLOR_BGR2GRAY)
            huobi_gray = cv2.cvtColor(huobi_resized, cv2.COLOR_BGR2GRAY)
            
            # 使用SSIM进行结构相似性对比
            if SKIMAGE_AVAILABLE:
                similarity_score = ssim(system_gray, huobi_gray)
                comparison_result['comparison_method'] = 'ssim'
                log(f"📊 SSIM相似度: {similarity_score:.4f}")
            else:
                # 使用基础的像素差异对比
                diff = cv2.absdiff(system_gray, huobi_gray)
                similarity_score = 1.0 - (np.mean(diff) / 255.0)
                comparison_result['comparison_method'] = 'pixel_diff'
                log(f"📊 像素差异相似度: {similarity_score:.4f}")
            
            comparison_result['similarity_score'] = similarity_score
            
            # 生成对比图像
            comparison_img = self._create_comparison_image(system_resized, huobi_resized, similarity_score)
            
            # 保存对比结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            comparison_path = os.path.join(self.comparison_dir, f"comparison_{timestamp}.png")
            cv2.imwrite(comparison_path, comparison_img)
            comparison_result['comparison_image_path'] = comparison_path
            
            # 详细分析
            comparison_result['details'] = self._analyze_pattern_details(system_gray, huobi_gray)
            
            log(f"✅ 形态对比完成，相似度: {similarity_score:.4f}")
            return comparison_result
            
        except Exception as e:
            log(f"❌ 形态对比失败: {e}")
            return comparison_result

    def _create_comparison_image(self, img1: np.ndarray, img2: np.ndarray, score: float) -> np.ndarray:
        """创建对比图像"""
        try:
            # 创建并排对比图
            h, w = img1.shape[:2]
            comparison = np.zeros((h, w*2 + 50, 3), dtype=np.uint8)
            
            # 放置两个图像
            comparison[:, :w] = img1
            comparison[:, w+50:] = img2
            
            # 添加分割线
            cv2.line(comparison, (w+25, 0), (w+25, h), (255, 255, 255), 2)
            
            # 添加标题和相似度分数
            cv2.putText(comparison, "System Chart", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(comparison, "Huobi Chart", (w+60, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(comparison, f"Similarity: {score:.4f}", (w//2-50, h-20), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)
            
            return comparison
            
        except Exception as e:
            log(f"❌ 创建对比图像失败: {e}")
            return np.zeros((600, 800, 3), dtype=np.uint8)

    def _analyze_pattern_details(self, img1: np.ndarray, img2: np.ndarray) -> Dict:
        """分析形态细节"""
        details = {}
        
        try:
            # 计算直方图相似性
            hist1 = cv2.calcHist([img1], [0], None, [256], [0, 256])
            hist2 = cv2.calcHist([img2], [0], None, [256], [0, 256])
            hist_similarity = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
            details['histogram_similarity'] = hist_similarity
            
            # 边缘检测对比
            edges1 = cv2.Canny(img1, 50, 150)
            edges2 = cv2.Canny(img2, 50, 150)
            edge_diff = cv2.absdiff(edges1, edges2)
            edge_similarity = 1.0 - (np.mean(edge_diff) / 255.0)
            details['edge_similarity'] = edge_similarity
            
            # 特征点检测（如果可用）
            try:
                orb = cv2.ORB_create()
                kp1, des1 = orb.detectAndCompute(img1, None)
                kp2, des2 = orb.detectAndCompute(img2, None)
                
                if des1 is not None and des2 is not None:
                    bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
                    matches = bf.match(des1, des2)
                    feature_similarity = len(matches) / max(len(kp1), len(kp2), 1)
                    details['feature_similarity'] = feature_similarity
                
            except Exception:
                details['feature_similarity'] = 0.0
            
            log(f"📊 详细分析完成: 直方图={hist_similarity:.4f}, 边缘={edge_similarity:.4f}")
            
        except Exception as e:
            log(f"❌ 详细分析失败: {e}")
        
        return details

    def validate_double_long_upper_shadow(self, symbol: str, system_chart_path: str) -> Dict:
        """验证双长上影线形态"""
        validation_result = {
            'symbol': symbol,
            'validation_success': False,
            'similarity_score': 0.0,
            'huobi_chart_path': None,
            'comparison_result': None,
            'recommendation': '',
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            log(f"🔍 开始验证 {symbol} 的双长上影线形态...")
            
            # 1. 抓取火币网图表
            huobi_screenshot = self.capture_huobi_chart(symbol)
            if not huobi_screenshot:
                validation_result['recommendation'] = "无法获取火币网图表，建议手动验证"
                return validation_result
            
            # 2. 提取图表区域
            huobi_chart = self.extract_chart_region(huobi_screenshot)
            validation_result['huobi_chart_path'] = huobi_chart
            
            # 3. 对比形态
            comparison = self.compare_patterns(system_chart_path, huobi_chart)
            validation_result['comparison_result'] = comparison
            validation_result['similarity_score'] = comparison['similarity_score']
            
            # 4. 生成建议
            if comparison['similarity_score'] >= 0.8:
                validation_result['validation_success'] = True
                validation_result['recommendation'] = "形态高度一致，系统分析可信度高"
            elif comparison['similarity_score'] >= 0.6:
                validation_result['validation_success'] = True
                validation_result['recommendation'] = "形态基本一致，建议结合其他指标确认"
            else:
                validation_result['recommendation'] = "形态差异较大，建议谨慎对待系统分析结果"
            
            log(f"✅ {symbol} 形态验证完成，相似度: {comparison['similarity_score']:.4f}")
            return validation_result
            
        except Exception as e:
            log(f"❌ 形态验证失败: {e}")
            validation_result['recommendation'] = f"验证过程出错: {e}"
            return validation_result

    def generate_validation_report(self, validation_results: List[Dict]) -> str:
        """生成验证报告"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_path = os.path.join(self.comparison_dir, f"validation_report_{timestamp}.html")
            
            html_content = self._create_html_report(validation_results)
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            log(f"📄 验证报告生成: {report_path}")
            return report_path
            
        except Exception as e:
            log(f"❌ 生成验证报告失败: {e}")
            return ""

    def _create_html_report(self, validation_results: List[Dict]) -> str:
        """创建HTML验证报告"""
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>火币网形态验证报告</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
                .result { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
                .success { background: #e8f5e8; }
                .warning { background: #fff3cd; }
                .error { background: #f8d7da; }
                .chart-container { display: flex; gap: 20px; margin: 10px 0; }
                .chart { max-width: 400px; }
                img { max-width: 100%; height: auto; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🔍 火币网形态验证报告</h1>
                <p>生成时间: """ + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """</p>
                <p>验证币种数量: """ + str(len(validation_results)) + """</p>
            </div>
        """
        
        for result in validation_results:
            symbol = result.get('symbol', 'Unknown')
            success = result.get('validation_success', False)
            similarity = result.get('similarity_score', 0.0)
            recommendation = result.get('recommendation', '')
            
            css_class = 'success' if success and similarity >= 0.8 else 'warning' if success else 'error'
            
            html += f"""
            <div class="result {css_class}">
                <h2>📊 {symbol}</h2>
                <p><strong>验证状态:</strong> {'✅ 通过' if success else '❌ 未通过'}</p>
                <p><strong>相似度评分:</strong> {similarity:.4f}</p>
                <p><strong>建议:</strong> {recommendation}</p>
            """
            
            # 添加对比图像
            comparison_result = result.get('comparison_result', {})
            comparison_image = comparison_result.get('comparison_image_path', '')
            if comparison_image and os.path.exists(comparison_image):
                html += f'<p><strong>对比图像:</strong></p><img src="{comparison_image}" alt="对比图像">'
            
            html += "</div>"
        
        html += """
        </body>
        </html>
        """
        
        return html

    def cleanup(self):
        """清理资源"""
        if self.driver:
            try:
                self.driver.quit()
                log("🧹 WebDriver已关闭")
            except Exception as e:
                log(f"⚠️ 关闭WebDriver时出错: {e}")

    def __del__(self):
        """析构函数"""
        self.cleanup()
