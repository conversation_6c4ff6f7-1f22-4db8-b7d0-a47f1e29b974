#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复验证测试脚本
验证三个关键修复的效果
"""

import sys
import os
import json
from datetime import datetime

def log(message):
    """日志函数"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def test_ssr_config():
    """测试SSR配置修复"""
    log("🔧 测试1: SSR配置文件修复")
    
    try:
        # 检查SSR配置文件
        if os.path.exists('ssr_config.json'):
            with open('ssr_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 验证配置完整性
            required_keys = ['ssr_servers', 'proxy_settings', 'fallback_settings']
            missing_keys = [key for key in required_keys if key not in config]
            
            if not missing_keys:
                servers = config.get('ssr_servers', [])
                proxy_settings = config.get('proxy_settings', {})
                
                log(f"✅ SSR配置文件完整")
                log(f"   - 服务器数量: {len(servers)}")
                log(f"   - 代理启用: {proxy_settings.get('enable_proxy', False)}")
                log(f"   - 代理地址: {proxy_settings.get('proxy_host', 'N/A')}:{proxy_settings.get('proxy_port', 'N/A')}")
                
                # 检查微信配置是否在SSR配置中
                if 'wechat_webhooks' in config:
                    wechat_config = config['wechat_webhooks']
                    robots = wechat_config.get('robots', [])
                    log(f"   - 微信机器人: {len(robots)} 个")
                    for robot in robots:
                        log(f"     * {robot.get('name', '未命名')}: {'启用' if robot.get('enabled', False) else '禁用'}")
                
                return True
            else:
                log(f"❌ SSR配置缺少必需项: {missing_keys}")
                return False
        else:
            log("❌ SSR配置文件不存在")
            return False
            
    except Exception as e:
        log(f"❌ SSR配置测试失败: {e}")
        return False

def test_wechat_config():
    """测试微信推送配置修复"""
    log("🔧 测试2: 微信推送功能修复")
    
    try:
        # 检查独立的微信配置文件
        wechat_config_exists = os.path.exists('wechat_bots_config.json')
        if wechat_config_exists:
            with open('wechat_bots_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            bots = config.get('bots', [])
            log(f"✅ 独立微信配置文件存在: {len(bots)} 个机器人")
        else:
            log("⚠️ 独立微信配置文件不存在")
        
        # 检查SSR配置中的微信配置
        if os.path.exists('ssr_config.json'):
            with open('ssr_config.json', 'r', encoding='utf-8') as f:
                ssr_config = json.load(f)
            
            wechat_config = ssr_config.get('wechat_webhooks', {})
            if wechat_config.get('enabled', False):
                robots = wechat_config.get('robots', [])
                enabled_robots = [r for r in robots if r.get('enabled', True)]
                log(f"✅ SSR配置中的微信功能: 启用")
                log(f"   - 总机器人数: {len(robots)}")
                log(f"   - 启用机器人: {len(enabled_robots)}")
                
                for robot in enabled_robots:
                    name = robot.get('name', '未命名')
                    url = robot.get('webhook_url', '')
                    log(f"   - {name}: {'有效' if url.startswith('https://') else '无效'}")
                
                return len(enabled_robots) > 0
            else:
                log("❌ SSR配置中微信功能未启用")
                return False
        else:
            log("❌ 无法检查SSR配置")
            return False
            
    except Exception as e:
        log(f"❌ 微信配置测试失败: {e}")
        return False

def test_data_sources():
    """测试真实数据源配置"""
    log("🔧 测试3: 真实数据源配置验证")
    
    try:
        from advanced_crypto_scraper import AdvancedCryptoScraper
        
        # 创建实例（不使用代理以避免阻塞）
        scraper = AdvancedCryptoScraper(use_proxy=False)
        
        # 检查数据源配置
        data_sources = scraper.data_sources
        log(f"✅ 数据源配置加载成功: {len(data_sources)} 个数据源")
        
        real_data_sources = []
        for name, config in data_sources.items():
            is_real = config.get('real_data', False)
            requires_proxy = config.get('requires_proxy', False)
            description = config.get('description', '无描述')
            
            log(f"   - {config.get('name', name)}: {'真实数据' if is_real else '非真实数据'}")
            log(f"     * 需要代理: {'是' if requires_proxy else '否'}")
            log(f"     * 描述: {description}")
            
            if is_real:
                real_data_sources.append(name)
        
        log(f"✅ 真实数据源: {len(real_data_sources)} 个 ({', '.join(real_data_sources)})")
        
        # 检查真实K线数据提供器
        if hasattr(scraper, 'real_kline_provider') and scraper.real_kline_provider:
            log("✅ 真实K线数据提供器: 可用")
        else:
            log("⚠️ 真实K线数据提供器: 不可用，将使用API数据")
        
        return len(real_data_sources) >= 4  # 至少4个真实数据源
        
    except Exception as e:
        log(f"❌ 数据源测试失败: {e}")
        import traceback
        log(f"错误详情: {traceback.format_exc()}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    log("🔧 测试4: 基本功能验证")
    
    try:
        from advanced_crypto_scraper import AdvancedCryptoScraper
        
        # 创建实例
        scraper = AdvancedCryptoScraper(use_proxy=False)
        
        # 测试连接
        log("测试网络连接...")
        scraper.test_connection()
        
        # 测试速度模式
        log("测试速度模式...")
        scraper.show_speed_modes()
        
        # 测试统计信息
        log("测试统计信息...")
        scraper.print_statistics()
        
        log("✅ 基本功能测试通过")
        return True
        
    except Exception as e:
        log(f"❌ 基本功能测试失败: {e}")
        return False

def main():
    """主函数"""
    log("🚀 开始修复验证测试...")
    log("="*60)
    
    results = []
    
    # 测试1: SSR配置修复
    results.append(test_ssr_config())
    log("="*60)
    
    # 测试2: 微信推送修复
    results.append(test_wechat_config())
    log("="*60)
    
    # 测试3: 真实数据源配置
    results.append(test_data_sources())
    log("="*60)
    
    # 测试4: 基本功能
    results.append(test_basic_functionality())
    log("="*60)
    
    # 统计结果
    passed = sum(results)
    total = len(results)
    
    log(f"📊 测试结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        log("🎉 所有修复验证通过！")
        log("✅ SSR配置功能正常")
        log("✅ 微信推送功能已恢复")
        log("✅ 真实数据源配置完整")
        log("✅ 基本功能运行正常")
        log("🎯 脚本已准备好用于实盘交易")
    else:
        log("⚠️ 部分修复需要进一步检查")
        
        if not results[0]:
            log("❌ SSR配置需要修复")
        if not results[1]:
            log("❌ 微信推送功能需要修复")
        if not results[2]:
            log("❌ 数据源配置需要修复")
        if not results[3]:
            log("❌ 基本功能需要修复")

if __name__ == "__main__":
    main()
