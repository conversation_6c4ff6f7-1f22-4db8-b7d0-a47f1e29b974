#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import time
import json
import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pycoingecko import CoinGeckoAPI
from loguru import logger
from get_new_crypto import NewCryptocurrencyFinder, deco_retry

class WeChatNotifier:
    """
    微信通知发送器（使用Server酱）
    """
    def __init__(self, send_keys):
        """
        初始化通知器
        
        Parameters
        ----------
        send_keys : str or list
            Server酱的SendKey或SendKey列表
        """
        if isinstance(send_keys, str):
            self.send_keys = [send_keys]
        else:
            self.send_keys = send_keys
        
    def send_message(self, title, content):
        """
        发送消息到所有配置的SendKey
        
        Parameters
        ----------
        title : str
            消息标题
        content : str
            消息内容（支持Markdown格式）
        """
        for send_key in self.send_keys:
            try:
                api_url = f"https://sctapi.ftqq.com/{send_key}.send"
                data = {
                    "title": title,
                    "desp": content
                }
                response = requests.post(api_url, data=data)
                if response.status_code == 200:
                    logger.info(f"微信通知发送成功 (SendKey: {send_key[:8]}... 状态码: {response.status_code} 响应: {response.text[:200]}")
                else:
                    logger.error(f"微信通知发送失败 (SendKey: {send_key[:8]}... 状态码: {response.status_code} 错误详情: {response.text}")
            except Exception as e:
                logger.error(f"发送微信通知时出错 (SendKey: {send_key[:8]}...): {e}")

class ShadowAnalyzer:
    """
    分析加密货币的上影线
    """
    def __init__(self, days=60, shadow_ratio=2.0, request_delay=1.0, wechat_key=None):
        """
        初始化分析器
        
        Parameters
        ----------
        days : int
            查找过去多少天内上市的币种，默认60天
        shadow_ratio : float
            上影线与实体的最小比例，默认2.0
        request_delay : float
            API请求间隔时间（秒），默认1.0秒
        wechat_key : str
            Server酱的SendKey，默认为None
        """
        self.days = days
        self.shadow_ratio = shadow_ratio
        self.request_delay = request_delay
        self.cg = CoinGeckoAPI()
        self.wechat_notifier = WeChatNotifier(wechat_key) if wechat_key else None
        
    @deco_retry(retry_times=3, retry_sleep=2)
    def get_coin_ohlc(self, coin_id, vs_currency='usd', days=1):
        """
        获取币种的OHLC数据
        
        Parameters
        ----------
        coin_id : str
            币种ID
        vs_currency : str
            计价货币，默认usd
        days : int
            获取多少天的数据，默认1天
            
        Returns
        -------
        list
            OHLC数据列表
        """
        try:
            ohlc = self.cg.get_coin_ohlc_by_id(id=coin_id, vs_currency=vs_currency, days=days)
            return ohlc
        except Exception as e:
            logger.error(f"获取{coin_id}的OHLC数据失败: {e}")
            return None
            
    def analyze_shadow(self, ohlc_data):
        """
        分析K线数据中的上影线
        
        Parameters
        ----------
        ohlc_data : list
            OHLC数据列表
            
        Returns
        -------
        bool
            是否满足上影线条件
        float
            上影线比例
        """
        if not ohlc_data or len(ohlc_data) == 0:
            return False, 0
            
        # 获取最新一根K线
        latest_candle = ohlc_data[-1]
        timestamp, open_price, high, low, close = latest_candle
        
        # 计算实体长度
        body_length = abs(close - open_price)
        if body_length == 0:  # 避免除以0
            return False, 0
            
        # 计算上影线长度
        upper_shadow = high - max(open_price, close)
        
        # 计算上影线比例
        shadow_ratio = upper_shadow / body_length
        
        # 判断是否满足条件
        return shadow_ratio >= self.shadow_ratio, shadow_ratio
        
    def get_listing_days(self, coin):
        """
        计算币种上市天数
        
        Parameters
        ----------
        coin : pd.Series
            币种信息
            
        Returns
        -------
        int
            上市天数
        """
        try:
            if 'ath_date' in coin and pd.notna(coin['ath_date']):
                ath_date = pd.to_datetime(coin['ath_date'])
                days = (datetime.now() - ath_date).days
                return max(0, days)
        except:
            pass
        return 0
        
    def generate_trading_suggestion(self, row):
        """
        生成交易建议
        
        Parameters
        ----------
        row : pd.Series
            单个币种的分析结果
            
        Returns
        -------
        str
            交易建议
        """
        shadow_ratio = row['上影线比例']
        listing_days = row['上市天数']
        
        if shadow_ratio > 3.0 and listing_days < 7:
            return "🚨 强烈卖出信号：超高上影线+新上市"
        elif shadow_ratio > 2.5:
            return "⚠️ 卖出信号：高上影线"
        elif shadow_ratio > 2.0:
            return "🔍 观察信号：中等上影线"
        else:
            return "➖ 无明确信号"
            
    def generate_markdown_report(self, results_df):
        """
        生成Markdown格式的报告
        
        Parameters
        ----------
        results_df : pd.DataFrame
            分析结果
            
        Returns
        -------
        str
            Markdown格式的报告
        """
        if results_df.empty:
            return "## 上影线分析报告\n\n没有找到满足条件的币种。"
            
        # 统计信息
        total_coins = len(results_df['币种ID'].unique())
        total_signals = len(results_df)
        max_ratio = results_df['上影线比例'].max()
        
        # 生成报告头部
        report = f"""## 🚀 加密货币上影线分析报告

### 📊 分析概况
- **分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **分析周期**: 过去{self.days}天内上市的新币
- **上影线比例阈值**: {self.shadow_ratio}倍
- **发现币种数**: {total_coins}个
- **信号总数**: {total_signals}个
- **最大上影线比例**: {max_ratio:.2f}倍

### 📈 详细数据
"""
        
        # 按周期分组显示数据
        for period in ['4小时', '日线']:
            period_data = results_df[results_df['周期'] == period]
            if not period_data.empty:
                report += f"\n#### {period}周期 ({len(period_data)}个信号)\n\n"
                report += "| 币种符号 | 币种名称 | 当前价格 | 上市天数 | 上影线比例 | 交易建议 |\n"
                report += "|:--------|:--------|:---------|:---------|:----------|:---------|\n"
                
                for _, row in period_data.iterrows():
                    suggestion = self.generate_trading_suggestion(row)
                    report += f"| {row['币种符号']} | {row['币种名称']} | {row['当前价格']} | {row['上市天数']}天 | {row['上影线比例']} | {suggestion} |\n"
        
        # 添加交易策略建议
        report += "\n### 💡 交易策略建议\n"
        report += "1. 对于'强烈卖出信号'的币种，建议立即减仓或清仓\n"
        report += "2. 对于'卖出信号'的币种，建议设置止损并密切关注\n"
        report += "3. 对于'观察信号'的币种，建议等待进一步确认趋势\n"
        
        # 添加提示信息
        report += "\n### ⚠️ 风险提示\n"
        report += "本报告仅供参考，不构成投资建议。加密货币市场风险较大，请谨慎投资。\n"
        
        return report
        
    def run(self, quick_test=False):
        """
        运行分析
        
        Parameters
        ----------
        quick_test : bool
            是否启用快速测试模式，找到满足条件的币种就立即生成报告
            
        Returns
        -------
        pd.DataFrame
            分析结果
        """
        logger.info(f"开始分析过去{self.days}天内上市的新币...")
        
        # 获取新币列表
        finder = NewCryptocurrencyFinder(days=self.days)
        new_coins = finder.get_new_listings()
        
        if new_coins.empty:
            logger.warning("没有找到新上市的币种")
            return pd.DataFrame()
            
        logger.info(f"找到{len(new_coins)}个新币，开始分析上影线...")
        
        # 准备存储分析结果
        results = {
            '币种ID': [],
            '币种符号': [],
            '币种名称': [],
            '当前价格': [],
            '上市天数': [],
            '周期': [],
            '上影线比例': [],
            '分析时间': []
        }
        
        # 分析每个币种
        for _, coin in new_coins.iterrows():
            try:
                coin_id = coin['id']
                symbol = coin['symbol'].upper()
                name = coin['name']
                current_price = coin.get('current_price', 0)
                listing_days = self.get_listing_days(coin)
                
                logger.info(f"分析币种: {symbol} ({name})")
                
                # 分析4小时和日线周期
                for period, days in [('4小时', 1), ('日线', 1)]:
                    # 快速测试模式下，如果已经找到该周期的样本就跳过
                    if quick_test:
                        if (period == '4小时' and found_4h) or (period == '日线' and found_daily):
                            continue
                    
                    # 获取OHLC数据
                    time.sleep(self.request_delay)  # 添加延迟，避免API限制
                    ohlc_data = self.get_coin_ohlc(coin_id, days=days)
                    
                    if ohlc_data:
                        # 分析上影线
                        has_shadow, shadow_ratio = self.analyze_shadow(ohlc_data)
                        
                        if has_shadow:
                            logger.info(f"发现{period}上影线比例大于{self.shadow_ratio}的币种: {symbol}")
                            results['币种ID'].append(coin_id)
                            results['币种符号'].append(symbol)
                            results['币种名称'].append(name)
                            results['当前价格'].append(current_price)
                            results['上市天数'].append(listing_days)
                            results['周期'].append(period)
                            results['上影线比例'].append(round(shadow_ratio, 2))
                            results['分析时间'].append(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                            
                
            except Exception as e:
                logger.error(f"分析{coin_id}时出错: {e}")
                continue
                
        # 创建结果DataFrame
        results_df = pd.DataFrame(results)
        
        if not results_df.empty:
            # 按上影线比例排序
            results_df = results_df.sort_values(by=['上影线比例', '周期'], ascending=[False, True])
            logger.info(f"分析完成，找到{len(results_df)}个满足条件的币种")
            
            # 生成并发送微信通知
            if self.wechat_notifier:
                report = self.generate_markdown_report(results_df)
                self.wechat_notifier.send_message(
                    title=f"加密货币上影线分析报告 - {datetime.now().strftime('%Y-%m-%d')}",
                    content=report
                )
        else:
            logger.info("没有找到满足条件的币种")
            
        return results_df

def main():
    """
    主函数
    """
    # 配置日志
    logger.remove()
    logger.add(lambda msg: print(msg), level="INFO")
    
    # 从配置文件获取Server酱的SendKey
    config_file = os.path.join(os.path.dirname(__file__), 'config.json')
    wechat_keys = []
    
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 支持单个key或key列表
                serverchan_key = config.get('serverchan_key')
                if serverchan_key:
                    if isinstance(serverchan_key, str):
                        wechat_keys.append(serverchan_key)
                    elif isinstance(serverchan_key, list):
                        wechat_keys.extend(serverchan_key)
                    logger.info(f"已从配置文件加载Server酱SendKey，共{len(wechat_keys)}个")
        except Exception as e:
            logger.error(f"读取配置文件失败: {e}")
    
    # 如果配置文件没有找到，尝试从环境变量获取
    if not wechat_keys:
        env_key = os.getenv('SERVERCHAN_KEY')
        if env_key:
            wechat_keys.append(env_key)
            logger.info("已从环境变量加载Server酱SendKey")
    
    # 创建分析器
    analyzer = ShadowAnalyzer(days=60, shadow_ratio=2.0, wechat_key=wechat_keys if wechat_keys else None)
    
    # 运行完整分析
    results = analyzer.run(quick_test=False)
    
    # 输出结果
    if not results.empty:
        print("\n分析结果:")
        # 设置显示选项
        pd.set_option('display.max_rows', None)
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', None)
        pd.set_option('display.max_colwidth', None)
        
        # 格式化显示
        results['当前价格'] = results['当前价格'].apply(lambda x: f"${x:,.4f}")
        results['上影线比例'] = results['上影线比例'].apply(lambda x: f"{x:.2f}")
        results['上市天数'] = results['上市天数'].apply(lambda x: f"{x}天")
        
        print(results.to_string(index=False))
        
        # 保存结果到CSV文件
        output_file = f"shadow_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        results.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n结果已保存到文件: {output_file}")
    else:
        print("\n没有找到满足条件的币种")

if __name__ == "__main__":
    main()