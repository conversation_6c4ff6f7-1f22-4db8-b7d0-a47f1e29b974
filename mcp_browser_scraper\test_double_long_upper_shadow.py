#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试双长上影线形态识别功能
"""

import sys
import os
from datetime import datetime
import importlib.util

def log(message):
    """日志函数"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def test_double_long_upper_shadow():
    """测试双长上影线形态识别"""
    log("🔧 测试双长上影线形态识别功能...")
    
    try:
        # 导入修改后的类
        script_path = "mcp_browser_scraper/advanced_crypto_scraper tuxing.py"
        spec = importlib.util.spec_from_file_location("advanced_crypto_scraper_tuxing", script_path)
        if spec is None or spec.loader is None:
            log("❌ 无法加载模块")
            return False
            
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # 创建实例
        scraper = module.AdvancedCryptoScraper(use_proxy=False)
        log("✅ 抓取器实例创建成功")
        
        # 构造测试数据 - 模拟双长上影线形态
        test_historical_data = [
            # 第一根K线 - 长上影线
            {
                'date': '2025-06-20',
                'open_price': 100.0,
                'high_price': 120.0,  # 高点
                'low_price': 95.0,
                'close_price': 105.0,  # 实体较小
                'volume': 1000000
            },
            # 第二根K线 - 长上影线，高点递减
            {
                'date': '2025-06-21',
                'open_price': 105.0,
                'high_price': 115.0,  # 比第一根低
                'low_price': 100.0,
                'close_price': 108.0,  # 实体较小
                'volume': 1200000
            }
        ]
        
        log("📊 测试数据:")
        for i, candle in enumerate(test_historical_data, 1):
            log(f"   第{i}根K线: 开盘={candle['open_price']}, 最高={candle['high_price']}, "
                f"最低={candle['low_price']}, 收盘={candle['close_price']}")
        
        # 测试形态识别
        log("🔍 开始形态识别测试...")
        patterns = scraper._identify_patterns(test_historical_data)
        
        log(f"🎯 识别结果: {patterns}")
        
        if "双长上影线" in patterns:
            log("✅ 双长上影线形态识别成功！")
            log("🎯 形态特征: 连续两根长上影线，高点递减")
            log("📈 技术含义: 上涨乏力，可能出现回调")
            return True
        else:
            log("❌ 双长上影线形态识别失败")
            log(f"   实际识别结果: {patterns}")
            return False
            
    except Exception as e:
        log(f"❌ 测试失败: {e}")
        import traceback
        log(f"错误详情: {traceback.format_exc()}")
        return False

def test_non_double_long_upper_shadow():
    """测试非双长上影线形态"""
    log("🔧 测试非双长上影线形态...")
    
    try:
        # 导入修改后的类
        script_path = "mcp_browser_scraper/advanced_crypto_scraper tuxing.py"
        spec = importlib.util.spec_from_file_location("advanced_crypto_scraper_tuxing", script_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        scraper = module.AdvancedCryptoScraper(use_proxy=False)
        
        # 构造测试数据 - 不符合双长上影线条件
        test_historical_data = [
            # 第一根K线 - 普通K线
            {
                'date': '2025-06-20',
                'open_price': 100.0,
                'high_price': 105.0,  # 上影线很短
                'low_price': 95.0,
                'close_price': 103.0,
                'volume': 1000000
            },
            # 第二根K线 - 普通K线
            {
                'date': '2025-06-21',
                'open_price': 103.0,
                'high_price': 108.0,
                'low_price': 100.0,
                'close_price': 106.0,
                'volume': 1200000
            }
        ]
        
        patterns = scraper._identify_patterns(test_historical_data)
        
        log(f"🎯 识别结果: {patterns}")
        
        if "双长上影线" not in patterns:
            log("✅ 正确识别为非双长上影线形态")
            return True
        else:
            log("❌ 错误识别为双长上影线形态")
            return False
            
    except Exception as e:
        log(f"❌ 测试失败: {e}")
        return False

def test_edge_cases():
    """测试边界情况"""
    log("🔧 测试边界情况...")
    
    try:
        script_path = "mcp_browser_scraper/advanced_crypto_scraper tuxing.py"
        spec = importlib.util.spec_from_file_location("advanced_crypto_scraper_tuxing", script_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        scraper = module.AdvancedCryptoScraper(use_proxy=False)
        
        # 测试1: 数据不足
        log("📊 测试数据不足情况...")
        patterns = scraper._identify_patterns([])
        log(f"   空数据识别结果: {patterns}")
        
        # 测试2: 只有一根K线
        log("📊 测试单根K线情况...")
        single_candle = [{
            'date': '2025-06-21',
            'open_price': 100.0,
            'high_price': 120.0,
            'low_price': 95.0,
            'close_price': 105.0,
            'volume': 1000000
        }]
        patterns = scraper._identify_patterns(single_candle)
        log(f"   单根K线识别结果: {patterns}")
        
        # 测试3: 边界条件 - 刚好满足条件
        log("📊 测试边界条件...")
        boundary_data = [
            {
                'date': '2025-06-20',
                'open_price': 100.0,
                'high_price': 115.0,  # 上影线 = 10, 总长度 = 20, 比例 = 50% > 33.3%
                'low_price': 95.0,
                'close_price': 105.0,  # 实体 = 5, 比例 = 25% < 66.7%
                'volume': 1000000
            },
            {
                'date': '2025-06-21',
                'open_price': 105.0,
                'high_price': 114.0,  # 比第一根低，上影线 = 6, 总长度 = 18, 比例 = 33.3%
                'low_price': 96.0,
                'close_price': 108.0,  # 实体 = 3, 比例 = 16.7% < 66.7%
                'volume': 1200000
            }
        ]
        patterns = scraper._identify_patterns(boundary_data)
        log(f"   边界条件识别结果: {patterns}")
        
        return True
        
    except Exception as e:
        log(f"❌ 边界测试失败: {e}")
        return False

def main():
    """主函数"""
    log("🚀 开始双长上影线形态识别测试...")
    log("="*60)
    
    results = []
    
    # 测试1: 双长上影线形态识别
    results.append(test_double_long_upper_shadow())
    log("="*60)
    
    # 测试2: 非双长上影线形态识别
    results.append(test_non_double_long_upper_shadow())
    log("="*60)
    
    # 测试3: 边界情况测试
    results.append(test_edge_cases())
    log("="*60)
    
    # 统计结果
    passed = sum(results)
    total = len(results)
    
    log(f"📊 测试结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        log("🎉 所有测试通过！")
        log("✅ 双长上影线形态识别功能正常")
        log("✅ 边界情况处理正确")
        log("✅ 错误处理机制完善")
        log("🎯 形态识别功能验证成功")
    else:
        log("⚠️ 部分测试失败，需要检查实现")
        
    log("\n💡 双长上影线形态说明:")
    log("📈 技术特征:")
    log("   - 连续两根K线都是长上影线")
    log("   - 上影线长度 ≥ 整根K线长度的1/3")
    log("   - 实体部分长度 ≤ 整根K线长度的2/3")
    log("   - 第二根K线最高价 < 第一根K线最高价")
    log("📊 技术含义:")
    log("   - 表明多头上攻乏力")
    log("   - 空头力量开始显现")
    log("   - 可能出现价格回调")
    log("   - 是重要的反转信号")

if __name__ == "__main__":
    main()
