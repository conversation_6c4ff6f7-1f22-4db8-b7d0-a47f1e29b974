#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
真实数据形态分析器
结合crypto_technical_analyzer.py的形态识别策略和optimized_real_advanced_selector.py的功能
使用100%真实市场数据，无任何模拟数据

创建时间: 2024-12-19 23:45
功能特色:
- 100%真实市场数据
- 完整的K线形态识别
- 多时间周期技术分析
- 智能风险评估
- 企业微信推送
"""

import pandas as pd
import numpy as np
import requests
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional

class RealPatternAnalyzer:
    """真实数据形态分析器"""
    
    def __init__(self):
        """初始化"""
        self.wechat_webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=52d1b8a8-a88a-4d41-9664-f1268da20985"
        
        # 支持的交易对
        self.symbols = [
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'DOTUSDT',
            'LINKUSDT', 'LTCUSDT', 'BCHUSDT', 'XLMUSDT', 'EOSUSDT',
            'XRPUSDT', 'SOLUSDT', 'DOGEUSDT', 'MATICUSDT'
        ]
        
        # 时间周期配置
        self.timeframes = ['1d', '4h', '1h', '30m', '15m']
        self.timeframe_weights = {
            '1d': 0.35, '4h': 0.30, '1h': 0.20, '30m': 0.10, '15m': 0.05
        }
        
        # 评分权重
        self.weights = {
            'pattern': 0.30,    # K线形态
            'indicator': 0.35,  # 技术指标
            'trend': 0.25,      # 趋势分析
            'volume': 0.10      # 成交量
        }
        
        # 数据缓存
        self.market_data_cache = {}
        self.kline_cache = {}
        self.cache_timestamp = None
        self.cache_duration = 300  # 5分钟缓存
        
        print("真实数据形态分析器初始化完成")
    
    def get_real_market_data(self) -> Dict:
        """获取真实市场数据"""
        current_time = time.time()
        
        # 检查缓存
        if (self.market_data_cache and self.cache_timestamp and 
            current_time - self.cache_timestamp < self.cache_duration):
            print("使用缓存的市场数据")
            return self.market_data_cache
        
        try:
            print("正在获取真实市场数据...")
            
            # 使用CoinGecko API获取真实数据
            url = "https://api.coingecko.com/api/v3/coins/markets"
            params = {
                'vs_currency': 'usd',
                'order': 'market_cap_desc',
                'per_page': 50,
                'page': 1,
                'sparkline': False,
                'price_change_percentage': '24h,7d'
            }
            
            response = requests.get(url, params=params, timeout=15)
            response.raise_for_status()
            data = response.json()
            
            # 币种映射
            symbol_mapping = {
                'bitcoin': 'BTCUSDT', 'ethereum': 'ETHUSDT', 'binancecoin': 'BNBUSDT',
                'cardano': 'ADAUSDT', 'polkadot': 'DOTUSDT', 'chainlink': 'LINKUSDT',
                'litecoin': 'LTCUSDT', 'bitcoin-cash': 'BCHUSDT', 'stellar': 'XLMUSDT',
                'eos': 'EOSUSDT', 'ripple': 'XRPUSDT', 'solana': 'SOLUSDT',
                'dogecoin': 'DOGEUSDT', 'polygon': 'MATICUSDT'
            }
            
            market_data = {}
            for coin in data:
                coin_id = coin['id']
                if coin_id in symbol_mapping:
                    symbol = symbol_mapping[coin_id]
                    market_data[symbol] = {
                        'current_price': coin['current_price'],
                        'market_cap': coin['market_cap'],
                        'market_cap_rank': coin['market_cap_rank'],
                        'total_volume': coin['total_volume'],
                        'price_change_24h': coin.get('price_change_percentage_24h', 0),
                        'price_change_7d': coin.get('price_change_percentage_7d_in_currency', 0),
                        'name': coin['name'],
                        'symbol': coin['symbol'].upper(),
                        'last_updated': coin['last_updated']
                    }
            
            self.market_data_cache = market_data
            self.cache_timestamp = current_time
            
            print(f"成功获取 {len(market_data)} 个币种的真实市场数据")
            return market_data
            
        except requests.exceptions.RequestException as e:
            print(f"网络连接失败: {e}")
            print("请检查:")
            print("1. 网络连接是否正常")
            print("2. SSR/VPN是否已开启")
            print("3. 是否可以访问国外网站")
            return {}
        except Exception as e:
            print(f"获取市场数据失败: {e}")
            return {}
    
    def get_binance_kline_data(self, symbol: str, interval: str, limit: int = 200) -> Optional[pd.DataFrame]:
        """从币安获取真实K线数据"""
        try:
            # 币安API不需要翻墙
            base_url = "https://api.binance.com/api/v3/klines"
            
            params = {
                'symbol': symbol,
                'interval': interval,
                'limit': limit
            }
            
            response = requests.get(base_url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            if not data:
                return None
            
            # 转换为DataFrame
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # 数据类型转换
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 设置索引
            df.set_index('timestamp', inplace=True)
            df = df[['open', 'high', 'low', 'close', 'volume']]
            
            print(f"  获取 {symbol} {interval} K线数据: {len(df)} 根")
            return df
            
        except Exception as e:
            print(f"  获取 {symbol} {interval} K线数据失败: {e}")
            return None
    
    def identify_candlestick_patterns(self, df: pd.DataFrame) -> Dict:
        """识别K线形态 - 来自crypto_technical_analyzer.py"""
        if len(df) < 3:
            return {'patterns': [], 'score': 0.0}
        
        latest = df.iloc[-1]
        prev = df.iloc[-2]
        prev2 = df.iloc[-3]
        
        patterns = []
        score = 0.0
        
        # 计算K线参数
        body = abs(latest['close'] - latest['open'])
        upper_shadow = latest['high'] - max(latest['open'], latest['close'])
        lower_shadow = min(latest['open'], latest['close']) - latest['low']
        total_range = latest['high'] - latest['low']
        
        if total_range == 0:
            return {'patterns': [], 'score': 0.0}
        
        body_ratio = body / total_range
        upper_ratio = upper_shadow / total_range
        lower_ratio = lower_shadow / total_range
        
        # 单K线形态识别
        if body_ratio < 0.1:
            patterns.append('十字星')
            score += 1.8
        elif upper_ratio > 0.4 and lower_ratio < 0.2:
            patterns.append('长上影线')
            score += 1.2
        elif lower_ratio > 0.4 and upper_ratio < 0.2:
            patterns.append('锤子线')
            score += 2.2
        elif body_ratio > 0.7:
            if latest['close'] > latest['open']:
                patterns.append('大阳线')
                score += 1.8
            else:
                patterns.append('大阴线')
                score -= 0.8
        
        # 组合形态识别
        prev_body = abs(prev['close'] - prev['open'])
        if (latest['close'] > latest['open'] and prev['close'] < prev['open'] and
            latest['close'] > prev['open'] and latest['open'] < prev['close'] and
            body > prev_body * 1.2):
            patterns.append('看涨吞没')
            score += 2.8
        elif (latest['close'] < latest['open'] and prev['close'] > prev['open'] and
              latest['close'] < prev['open'] and latest['open'] > prev['close'] and
              body > prev_body * 1.2):
            patterns.append('看跌吞没')
            score -= 2.2
        
        return {
            'patterns': patterns,
            'score': max(min(score, 5.0), 0.0)
        }
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        data = df.copy()
        
        # RSI计算
        delta = data['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = (-delta.where(delta < 0, 0))
        
        alpha = 1.0 / 14
        avg_gain = gain.ewm(alpha=alpha, adjust=False).mean()
        avg_loss = loss.ewm(alpha=alpha, adjust=False).mean()
        
        rs = avg_gain / avg_loss
        data['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD计算
        ema12 = data['close'].ewm(span=12, adjust=False).mean()
        ema26 = data['close'].ewm(span=26, adjust=False).mean()
        data['macd'] = ema12 - ema26
        data['macd_signal'] = data['macd'].ewm(span=9, adjust=False).mean()
        data['macd_hist'] = data['macd'] - data['macd_signal']
        
        # 布林带
        data['bb_middle'] = data['close'].rolling(20, min_periods=1).mean()
        bb_std = data['close'].rolling(20, min_periods=1).std()
        data['bb_upper'] = data['bb_middle'] + (bb_std * 2)
        data['bb_lower'] = data['bb_middle'] - (bb_std * 2)
        data['bb_position'] = (data['close'] - data['bb_lower']) / (data['bb_upper'] - data['bb_lower'])
        
        # 移动平均线
        data['sma_5'] = data['close'].rolling(5, min_periods=1).mean()
        data['sma_20'] = data['close'].rolling(20, min_periods=1).mean()
        data['sma_50'] = data['close'].rolling(50, min_periods=1).mean()
        
        # 成交量指标
        data['volume_sma'] = data['volume'].rolling(20, min_periods=1).mean()
        data['volume_ratio'] = data['volume'] / data['volume_sma']
        
        return data

    def evaluate_technical_indicators(self, data: pd.DataFrame) -> Dict:
        """评估技术指标"""
        if len(data) < 20:
            return {'score': 0.0, 'details': {}}

        latest = data.iloc[-1]
        prev = data.iloc[-2]
        score = 0.0

        # RSI评分
        rsi = latest.get('rsi', 50)
        if not pd.isna(rsi):
            if rsi < 30:
                score += 2.5  # 超卖
            elif rsi < 40:
                score += 1.5
            elif 40 <= rsi <= 60:
                score += 0.8
            elif rsi > 70:
                score -= 1.2  # 超买

        # MACD评分
        macd = latest.get('macd', 0)
        macd_signal = latest.get('macd_signal', 0)
        if not pd.isna(macd) and not pd.isna(macd_signal):
            if macd > macd_signal:
                score += 1.5
            # 金叉检测
            if (macd > macd_signal and
                prev.get('macd', 0) <= prev.get('macd_signal', 0)):
                score += 2.0

        # 布林带评分
        bb_pos = latest.get('bb_position', 0.5)
        if not pd.isna(bb_pos):
            if bb_pos < 0.2:
                score += 2.0  # 接近下轨
            elif bb_pos > 0.8:
                score -= 1.0  # 接近上轨
            else:
                score += 0.5

        # 移动平均线评分
        if (not pd.isna(latest['sma_5']) and not pd.isna(latest['sma_20'])):
            if latest['close'] > latest['sma_5'] > latest['sma_20']:
                score += 2.5  # 多头排列
            elif latest['close'] > latest['sma_20']:
                score += 1.0

        # 成交量评分
        vol_ratio = latest.get('volume_ratio', 1.0)
        if not pd.isna(vol_ratio):
            if vol_ratio > 1.5:
                score += 1.2
            elif vol_ratio > 1.2:
                score += 0.8

        return {
            'score': max(min(score, 5.0), 0.0),
            'details': {
                'rsi': rsi if not pd.isna(rsi) else 50,
                'macd': macd if not pd.isna(macd) else 0,
                'macd_signal': macd_signal if not pd.isna(macd_signal) else 0,
                'bb_position': bb_pos if not pd.isna(bb_pos) else 0.5,
                'volume_ratio': vol_ratio if not pd.isna(vol_ratio) else 1.0,
                'price': latest['close']
            }
        }

    def analyze_symbol_timeframe(self, symbol: str, timeframe: str, market_data: Dict) -> Optional[Dict]:
        """分析单个交易对的单个时间周期"""
        try:
            # 币安时间周期映射
            interval_map = {
                '1d': '1d', '4h': '4h', '1h': '1h', '30m': '30m', '15m': '15m'
            }

            if timeframe not in interval_map:
                return None

            # 获取K线数据
            df = self.get_binance_kline_data(symbol, interval_map[timeframe])
            if df is None or len(df) < 50:
                return None

            # 计算技术指标
            df = self.calculate_technical_indicators(df)

            # 形态分析
            pattern_result = self.identify_candlestick_patterns(df)

            # 技术指标分析
            indicator_result = self.evaluate_technical_indicators(df)

            # 趋势分析
            trend_score = self.evaluate_trend(df)

            # 成交量分析
            volume_score = self.evaluate_volume(df)

            # 综合评分
            total_score = (
                pattern_result['score'] * self.weights['pattern'] +
                indicator_result['score'] * self.weights['indicator'] +
                trend_score * self.weights['trend'] +
                volume_score * self.weights['volume']
            )

            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'total_score': total_score,
                'pattern_score': pattern_result['score'],
                'indicator_score': indicator_result['score'],
                'trend_score': trend_score,
                'volume_score': volume_score,
                'patterns': pattern_result['patterns'],
                'rsi': indicator_result['details']['rsi'],
                'macd': indicator_result['details']['macd'],
                'bb_position': indicator_result['details']['bb_position'],
                'volume_ratio': indicator_result['details']['volume_ratio'],
                'current_price': df['close'].iloc[-1],
                'price_change': ((df['close'].iloc[-1] / df['close'].iloc[-2]) - 1) * 100,
                'timestamp': datetime.now()
            }

        except Exception as e:
            print(f"    分析 {symbol} {timeframe} 失败: {e}")
            return None

    def evaluate_trend(self, data: pd.DataFrame) -> float:
        """评估趋势得分"""
        if len(data) < 20:
            return 0.0

        latest = data.iloc[-1]
        score = 0.0

        # 移动平均线趋势
        if not pd.isna(latest['sma_5']) and not pd.isna(latest['sma_20']):
            if latest['close'] > latest['sma_5'] > latest['sma_20']:
                score += 2.0
            elif latest['close'] > latest['sma_20']:
                score += 1.0

        # 价格动量
        price_momentum = (latest['close'] / data['close'].iloc[-6]) - 1
        if price_momentum > 0.05:
            score += 1.5
        elif price_momentum > 0:
            score += 0.5

        # MACD趋势
        if not pd.isna(latest['macd_hist']):
            if latest['macd_hist'] > 0:
                score += 1.0

        return max(min(score, 5.0), 0.0)

    def evaluate_volume(self, data: pd.DataFrame) -> float:
        """评估成交量得分"""
        if len(data) < 20:
            return 0.0

        latest = data.iloc[-1]
        score = 0.0

        # 成交量比率
        if not pd.isna(latest['volume_ratio']):
            if latest['volume_ratio'] > 2.0:
                score += 2.0
            elif latest['volume_ratio'] > 1.5:
                score += 1.5
            elif latest['volume_ratio'] > 1.2:
                score += 1.0
            else:
                score += 0.5

        return max(min(score, 5.0), 0.0)

    def multi_timeframe_analysis(self, symbol: str, market_data: Dict) -> Dict:
        """多时间周期分析"""
        print(f"  开始多周期分析 {symbol}...")

        results = {}

        # 分析各个时间周期
        for timeframe in self.timeframes:
            result = self.analyze_symbol_timeframe(symbol, timeframe, market_data)
            if result:
                results[timeframe] = result

        if not results:
            return {}

        # 计算加权综合得分
        weighted_score = 0.0
        total_weight = 0.0

        for timeframe, weight in self.timeframe_weights.items():
            if timeframe in results:
                weighted_score += results[timeframe]['total_score'] * weight
                total_weight += weight

        final_score = weighted_score / total_weight if total_weight > 0 else 0.0

        # 风险评估
        risk_level = 'low' if final_score > 3.5 else 'medium' if final_score > 2.5 else 'high'

        return {
            'symbol': symbol,
            'final_score': final_score,
            'timeframe_results': results,
            'risk_level': risk_level,
            'market_data': market_data.get(symbol, {}),
            'analysis_time': datetime.now()
        }

    def send_wechat_message(self, message: str) -> bool:
        """发送企业微信消息"""
        try:
            data = {
                "msgtype": "markdown",
                "markdown": {
                    "content": message
                }
            }

            response = requests.post(
                self.wechat_webhook,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )

            result = response.json()
            success = result.get('errcode') == 0

            if success:
                print("  企业微信推送成功")
            else:
                print(f"  企业微信推送失败: {result}")

            return success

        except Exception as e:
            print(f"  发送微信消息失败: {e}")
            return False

    def format_analysis_message(self, results: List[Dict]) -> str:
        """格式化分析结果消息"""
        if not results:
            return "# 真实数据形态分析结果\n\n暂无符合条件的交易信号"

        message = f"""# 真实数据形态分析结果
分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
数据来源: 100%真实市场数据
分析周期: {'/'.join(self.timeframes)}
发现 {len(results)} 个潜力标的

"""

        for i, result in enumerate(results, 1):
            symbol = result['symbol']
            score = result['final_score']
            risk = result['risk_level']
            market_data = result['market_data']

            # 获取主要形态
            main_patterns = []
            if '1d' in result['timeframe_results']:
                main_patterns = result['timeframe_results']['1d'].get('patterns', [])

            patterns_str = ', '.join(main_patterns[:3]) if main_patterns else '无明显形态'

            # 获取主要指标
            rsi = 0
            current_price = 0
            price_change_24h = 0
            if '1d' in result['timeframe_results']:
                rsi = result['timeframe_results']['1d'].get('rsi', 0)
                current_price = result['timeframe_results']['1d'].get('current_price', 0)

            if market_data:
                price_change_24h = market_data.get('price_change_24h', 0)
                market_cap_rank = market_data.get('market_cap_rank', 0)
                name = market_data.get('name', symbol)
            else:
                name = symbol
                market_cap_rank = 0

            risk_text = {'low': '低风险', 'medium': '中风险', 'high': '高风险'}[risk]

            message += f"""{i}. {symbol} ({name}) [{risk_text}]
   综合得分: {score:.2f}/5.0
   当前价格: ${current_price:.6f}
   24h涨跌: {price_change_24h:+.2f}%
   市值排名: #{market_cap_rank}
   RSI指标: {rsi:.1f}
   K线形态: {patterns_str}

"""

        message += """投资建议:
低风险: 建议重点关注，适合稳健投资
中风险: 可适量配置，注意风险控制
高风险: 谨慎观察，等待更好时机

技术说明:
- 数据来源: CoinGecko + 币安API
- 分析方法: K线形态 + 技术指标 + 多周期确认
- 评分权重: 形态30% + 指标35% + 趋势25% + 成交量10%

风险提示:
本分析基于真实市场数据，但仅供参考。
数字货币投资有风险，请谨慎决策。"""

        return message

    def run_analysis(self) -> List[Dict]:
        """运行完整分析"""
        print("="*60)
        print("启动真实数据形态分析器")
        print("="*60)

        # 获取真实市场数据
        market_data = self.get_real_market_data()
        if not market_data:
            print("无法获取市场数据，请检查网络连接和SSR设置")
            return []

        print(f"\n开始分析 {len(self.symbols)} 个交易对...")

        results = []

        for symbol in self.symbols:
            if symbol not in market_data:
                print(f"  跳过 {symbol} (无市场数据)")
                continue

            try:
                print(f"  分析 {symbol}...")

                # 多时间周期分析
                result = self.multi_timeframe_analysis(symbol, market_data)

                if result and result['final_score'] > 2.0:  # 只保留得分较高的
                    results.append(result)
                    print(f"    {symbol} 完成，得分: {result['final_score']:.2f}")
                else:
                    print(f"    {symbol} 得分较低，跳过")

                # 避免API限制
                time.sleep(1)

            except Exception as e:
                print(f"    分析 {symbol} 失败: {e}")
                continue

        # 按得分排序
        results.sort(key=lambda x: x['final_score'], reverse=True)

        print(f"\n分析完成，发现 {len(results)} 个潜力标的")
        return results


def main():
    """主程序"""
    try:
        analyzer = RealPatternAnalyzer()
        results = analyzer.run_analysis()

        # 显示结果
        if results:
            print("\n" + "="*60)
            print("真实数据分析结果")
            print("="*60)

            for i, result in enumerate(results, 1):
                symbol = result['symbol']
                score = result['final_score']
                risk = result['risk_level']
                market_data = result['market_data']

                name = market_data.get('name', symbol) if market_data else symbol
                price = 0
                change_24h = 0

                if '1d' in result['timeframe_results']:
                    price = result['timeframe_results']['1d'].get('current_price', 0)

                if market_data:
                    change_24h = market_data.get('price_change_24h', 0)

                print(f"\n{i}. {symbol} ({name})")
                print(f"   综合得分: {score:.2f}/5.0")
                print(f"   风险等级: {risk}")
                print(f"   当前价格: ${price:.6f}")
                print(f"   24h涨跌: {change_24h:+.2f}%")

            # 发送企业微信推送
            print(f"\n准备发送企业微信推送...")
            message = analyzer.format_analysis_message(results)

            if analyzer.send_wechat_message(message):
                print("企业微信推送成功！")
            else:
                print("企业微信推送失败")
        else:
            print("\n未发现符合条件的交易信号")

        print(f"\n真实数据形态分析完成！")

        print(f"\n分析总结:")
        print(f"   总计分析: {len(analyzer.symbols)} 个交易对")
        print(f"   发现机会: {len(results)} 个")
        if len(analyzer.symbols) > 0:
            print(f"   成功率: {len(results)/len(analyzer.symbols)*100:.1f}%")

        return 0

    except Exception as e:
        print(f"程序运行失败: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
