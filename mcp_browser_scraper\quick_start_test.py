#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速启动测试脚本
"""

import sys
import os
from datetime import datetime

def log(message):
    """日志函数"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def main():
    """主函数"""
    log("开始快速启动测试...")
    
    try:
        # 导入主类
        log("导入主类...")
        from advanced_crypto_scraper import AdvancedCryptoScraper
        log("✅ 主类导入成功")
        
        # 创建实例
        log("创建实例...")
        scraper = AdvancedCryptoScraper(use_proxy=False)
        log("✅ 实例创建成功")
        
        # 检查数据源配置
        log("检查数据源配置...")
        data_sources = scraper.data_sources
        primary_source = next((name for name, config in data_sources.items() if config.get('priority') == 1), None)
        log(f"✅ 主数据源: {primary_source}")
        
        # 检查微信配置
        log("检查微信配置...")
        wechat_bots = scraper.wechat_webhooks
        log(f"✅ 微信机器人: {len(wechat_bots)} 个")
        
        log("🎉 快速启动测试成功！")
        log("💡 脚本可以正常运行")
        
        return True
        
    except Exception as e:
        log(f"❌ 启动测试失败: {e}")
        import traceback
        log(f"错误详情: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n" + "="*50)
        print("✅ 脚本启动测试通过")
        print("💡 现在可以运行主脚本了")
        print("🚀 运行命令: python advanced_crypto_scraper.py")
        print("="*50)
    else:
        print("\n" + "="*50)
        print("❌ 脚本启动测试失败")
        print("💡 请检查错误信息并修复")
        print("="*50)
