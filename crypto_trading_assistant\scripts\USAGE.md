# 脚本使用说明

本文档提供了 `qlib-main - cursor - 数字货币/scripts/` 目录下主要 Python 脚本的使用说明。

## 目录

1.  [环境准备](#环境准备)
2.  [配置文件说明 (`config.json`)](#配置文件说明-configjson)
3.  [加密货币形态分析脚本 (`crypto_pattern_analyzer.py`)](#加密货币形态分析脚本-crypto_pattern_analyzerpy)
    *   [功能概述](#功能概述)
    *   [运行方式](#运行方式)
    *   [自动邮件发送](#自动邮件发送)
    *   [命令行参数](#命令行参数)
    *   [输出](#输出)
4.  [Outlook邮件发送脚本 (`send_outlook_report.py`)](#outlook邮件发送脚本-send_outlook_reportpy)
    *   [功能概述](#功能概述-1)
    *   [运行方式](#运行方式-1)
    *   [注意事项](#注意事项)

---

## 环境准备

在运行这些脚本之前，请确保您的 Python 环境已安装所有必要的依赖库。主要的依赖可能包括：

*   `pandas`
*   `numpy`
*   `requests`
*   `ccxt`
*   `rich`
*   `tqdm`
*   `aiohttp`
*   `tenacity`
*   `matplotlib` (如果涉及到图表生成，尽管当前邮件报告是文本和CSV)
*   `seaborn` (同上)

您可以通过 `pip` 安装这些库，例如：
```bash
pip install pandas numpy requests ccxt rich tqdm aiohttp tenacity matplotlib seaborn
```
建议在项目的根目录（`qlib-main - cursor - 数字货币/`）下创建一个 `requirements.txt` 文件来管理这些依赖。

---

## 配置文件说明 (`config.json`)

这两个脚本都依赖于位于 `qlib-main - cursor - 数字货币/scripts/` 目录下的 `config.json` 文件进行配置。请确保此文件存在且包含以下关键部分：

```json
{
  "wechat": {
    "send_keys": [
      "YOUR_SERVERCHAN_SEND_KEY_1",
      "YOUR_SERVERCHAN_SEND_KEY_2"
    ]
  },
  "outlook_smtp": {
    "server": "smtp.office365.com",
    "port": 587,
    "user": "<EMAIL>",
    "password": "your_outlook_password_or_app_password",
    "sender_email": "<EMAIL>"
  },
  "email_recipients": [
    "<EMAIL>",
    "<EMAIL>"
  ],
  "gateio_api_keys": {
    "apiKey": "YOUR_GATEIO_API_KEY_HERE",
    "secret": "YOUR_GATEIO_SECRET_KEY_HERE"
  },
  "use_proxy": false,
  "request_delay": 1.0,
  "max_listings_for_email_report": 0
}
```

**重要**：
*   **实际的 `config.json` 文件不应包含注释。** 上述示例仅为结构说明。
*   请在您实际的 `qlib-main - cursor - 数字货币/scripts/config.json` 文件中，将 `"<EMAIL>"`、`"your_outlook_password_or_app_password"`、`"YOUR_GATEIO_API_KEY_HERE"` 等占位符替换为您的真实凭据和信息。
*   例如，如果您按照之前的步骤操作，您的 `config.json` 中 `outlook_smtp` 部分可能已经填入了 `<EMAIL>` 的信息。
*   根据需要更新 `"email_recipients"` 列表，添加所有希望接收报告的邮箱地址。
*   更新 `"email_recipients"` 为实际接收报告的邮箱地址列表。
*   如果需要，配置 `gateio_api_keys`。

---

## 加密货币形态分析脚本 (`crypto_pattern_analyzer.py`)

### 功能概述

此脚本用于分析加密货币市场，特别是新上市币种的特定价格和成交量形态。它会：
1.  从交易所（如 Gate.io）获取市场数据和K线数据。
2.  分析指定天数内新上市的币种。
3.  对这些币种进行技术指标计算和形态识别。
4.  生成一份包含分析结果的汇总报告（文本格式）和详细数据（CSV格式）。
5.  将生成的报告保存到 `qlib-main - cursor - 数字货币/analysis_results/` 目录下。
6.  **自动触发邮件发送**：在成功生成报告后，会自动调用 `send_outlook_report.py` 的逻辑，将最新的报告通过 Outlook 邮件发送出去。

### 运行方式

1.  确保 `config.json` 文件已正确配置，特别是 `gateio_api_keys` (如果需要从未登录的交易所获取数据) 和邮件相关的配置 (`outlook_smtp`, `email_recipients`)。
2.  打开终端或命令行。
3.  导航到项目的根目录：`cd path/to/qlib-main - cursor - 数字货币/`
4.  执行脚本：
    ```bash
    python scripts/crypto_pattern_analyzer.py [可选参数]
    ```

### 自动邮件发送

脚本在完成分析并成功保存报告文件（`summary_*.json` 和 `summary_*.csv`）后，会自动调用 `scripts/send_outlook_report.py` 中的 `main()` 函数。这将导致：
*   `send_outlook_report.py` 查找刚刚由 `crypto_pattern_analyzer.py` 生成的最新报告文件。
*   使用这些文件准备邮件内容（HTML正文和CSV附件）。
*   通过 `config.json` 中配置的 Outlook SMTP 服务器将邮件发送给指定的收件人。

### 命令行参数

*   `--max-listings <数量>`: 可选参数，用于指定分析和报告中包含的新上市币种的最大数量。如果设置为 `0` 或未提供，则会分析所有在设定天数内（默认为60天）新上市的币种。
    *   示例: `python scripts/crypto_pattern_analyzer.py --max-listings 10` (只分析最新的10个币种)

### 输出

*   **控制台输出**：脚本运行时会在控制台打印日志信息和分析摘要。
*   **日志文件**：详细日志保存在 `qlib-main - cursor - 数字货币/crypto_pattern_analyzer.log`。
*   **报告文件**：
    *   JSON 格式的详细分析数据：`qlib-main - cursor - 数字货币/analysis_results/summary_YYYYMMDD_HHMMSS.json`
    *   CSV 格式的详细分析数据：`qlib-main - cursor - 数字货币/analysis_results/summary_YYYYMMDD_HHMMSS.csv`
*   **邮件报告**：通过 Outlook 发送给 `config.json` 中指定的收件人。

---

## Outlook邮件发送脚本 (`send_outlook_report.py`)

### 功能概述

此脚本的主要功能是：
1.  查找 `qlib-main - cursor - 数字货币/analysis_results/` 目录下由 `crypto_pattern_analyzer.py` 生成的**最新**的分析报告文件（`summary_*.json` 和 `summary_*.csv`）。
2.  加载这些报告文件。
3.  使用 `PatternAnalyzer` 类中的格式化逻辑，将 JSON 数据转换成适合邮件正文的 HTML 格式。
4.  将 CSV 文件作为邮件附件。
5.  通过 `config.json` 文件中配置的 Outlook SMTP 服务器，将包含 HTML 报告和 CSV 附件的邮件发送给指定的收件人。

### 运行方式

**1. 自动触发 (推荐)**：
   如上所述，当您运行 `crypto_pattern_analyzer.py` 并且它成功完成分析后，会自动调用此脚本的邮件发送功能。这是推荐的使用方式，以确保发送的是最新的分析结果。

**2. 手动独立运行**：
   您也可以在需要时手动运行此脚本，以重新发送（当时）最新的已生成报告。
   1.  确保 `config.json` 文件已正确配置 Outlook SMTP 和收件人信息。
   2.  确保 `qlib-main - cursor - 数字货币/analysis_results/` 目录下至少存在一组由 `crypto_pattern_analyzer.py` 生成的 `summary_*.json` 和 `summary_*.csv` 文件。
   3.  打开终端或命令行。
   4.  导航到项目的根目录：`cd path/to/qlib-main - cursor - 数字货币/`
   5.  执行脚本：
       ```bash
       python scripts/send_outlook_report.py
       ```
   此脚本会查找最新的报告文件并发送。

### 注意事项

*   **依赖 `crypto_pattern_analyzer.py` 生成的报告**：此脚本本身不执行数据分析或报告生成。它依赖于 `crypto_pattern_analyzer.py` 已经创建的报告文件。如果 `analysis_results/` 目录为空或没有有效的报告文件，脚本将无法发送邮件并会报错。
*   **配置文件**：正确的 `config.json` 配置（特别是 `outlook_smtp` 和 `email_recipients`）对邮件发送至关重要。
*   **日志文件**：此脚本的日志会输出到控制台，并追加到 `qlib-main - cursor - 数字货币/logs/send_outlook_report.log`。