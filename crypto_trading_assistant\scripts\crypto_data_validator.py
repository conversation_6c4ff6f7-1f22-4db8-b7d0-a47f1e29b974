import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import json
from typing import Dict, List, Tuple
import logging
import gzip

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CryptoDataValidator:
    def __init__(self, data_dir: str):
        self.data_dir = data_dir
        self.results = {}
        
    def validate_file(self, file_path: str) -> Dict:
        """验证单个CSV文件的数据质量"""
        try:
            # 根据文件扩展名选择读取方式
            if file_path.endswith('.gz'):
                with gzip.open(file_path, 'rt') as f:
                    df = pd.read_csv(f)
            else:
                df = pd.read_csv(file_path)
            
            if 'datetime' not in df.columns:
                return {'file_name': os.path.basename(file_path), 'error': 'No datetime column'}
            
            df['datetime'] = pd.to_datetime(df['datetime'])
            df = df.sort_values('datetime')
            
            results = {
                'file_name': os.path.basename(file_path),
                'total_rows': len(df),
                'date_range': {
                    'start': df['datetime'].min().strftime('%Y-%m-%d'),
                    'end': df['datetime'].max().strftime('%Y-%m-%d')
                },
                'missing_values': df.isnull().sum().to_dict(),
                'duplicates': len(df[df.duplicated(subset=['datetime'], keep=False)]),
                'gaps': self._find_time_gaps(df),
                'price_anomalies': self._check_price_anomalies(df),
                'volume_anomalies': self._check_volume_anomalies(df)
            }
            
            return results
        except Exception as e:
            logger.error(f"Error validating file {file_path}: {str(e)}")
            return {'file_name': os.path.basename(file_path), 'error': str(e)}
    
    def _find_time_gaps(self, df: pd.DataFrame) -> List[Dict]:
        """查找时间序列中的间隔"""
        df = df.sort_values('datetime')
        expected_days = (df['datetime'].max() - df['datetime'].min()).days + 1
        actual_days = len(df)
        
        if actual_days < expected_days:
            all_dates = pd.date_range(df['datetime'].min(), df['datetime'].max(), freq='D')
            missing_dates = all_dates.difference(df['datetime'])
            return [{'date': d.strftime('%Y-%m-%d')} for d in missing_dates]
        return []
    
    def _check_price_anomalies(self, df: pd.DataFrame) -> List[Dict]:
        """检查价格异常"""
        anomalies = []
        
        # 检查价格是否为负
        negative_prices = df[(df['open'] <= 0) | (df['high'] <= 0) | 
                           (df['low'] <= 0) | (df['close'] <= 0)]
        if not negative_prices.empty:
            anomalies.append({
                'type': 'negative_prices',
                'dates': negative_prices['datetime'].dt.strftime('%Y-%m-%d').tolist()
            })
        
        # 检查价格是否合理（例如，价格变化超过50%）
        df['price_change'] = df['close'].pct_change().abs()
        large_changes = df[df['price_change'] > 0.5]
        if not large_changes.empty:
            anomalies.append({
                'type': 'large_price_changes',
                'dates': large_changes['datetime'].dt.strftime('%Y-%m-%d').tolist()
            })
        
        return anomalies
    
    def _check_volume_anomalies(self, df: pd.DataFrame) -> List[Dict]:
        """检查成交量异常"""
        anomalies = []
        
        # 检查负成交量
        negative_volume = df[df['volume'] < 0]
        if not negative_volume.empty:
            anomalies.append({
                'type': 'negative_volume',
                'dates': negative_volume['datetime'].dt.strftime('%Y-%m-%d').tolist()
            })
        
        # 检查异常大的成交量（超过3个标准差）
        volume_mean = df['volume'].mean()
        volume_std = df['volume'].std()
        large_volume = df[df['volume'] > volume_mean + 3 * volume_std]
        if not large_volume.empty:
            anomalies.append({
                'type': 'unusually_large_volume',
                'dates': large_volume['datetime'].dt.strftime('%Y-%m-%d').tolist()
            })
        
        return anomalies
    
    def validate_all_files(self):
        """验证所有CSV文件"""
        for root, _, files in os.walk(self.data_dir):
            for file in files:
                if file.endswith(('.csv', '.csv.gz')) and '1d' in root:
                    file_path = os.path.join(root, file)
                    self.results[file] = self.validate_file(file_path)
    
    def save_results(self, output_file: str):
        """保存验证结果"""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
    
    def print_summary(self):
        """打印验证结果摘要"""
        for file_name, result in self.results.items():
            print(f"\n=== 验证结果: {file_name} ===")
            if 'error' in result:
                print(f"错误: {result['error']}")
                continue
                
            print(f"总行数: {result['total_rows']}")
            print(f"日期范围: {result['date_range']['start']} 到 {result['date_range']['end']}")
            
            if result['missing_values']:
                print("\n缺失值:")
                for col, count in result['missing_values'].items():
                    if count > 0:
                        print(f"  {col}: {count}")
            
            if result['duplicates'] > 0:
                print(f"\n重复记录: {result['duplicates']}")
            
            if result['gaps']:
                print(f"\n时间间隔: {len(result['gaps'])} 个")
                for gap in result['gaps'][:5]:  # 只显示前5个
                    print(f"  {gap['date']}")
                if len(result['gaps']) > 5:
                    print(f"  ... 还有 {len(result['gaps']) - 5} 个间隔")
            
            if result['price_anomalies']:
                print("\n价格异常:")
                for anomaly in result['price_anomalies']:
                    print(f"  {anomaly['type']}: {len(anomaly['dates'])} 个")
            
            if result['volume_anomalies']:
                print("\n成交量异常:")
                for anomaly in result['volume_anomalies']:
                    print(f"  {anomaly['type']}: {len(anomaly['dates'])} 个")

def main():
    # 设置数据目录
    data_dir = os.path.join('data', 'crypto')
    
    # 创建验证器实例
    validator = CryptoDataValidator(data_dir)
    
    # 执行验证
    logger.info("开始验证数据...")
    validator.validate_all_files()
    
    # 保存结果
    output_file = os.path.join(data_dir, 'validation_results.json')
    validator.save_results(output_file)
    
    # 打印摘要
    validator.print_summary()
    
    logger.info(f"验证完成，详细结果已保存到: {output_file}")

if __name__ == "__main__":
    main() 