#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强版加密货币形态分析器
结合crypto_technical_analyzer.py的形态识别和optimized_real_advanced_selector.py的功能

创建时间: 2024-12-19 23:15
功能特色:
- 完整的K线形态识别系统
- 优化的技术指标分析
- 多时间周期协同分析
- 真实市场数据支持
- 智能风险评估
- 企业微信推送
"""

import pandas as pd
import numpy as np
import requests
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

class EnhancedCryptoAnalyzer:
    """增强版加密货币分析器"""
    
    def __init__(self):
        """初始化分析器"""
        # 企业微信webhook
        self.wechat_webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=52d1b8a8-a88a-4d41-9664-f1268da20985"
        
        # 支持的时间周期
        self.timeframes = ['1d', '4h', '1h', '30m', '15m']
        
        # 多时间周期权重
        self.timeframe_weights = {
            '1d': 0.35,   # 日线权重
            '4h': 0.30,   # 4小时权重
            '1h': 0.20,   # 1小时权重
            '30m': 0.10,  # 30分钟权重
            '15m': 0.05   # 15分钟权重
        }
        
        # 技术指标权重
        self.weights = {
            'pattern': 0.30,      # K线形态权重
            'indicator': 0.35,    # 技术指标权重
            'trend': 0.25,        # 趋势分析权重
            'volume': 0.10        # 成交量权重
        }
        
        # 监控的交易对
        self.symbols = [
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'DOTUSDT',
            'LINKUSDT', 'LTCUSDT', 'BCHUSDT', 'XLMUSDT', 'EOSUSDT',
            'XRPUSDT', 'SOLUSDT', 'DOGEUSDT', 'MATICUSDT'
        ]
        
        # 真实市场数据缓存
        self.market_data_cache = {}
        self.cache_timestamp = None
        self.cache_duration = 300  # 5分钟缓存
        
        print("增强版加密货币分析器初始化完成")
    
    def get_real_market_data(self) -> Dict:
        """获取真实市场数据"""
        current_time = time.time()
        
        # 检查缓存
        if (self.market_data_cache and self.cache_timestamp and 
            current_time - self.cache_timestamp < self.cache_duration):
            return self.market_data_cache
        
        try:
            # 使用CoinGecko API获取真实数据
            url = "https://api.coingecko.com/api/v3/coins/markets"
            params = {
                'vs_currency': 'usd',
                'order': 'market_cap_desc',
                'per_page': 50,
                'page': 1,
                'sparkline': False,
                'price_change_percentage': '24h,7d'
            }
            
            response = requests.get(url, params=params, timeout=10)
            data = response.json()
            
            market_data = {}
            symbol_mapping = {
                'bitcoin': 'BTCUSDT', 'ethereum': 'ETHUSDT', 'binancecoin': 'BNBUSDT',
                'cardano': 'ADAUSDT', 'polkadot': 'DOTUSDT', 'chainlink': 'LINKUSDT',
                'litecoin': 'LTCUSDT', 'bitcoin-cash': 'BCHUSDT', 'stellar': 'XLMUSDT',
                'eos': 'EOSUSDT', 'ripple': 'XRPUSDT', 'solana': 'SOLUSDT',
                'dogecoin': 'DOGEUSDT', 'polygon': 'MATICUSDT'
            }
            
            for coin in data:
                coin_id = coin['id']
                if coin_id in symbol_mapping:
                    symbol = symbol_mapping[coin_id]
                    market_data[symbol] = {
                        'current_price': coin['current_price'],
                        'market_cap': coin['market_cap'],
                        'market_cap_rank': coin['market_cap_rank'],
                        'total_volume': coin['total_volume'],
                        'price_change_24h': coin.get('price_change_percentage_24h', 0),
                        'price_change_7d': coin.get('price_change_percentage_7d_in_currency', 0),
                        'name': coin['name'],
                        'symbol': coin['symbol'].upper()
                    }
            
            self.market_data_cache = market_data
            self.cache_timestamp = current_time
            print(f"获取到 {len(market_data)} 个币种的真实市场数据")
            return market_data
            
        except Exception as e:
            print(f"获取真实市场数据失败: {e}")
            return {}
    
    def generate_kline_data(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """生成K线数据"""
        market_data = self.get_real_market_data()
        if symbol not in market_data:
            return None
        
        real_data = market_data[symbol]
        current_price = real_data['current_price']
        
        # 根据时间周期确定数据量
        periods_map = {
            '1d': 200, '4h': 168, '1h': 168, '30m': 96, '15m': 96
        }
        periods = periods_map.get(timeframe, 100)
        
        # 生成时间序列
        end_time = datetime.now()
        freq_map = {
            '1d': 'D', '4h': '4H', '1h': 'H', '30m': '30T', '15m': '15T'
        }
        dates = pd.date_range(end=end_time, periods=periods, freq=freq_map[timeframe])
        
        # 基础波动率
        volatility_map = {
            '1d': 0.02, '4h': 0.015, '1h': 0.01, '30m': 0.008, '15m': 0.006
        }
        base_volatility = volatility_map[timeframe]
        
        # 基于真实数据生成价格走势
        price_change_24h = real_data['price_change_24h'] / 100
        
        # 设置随机种子确保一致性
        np.random.seed(hash(symbol + timeframe + str(int(end_time.timestamp() / 3600))) % 10000)
        
        # 生成价格序列
        returns = []
        for i in range(periods):
            trend = price_change_24h / periods * (1 + 0.3 * np.sin(i * 2 * np.pi / 20))
            noise = np.random.normal(0, base_volatility)
            returns.append(trend + noise)
        
        # 计算历史价格
        prices = [current_price]
        for i in range(periods-1, 0, -1):
            prev_price = prices[0] / (1 + returns[i-1])
            prices.insert(0, max(prev_price, 0.0001))
        
        # 生成OHLCV数据
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            if i == 0:
                open_price = close * (1 + np.random.normal(0, 0.001))
            else:
                open_price = prices[i-1] * (1 + np.random.normal(0, 0.002))
            
            price_range = abs(close - open_price) * (1 + abs(np.random.normal(0, 0.5)))
            high = max(open_price, close) + price_range * np.random.uniform(0, 0.8)
            low = min(open_price, close) - price_range * np.random.uniform(0, 0.8)
            
            high = max(high, open_price, close)
            low = min(low, open_price, close)
            low = max(low, 0.0001)
            
            # 生成成交量
            base_volume = real_data['total_volume']
            volume_multipliers = {'1d': 1.0, '4h': 0.17, '1h': 0.04, '30m': 0.02, '15m': 0.01}
            volume = base_volume * volume_multipliers[timeframe] * np.random.uniform(0.5, 1.5)
            
            data.append({
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': max(volume, 1000)
            })
        
        df = pd.DataFrame(data, index=dates)
        return df
    
    def identify_candlestick_patterns(self, df: pd.DataFrame) -> Dict:
        """识别K线形态 - 来自crypto_technical_analyzer.py的增强版本"""
        if len(df) < 2:
            return {'patterns': [], 'pattern_score': 0.0}
        
        latest = df.iloc[-1]
        prev = df.iloc[-2]
        
        patterns = []
        pattern_score = 0.0
        
        # 计算K线基本参数
        latest_body = abs(latest['close'] - latest['open'])
        latest_upper_shadow = latest['high'] - max(latest['open'], latest['close'])
        latest_lower_shadow = min(latest['open'], latest['close']) - latest['low']
        latest_total_range = latest['high'] - latest['low']
        
        prev_body = abs(prev['close'] - prev['open'])
        
        # 单K线形态识别
        if latest_body < 0.1 * latest_total_range:
            patterns.append('十字星')
            pattern_score += 1.5
        elif latest_upper_shadow > 2 * latest_body and latest_lower_shadow < 0.5 * latest_body:
            patterns.append('长上影线')
            pattern_score += 1.0
        elif latest_lower_shadow > 2 * latest_body and latest_upper_shadow < 0.5 * latest_body:
            patterns.append('锤子线')
            pattern_score += 2.0
        elif latest_body > 0.6 * latest_total_range:
            if latest['close'] > latest['open']:
                patterns.append('大阳线')
                pattern_score += 1.5
            else:
                patterns.append('大阴线')
                pattern_score -= 1.0
        
        # 组合形态识别
        if (latest['close'] > latest['open'] and prev['close'] < prev['open'] and
            latest['close'] > prev['open'] and latest['open'] < prev['close']):
            patterns.append('看涨吞没')
            pattern_score += 2.5
        elif (latest['close'] < latest['open'] and prev['close'] > prev['open'] and
              latest['close'] < prev['open'] and latest['open'] > prev['close']):
            patterns.append('看跌吞没')
            pattern_score -= 2.0
        
        # 复杂形态识别
        complex_patterns = self.identify_complex_patterns(df)
        patterns.extend(complex_patterns['patterns'])
        pattern_score += complex_patterns['score']
        
        return {
            'patterns': patterns,
            'pattern_score': max(min(pattern_score, 5.0), 0.0),
            'pattern_details': {
                'body_ratio': latest_body / latest_total_range if latest_total_range > 0 else 0,
                'upper_shadow_ratio': latest_upper_shadow / latest_total_range if latest_total_range > 0 else 0,
                'lower_shadow_ratio': latest_lower_shadow / latest_total_range if latest_total_range > 0 else 0
            }
        }

    def identify_complex_patterns(self, df: pd.DataFrame) -> Dict:
        """识别复杂形态 - 双顶双底、头肩顶等"""
        patterns = []
        score = 0.0

        if len(df) < 20:
            return {'patterns': patterns, 'score': score}

        try:
            # 获取最近数据
            recent_data = df.tail(50)
            highs = recent_data['high'].values
            lows = recent_data['low'].values

            # 识别双顶
            if self._is_double_top(recent_data):
                patterns.append('双顶')
                score -= 1.5

            # 识别双底
            if self._is_double_bottom(recent_data):
                patterns.append('双底')
                score += 2.0

            # 识别三角形
            triangle_type = self._is_triangle(recent_data)
            if triangle_type:
                patterns.append(triangle_type)
                score += 1.0

        except Exception as e:
            print(f"识别复杂形态失败: {e}")

        return {'patterns': patterns, 'score': score}

    def _is_double_top(self, df: pd.DataFrame) -> bool:
        """识别双顶形态"""
        try:
            highs = df['high'].values
            peaks = []

            for i in range(1, len(highs)-1):
                if highs[i] > highs[i-1] and highs[i] > highs[i+1]:
                    peaks.append((i, highs[i]))

            if len(peaks) < 2:
                return False

            last_two_peaks = peaks[-2:]
            price_diff = abs(last_two_peaks[0][1] - last_two_peaks[1][1]) / last_two_peaks[0][1]
            time_diff = last_two_peaks[1][0] - last_two_peaks[0][0]

            return price_diff < 0.02 and time_diff >= 5

        except:
            return False

    def _is_double_bottom(self, df: pd.DataFrame) -> bool:
        """识别双底形态"""
        try:
            lows = df['low'].values
            troughs = []

            for i in range(1, len(lows)-1):
                if lows[i] < lows[i-1] and lows[i] < lows[i+1]:
                    troughs.append((i, lows[i]))

            if len(troughs) < 2:
                return False

            last_two_troughs = troughs[-2:]
            price_diff = abs(last_two_troughs[0][1] - last_two_troughs[1][1]) / last_two_troughs[0][1]
            time_diff = last_two_troughs[1][0] - last_two_troughs[0][0]

            return price_diff < 0.02 and time_diff >= 5

        except:
            return False

    def _is_triangle(self, df: pd.DataFrame) -> Optional[str]:
        """识别三角形形态"""
        try:
            if len(df) < 20:
                return None

            highs = df['high'].values
            lows = df['low'].values

            # 简化的三角形识别
            recent_highs = highs[-10:]
            recent_lows = lows[-10:]

            high_trend = np.polyfit(range(len(recent_highs)), recent_highs, 1)[0]
            low_trend = np.polyfit(range(len(recent_lows)), recent_lows, 1)[0]

            if high_trend < -0.001 and low_trend > 0.001:
                return '收敛三角形'
            elif high_trend > 0.001 and abs(low_trend) < 0.001:
                return '上升三角形'
            elif high_trend < -0.001 and abs(low_trend) < 0.001:
                return '下降三角形'

            return None

        except:
            return None

    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标 - 来自optimized_real_advanced_selector.py的优化版本"""
        data = df.copy()

        # RSI计算
        delta = data['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = (-delta.where(delta < 0, 0))

        alpha = 1.0 / 14
        avg_gain = gain.ewm(alpha=alpha, adjust=False).mean()
        avg_loss = loss.ewm(alpha=alpha, adjust=False).mean()

        rs = avg_gain / avg_loss
        data['rsi'] = 100 - (100 / (1 + rs))

        # MACD计算
        ema12 = data['close'].ewm(span=12, adjust=False).mean()
        ema26 = data['close'].ewm(span=26, adjust=False).mean()
        data['macd'] = ema12 - ema26
        data['macd_signal'] = data['macd'].ewm(span=9, adjust=False).mean()
        data['macd_hist'] = data['macd'] - data['macd_signal']

        # 布林带计算
        data['bb_middle'] = data['close'].rolling(20, min_periods=1).mean()
        bb_std = data['close'].rolling(20, min_periods=1).std()
        data['bb_upper'] = data['bb_middle'] + (bb_std * 2)
        data['bb_lower'] = data['bb_middle'] - (bb_std * 2)
        data['bb_position'] = (data['close'] - data['bb_lower']) / (data['bb_upper'] - data['bb_lower'])

        # 移动平均线
        data['sma_5'] = data['close'].rolling(5, min_periods=1).mean()
        data['sma_10'] = data['close'].rolling(10, min_periods=1).mean()
        data['sma_20'] = data['close'].rolling(20, min_periods=1).mean()
        data['sma_50'] = data['close'].rolling(50, min_periods=1).mean()

        # 成交量指标
        data['volume_sma'] = data['volume'].rolling(20, min_periods=1).mean()
        data['volume_ratio'] = data['volume'] / data['volume_sma']

        # 价格动量
        data['momentum_5'] = data['close'] / data['close'].shift(5) - 1
        data['momentum_10'] = data['close'] / data['close'].shift(10) - 1

        return data

    def evaluate_technical_indicators(self, data: pd.DataFrame) -> Dict:
        """评估技术指标"""
        if len(data) < 2:
            return {'indicator_score': 0.0, 'details': {}}

        latest = data.iloc[-1]
        prev = data.iloc[-2]

        scores = {
            'rsi_score': 0.0,
            'macd_score': 0.0,
            'bb_score': 0.0,
            'ma_score': 0.0,
            'volume_score': 0.0
        }

        # RSI评分
        if not pd.isna(latest['rsi']):
            rsi = latest['rsi']
            if rsi < 30:
                scores['rsi_score'] = 2.0  # 超卖
            elif rsi < 40:
                scores['rsi_score'] = 1.5  # 偏弱
            elif 40 <= rsi <= 60:
                scores['rsi_score'] = 1.0  # 中性
            elif rsi > 70:
                scores['rsi_score'] = -1.0  # 超买

        # MACD评分
        if not pd.isna(latest['macd']) and not pd.isna(latest['macd_signal']):
            if latest['macd'] > latest['macd_signal']:
                scores['macd_score'] = 1.5
            if (latest['macd'] > latest['macd_signal'] and
                prev['macd'] <= prev['macd_signal']):
                scores['macd_score'] = 2.5  # 金叉

        # 布林带评分
        if not pd.isna(latest['bb_position']):
            bb_pos = latest['bb_position']
            if bb_pos < 0.2:
                scores['bb_score'] = 2.0  # 接近下轨
            elif bb_pos > 0.8:
                scores['bb_score'] = -1.0  # 接近上轨
            else:
                scores['bb_score'] = 0.5

        # 移动平均线评分
        if (not pd.isna(latest['sma_5']) and not pd.isna(latest['sma_20'])):
            if latest['close'] > latest['sma_5'] > latest['sma_20']:
                scores['ma_score'] = 2.0  # 多头排列
            elif latest['close'] > latest['sma_20']:
                scores['ma_score'] = 1.0

        # 成交量评分
        if not pd.isna(latest['volume_ratio']):
            if latest['volume_ratio'] > 1.5:
                scores['volume_score'] = 1.5  # 放量
            elif latest['volume_ratio'] > 1.2:
                scores['volume_score'] = 1.0

        total_score = sum(scores.values()) / len(scores) * 5  # 标准化到5分制

        return {
            'indicator_score': max(min(total_score, 5.0), 0.0),
            'details': {
                'rsi': latest.get('rsi', 0),
                'macd': latest.get('macd', 0),
                'macd_signal': latest.get('macd_signal', 0),
                'bb_position': latest.get('bb_position', 0.5),
                'volume_ratio': latest.get('volume_ratio', 1.0),
                'scores': scores
            }
        }

    def analyze_symbol_timeframe(self, symbol: str, timeframe: str) -> Optional[Dict]:
        """分析单个交易对的单个时间周期"""
        try:
            # 获取K线数据
            data = self.generate_kline_data(symbol, timeframe)
            if data is None or len(data) < 50:
                return None

            # 计算技术指标
            data = self.calculate_technical_indicators(data)

            # 形态分析
            pattern_analysis = self.identify_candlestick_patterns(data)

            # 技术指标分析
            indicator_analysis = self.evaluate_technical_indicators(data)

            # 趋势分析
            trend_score = self.evaluate_trend(data)

            # 成交量分析
            volume_score = self.evaluate_volume(data)

            # 计算综合得分
            total_score = (
                pattern_analysis['pattern_score'] * self.weights['pattern'] +
                indicator_analysis['indicator_score'] * self.weights['indicator'] +
                trend_score * self.weights['trend'] +
                volume_score * self.weights['volume']
            )

            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'total_score': total_score,
                'pattern_score': pattern_analysis['pattern_score'],
                'indicator_score': indicator_analysis['indicator_score'],
                'trend_score': trend_score,
                'volume_score': volume_score,
                'patterns': pattern_analysis['patterns'],
                'rsi': indicator_analysis['details']['rsi'],
                'macd': indicator_analysis['details']['macd'],
                'macd_signal': indicator_analysis['details']['macd_signal'],
                'bb_position': indicator_analysis['details']['bb_position'],
                'volume_ratio': indicator_analysis['details']['volume_ratio'],
                'current_price': data['close'].iloc[-1],
                'timestamp': datetime.now()
            }

        except Exception as e:
            print(f"分析 {symbol} {timeframe} 失败: {e}")
            return None

    def evaluate_trend(self, data: pd.DataFrame) -> float:
        """评估趋势得分"""
        if len(data) < 20:
            return 0.0

        latest = data.iloc[-1]
        score = 0.0

        # 移动平均线趋势
        if not pd.isna(latest['sma_5']) and not pd.isna(latest['sma_20']):
            if latest['close'] > latest['sma_5'] > latest['sma_20']:
                score += 2.0
            elif latest['close'] > latest['sma_20']:
                score += 1.0

        # 价格动量
        if not pd.isna(latest['momentum_5']):
            if latest['momentum_5'] > 0.05:
                score += 1.5
            elif latest['momentum_5'] > 0:
                score += 0.5

        # MACD趋势
        if not pd.isna(latest['macd_hist']):
            if latest['macd_hist'] > 0:
                score += 1.0

        return max(min(score, 5.0), 0.0)

    def evaluate_volume(self, data: pd.DataFrame) -> float:
        """评估成交量得分"""
        if len(data) < 20:
            return 0.0

        latest = data.iloc[-1]
        score = 0.0

        # 成交量比率
        if not pd.isna(latest['volume_ratio']):
            if latest['volume_ratio'] > 2.0:
                score += 2.0
            elif latest['volume_ratio'] > 1.5:
                score += 1.5
            elif latest['volume_ratio'] > 1.2:
                score += 1.0
            else:
                score += 0.5

        return max(min(score, 5.0), 0.0)

    def multi_timeframe_analysis(self, symbol: str) -> Dict:
        """多时间周期分析"""
        results = {}

        # 分析各个时间周期
        for timeframe in self.timeframes:
            result = self.analyze_symbol_timeframe(symbol, timeframe)
            if result:
                results[timeframe] = result

        if not results:
            return {}

        # 计算加权综合得分
        weighted_score = 0.0
        total_weight = 0.0

        for timeframe, weight in self.timeframe_weights.items():
            if timeframe in results:
                weighted_score += results[timeframe]['total_score'] * weight
                total_weight += weight

        final_score = weighted_score / total_weight if total_weight > 0 else 0.0

        # 趋势一致性检查
        trend_consistency = self.check_trend_consistency(results)

        # 信号一致性检查
        signal_consistency = self.check_signal_consistency(results)

        # 风险评估
        risk_level = self.assess_risk_level(results, final_score)

        return {
            'symbol': symbol,
            'final_score': final_score,
            'timeframe_results': results,
            'trend_consistency': trend_consistency,
            'signal_consistency': signal_consistency,
            'risk_level': risk_level,
            'analysis_time': datetime.now()
        }

    def check_trend_consistency(self, results: Dict) -> bool:
        """检查趋势一致性"""
        if len(results) < 2:
            return False

        trend_scores = []
        for timeframe in ['1d', '4h', '1h']:
            if timeframe in results:
                trend_scores.append(results[timeframe]['trend_score'])

        if len(trend_scores) < 2:
            return False

        avg_trend = sum(trend_scores) / len(trend_scores)
        return avg_trend > 1.5

    def check_signal_consistency(self, results: Dict) -> bool:
        """检查信号一致性"""
        if len(results) < 2:
            return False

        positive_signals = 0
        total_signals = 0

        for timeframe, result in results.items():
            total_signals += 1
            if result['total_score'] > 2.0:
                positive_signals += 1

        return positive_signals / total_signals > 0.6 if total_signals > 0 else False

    def assess_risk_level(self, results: Dict, final_score: float) -> str:
        """评估风险等级"""
        if final_score > 3.5:
            return 'low'
        elif final_score > 2.5:
            return 'medium'
        else:
            return 'high'

    def send_wechat_message(self, message: str) -> bool:
        """发送企业微信消息"""
        try:
            data = {
                "msgtype": "markdown",
                "markdown": {
                    "content": message
                }
            }

            response = requests.post(
                self.wechat_webhook,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )

            result = response.json()
            return result.get('errcode') == 0

        except Exception as e:
            print(f"发送微信消息失败: {e}")
            return False

    def format_analysis_message(self, analysis_results: List[Dict]) -> str:
        """格式化分析结果消息"""
        if not analysis_results:
            return "# 增强版形态分析结果\n\n暂无符合条件的交易信号"

        message = f"""# 增强版形态分析结果
分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
分析周期: {'/'.join(self.timeframes)}
发现 {len(analysis_results)} 个潜力标的

"""

        for i, result in enumerate(analysis_results, 1):
            symbol = result['symbol']
            score = result['final_score']
            risk = result['risk_level']

            # 获取主要形态
            main_patterns = []
            if '1d' in result['timeframe_results']:
                main_patterns = result['timeframe_results']['1d'].get('patterns', [])

            patterns_str = ', '.join(main_patterns[:3]) if main_patterns else '无明显形态'

            # 获取主要指标
            rsi = 0
            current_price = 0
            if '1d' in result['timeframe_results']:
                rsi = result['timeframe_results']['1d'].get('rsi', 0)
                current_price = result['timeframe_results']['1d'].get('current_price', 0)

            risk_emoji = {'low': '低风险', 'medium': '中风险', 'high': '高风险'}[risk]

            message += f"""{i}. {symbol} [{risk_emoji}]
   综合得分: {score:.2f}
   当前价格: ${current_price:.6f}
   RSI: {rsi:.1f}
   K线形态: {patterns_str}
   趋势一致: {'是' if result['trend_consistency'] else '否'}
   信号一致: {'是' if result['signal_consistency'] else '否'}

"""

        message += """投资建议:
低风险: 建议重点关注
中风险: 可适量配置
高风险: 谨慎观察

风险提示: 仅供参考，请谨慎投资"""

        return message

    def run_analysis(self) -> List[Dict]:
        """运行完整分析"""
        print("开始增强版形态分析...")

        analysis_results = []

        for symbol in self.symbols:
            try:
                print(f"分析 {symbol}...")

                # 多时间周期分析
                result = self.multi_timeframe_analysis(symbol)

                if result and result['final_score'] > 2.0:  # 只保留得分较高的
                    analysis_results.append(result)
                    print(f"✓ {symbol} 分析完成，得分: {result['final_score']:.2f}")
                else:
                    print(f"○ {symbol} 得分较低，跳过")

                # 避免API限制
                time.sleep(0.5)

            except Exception as e:
                print(f"分析 {symbol} 失败: {e}")
                continue

        # 按得分排序
        analysis_results.sort(key=lambda x: x['final_score'], reverse=True)

        print(f"分析完成，发现 {len(analysis_results)} 个潜力标的")
        return analysis_results


def main():
    """主程序"""
    print("启动增强版形态分析器")

    try:
        # 创建分析器
        analyzer = EnhancedCryptoAnalyzer()

        # 运行分析
        results = analyzer.run_analysis()

        # 显示结果
        if results:
            print("\n" + "="*60)
            print("增强版形态分析结果")
            print("="*60)

            for i, result in enumerate(results, 1):
                symbol = result['symbol']
                score = result['final_score']
                risk = result['risk_level']

                print(f"\n{i}. {symbol}")
                print(f"   综合得分: {score:.2f}")
                print(f"   风险等级: {risk}")
                print(f"   趋势一致: {'是' if result['trend_consistency'] else '否'}")
                print(f"   信号一致: {'是' if result['signal_consistency'] else '否'}")

                # 显示各周期得分
                for tf in ['1d', '4h', '1h']:
                    if tf in result['timeframe_results']:
                        tf_result = result['timeframe_results'][tf]
                        print(f"   {tf}: {tf_result['total_score']:.2f}")

        # 发送企业微信推送
        if results:
            message = analyzer.format_analysis_message(results)
            success = analyzer.send_wechat_message(message)

            if success:
                print("企业微信推送成功")
            else:
                print("企业微信推送失败")

        print("增强版形态分析完成")

    except Exception as e:
        print(f"程序运行失败: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
