#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
从TXT文件读取币种列表，分析上影线，并通过Server酱发送报告。
"""

import os
import time
import json
import requests
import pandas as pd
from datetime import datetime
from pycoingecko import CoinGeckoAPI
import logging

logger = logging.getLogger(__name__)

logger.setLevel(logging.INFO)
handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)

# 初始化CoinGeckoAPI实例
cg = CoinGeckoAPI()

# 定义start_time变量
start_time = datetime.now()

# Server酱推送Key (需要替换成你自己的)
SERVERCHAN_SEND_KEY = os.environ.get('SERVERCHAN_SEND_KEY', '') # 从环境变量读取，或者直接填入字符串

# 输入文件路径
INPUT_FILE = os.path.join(os.path.dirname(__file__), "new_coins_list.txt")

# 分析参数
UPPER_SHADOW_THRESHOLD = 1.5 # 上影线长度是实体长度的多少倍
VOLUME_INCREASE_THRESHOLD = 2.0 # 成交量是前一日的多少倍

def get_historical_data(coin_id, days=30):
    """获取指定币种的历史K线数据"""
    try:
        # 获取日线数据
        chart_data = cg.get_coin_market_chart_by_id(id=coin_id, vs_currency='usd', days=days)
        if not chart_data or 'prices' not in chart_data or len(chart_data['prices']) < 2:
            logger.warning(f"无法获取或数据不足: {coin_id}")
            return None

        prices = chart_data['prices']
        volumes = chart_data['total_volumes']

        df = pd.DataFrame(prices, columns=['timestamp', 'price'])
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

        # 获取开高低收数据需要ohlc接口
        ohlc_data = cg.get_coin_ohlc_by_id(id=coin_id, vs_currency='usd', days=days)
        if not ohlc_data or len(ohlc_data) < 2:
            logger.warning(f"无法获取OHLC数据: {coin_id}")
            # 尝试从价格数据估算，但不准确
            df['open'] = df['price'].shift(1)
            df['high'] = df['price'] # 简化处理
            df['low'] = df['price'] # 简化处理
            df['close'] = df['price']
        else:
            ohlc_df = pd.DataFrame(ohlc_data, columns=['timestamp', 'open', 'high', 'low', 'close'])
            ohlc_df['timestamp'] = pd.to_datetime(ohlc_df['timestamp'], unit='ms')
            df = pd.merge(df, ohlc_df, on='timestamp', how='left')

        # 合并成交量数据
        vol_df = pd.DataFrame(volumes, columns=['timestamp', 'volume'])
        vol_df['timestamp'] = pd.to_datetime(vol_df['timestamp'], unit='ms')
        df = pd.merge(df, vol_df, on='timestamp', how='left')

        df = df.dropna()
        df = df.sort_values('timestamp')
        return df

    except Exception as e:
        logger.error(f"获取 {coin_id} 数据时出错: {e}")
        time.sleep(5) # 出错时等待更长时间
        return None

def analyze_upper_shadow(df):
    """分析是否存在显著的上影线和放量"""
    if df is None or len(df) < 2:
        return False, "数据不足"

    latest = df.iloc[-1]
    previous = df.iloc[-2]

    # 计算实体和上影线
    body = abs(latest['close'] - latest['open'])
    upper_shadow = latest['high'] - max(latest['open'], latest['close'])

    # 检查上影线长度
    is_long_upper_shadow = False
    if body > 0.000001: # 避免除以零
        is_long_upper_shadow = upper_shadow / body >= UPPER_SHADOW_THRESHOLD
    elif upper_shadow > 0: # 如果实体为0但有上影线
        is_long_upper_shadow = True

    # 检查成交量
    is_volume_increase = False
    if previous['volume'] > 0.000001: # 避免除以零
        is_volume_increase = latest['volume'] / previous['volume'] >= VOLUME_INCREASE_THRESHOLD
    elif latest['volume'] > 0: # 如果前一天成交量为0
        is_volume_increase = True

    if is_long_upper_shadow and is_volume_increase:
        reason = f"上影线/实体比: {upper_shadow / body:.2f} >= {UPPER_SHADOW_THRESHOLD}, 成交量比: {latest['volume'] / previous['volume']:.2f} >= {VOLUME_INCREASE_THRESHOLD}"
        return True, reason
    else:
        reason = f"上影线/实体比: {upper_shadow / body:.2f}, 成交量比: {latest['volume'] / previous['volume']:.2f}"
        return False, reason

def send_serverchan_message(title, content):
    """通过Server酱发送消息"""
    if not SERVERCHAN_SEND_KEY:
        logger.warning("未配置Server酱SendKey，无法发送通知")
        return

    url = f"https://sctapi.ftqq.com/{SERVERCHAN_SEND_KEY}.send"
    data = {
        'title': title,
        'desp': content
    }
    try:
        response = requests.post(url, data=data)
        response.raise_for_status() # 检查请求是否成功
        result = response.json()
        if result.get('code') == 0:
            logger.info("Server酱消息发送成功")
        else:
            logger.error(f"Server酱消息发送失败: {result.get('message')}")
    except requests.exceptions.RequestException as e:
        logger.error(f"发送Server酱消息时网络错误: {e}")
    except json.JSONDecodeError:
        logger.error(f"无法解析Server酱响应: {response.text}")
    except Exception as e:
        logger.error(f"发送Server酱消息时发生未知错误: {e}")

def main():
    logger.info("开始从列表分析上影线...")
    
    if not os.path.exists(INPUT_FILE):
        logger.error(f"输入文件不存在: {INPUT_FILE}")
        return

    try:
        with open(INPUT_FILE, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        # 解析文件，格式：coin_id,listing_days
        coin_ids = [line.strip().split(',')[0] for line in lines if line.strip()]
    except Exception as e:
        logger.error(f"读取或解析输入文件失败: {e}")
        return

    if not coin_ids:
        logger.warning("输入文件中没有找到币种ID")
        return

    logger.info(f"从文件加载了 {len(coin_ids)} 个币种ID")

    suspicious_coins = []
    analysis_details = []

    for coin_id in coin_ids:
        logger.info(f"正在分析: {coin_id}")
        # 获取最近几天的数据用于分析，例如7天
        df = get_historical_data(coin_id, days=7)
        
        if df is not None:
            is_suspicious, reason = analyze_upper_shadow(df)
            analysis_details.append(f"- {coin_id}: {reason}")
            if is_suspicious:
                logger.warning(f"发现可疑币种: {coin_id} - {reason}")
                suspicious_coins.append(coin_id)
        else:
            analysis_details.append(f"- {coin_id}: 数据获取失败或不足")
            logger.warning(f"无法分析币种: {coin_id} (数据问题)")
        
        time.sleep(2) # 避免API频率限制

    # 生成报告内容
    report_title = f"上影线分析报告 - {start_time.strftime('%Y-%m-%d %H:%M')}"
    report_content = f"# {report_title}\n\n"
    report_content += f"分析了 {len(coin_ids)} 个币种，列表来源: `{os.path.basename(INPUT_FILE)}`\n\n"
    
    if suspicious_coins:
        report_content += f"## 发现 {len(suspicious_coins)} 个可疑币种:\n"
        for coin in suspicious_coins:
            report_content += f"- `{coin}`\n"
    else:
        report_content += "## 未发现明显放量上影线特征的币种。\n"
        
    report_content += "\n## 分析详情:\n"
    report_content += "\n".join(analysis_details)
    
    end_time = datetime.now()
    duration = end_time - start_time
    report_content += f"\n\n分析耗时: {duration}\n"

    logger.info("分析完成，准备发送报告...")
    # 发送报告
    send_serverchan_message(report_title, report_content)

if __name__ == "__main__":
    # 配置日志文件
    log_file = os.path.join(os.path.dirname(__file__), "analyze_from_list.log")
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    main()