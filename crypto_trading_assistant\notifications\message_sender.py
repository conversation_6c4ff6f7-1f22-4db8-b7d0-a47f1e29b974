"""
消息推送模块
支持企业微信和钉钉机器人推送
"""

import requests
import json
import time
import hashlib
import hmac
import base64
import urllib.parse
from typing import Dict, List, Optional
from loguru import logger
import matplotlib.pyplot as plt
import mplfinance as mpf
import io
import base64 as b64
from datetime import datetime

from ..analysis.signal_generator import TradingSignal


class WeChatBot:
    """企业微信机器人"""
    
    def __init__(self, webhook_url: str, secret: str = ""):
        """
        初始化企业微信机器人
        
        Args:
            webhook_url: 机器人webhook地址
            secret: 机器人密钥（可选）
        """
        self.webhook_url = webhook_url
        self.secret = secret
        
    def send_message(self, content: str, mentioned_list: List[str] = None, 
                    mentioned_mobile_list: List[str] = None) -> bool:
        """
        发送文本消息
        
        Args:
            content: 消息内容
            mentioned_list: @用户列表
            mentioned_mobile_list: @手机号列表
            
        Returns:
            发送是否成功
        """
        try:
            data = {
                "msgtype": "text",
                "text": {
                    "content": content,
                    "mentioned_list": mentioned_list or [],
                    "mentioned_mobile_list": mentioned_mobile_list or []
                }
            }
            
            response = requests.post(
                self.webhook_url,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            result = response.json()
            if result.get('errcode') == 0:
                logger.info("企业微信消息发送成功")
                return True
            else:
                logger.error(f"企业微信消息发送失败: {result}")
                return False
                
        except Exception as e:
            logger.error(f"发送企业微信消息时出错: {e}")
            return False
    
    def send_markdown(self, content: str) -> bool:
        """
        发送Markdown消息
        
        Args:
            content: Markdown格式的消息内容
            
        Returns:
            发送是否成功
        """
        try:
            data = {
                "msgtype": "markdown",
                "markdown": {
                    "content": content
                }
            }
            
            response = requests.post(
                self.webhook_url,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            result = response.json()
            return result.get('errcode') == 0
            
        except Exception as e:
            logger.error(f"发送企业微信Markdown消息时出错: {e}")
            return False


class DingTalkBot:
    """钉钉机器人"""
    
    def __init__(self, webhook_url: str, secret: str = ""):
        """
        初始化钉钉机器人
        
        Args:
            webhook_url: 机器人webhook地址
            secret: 机器人密钥
        """
        self.webhook_url = webhook_url
        self.secret = secret
    
    def _generate_sign(self, timestamp: str) -> str:
        """生成签名"""
        if not self.secret:
            return ""
        
        string_to_sign = f"{timestamp}\n{self.secret}"
        hmac_code = hmac.new(
            self.secret.encode('utf-8'),
            string_to_sign.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
        return sign
    
    def send_message(self, content: str, at_mobiles: List[str] = None, at_all: bool = False) -> bool:
        """
        发送文本消息
        
        Args:
            content: 消息内容
            at_mobiles: @手机号列表
            at_all: 是否@所有人
            
        Returns:
            发送是否成功
        """
        try:
            timestamp = str(round(time.time() * 1000))
            sign = self._generate_sign(timestamp)
            
            url = self.webhook_url
            if sign:
                url += f"&timestamp={timestamp}&sign={sign}"
            
            data = {
                "msgtype": "text",
                "text": {
                    "content": content
                },
                "at": {
                    "atMobiles": at_mobiles or [],
                    "isAtAll": at_all
                }
            }
            
            response = requests.post(
                url,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            result = response.json()
            if result.get('errcode') == 0:
                logger.info("钉钉消息发送成功")
                return True
            else:
                logger.error(f"钉钉消息发送失败: {result}")
                return False
                
        except Exception as e:
            logger.error(f"发送钉钉消息时出错: {e}")
            return False
    
    def send_markdown(self, title: str, content: str, at_mobiles: List[str] = None, at_all: bool = False) -> bool:
        """
        发送Markdown消息
        
        Args:
            title: 消息标题
            content: Markdown格式的消息内容
            at_mobiles: @手机号列表
            at_all: 是否@所有人
            
        Returns:
            发送是否成功
        """
        try:
            timestamp = str(round(time.time() * 1000))
            sign = self._generate_sign(timestamp)
            
            url = self.webhook_url
            if sign:
                url += f"&timestamp={timestamp}&sign={sign}"
            
            data = {
                "msgtype": "markdown",
                "markdown": {
                    "title": title,
                    "text": content
                },
                "at": {
                    "atMobiles": at_mobiles or [],
                    "isAtAll": at_all
                }
            }
            
            response = requests.post(
                url,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            result = response.json()
            return result.get('errcode') == 0
            
        except Exception as e:
            logger.error(f"发送钉钉Markdown消息时出错: {e}")
            return False


class ChartGenerator:
    """图表生成器"""
    
    @staticmethod
    def generate_candlestick_chart(df, symbol: str, timeframe: str, signal: TradingSignal = None) -> Optional[str]:
        """
        生成K线图
        
        Args:
            df: OHLCV数据
            symbol: 交易对符号
            timeframe: 时间周期
            signal: 交易信号（可选）
            
        Returns:
            图表的base64编码字符串
        """
        try:
            # 准备数据
            df_chart = df.copy()
            df_chart.index = df_chart.index.tz_localize(None) if df_chart.index.tz else df_chart.index
            
            # 设置图表样式
            mc = mpf.make_marketcolors(
                up='g', down='r',
                edge='inherit',
                wick={'up': 'green', 'down': 'red'},
                volume='in'
            )
            
            s = mpf.make_mpf_style(
                marketcolors=mc,
                gridstyle='-',
                y_on_right=True
            )
            
            # 添加信号标记
            apds = []
            if signal and signal.pattern_info:
                # 添加形态关键点
                key_points = signal.pattern_info.key_points
                if key_points:
                    x_coords = [point[0] for point in key_points]
                    y_coords = [point[1] for point in key_points]
                    
                    apds.append(
                        mpf.make_addplot(
                            [y_coords[i] if i in x_coords else float('nan') for i in range(len(df_chart))],
                            type='scatter',
                            markersize=100,
                            marker='o',
                            color='blue'
                        )
                    )
            
            # 生成图表
            fig, axes = mpf.plot(
                df_chart.tail(100),  # 显示最近100根K线
                type='candle',
                style=s,
                title=f'{symbol} {timeframe}',
                ylabel='Price',
                volume=True,
                addplot=apds if apds else None,
                returnfig=True,
                figsize=(12, 8)
            )
            
            # 转换为base64
            buffer = io.BytesIO()
            fig.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
            buffer.seek(0)
            
            chart_base64 = b64.b64encode(buffer.getvalue()).decode()
            plt.close(fig)
            
            return chart_base64
            
        except Exception as e:
            logger.error(f"生成图表时出错: {e}")
            return None


class MessageFormatter:
    """消息格式化器"""
    
    @staticmethod
    def format_signal_message(signal: TradingSignal, include_chart: bool = False, chart_data: str = None) -> str:
        """
        格式化交易信号消息
        
        Args:
            signal: 交易信号
            include_chart: 是否包含图表
            chart_data: 图表数据
            
        Returns:
            格式化后的消息
        """
        try:
            # 基本信息
            message = f"""
🚀 **交易信号提醒** 🚀

**交易对**: {signal.symbol}
**信号类型**: {signal.signal_type.value.upper()}
**信号强度**: {signal.strength.value}
**置信度**: {signal.confidence:.2%}
**时间周期**: {signal.timeframe}

💰 **价格信息**:
- 当前价格: ${signal.entry_price:.6f}
- 目标价格: ${signal.target_price:.6f}
- 止损价格: ${signal.stop_loss:.6f}
- 风险回报比: {signal.risk_reward_ratio:.2f}:1

📊 **技术分析**:
"""
            
            # 添加信号原因
            if signal.reasons:
                message += f"- 信号原因: {', '.join(signal.reasons)}\n"
            
            # 添加技术指标
            if signal.technical_indicators:
                message += "- 技术指标:\n"
                for indicator, value in signal.technical_indicators.items():
                    message += f"  • {indicator}: {value:.4f}\n"
            
            # 添加形态信息
            if signal.pattern_info:
                pattern = signal.pattern_info
                message += f"- 识别形态: {pattern.pattern_type.value}\n"
                message += f"- 形态置信度: {pattern.confidence:.2%}\n"
                if pattern.support_level:
                    message += f"- 支撑位: ${pattern.support_level:.6f}\n"
                if pattern.resistance_level:
                    message += f"- 阻力位: ${pattern.resistance_level:.6f}\n"
            
            message += f"\n⏰ **时间**: {signal.timestamp.strftime('%Y-%m-%d %H:%M:%S')}"
            
            # 添加风险提示
            message += "\n\n⚠️ **风险提示**: 投资有风险，请谨慎决策，合理控制仓位！"
            
            return message
            
        except Exception as e:
            logger.error(f"格式化信号消息时出错: {e}")
            return f"交易信号: {signal.symbol} {signal.signal_type.value}"
