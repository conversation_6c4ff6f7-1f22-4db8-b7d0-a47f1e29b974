#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试修复后的数据源
"""

import sys
import os
from datetime import datetime

def log(message):
    """日志函数"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def main():
    """主函数"""
    log("🔧 测试修复后的数据源...")
    
    try:
        from advanced_crypto_scraper import AdvancedCryptoScraper
        
        # 创建实例（不使用代理，测试火币等国内数据源）
        log("📋 创建实例（直连模式）...")
        scraper = AdvancedCryptoScraper(use_proxy=False)
        
        # 检查数据源配置
        data_sources = scraper.data_sources
        sorted_sources = sorted(data_sources.items(), key=lambda x: x[1].get('priority', 999))
        
        log("📊 数据源优先级:")
        for name, config in sorted_sources:
            priority = config.get('priority', 999)
            requires_proxy = config.get('requires_proxy', False)
            source_type = "主数据源" if priority == 1 else f"备用{priority-1}"
            proxy_status = "需要代理" if requires_proxy else "直连可用"
            log(f"   {priority}. {config['name']} - {source_type} ({proxy_status})")
        
        # 测试主数据源（火币）
        log("\n🔧 测试主数据源（火币）...")
        try:
            huobi_data = scraper.get_cryptocurrencies_from_huobi()
            if huobi_data:
                log(f"✅ 火币数据获取成功: {len(huobi_data)} 个币种")
                log("📈 前5个币种:")
                for i, crypto in enumerate(huobi_data[:5], 1):
                    symbol = crypto.get('symbol', 'N/A')
                    price = crypto.get('current_price', 0)
                    volume = crypto.get('total_volume', 0)
                    log(f"   {i}. {symbol}: ${price:.6f} 交易量:{volume:.0f}")
            else:
                log("❌ 火币数据获取失败")
        except Exception as e:
            log(f"❌ 火币测试异常: {e}")
        
        # 测试增强版数据获取
        log("\n🚀 测试增强版数据获取...")
        try:
            test_data = scraper.get_all_cryptocurrencies_enhanced(max_coins=20)
            if test_data:
                log(f"✅ 增强版数据获取成功: {len(test_data)} 个币种")
                
                # 检查数据源
                data_sources_used = set(crypto.get('data_source', 'unknown') for crypto in test_data)
                log(f"📊 使用的数据源: {', '.join(data_sources_used)}")
                
                # 显示前10个币种
                log("📈 前10个币种:")
                for i, crypto in enumerate(test_data[:10], 1):
                    symbol = crypto.get('symbol', 'N/A')
                    price = crypto.get('current_price', 0)
                    source = crypto.get('data_source', 'N/A')
                    log(f"   {i:2d}. {symbol:8s} ${price:>12.6f} [{source}]")
            else:
                log("❌ 增强版数据获取失败")
        except Exception as e:
            log(f"❌ 增强版数据获取异常: {e}")
        
        log("\n" + "="*50)
        log("🎉 数据源修复测试完成！")
        log("💡 主数据源已改为火币（国内可直接访问）")
        log("🔄 备用数据源包括Gate.io、CoinGecko、Binance等")
        log("="*50)
        
    except Exception as e:
        log(f"❌ 测试失败: {e}")
        import traceback
        log(f"错误详情: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
