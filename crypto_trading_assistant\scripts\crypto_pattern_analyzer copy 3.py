# -*- coding: utf-8 -*-
"""
加密货币形态分析模块
专门用于分析新上市币种的放量上影线形态和反转特征
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import sys
import os
import time
from tqdm import tqdm
import requests
import ccxt
import argparse
import json
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich import box
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import asyncio
import aiohttp
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from tenacity import retry, stop_after_attempt, wait_exponential
from io import StringIO

# 设置系统默认编码
import sys
sys.stdout.reconfigure(encoding='utf-8')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('crypto_pattern_analyzer.log', mode='w', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

# 添加控制台处理器
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
logger.addHandler(console_handler)

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from scripts.crypto_technical_analyzer import TechnicalAnalyzer
from scripts.crypto_exchange_data import ExchangeDataCollector

@dataclass
class ExchangeConfig:
    """交易所配置"""
    name: str
    api_base_url: str
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    use_proxy: bool = False
    request_delay: float = 1.0

@dataclass
class PatternParameters:
    """形态识别参数"""
    volume_ma_period: int = 10  # 成交量均线周期
    volume_threshold: float = 1.2  # 放量倍数阈值
    shadow_ratio_threshold: float = 1.0  # 上影线比例阈值
    body_ratio_threshold: float = 0.1  # 实体比例阈值
    reversal_days: int = 10  # 反转观察天数
    reversal_threshold: float = 0.05  # 反转幅度阈值
    listing_days: int = 60  # 新上市天数阈值

class PatternAnalyzer:
    def __init__(self, 
                 api_keys: Optional[Dict] = None,
                 use_proxy: bool = False,
                 request_delay: float = 1.0):
        """
        初始化模式分析器
        
        Args:
            api_keys: API密钥字典
            use_proxy: 是否使用代理
            request_delay: 请求延迟（秒）
        """
        self.logger = logging.getLogger(__name__)
        self.use_proxy = use_proxy
        self.request_delay = request_delay
        
        # 初始化交易所API
        self.exchange = ccxt.gateio({
            'enableRateLimit': True,
            'timeout': 30000,
            'apiKey': api_keys['apiKey'] if api_keys else None,
            'secret': api_keys['secret'] if api_keys else None,
            'options': {
                'defaultType': 'spot',
                'createMarketBuyOrderRequiresPrice': False,
                'fetchTradesMethod': 'publicGetTradeHistory',
                'recvWindow': 5000,
            }
        })
        
        # 初始化代理设置
        if use_proxy:
            self.exchange.proxies = {
                'http': 'http://127.0.0.1:7890',
                'https': 'http://127.0.0.1:7890'
            }
        
        # 初始化技术分析器
        self.technical_analyzer = TechnicalAnalyzer()
        
        # 设置重试次数和延迟
        self.max_retries = 3
        self.retry_delay = 5  # 秒

        # 初始化rich控制台
        self.console = Console(force_terminal=True, color_system="auto")
        
        # 创建结果保存目录
        self.results_dir = Path("analysis_results")
        self.results_dir.mkdir(exist_ok=True)
        
        # 初始化缓存
        self.ohlcv_cache = {}
        self.analysis_cache = {}
        self.cache_ttl = 300  # 缓存有效期5分钟
        
        # 加载SERVER酱配置
        try:
            with open('scripts/config.json', 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {str(e)}")
            self.config = {"wechat": {"send_keys": []}}

    def get_cached_ohlcv(self, symbol: str) -> Optional[List]:
        """获取缓存的K线数据"""
        if symbol in self.ohlcv_cache:
            cache_time, data = self.ohlcv_cache[symbol]
            if time.time() - cache_time < self.cache_ttl:
                return data
        return None

    def set_cached_ohlcv(self, symbol: str, data: List):
        """设置K线数据缓存"""
        self.ohlcv_cache[symbol] = (time.time(), data)

    def fetch_ohlcv(self, symbol: str) -> Optional[pd.DataFrame]:
        """获取K线数据"""
        try:
            # 使用ccxt获取K线数据
            ohlcv = self.exchange.fetch_ohlcv(symbol, '1d', limit=30)
            if ohlcv:
                # 转换为DataFrame
                df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                return df
            else:
                self.logger.error(f"获取{symbol}的K线数据失败: 未获取到数据")
                return None
        except Exception as e:
            self.logger.error(f"获取{symbol}的K线数据失败: {str(e)}")
            return None

    def process_trading_pairs(self, trading_pairs: List[str]) -> Dict:
        """处理交易对列表"""
        results = {}
        
        # 创建进度条
        with Progress() as progress:
            task = progress.add_task("[cyan]获取K线数据...", total=len(trading_pairs))
            
            # 处理每个交易对
            for symbol in trading_pairs:
                result = self.process_single_pair(symbol, {'symbol': symbol})
                if result:
                    results[symbol] = result
                progress.update(task, advance=1)
        
        return results

    def process_single_pair(self, symbol: str, pair_info: Dict) -> Optional[Dict]:
        """处理单个交易对"""
        try:
            # 获取K线数据
            df = self.fetch_ohlcv(symbol)
            if df is None or len(df) < 5:
                return None
            
            # 计算技术指标
            df = self.preprocess_ohlcv(df)
            
            # 分析形态
            analysis = self.analyze_pattern(df)
            
            # 添加基本信息
            result = {
                'symbol': symbol,
                'analysis': analysis
            }
            
            return result
        except Exception as e:
            self.logger.error(f"处理{symbol}时出错: {str(e)}")
            return None

    def preprocess_ohlcv(self, df: pd.DataFrame) -> pd.DataFrame:
        """预处理K线数据，计算常用指标"""
        # 确保数据类型正确
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        df[numeric_columns] = df[numeric_columns].astype(float)
        
        # 计算技术指标
        df['MA5'] = df['close'].rolling(window=5, min_periods=1).mean()
        df['MA10'] = df['close'].rolling(window=10, min_periods=1).mean()
        df['volume_ma'] = df['volume'].rolling(window=10, min_periods=1).mean()
        
        # 计算涨跌幅
        df['price_change'] = df['close'].pct_change()
        df['volume_change'] = df['volume'].pct_change()
        
        # 计算波动率
        df['volatility'] = df['close'].rolling(window=10, min_periods=1).std()
        
        # 计算上下影线比例
        df['upper_shadow'] = (df['high'] - df[['open', 'close']].max(axis=1)) / df['close']
        df['lower_shadow'] = (df[['open', 'close']].min(axis=1) - df['low']) / df['close']
        df['body_size'] = abs(df['close'] - df['open']) / df['close']
        
        return df

    def get_new_listings(self, max_listings: int = 0) -> Dict:
        """
        获取从60天前到今天的新上市币种信息
        
        Args:
            max_listings: 最大获取数量，大于0时获取到指定数量后立即返回，否则获取全部符合条件的币种
        
        Returns:
            新上市币种信息字典
        """
        try:
            self.logger.info("正在获取所有交易对信息...")
            # 获取所有交易对
            markets = self.exchange.load_markets()
            if not markets:
                self.logger.error("未能获取到交易对信息")
                return {}
            
            self.logger.info(f"成功获取{len(markets)}个交易对")
            
            # 获取新币上线时间
            new_listings = {}
            current_date = datetime.now()
            start_date = current_date - timedelta(days=60)  # 60天前的日期
            end_date = current_date
            
            self.logger.info(f"开始日期: {start_date.strftime('%Y-%m-%d')}")
            self.logger.info(f"结束日期: {end_date.strftime('%Y-%m-%d')}")
            
            # 只处理USDT交易对，并转换格式
            usdt_markets = {k: v for k, v in markets.items() if k.endswith('/USDT')}
            self.logger.info(f"找到{len(usdt_markets)}个USDT交易对")
            
            # 批量获取K线数据
            symbols = list(usdt_markets.keys())
            
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TaskProgressColumn(),
                console=self.console
            ) as progress:
                task = progress.add_task("获取K线数据...", total=len(symbols))
                
                for i in range(0, len(symbols), 10):  # 每10个交易对一批
                    batch = symbols[i:i+10]
                    batch_data = self.process_trading_pairs(batch)
                    
                    # 处理每个交易对的数据
                    for symbol, data in batch_data.items():
                        if data and 'analysis' in data:
                            market = usdt_markets[symbol]
                            listing_date = self._get_listing_date(market)
                            
                            if start_date <= listing_date <= end_date:
                                new_listings[symbol] = {
                                    'listing_date': listing_date.strftime('%Y-%m-%d'),
                                    'analysis': data['analysis']
                                }
                                
                                if max_listings > 0 and len(new_listings) >= max_listings:
                                    return new_listings
                    
                    progress.update(task, advance=len(batch))
                    time.sleep(1)  # 每批请求后等待1秒
            
            return new_listings
            
        except Exception as e:
            self.logger.error(f"获取新上市币种时发生错误: {str(e)}")
            import traceback
            self.logger.error(f"错误详情:\n{traceback.format_exc()}")
            return {}

    def _get_listing_date(self, market, current_date=None):
        """
        获取币种的上市日期
        
        Args:
            market: 交易对信息
            current_date: 当前日期，默认为None
            
        Returns:
            上市日期
        """
        if current_date is None:
            current_date = datetime.now()
            
        try:
            # 使用ccxt获取K线数据
            ohlcv = self.exchange.fetch_ohlcv(market['symbol'], '1d', limit=1)
            if ohlcv:
                return datetime.fromtimestamp(ohlcv[0][0] / 1000)
        except Exception as e:
            self.logger.error(f"获取{market['symbol']}的上市日期失败: {str(e)}")
        return current_date

    def backtest(self, symbol: str, start_date: str, end_date: str) -> Dict:
        """
        回测指定币种的交易策略
        
        Args:
            symbol: 交易对符号
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            回测结果字典
        """
        try:
            # 获取历史数据
            retries = 0
            while retries < self.max_retries:
                try:
                    # 获取K线数据
                    ohlcv = self.exchange.fetch_ohlcv(symbol, '1d', limit=200)
                    if not ohlcv:
                        raise Exception("未获取到K线数据")
                        
                    # 转换为DataFrame
                    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    
                    # 计算技术指标
                    df = self.technical_analyzer.calculate_indicators(df)
                    
                    # 识别上影线模式
                    signals = self.technical_analyzer.identify_upper_shadow(df)
                    
                    # 计算回测结果
                    results = self._calculate_backtest_results(df, signals)
                    
                    return results
                    
                except Exception as e:
                    retries += 1
                    self.logger.error(f"回测失败 (尝试 {retries}/{self.max_retries}): {str(e)}")
                    if retries < self.max_retries:
                        time.sleep(self.retry_delay)
                    else:
                        raise Exception("回测失败，已达到最大重试次数")
                        
        except Exception as e:
            self.logger.error(f"回测{symbol}失败: {str(e)}")
            raise

    def test_exchange_connection(self):
        """测试交易所API连接"""
        try:
            self.logger.info("正在测试交易所API连接...")
            
            # 测试获取交易对
            self.logger.info("1. 测试获取交易对...")
            markets = self.exchange.load_markets()
            self.logger.info(f"成功获取交易对，总数: {len(markets)}")
            
            # 测试获取K线数据
            self.logger.info("\n2. 测试获取K线数据...")
            symbol = "BTC/USDT"
            self.logger.info(f"使用测试交易对: {symbol}")
            ohlcv = self.exchange.fetch_ohlcv(symbol, '1d', limit=1)
            self.logger.info(f"成功获取K线数据: {ohlcv}")
            
            self.logger.info("\n交易所API连接测试成功!")
            return True
            
        except Exception as e:
            self.logger.error(f"交易所API连接测试失败: {str(e)}")
            self.logger.error(f"错误详情: {type(e).__name__}: {str(e)}")
            return False

    def save_analysis_results(self, results: Dict, symbol: str):
        """保存分析结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = self.results_dir / f"{symbol}_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            self.logger.info(f"分析结果已保存到: {filename}")
        except Exception as e:
            self.logger.error(f"保存分析结果失败: {str(e)}")

    def send_wechat_notification(self, title: str, content: str):
        """发送微信通知"""
        if not self.config.get("wechat", {}).get("send_keys"):
            self.logger.warning("未配置SERVER酱API密钥")
            return
            
        for send_key in self.config["wechat"]["send_keys"]:
            try:
                url = f"https://sctapi.ftqq.com/{send_key}.send"
                data = {
                    "title": title,
                    "desp": content
                }
                response = requests.post(url, data=data)
                if response.status_code == 200:
                    self.logger.info("微信通知发送成功")
                else:
                    self.logger.error(f"微信通知发送失败: {response.text}")
            except Exception as e:
                self.logger.error(f"发送微信通知时出错: {str(e)}")

    def format_analysis_report(self, symbol: str, analysis: Dict) -> str:
        """格式化分析报告"""
        # 创建rich表格
        table = Table(title=f"\n{symbol} 技术分析报告", box=box.ROUNDED)
        
        # 添加列
        table.add_column("指标", style="cyan")
        table.add_column("数值", style="green")
        
        # 添加基本信息
        table.add_row("当前价格", f"{analysis['当前价格']:.4f} USDT")
        table.add_row("24h涨跌幅", f"{analysis['24h涨跌幅']:.2f}%")
        table.add_row("24h成交量变化", f"{analysis['24h成交量变化']:.2f}%")
        
        # 添加趋势信息
        table.add_row("MA5趋势", analysis['MA5趋势'])
        table.add_row("MA10趋势", analysis['MA10趋势'])
        table.add_row("成交量趋势", analysis['成交量趋势'])
        
        # 添加技术指标
        table.add_row("波动率", f"{analysis['波动率']:.2f}%")
        table.add_row("上影线比例", f"{analysis['上影线比例']:.2f}%")
        table.add_row("下影线比例", f"{analysis['下影线比例']:.2f}%")
        table.add_row("实体大小", f"{analysis['实体大小']:.2f}%")
        
        # 添加形态和风险信息
        table.add_row("K线形态", analysis['K线形态'])
        table.add_row("风险等级", analysis['风险等级'])
        table.add_row("交易建议", analysis['交易建议'])
        
        # 使用rich渲染表格
        console = Console()
        report = console.render(table)
        
        # 添加时间戳
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        report = f"分析时间: {timestamp}\n{report}"
        
        return report

    def analyze_pattern(self, df: pd.DataFrame) -> Dict:
        """
        分析K线形态
        :param df: 包含OHLCV数据的DataFrame
        :return: 分析结果
        """
        try:
            # 计算基本指标
            current_price = float(df['close'].iloc[-1])
            prev_price = float(df['close'].iloc[-2]) if len(df) > 1 else current_price
            current_volume = float(df['volume'].iloc[-1])
            prev_volume = float(df['volume'].iloc[-2]) if len(df) > 1 else current_volume
            
            # 计算价格和成交量变化
            price_change = ((current_price - prev_price) / prev_price * 100) if prev_price != 0 else 0
            volume_change = ((current_volume - prev_volume) / prev_volume * 100) if prev_volume != 0 else 0
            
            # 计算移动平均线
            if len(df) >= 5:
                df['MA5'] = df['close'].rolling(window=5, min_periods=1).mean()
                ma5_trend = self._determine_trend(df['MA5'].iloc[-2:])
            else:
                ma5_trend = "unknown"
                
            if len(df) >= 10:
                df['MA10'] = df['close'].rolling(window=10, min_periods=1).mean()
                ma10_trend = self._determine_trend(df['MA10'].iloc[-2:])
            else:
                ma10_trend = "unknown"
                
            # 计算成交量趋势
            volume_trend = self._determine_trend(df['volume'].iloc[-2:]) if len(df) > 1 else "unknown"
            
            # 计算波动率
            latest_candle = df.iloc[-1]
            volatility = (latest_candle['high'] - latest_candle['low']) / latest_candle['low'] * 100
            
            # 计算影线比例和实体大小
            body_size = abs(latest_candle['close'] - latest_candle['open'])
            upper_shadow = latest_candle['high'] - max(latest_candle['open'], latest_candle['close'])
            lower_shadow = min(latest_candle['open'], latest_candle['close']) - latest_candle['low']
            
            upper_shadow_ratio = (upper_shadow / latest_candle['close'] * 100) if latest_candle['close'] != 0 else 0
            lower_shadow_ratio = (lower_shadow / latest_candle['close'] * 100) if latest_candle['close'] != 0 else 0
            body_size_ratio = (body_size / latest_candle['close'] * 100) if latest_candle['close'] != 0 else 0
            
            # 识别K线形态
            pattern = self._identify_candlestick_pattern(latest_candle)
            
            # 格式化分析结果
            analysis_result = {
                "current_price": current_price,
                "price_change_24h": price_change,
                "volume_change_24h": volume_change,
                "ma5_trend": ma5_trend,
                "ma10_trend": ma10_trend,
                "volume_trend": volume_trend,
                "volatility": volatility,
                "upper_shadow_ratio": upper_shadow_ratio,
                "lower_shadow_ratio": lower_shadow_ratio,
                "body_size": body_size_ratio,
                "pattern": pattern,
                "risk_level": self._evaluate_risk(volatility, volume_change, price_change),
                "trading_suggestion": self._generate_trade_signal(pattern, price_change, volume_change, volatility)
            }
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"分析K线形态时发生错误: {str(e)}")
            return self._get_default_analysis()

    def _determine_trend(self, data):
        """
        判断趋势
        """
        if len(data) < 2:
            return "unknown"
        change = (data.iloc[-1] - data.iloc[0]) / data.iloc[0] * 100
        if change > 1:
            return "uptrend"
        elif change < -1:
            return "downtrend"
        else:
            return "sideways"
            
    def _identify_candlestick_pattern(self, candle):
        """
        识别K线形态
        """
        open_price = candle['open']
        close = candle['close']
        high = candle['high']
        low = candle['low']
        
        body = abs(close - open_price)
        upper_shadow = high - max(open_price, close)
        lower_shadow = min(open_price, close) - low
        
        # 判断是否为十字星
        if body / (high - low) < 0.1:
            return "doji"
            
        # 判断是否为锤子线
        if lower_shadow > 2 * body and upper_shadow < body:
            return "hammer"
            
        # 判断是否为上吊线
        if upper_shadow > 2 * body and lower_shadow < body:
            return "hanging_man"
            
        # 判断是否为看涨实体
        if close > open_price and body > (high - low) * 0.6:
            return "bullish_body"
            
        # 判断是否为看跌实体
        if close < open_price and body > (high - low) * 0.6:
            return "bearish_body"
            
        return "no_clear_pattern"
        
    def _evaluate_risk(self, volatility, volume_change, price_change):
        """
        评估风险等级
        """
        risk_score = 0
        
        # 根据波动率评估风险
        if volatility > 20:
            risk_score += 3
        elif volatility > 10:
            risk_score += 2
        elif volatility > 5:
            risk_score += 1
            
        # 根据成交量变化评估风险
        if abs(volume_change) > 100:
            risk_score += 2
        elif abs(volume_change) > 50:
            risk_score += 1
            
        # 根据价格变化评估风险
        if abs(price_change) > 10:
            risk_score += 2
        elif abs(price_change) > 5:
            risk_score += 1
            
        # 返回风险等级
        if risk_score >= 6:
            return "high_risk"
        elif risk_score >= 3:
            return "medium_risk"
        else:
            return "low_risk"
            
    def _generate_trade_signal(self, pattern, price_change, volume_change, volatility):
        """
        生成交易信号
        """
        if pattern in ["hammer", "bullish_body"] and price_change > 0 and volume_change > 0:
            return "consider_buy"
        elif pattern in ["hanging_man", "bearish_body"] or (price_change < -5 and volume_change > 50):
            return "wait_and_see"
        elif volatility > 15:
            return "high_volatility_caution"
        else:
            return "need_more_observation"
            
    def _get_default_analysis(self):
        """
        返回默认的分析结果
        """
        return {
            "current_price": 0,
            "price_change_24h": 0,
            "volume_change_24h": 0,
            "ma5_trend": "unknown",
            "ma10_trend": "unknown",
            "volume_trend": "unknown",
            "volatility": 0,
            "upper_shadow_ratio": 0,
            "lower_shadow_ratio": 0,
            "body_size": 0,
            "pattern": "无明显形态",
            "risk_level": "未知",
            "trading_suggestion": "数据不足"
        }

    def _translate_to_chinese(self, text: str) -> str:
        """将分析结果转换为中文"""
        translations = {
            # 趋势
            "uptrend": "上涨",
            "downtrend": "下跌",
            "sideways": "横盘",
            "unknown": "未知",
            
            # K线形态
            "doji": "十字星",
            "hammer": "锤子线",
            "hanging_man": "上吊线",
            "bullish_body": "看涨实体",
            "bearish_body": "看跌实体",
            "no_clear_pattern": "无明显形态",
            
            # 风险等级
            "high_risk": "高风险",
            "medium_risk": "中等风险",
            "low_risk": "低风险",
            
            # 交易建议
            "consider_buy": "可以考虑买入",
            "wait_and_see": "建议观望",
            "high_volatility_caution": "波动较大，需谨慎",
            "need_more_observation": "需要更多观察"
        }
        return translations.get(text, text)

    def format_summary_report(self, new_listings: Dict) -> str:
        """格式化汇总报告"""
        # 添加时间戳和统计信息
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        total_pairs = len(new_listings)
        
        header = f"""# 加密货币新上市币种分析报告
分析时间: {timestamp}
分析币种数量: {total_pairs}

交易对         上市日期    当前价格    24h涨跌幅   K线形态     风险等级   交易建议
"""
        
        # 格式化每一行数据
        rows = []
        for symbol, info in new_listings.items():
            analysis = info['analysis']
            price_change = analysis['price_change_24h']
            price_text = f"{price_change:+.2f}%" if price_change != 0 else "0.00%"
            
            row = f"{symbol:<12} {info['listing_date']} {analysis['current_price']:<10.6f} {price_text:<10} {self._translate_to_chinese(analysis['pattern']):<10} {self._translate_to_chinese(analysis['risk_level']):<8} {self._translate_to_chinese(analysis['trading_suggestion'])}"
            rows.append(row)
        
        # 添加分隔线
        separator = "-" * 100
        
        # 组合报告
        report = header + separator + "\n" + "\n".join(rows)
        
        footer = """

## 风险提示
1. 新上市币种波动较大，请谨慎投资
2. 建议设置止损位，控制风险
3. 关注项目基本面和市场情绪变化
4. 杠杆代币风险较高，不建议长期持有

## 免责声明
本分析报告仅供参考，不构成投资建议。投资有风险，入市需谨慎。"""
        
        return report + footer

    def save_csv_report(self, new_listings: Dict, filename: str):
        """保存CSV格式的分析报告"""
        try:
            # 准备数据
            rows = []
            for symbol, info in new_listings.items():
                analysis = info['analysis']
                row = {
                    '交易对': symbol,
                    '上市日期': info['listing_date'],
                    '当前价格': analysis['current_price'],
                    '24h涨跌幅': f"{analysis['price_change_24h']:.2f}%",
                    '成交量变化': f"{analysis['volume_change_24h']:.2f}%",
                    'MA5趋势': self._translate_to_chinese(analysis['ma5_trend']),
                    'MA10趋势': self._translate_to_chinese(analysis['ma10_trend']),
                    '成交量趋势': self._translate_to_chinese(analysis['volume_trend']),
                    '波动率': f"{analysis['volatility']:.2f}%",
                    'K线形态': self._translate_to_chinese(analysis['pattern']),
                    '风险等级': self._translate_to_chinese(analysis['risk_level']),
                    '交易建议': self._translate_to_chinese(analysis['trading_suggestion'])
                }
                rows.append(row)
            
            # 创建DataFrame并保存为CSV
            df = pd.DataFrame(rows)
            df.to_csv(filename, index=False, encoding='utf-8-sig')  # 使用utf-8-sig编码以支持Excel中的中文
            self.logger.info(f"CSV报告已保存到: {filename}")
            
        except Exception as e:
            self.logger.error(f"保存CSV报告时出错: {str(e)}")

def test_real_data(max_listings: int = 0):
    """使用真实数据进行测试"""
    try:
        # 初始化分析器
        analyzer = PatternAnalyzer(
            api_keys=None,  # 暂时不使用API密钥
            use_proxy=False,  # 暂时不使用代理
            request_delay=1.0  # 请求延迟1秒
        )
        
        try:
            new_listings = analyzer.get_new_listings(max_listings=max_listings)
            
            if not new_listings:
                logger.warning("未找到新上市币种")
                return
            
            # 生成汇总报告
            summary_report = analyzer.format_summary_report(new_listings)
            
            # 打印汇总报告
            console = Console(force_terminal=True, color_system="auto")
            console.print(summary_report)
            
            # 保存汇总结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            summary_file = Path("analysis_results") / f"summary_{timestamp}.json"
            csv_file = Path("analysis_results") / f"summary_{timestamp}.csv"
            
            # 保存JSON格式
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(new_listings, f, ensure_ascii=False, indent=2)
            
            # 保存CSV格式
            analyzer.save_csv_report(new_listings, csv_file)
            
            # 暂时注释掉微信通知
            # analyzer.send_wechat_notification(
            #     title=f"加密货币新上市币种分析报告 - {timestamp}",
            #     content=summary_report
            # )
            
            logger.info(f"\n分析完成，结果已保存到:")
            logger.info(f"JSON文件: {summary_file}")
            logger.info(f"CSV文件: {csv_file}")
            
        except Exception as e:
            logger.error(f"获取新上市币种时发生错误: {str(e)}")
            import traceback
            logger.error(f"错误详情:\n{traceback.format_exc()}")
            
    except Exception as e:
        logger.error(f"程序运行出错: {str(e)}")
        logger.error(f"错误详情: {type(e).__name__}: {str(e)}")
        import traceback
        logger.error(f"堆栈跟踪:\n{traceback.format_exc()}")

def print_results(results: Dict):
    """打印回测结果"""
    if results:
        print(f"总信号数: {results['total_signals']}")
        print(f"成功信号数: {results['successful_signals']}")
        print(f"胜率: {results['win_rate']:.2%}")
        print(f"平均收益: {results['avg_gain']:.2%}")
        print(f"平均亏损: {results['avg_loss']:.2%}")
        print(f"盈亏比: {results['profit_factor']:.2f}")
        print(f"平均风险收益比: {results['avg_risk_reward']:.2f}")
        
        # 显示最近的信号
        if results['signals']:
            print("\n最近的信号:")
            for signal in results['signals'][-3:]:  # 显示最后3个信号
                print(f"\n日期: {signal['date']}")
                print(f"价格: {signal['price']:.2f}")
                print(f"成交量比例: {signal['volume_ratio']:.2f}")
                print(f"上影线比例: {signal['shadow_ratio']:.2f}")
                print(f"信号强度: {signal['signal_strength']:.2f}")
                print(f"趋势: {signal['trend']:.2%}")
                print(f"波动率: {signal['volatility']:.2%}")
                if 'reversal_occurred' in signal:
                    print(f"是否发生反转: {'是' if signal['reversal_occurred'] else '否'}")
                    print(f"最大收益: {signal['max_gain']:.2%}")
                    print(f"最大亏损: {signal['max_loss']:.2%}")
                    print(f"风险收益比: {signal['risk_reward_ratio']:.2f}")
    else:
        print("回测失败")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='加密货币形态分析')
    parser.add_argument('--max-listings', type=int, default=0, help='最大获取新上市币种数量，0表示获取全部')
    args = parser.parse_args()
    test_real_data(max_listings=args.max_listings)