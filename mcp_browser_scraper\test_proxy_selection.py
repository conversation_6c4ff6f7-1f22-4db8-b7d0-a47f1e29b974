#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试代理选择功能
"""

import sys
import os
from datetime import datetime

def log(message):
    """日志函数"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def test_direct_mode():
    """测试直连模式"""
    log("🔧 测试直连模式...")
    
    try:
        from advanced_crypto_scraper import AdvancedCryptoScraper
        
        # 创建直连模式实例
        scraper = AdvancedCryptoScraper(use_proxy=False)
        
        log(f"✅ 直连模式初始化成功")
        log(f"   代理状态: {'启用' if scraper.use_proxy else '禁用'}")
        log(f"   SSR状态: {'已连接' if scraper.ssr_connected else '未连接'}")
        
        # 检查数据源配置
        primary_source = next((name for name, config in scraper.data_sources.items() if config.get('priority') == 1), 'unknown')
        log(f"   主数据源: {primary_source}")
        
        return True
        
    except Exception as e:
        log(f"❌ 直连模式测试失败: {e}")
        return False

def test_proxy_mode():
    """测试代理模式"""
    log("🔧 测试代理模式...")
    
    try:
        from advanced_crypto_scraper import AdvancedCryptoScraper
        
        # 创建代理模式实例
        scraper = AdvancedCryptoScraper(use_proxy=True)
        
        log(f"✅ 代理模式初始化成功")
        log(f"   代理状态: {'启用' if scraper.use_proxy else '禁用'}")
        log(f"   SSR状态: {'已连接' if scraper.ssr_connected else '未连接'}")
        
        # 检查代理配置
        if scraper.proxy_configs:
            log(f"   代理配置: {len(scraper.proxy_configs)} 个")
            for i, proxy in enumerate(scraper.proxy_configs, 1):
                log(f"     {i}. {proxy}")
        else:
            log("   ⚠️ 未找到代理配置")
        
        # 检查SSR服务器配置
        if hasattr(scraper, 'ssr_config') and scraper.ssr_config:
            servers = scraper.ssr_config.get('ssr_servers', [])
            log(f"   SSR服务器: {len(servers)} 个")
            for i, server in enumerate(servers, 1):
                log(f"     {i}. {server.get('name', 'N/A')} - {server.get('host', 'N/A')}:{server.get('port', 'N/A')}")
        
        return True
        
    except Exception as e:
        log(f"❌ 代理模式测试失败: {e}")
        return False

def test_ssr_config():
    """测试SSR配置"""
    log("🔧 测试SSR配置...")
    
    try:
        import json
        
        # 读取SSR配置文件
        with open('mcp_browser_scraper/ssr_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        log("✅ SSR配置文件读取成功")
        
        # 检查服务器配置
        servers = config.get('ssr_servers', [])
        log(f"   SSR服务器数量: {len(servers)}")
        
        for i, server in enumerate(servers, 1):
            log(f"   服务器{i}: {server.get('name', 'N/A')}")
            log(f"     地址: {server.get('host', 'N/A')}:{server.get('port', 'N/A')}")
            log(f"     本地端口: {server.get('local_port', 'N/A')}")
            log(f"     状态: {'启用' if server.get('enabled', False) else '禁用'}")
        
        # 检查代理设置
        proxy_settings = config.get('proxy_settings', {})
        log(f"   代理设置:")
        log(f"     启用: {'是' if proxy_settings.get('enable_proxy', False) else '否'}")
        log(f"     类型: {proxy_settings.get('proxy_type', 'N/A')}")
        log(f"     地址: {proxy_settings.get('proxy_host', 'N/A')}:{proxy_settings.get('proxy_port', 'N/A')}")
        
        # 检查微信配置
        wechat_config = config.get('wechat_webhooks', {})
        if wechat_config.get('enabled', False):
            robots = wechat_config.get('robots', [])
            log(f"   微信机器人: {len(robots)} 个")
            for i, robot in enumerate(robots, 1):
                log(f"     {i}. {robot.get('name', 'N/A')} - {'启用' if robot.get('enabled', False) else '禁用'}")
        
        return True
        
    except Exception as e:
        log(f"❌ SSR配置测试失败: {e}")
        return False

def main():
    """主函数"""
    log("🚀 开始代理选择功能测试...")
    log("="*50)
    
    results = []
    
    # 测试1: SSR配置
    results.append(test_ssr_config())
    log("="*50)
    
    # 测试2: 直连模式
    results.append(test_direct_mode())
    log("="*50)
    
    # 测试3: 代理模式
    results.append(test_proxy_mode())
    log("="*50)
    
    # 统计结果
    passed = sum(results)
    total = len(results)
    
    log(f"📊 测试结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        log("🎉 所有测试通过！")
        log("✅ SSR配置正确")
        log("✅ 直连模式正常")
        log("✅ 代理模式正常")
        log("🎯 代理选择功能修复成功")
    else:
        log("⚠️ 部分测试失败，需要检查配置")

if __name__ == "__main__":
    main()
