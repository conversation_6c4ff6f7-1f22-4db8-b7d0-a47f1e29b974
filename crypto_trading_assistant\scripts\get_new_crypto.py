#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import time
import argparse
import pandas as pd
from datetime import datetime, timedelta
from pycoingecko import CoinGeckoAPI
from loguru import logger

# 配置日志
logger.remove()
logger.add(lambda msg: print(msg), level="INFO")

# 自己实现的重试装饰器
def deco_retry(retry_times=3, retry_sleep=1):
    """
    重试装饰器
    
    Parameters
    ----------
    retry_times : int
        重试次数，默认3次
    retry_sleep : float
        重试间隔时间（秒），默认1秒
    """
    def _decorator(func):
        def _wrapper(*args, **kwargs):
            _retry_times = retry_times
            while _retry_times > 0:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    _retry_times -= 1
                    if _retry_times == 0:
                        # 重试完毕，失败则抛出异常
                        raise e
                    else:
                        logger.warning(f"请求失败，{retry_sleep}秒后重试，剩余{_retry_times}次重试机会, 错误: {e}")
                        time.sleep(retry_sleep)
        return _wrapper
    return _decorator

class NewCryptocurrencyFinder:
    """
    获取上市一个月内的新币列表
    """
    def __init__(self, days=30, output_file=None, vs_currency="usd", check_limit=50, request_delay=1.0):
        """
        初始化参数
        
        Parameters
        ----------
        days : int
            查找过去多少天内上市的币种，默认30天
        output_file : str
            输出结果的文件路径，默认为None，输出到控制台
        vs_currency : str
            计价货币，默认为美元(usd)
        check_limit : int
            检查详细信息的币种数量限制，默认50个
        request_delay : float
            API请求间隔时间（秒），默认1.0秒
        """
        self.days = days
        self.output_file = output_file
        self.vs_currency = vs_currency
        self.check_limit = check_limit
        self.request_delay = request_delay
        self.cg = CoinGeckoAPI()
        
    @deco_retry(retry_times=5, retry_sleep=2)
    def get_all_coins(self):
        """
        获取所有币种的信息
        
        Returns
        -------
        pd.DataFrame
            包含所有币种信息的DataFrame
        """
        try:
            logger.info("获取所有币种信息...")
            # 获取所有币种的市场数据
            coins_markets = self.cg.get_coins_markets(vs_currency=self.vs_currency, per_page=250, page=1)
            logger.info(f"获取到第1页数据，包含{len(coins_markets)}个币种")
            
            # 获取更多页的数据
            page = 2
            max_page = 4  # 限制最多查询4页，以加快处理速度
            while page <= max_page:
                try:
                    logger.info(f"开始获取第{page}页数据...")
                    time.sleep(self.request_delay)  # 添加延迟，避免API限制
                    next_page = self.cg.get_coins_markets(vs_currency=self.vs_currency, per_page=250, page=page)
                    if not next_page:
                        logger.info(f"第{page}页无数据，停止获取")
                        break
                    logger.info(f"获取到第{page}页数据，包含{len(next_page)}个币种")
                    coins_markets.extend(next_page)
                    page += 1
                except Exception as e:
                    logger.warning(f"获取第{page}页数据失败: {e}")
                    break
            
            coins_df = pd.DataFrame(coins_markets)
            logger.info(f"共获取到 {len(coins_df)} 个币种的基本信息")
            return coins_df
        except Exception as e:
            logger.error(f"获取币种信息失败: {e}")
            raise
    
    def get_new_listings(self):
        """
        获取近期上市的新币
        
        Returns
        -------
        pd.DataFrame
            包含新上市币种信息的DataFrame
        """
        # 获取所有币种信息
        try:
            all_coins = self.get_all_coins()
            if all_coins.empty:
                logger.warning("没有获取到币种信息")
                return pd.DataFrame()
                
            # 计算一个月前的日期
            current_date = datetime.now()
            month_ago = current_date - timedelta(days=self.days)
            logger.info(f"查找 {month_ago.strftime('%Y-%m-%d')} 之后上市的新币")
            
            # 尝试直接根据market_data字段查找最近添加的币种
            try:
                # 有些币种会有created_at或added_at字段
                if 'created_at' in all_coins.columns:
                    all_coins['created_at'] = pd.to_datetime(all_coins['created_at'], errors='coerce')
                    new_by_created = all_coins[all_coins['created_at'] >= month_ago].copy()
                    if not new_by_created.empty:
                        logger.info(f"通过created_at字段找到 {len(new_by_created)} 个新币")
                        return new_by_created
            except Exception as e:
                logger.warning(f"处理created_at字段时出错: {e}")
            
            # 筛选出近期上市的币种
            new_coins_by_genesis = pd.DataFrame()
            if 'genesis_date' in all_coins.columns:
                all_coins['genesis_date'] = pd.to_datetime(all_coins['genesis_date'], errors='coerce')
                new_coins_by_genesis = all_coins[all_coins['genesis_date'] >= month_ago].copy()
                if not new_coins_by_genesis.empty:
                    logger.info(f"通过genesis_date找到 {len(new_coins_by_genesis)} 个新币")
                    return new_coins_by_genesis
            
            # 尝试通过ATH日期(历史最高价日期)检测新币
            try:
                if 'ath_date' in all_coins.columns:
                    # 将日期字符串转换为datetime
                    all_coins['ath_date_str'] = all_coins['ath_date'].astype(str).str[:10]
                    all_coins['ath_date_parsed'] = pd.to_datetime(all_coins['ath_date_str'], errors='coerce')
                    
                    # 筛选最近有ATH的币种
                    month_ago_str = month_ago.strftime('%Y-%m-%d')
                    new_by_ath = all_coins[all_coins['ath_date_parsed'] >= pd.to_datetime(month_ago_str)].copy()
                    
                    if not new_by_ath.empty:
                        logger.info(f"通过ath_date找到 {len(new_by_ath)} 个可能的新币")
                        return new_by_ath
            except Exception as e:
                logger.warning(f"处理ath_date字段时出错: {e}")
            
            # 如果没有找到明确上市日期的币种，尝试通过市值排名和交易量特征识别
            logger.info("无法通过日期字段找到新币，尝试通过其他方式识别...")
            
            # 按市值排序，获取排名靠后的币种（可能是新上市的）
            try:
                if 'market_cap_rank' in all_coins.columns:
                    low_ranked = all_coins[all_coins['market_cap_rank'] > 1000].copy()
                    if not low_ranked.empty:
                        logger.info(f"找到 {len(low_ranked)} 个市值排名较低的币种")
                        return low_ranked.head(30)  # 限制返回数量
            except Exception as e:
                logger.warning(f"处理market_cap_rank字段时出错: {e}")
            
            # 最后尝试获取币种详细信息
            logger.info("尝试获取币种详细信息...")
            # 按市值排序并限制检查数量
            if 'market_cap_rank' in all_coins.columns:
                all_coins_sorted = all_coins.sort_values(by='market_cap_rank', na_position='last')
            else:
                all_coins_sorted = all_coins
            
            # 获取币种详细信息来找出最近创建的币种
            new_coins = self._get_recently_listed_coins(all_coins_sorted, month_ago)
            
            # 排序结果
            if not new_coins.empty:
                if 'market_cap_rank' in new_coins.columns:
                    new_coins = new_coins.sort_values(by='market_cap_rank')
                logger.info(f"通过详细信息找到 {len(new_coins)} 个最近上市的新币")
                return new_coins
            
            # 如果所有方法都失败，返回空DataFrame
            logger.warning("无法找到任何新上市的币种")
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"获取新上市币种时出错: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    @deco_retry(retry_times=3, retry_sleep=2)
    def _get_recently_listed_coins(self, all_coins, month_ago):
        """
        通过获取币种的详细信息来确定最近上市的币种
        
        Parameters
        ----------
        all_coins : pd.DataFrame
            所有币种的基本信息
        month_ago : datetime
            一个月前的日期
        
        Returns
        -------
        pd.DataFrame
            最近上市的币种信息
        """
        new_coins_list = []
        # 限制检查数量
        check_limit = min(self.check_limit, len(all_coins))
        
        logger.info(f"检查前 {check_limit} 个币种的详细信息...")
        for idx, (_, coin) in enumerate(all_coins.head(check_limit).iterrows()):
            try:
                if idx > 0 and idx % 5 == 0:
                    logger.info(f"已检查 {idx}/{check_limit} 个币种...")
                
                coin_id = coin['id']
                logger.info(f"获取币种 {coin_id} 的详细信息...")
                time.sleep(self.request_delay)  # 添加延迟，避免API限制
                
                # 设置超时时间
                coin_info = self.cg.get_coin_by_id(coin_id)
                
                # 获取币种的创建日期
                if 'genesis_date' in coin_info and coin_info['genesis_date']:
                    try:
                        genesis_date = pd.to_datetime(coin_info['genesis_date'])
                        if genesis_date >= month_ago:
                            logger.info(f"币种 {coin_id} 符合条件，创建日期: {genesis_date}")
                            new_coins_list.append(coin)
                            continue
                    except Exception:
                        pass
                
                # 检查上线时间
                if idx < 10 and 'tickers' in coin_info and len(coin_info['tickers']) > 0:
                    newest_timestamp = None
                    for ticker in coin_info['tickers'][:5]:  # 只检查前5个交易所记录
                        if 'timestamp' in ticker:
                            try:
                                timestamp = pd.to_datetime(ticker['timestamp'], unit='ms')
                                if newest_timestamp is None or timestamp > newest_timestamp:
                                    newest_timestamp = timestamp
                            except Exception:
                                pass
                    
                    if newest_timestamp and newest_timestamp >= month_ago:
                        logger.info(f"币种 {coin_id} 符合条件，上线时间: {newest_timestamp}")
                        new_coins_list.append(coin)
            except Exception as e:
                logger.warning(f"获取币种 {coin.get('id', 'unknown')} 详情失败: {e}")
        
        logger.info(f"共找到 {len(new_coins_list)} 个最近上市的新币")
        return pd.DataFrame(new_coins_list)
    
    def run(self):
        """
        运行脚本并输出结果
        """
        try:
            new_coins = self.get_new_listings()
            
            if new_coins.empty:
                logger.warning(f"未找到过去 {self.days} 天内上市的新币")
                return
            
            # 整理输出结果，只保留重要信息
            columns_to_keep = ['id', 'symbol', 'name', 'current_price', 'market_cap', 'market_cap_rank', 
                           'total_volume', 'price_change_percentage_24h']
            
            # 确保所有列都存在
            available_columns = [col for col in columns_to_keep if col in new_coins.columns]
            date_fields = ['genesis_date', 'ath_date', 'ath_date_parsed', 'created_at']
            for field in date_fields:
                if field in new_coins.columns:
                    available_columns.append(field)
                
            result = new_coins[available_columns].copy()
            
            # 输出结果
            if self.output_file:
                result.to_csv(self.output_file, index=False, encoding='utf-8')
                logger.info(f"结果已保存到文件: {self.output_file}")
            else:
                pd.set_option('display.max_rows', None)
                pd.set_option('display.max_columns', None)
                pd.set_option('display.width', 1000)
                print("\n新上市币种列表 (过去{}天):\n".format(self.days))
                print(result)
                print("\n总计: {} 个新币".format(len(result)))
        except Exception as e:
            logger.error(f"运行过程中发生错误: {e}")
            import traceback
            traceback.print_exc()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description='获取上市一个月内的数字货币新币列表')
    parser.add_argument('-d', '--days', type=int, default=30, help='查找过去多少天内上市的币种，默认30天')
    parser.add_argument('-o', '--output', type=str, help='输出结果的CSV文件路径')
    parser.add_argument('-c', '--currency', type=str, default='usd', help='计价货币，默认为美元(usd)')
    parser.add_argument('-l', '--limit', type=int, default=50, help='检查详细信息的币种数量限制，默认50个')
    parser.add_argument('-r', '--request-delay', type=float, default=1.0, help='API请求间隔(秒)，默认1.0秒')
    return parser.parse_args()


if __name__ == "__main__":
    args = parse_args()
    print(f"开始获取过去{args.days}天内上市的新币列表...")
    finder = NewCryptocurrencyFinder(
        days=args.days, 
        output_file=args.output, 
        vs_currency=args.currency,
        check_limit=args.limit,
        request_delay=args.request_delay
    )
    finder.run()
    print("程序执行完毕") 