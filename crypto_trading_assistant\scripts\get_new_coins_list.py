#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
获取过去指定天数内上市的新币种列表，并将结果保存到TXT文件。
"""

import os
import time
import pandas as pd
from datetime import datetime, timedelta
from pycoingecko import CoinGeckoAPI
from loguru import logger

class NewCoinFetcher:
    """
    获取新上市币种并保存到文件
    """
    def __init__(self, days=60, output_file="new_coins_list.txt", request_delay=0.6, max_retries=3):
        """
        初始化
        
        Parameters
        ----------
        days : int
            查找过去多少天内上市的币种，默认60天
        output_file : str
            输出文件名，默认 new_coins_list.txt
        request_delay : float
            API请求间隔时间（秒），默认0.6秒
        max_retries : int
            API请求最大重试次数
        """
        self.days = days
        self.output_file = os.path.join(os.path.dirname(__file__), output_file)
        self.request_delay = request_delay
        self.max_retries = max_retries
        self.cg = CoinGeckoAPI()

    def _get_listing_days(self, coin):
        """
        计算币种上市天数 (基于ATH日期)
        
        Parameters
        ----------
        coin : dict
            币种信息字典
            
        Returns
        -------
        int
            上市天数，如果无法计算则返回 -1
        """
        try:
            if 'ath_date' in coin and coin['ath_date']:
                # CoinGecko的日期格式可能包含'Z'或时区信息，需要处理
                ath_date_str = coin['ath_date'].split('T')[0] # 取日期部分
                ath_date = datetime.strptime(ath_date_str, '%Y-%m-%d')
                # 使用当前时区的now()，避免时区问题
                now_local = datetime.now()
                days_diff = (now_local - ath_date).days
                return max(0, days_diff) # 确保天数不为负
        except Exception as e:
            logger.warning(f"无法解析币种 {coin.get('id', 'N/A')} 的ATH日期 '{coin.get('ath_date', 'N/A')}': {e}")
        return -1 # 返回-1表示无法计算

    def fetch_and_save(self):
        """
        获取新币列表并保存到文件
        """
        try:
            logger.info(f"开始获取过去 {self.days} 天内上市的新币...")
            all_coins_data = []
            # 分页获取，每页250个币种
            # 增加页数以获取更多币种，因为按ID排序可能需要更多页才能覆盖近期币种
            for page in range(1, 10): # 尝试获取更多页
                logger.info(f"获取第 {page} 页数据...")
                if page > 1:
                    time.sleep(self.request_delay)
                
                for retry in range(self.max_retries):
                    try:
                        # 获取市场数据，按ID升序排序，理论上新币ID更大，但CoinGecko行为可能变化
                        # 尝试按market_cap_desc排序，通常新币市值较低，但波动大
                        # 最可靠的方式还是获取后基于日期过滤
                        params = {
                            'vs_currency': 'usd',
                            'per_page': 250,
                            'page': page,
                            'sparkline': False,
                            'order': 'market_cap_desc' # 尝试按市值排序
                        }
                        coins = self.cg.get_coins_markets(**params)
                        
                        if not coins:
                            logger.info(f"第 {page} 页没有获取到数据，停止获取。")
                            break # 没有更多数据了
                        
                        logger.info(f"获取到 {len(coins)} 个币种数据")
                        all_coins_data.extend(coins)
                        break # 成功获取数据，跳出重试循环
                    except Exception as e:
                        logger.warning(f"获取第 {page} 页数据失败 (尝试 {retry+1}/{self.max_retries}): {e}")
                        if retry == self.max_retries - 1:
                            logger.error(f"获取第 {page} 页数据失败，已达到最大重试次数")
                            break
                        time.sleep(self.request_delay * (2 ** retry))
                        continue
                if not coins: # 如果当前页没有获取到数据，则停止
                    break

            if not all_coins_data:
                logger.warning("未能从CoinGecko获取到任何币种数据。")
                return

            logger.info(f"总共获取到 {len(all_coins_data)} 个币种数据，开始计算上市天数并过滤...")
            
            new_coins_list = []
            cutoff_date = datetime.now() - timedelta(days=self.days)

            for coin in all_coins_data:
                listing_days = self._get_listing_days(coin)
                # 只有成功计算出上市天数且天数在指定范围内才添加
                if listing_days != -1 and listing_days <= self.days:
                    new_coins_list.append({'id': coin['id'], 'listing_days': listing_days})
            
            if not new_coins_list:
                logger.warning(f"在获取到的数据中，没有找到过去 {self.days} 天内上市的币种。")
                # 即使没找到，也创建一个空文件或覆盖旧文件
                with open(self.output_file, 'w', encoding='utf-8') as f:
                    f.write("") # 写入空内容
                logger.info(f"已创建空的输出文件: {self.output_file}")
                return

            # 按上市天数升序排序
            new_coins_df = pd.DataFrame(new_coins_list)
            new_coins_df = new_coins_df.sort_values(by='listing_days')

            logger.info(f"找到 {len(new_coins_df)} 个满足条件的币种，准备写入文件: {self.output_file}")

            # 保存到TXT文件，格式：coin_id,listing_days
            with open(self.output_file, 'w', encoding='utf-8') as f:
                for _, row in new_coins_df.iterrows():
                    f.write(f"{row['id']},{row['listing_days']}\n")
            
            logger.info(f"新币列表已成功保存到: {self.output_file}")

        except Exception as e:
            logger.error(f"获取和保存新币列表时出错: {e}")

def main():
    """
    主函数
    """
    # 配置日志
    log_file = os.path.join(os.path.dirname(__file__), "crypto_collector_step1.log")
    logger.remove()
    logger.add(lambda msg: print(msg), level="INFO") # 控制台输出INFO级别
    logger.add(log_file, level="DEBUG", rotation="10 MB", retention="7 days") # 文件记录DEBUG级别

    fetcher = NewCoinFetcher(days=60) # 获取60天内的新币
    fetcher.fetch_and_save()

if __name__ == "__main__":
    main()