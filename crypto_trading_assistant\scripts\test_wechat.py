import requests
import json
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

def test_sendkey(sendkey: str):
    url = f'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={sendkey}'
    payload = {
        "msgtype": "markdown",
        "markdown": {
            "content": "**测试消息**\n> 发送时间: 2025-04-21 15:00:00\n> 状态: 连接正常"
        }
    }
    try:
        response = requests.post(url, json=payload, timeout=10)
        result = response.json()
        if response.status_code == 200 and result.get('errcode') == 0:
            return True, "验证成功"
        return False, f"接口返回错误: {result}"
    except Exception as e:
        return False, f"请求异常: {str(e)}"

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='微信SendKey验证工具')
    parser.add_argument('--config', default='config.json', help='配置文件路径')
    args = parser.parse_args()

    try:
        config_path = Path(args.config)
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        send_keys = config['wechat']['send_keys']
        print(f"找到 {len(send_keys)} 个SendKey进行测试")
        
        for i, key in enumerate(send_keys, 1):
            print(f"正在测试第{i}个SendKey...")
            success, message = test_sendkey(key)
            status = "成功" if success else "失败"
            print(f"测试结果: {status} | {message}")
            
    except Exception as e:
        logger.error(f"测试执行失败: {str(e)}")
        exit(1)