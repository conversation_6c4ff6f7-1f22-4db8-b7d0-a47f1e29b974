#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
完整功能测试 - 包括数据下载和形态分析
"""

import os
import sys
import time
from datetime import datetime

def log(message):
    """日志函数"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def test_data_download_and_pattern_analysis():
    """测试数据下载和形态分析完整流程"""
    try:
        print("🚀 完整功能测试 - 数据下载 + 形态分析")
        print("="*80)
        
        # 获取脚本路径
        script_dir = os.path.dirname(os.path.abspath(__file__))
        script_path = os.path.join(script_dir, "advanced_crypto_scraper tuxing.py")
        
        if not os.path.exists(script_path):
            print(f"❌ 主脚本文件不存在: {script_path}")
            return False
        
        # 导入模块
        print("🔧 加载主脚本模块...")
        import importlib.util
        spec = importlib.util.spec_from_file_location("advanced_crypto_scraper_tuxing", script_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        print("✅ 模块加载成功")
        
        # 创建实例 - 使用直连模式避免代理问题
        print("\n🔧 创建抓取器实例 (直连模式)...")
        scraper = module.AdvancedCryptoScraper(use_proxy=False)
        print("✅ 抓取器实例创建成功")
        
        # 测试网络连接
        print("\n🌐 测试网络连接...")
        connection_ok = scraper.test_connection()
        if not connection_ok:
            print("⚠️ 网络连接测试失败，但继续尝试数据获取...")
        else:
            print("✅ 网络连接正常")
        
        # 测试数据下载
        print("\n📊 测试数据下载功能...")
        print("🔍 尝试获取加密货币数据 (限制10个币种)...")
        
        cryptocurrencies = scraper.get_all_cryptocurrencies(limit=10)
        
        if cryptocurrencies:
            print(f"✅ 数据下载成功！获取到 {len(cryptocurrencies)} 个币种")
            print("\n📈 获取到的币种列表:")
            for i, crypto in enumerate(cryptocurrencies[:5], 1):  # 只显示前5个
                symbol = crypto.get('symbol', 'N/A')
                name = crypto.get('name', 'N/A')
                price = crypto.get('current_price', 0)
                volume = crypto.get('volume_24h', 0)
                source = crypto.get('data_source', 'N/A')
                print(f"   {i}. {symbol} ({name}) - 价格: ${price:.6f}, 24h成交量: ${volume:,.0f}, 数据源: {source}")
            
            if len(cryptocurrencies) > 5:
                print(f"   ... 还有 {len(cryptocurrencies) - 5} 个币种")
        else:
            print("❌ 数据下载失败！无法获取加密货币数据")
            print("💡 可能的原因:")
            print("   - 网络连接问题")
            print("   - API访问限制")
            print("   - 数据源不可用")
            return False
        
        # 测试历史数据获取
        print("\n📊 测试历史数据获取...")
        if cryptocurrencies:
            test_crypto = cryptocurrencies[0]  # 使用第一个币种测试
            symbol = test_crypto.get('symbol', 'BTC')
            crypto_id = test_crypto.get('id', 'bitcoin')
            
            print(f"🔍 获取 {symbol} 的历史数据...")
            
            # 尝试获取历史数据
            try:
                historical_data = scraper.get_historical_data(crypto_id, days=30)
                
                if historical_data and len(historical_data) >= 2:
                    print(f"✅ 历史数据获取成功！获取到 {len(historical_data)} 天的数据")
                    print(f"📈 最新数据 (最近2天):")
                    for i, data in enumerate(historical_data[-2:], 1):
                        date = data.get('date', 'N/A')
                        open_price = data.get('open_price', 0)
                        high_price = data.get('high_price', 0)
                        low_price = data.get('low_price', 0)
                        close_price = data.get('close_price', 0)
                        print(f"   第{i}天 ({date}): 开盘={open_price:.6f}, 最高={high_price:.6f}, "
                              f"最低={low_price:.6f}, 收盘={close_price:.6f}")
                    
                    # 测试形态分析
                    print(f"\n🔍 对 {symbol} 进行形态分析...")
                    patterns = scraper._identify_patterns(historical_data)
                    print(f"🎯 形态识别结果: {patterns}")
                    
                    if "双长上影线" in patterns:
                        print(f"🎉 发现双长上影线形态！{symbol} 可能面临回调")
                    else:
                        print(f"📊 {symbol} 未发现双长上影线形态")
                    
                    historical_success = True
                else:
                    print("⚠️ 历史数据不足，无法进行形态分析")
                    historical_success = False
                    
            except Exception as e:
                print(f"❌ 历史数据获取失败: {e}")
                historical_success = False
        
        # 测试完整的形态分析流程
        print("\n🎯 测试完整形态分析流程...")
        try:
            # 模拟调用形态分析筛选器
            print("🔍 执行形态分析筛选...")
            filtered_results = scraper._filter_pattern_analysis(cryptocurrencies)
            
            if filtered_results:
                print(f"🎉 形态分析完成！发现 {len(filtered_results)} 个符合双长上影线形态的币种:")
                for result in filtered_results:
                    symbol = result.get('symbol', 'N/A')
                    reason = result.get('selection_reason', 'N/A')
                    print(f"   ✅ {symbol}: {reason}")
            else:
                print("📊 形态分析完成，当前市场中未发现符合双长上影线形态的币种")
            
            pattern_analysis_success = True
            
        except Exception as e:
            print(f"❌ 形态分析失败: {e}")
            import traceback
            print(f"错误详情: {traceback.format_exc()}")
            pattern_analysis_success = False
        
        # 测试结果总结
        print("\n" + "="*80)
        print("📊 完整功能测试结果总结:")
        print("="*80)
        
        data_download_success = len(cryptocurrencies) > 0
        
        print(f"✅ 数据下载功能: {'正常' if data_download_success else '异常'}")
        print(f"✅ 历史数据获取: {'正常' if historical_success else '异常'}")
        print(f"✅ 形态分析功能: {'正常' if pattern_analysis_success else '异常'}")
        
        overall_success = data_download_success and pattern_analysis_success
        
        if overall_success:
            print("🎉 所有核心功能测试通过！系统可以正常使用")
            print("\n💡 使用建议:")
            print("   1. 数据下载功能正常，可以获取实时市场数据")
            print("   2. 形态分析功能正常，可以识别双长上影线形态")
            print("   3. 可以使用完整的形态分析功能进行实盘分析")
        else:
            print("⚠️ 部分功能存在问题，需要进一步检查")
            if not data_download_success:
                print("   - 数据下载功能需要检查网络连接和API配置")
            if not pattern_analysis_success:
                print("   - 形态分析功能需要检查算法逻辑")
        
        return overall_success
        
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    try:
        print("🚀 启动完整功能测试...")
        print("="*80)
        
        # 检查当前目录
        current_dir = os.getcwd()
        print(f"📍 当前目录: {current_dir}")
        
        # 运行完整测试
        success = test_data_download_and_pattern_analysis()
        
        print("\n" + "="*80)
        if success:
            print("🎉 完整功能测试通过！系统可以正常使用")
            print("\n🚀 下一步:")
            print("   1. 可以运行 '直接启动形态分析.py' 进行实际分析")
            print("   2. 或者运行主脚本选择菜单选项15")
            print("   3. 系统会自动下载数据并进行形态分析")
        else:
            print("⚠️ 功能测试发现问题，请检查:")
            print("   1. 网络连接是否正常")
            print("   2. Python环境和依赖库是否完整")
            print("   3. 数据源API是否可访问")
        
    except KeyboardInterrupt:
        print("\n👋 用户中断测试")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
