#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
加密货币分析系统主程序

负责协调各个模块的工作，包括：
- 数据收集
- 技术分析
- 回测
- 报告生成
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional, Union
import json

from crypto_data_collector import CryptoDataCollector
from crypto_technical_analyzer import TechnicalAnalyzer
from crypto_backtest import CryptoBacktest
from crypto_report_generator import ReportGenerator

class CryptoAnalyzer:
    """
    加密货币分析系统主类
    """
    def __init__(self, exchange_id: str = 'gateio'):
        """
        初始化分析系统
        
        Args:
            exchange_id: 交易所ID，默认为gateio
        """
        # 初始化各个模块
        self.data_collector = CryptoDataCollector(exchange_id)
        self.technical_analyzer = TechnicalAnalyzer()
        self.backtest = CryptoBacktest()
        self.report_generator = ReportGenerator()
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('crypto_analyzer.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def analyze_new_coins(self, days: int = 60) -> pd.DataFrame:
        """
        分析新上市的币种
        
        Args:
            days: 获取最近多少天内上市的币种，默认60天
            
        Returns:
            包含分析结果的DataFrame
        """
        try:
            # 获取新币数据
            new_coins = self.data_collector.get_new_coins(days)
            
            if new_coins.empty:
                self.logger.warning("未找到新上市的币种")
                return pd.DataFrame()
                
            # 对每个币种进行技术分析
            analysis_results = []
            for _, coin in new_coins.iterrows():
                try:
                    # 获取历史数据
                    historical_data = self.data_collector.get_historical_data(
                        coin['symbol'], 
                        timeframe='1d',
                        limit=100
                    )
                    
                    if historical_data.empty:
                        continue
                        
                    # 计算技术指标
                    df_with_indicators = self.technical_analyzer.calculate_indicators(historical_data)
                    
                    # 分析趋势
                    trend_analysis = self.technical_analyzer.analyze_trend(df_with_indicators)
                    
                    # 识别形态
                    patterns = self.technical_analyzer.find_patterns(df_with_indicators)
                    
                    # 生成交易信号
                    signals = self.technical_analyzer.generate_signals(df_with_indicators)
                    
                    # 合并分析结果
                    analysis_result = {
                        'symbol': coin['symbol'],
                        'name': coin['name'],
                        'first_trade_date': coin['first_trade_date'],
                        'current_price': coin['current_price'],
                        'volume_24h': coin['volume_24h'],
                        'price_change_24h': coin['price_change_24h'],
                        'trend': trend_analysis.get('trend', 'unknown'),
                        'strength': trend_analysis.get('strength', 'unknown'),
                        'patterns': patterns,
                        'signals': signals
                    }
                    
                    analysis_results.append(analysis_result)
                    
                except Exception as e:
                    self.logger.error(f"分析{coin['symbol']}时出错: {str(e)}")
                    continue
                    
            return pd.DataFrame(analysis_results)
            
        except Exception as e:
            self.logger.error(f"分析新币失败: {str(e)}")
            return pd.DataFrame()
            
    def run_backtest(self, symbol: str, start_date: str, end_date: str,
                    position_size: float = 0.1, stop_loss: float = 0.05,
                    take_profit: float = 0.1) -> Dict:
        """
        运行回测
        
        Args:
            symbol: 交易对符号
            start_date: 开始日期
            end_date: 结束日期
            position_size: 仓位大小
            stop_loss: 止损比例
            take_profit: 止盈比例
            
        Returns:
            回测结果
        """
        try:
            # 获取历史数据
            historical_data = self.data_collector.get_historical_data(
                symbol,
                timeframe='1d',
                limit=1000  # 获取足够的历史数据
            )
            
            if historical_data.empty:
                self.logger.error(f"无法获取{symbol}的历史数据")
                return {}
                
            # 过滤日期范围
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            historical_data = historical_data[
                (historical_data.index >= start_dt) & 
                (historical_data.index <= end_dt)
            ]
            
            if historical_data.empty:
                self.logger.error(f"在指定日期范围内没有{symbol}的数据")
                return {}
                
            # 运行回测
            backtest_results = self.backtest.run_backtest(
                historical_data,
                position_size=position_size,
                stop_loss=stop_loss,
                take_profit=take_profit
            )
            
            return backtest_results
            
        except Exception as e:
            self.logger.error(f"回测失败: {str(e)}")
            return {}
            
    def generate_analysis_report(self, analysis_results: pd.DataFrame,
                               backtest_results: Optional[Dict] = None,
                               strategy_name: str = 'Strategy') -> str:
        """
        生成分析报告
        
        Args:
            analysis_results: 分析结果
            backtest_results: 回测结果
            strategy_name: 策略名称
            
        Returns:
            报告文件路径
        """
        try:
            # 生成回测报告
            if backtest_results:
                report_path = self.report_generator.generate_backtest_report(
                    backtest_results,
                    strategy_name
                )
                return report_path
            else:
                self.logger.warning("没有回测结果，无法生成报告")
                return ''
                
        except Exception as e:
            self.logger.error(f"生成报告失败: {str(e)}")
            return ''
            
    def optimize_strategy(self, symbol: str, start_date: str, end_date: str,
                         position_sizes: List[float] = [0.1, 0.2, 0.3],
                         stop_losses: List[float] = [0.05, 0.1, 0.15],
                         take_profits: List[float] = [0.1, 0.2, 0.3]) -> Dict:
        """
        优化策略参数
        
        Args:
            symbol: 交易对符号
            start_date: 开始日期
            end_date: 结束日期
            position_sizes: 仓位大小列表
            stop_losses: 止损比例列表
            take_profits: 止盈比例列表
            
        Returns:
            优化结果
        """
        try:
            # 获取历史数据
            historical_data = self.data_collector.get_historical_data(
                symbol,
                timeframe='1d',
                limit=1000
            )
            
            if historical_data.empty:
                self.logger.error(f"无法获取{symbol}的历史数据")
                return {}
                
            # 过滤日期范围
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            historical_data = historical_data[
                (historical_data.index >= start_dt) & 
                (historical_data.index <= end_dt)
            ]
            
            if historical_data.empty:
                self.logger.error(f"在指定日期范围内没有{symbol}的数据")
                return {}
                
            # 优化参数
            optimization_results = self.backtest.optimize_parameters(
                historical_data,
                position_sizes=position_sizes,
                stop_losses=stop_losses,
                take_profits=take_profits
            )
            
            return optimization_results
            
        except Exception as e:
            self.logger.error(f"优化策略失败: {str(e)}")
            return {}

if __name__ == "__main__":
    # 测试代码
    analyzer = CryptoAnalyzer()
    
    # 分析新币
    new_coins_analysis = analyzer.analyze_new_coins(days=60)
    print("\n新币分析结果:")
    print(new_coins_analysis)
    
    # 运行回测
    backtest_results = analyzer.run_backtest(
        symbol='BTC/USDT',
        start_date='2024-01-01',
        end_date='2024-03-01'
    )
    
    # 生成报告
    if backtest_results:
        report_path = analyzer.generate_analysis_report(
            new_coins_analysis,
            backtest_results,
            'BTC策略'
        )
        print(f"\n报告已生成: {report_path}")
        
    # 优化策略
    optimization_results = analyzer.optimize_strategy(
        symbol='BTC/USDT',
        start_date='2024-01-01',
        end_date='2024-03-01'
    )
    print("\n策略优化结果:")
    print(json.dumps(optimization_results, indent=2)) 