#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最终功能测试脚本
"""

import sys
import os
from datetime import datetime

def log(message):
    """日志函数"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def test_basic_functionality():
    """测试基本功能"""
    log("🔍 测试基本功能...")
    
    try:
        # 导入主类
        from advanced_crypto_scraper import AdvancedCryptoScraper
        
        # 创建实例
        log("创建抓取器实例...")
        scraper = AdvancedCryptoScraper(use_proxy=False)
        
        # 测试速度模式
        log("测试速度模式显示...")
        scraper.show_speed_modes()
        
        # 测试统计信息
        log("测试统计信息...")
        scraper.print_statistics()
        
        # 测试连接
        log("测试网络连接...")
        scraper.test_connection()
        
        log("✅ 基本功能测试完成")
        return True
        
    except Exception as e:
        log(f"❌ 基本功能测试失败: {e}")
        import traceback
        log(f"错误详情: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    log("🚀 开始最终功能测试...")
    
    if test_basic_functionality():
        log("🎉 所有测试通过！脚本可以正常使用")
        log("💡 可以运行 advanced_crypto_scraper.py 开始使用")
    else:
        log("❌ 测试失败，请检查依赖和配置")

if __name__ == "__main__":
    main()
