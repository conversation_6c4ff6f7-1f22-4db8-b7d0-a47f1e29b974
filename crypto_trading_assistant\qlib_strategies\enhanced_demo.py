"""
增强版数字货币选币系统演示
包含15分钟和30分钟周期，支持企业微信推送
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import requests
import json
import warnings
warnings.filterwarnings('ignore')


class EnhancedCryptoAnalyzer:
    """增强版数字货币分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.symbols = [
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'XRPUSDT',
            'SOLUSDT', 'DOTUSDT', 'DOGEUSDT', 'AVAXUSDT', 'MATICUSDT',
            'LINKUSDT', 'UNIUSDT', 'LTCUSDT', 'BCHUSDT', 'ETCUSDT'
        ]
        
        # 支持的时间周期
        self.timeframes = ['1d', '4h', '1h', '30m', '15m']
        
        # 多时间周期权重
        self.timeframe_weights = {
            '1d': 0.4,   # 日线权重
            '4h': 0.25,  # 4小时权重
            '1h': 0.15,  # 1小时权重
            '30m': 0.12, # 30分钟权重
            '15m': 0.08  # 15分钟权重
        }
        
        # 技术指标权重
        self.weights = {
            'pattern': 0.3,
            'indicator': 0.4,
            'trend': 0.2,
            'volume': 0.1
        }
        
        # 企业微信webhook
        self.wechat_webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=52d1b8a8-a88a-4d41-9664-f1268da20985"
    
    def generate_mock_data(self, symbol: str, timeframe: str, days: int = 100) -> pd.DataFrame:
        """生成模拟数据"""
        # 基础价格设置
        base_prices = {
            'BTCUSDT': 45000, 'ETHUSDT': 3000, 'BNBUSDT': 400,
            'ADAUSDT': 1.2, 'XRPUSDT': 0.6, 'SOLUSDT': 100,
            'DOTUSDT': 25, 'DOGEUSDT': 0.08, 'AVAXUSDT': 35, 
            'MATICUSDT': 1.5, 'LINKUSDT': 15, 'UNIUSDT': 8,
            'LTCUSDT': 80, 'BCHUSDT': 250, 'ETCUSDT': 30
        }
        
        base_price = base_prices.get(symbol, 100)
        
        # 根据时间周期调整数据点数量
        timeframe_periods = {
            '1d': days,
            '4h': days * 6,
            '1h': days * 24,
            '30m': days * 48,
            '15m': days * 96
        }
        
        periods = timeframe_periods.get(timeframe, days)
        
        # 根据时间周期设置频率
        freq_map = {
            '1d': 'D',
            '4h': '4H', 
            '1h': 'H',
            '30m': '30T',
            '15m': '15T'
        }
        
        freq = freq_map.get(timeframe, 'D')
        
        # 生成日期序列
        end_time = datetime.now()
        if timeframe == '1d':
            start_time = end_time - timedelta(days=days)
        elif timeframe == '4h':
            start_time = end_time - timedelta(hours=days*6)
        elif timeframe == '1h':
            start_time = end_time - timedelta(hours=days*24)
        elif timeframe == '30m':
            start_time = end_time - timedelta(minutes=days*48*30)
        else:  # 15m
            start_time = end_time - timedelta(minutes=days*96*15)
        
        dates = pd.date_range(start=start_time, end=end_time, freq=freq)
        
        # 限制数据点数量
        if len(dates) > periods:
            dates = dates[-periods:]
        
        # 生成价格走势
        np.random.seed(hash(symbol + timeframe) % 1000)
        
        # 根据时间周期调整波动率
        volatility_map = {
            '1d': 0.02,
            '4h': 0.015,
            '1h': 0.01,
            '30m': 0.008,
            '15m': 0.006
        }
        
        volatility = volatility_map.get(timeframe, 0.02)
        returns = np.random.normal(0, volatility, len(dates))
        
        prices = [base_price]
        for ret in returns[1:]:
            new_price = prices[-1] * (1 + ret)
            prices.append(new_price)
        
        # 生成OHLCV数据
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            intraday_vol = abs(np.random.normal(0, volatility/2))
            
            open_price = close * (1 + np.random.normal(0, volatility/4))
            high = max(open_price, close) * (1 + intraday_vol)
            low = min(open_price, close) * (1 - intraday_vol)
            volume = np.random.lognormal(10, 1) * 1000
            
            data.append({
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume
            })
        
        df = pd.DataFrame(data, index=dates)
        return df
    
    def calculate_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        df = data.copy()
        
        # RSI
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        ema12 = df['close'].ewm(span=12).mean()
        ema26 = df['close'].ewm(span=26).mean()
        df['macd'] = ema12 - ema26
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_hist'] = df['macd'] - df['macd_signal']
        
        # 布林带
        df['bb_middle'] = df['close'].rolling(20).mean()
        bb_std = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        
        # 移动平均线
        df['sma_5'] = df['close'].rolling(5).mean()
        df['sma_10'] = df['close'].rolling(10).mean()
        df['sma_20'] = df['close'].rolling(20).mean()
        df['sma_50'] = df['close'].rolling(50).mean()
        
        # 成交量均线
        df['volume_sma'] = df['volume'].rolling(20).mean()
        
        return df
    
    def analyze_symbol_timeframe(self, symbol: str, timeframe: str) -> dict:
        """分析单个交易对的单个时间周期"""
        try:
            # 获取数据
            data = self.generate_mock_data(symbol, timeframe, 100)
            
            # 计算技术指标
            data = self.calculate_technical_indicators(data)
            
            if len(data) < 50:
                return None
            
            # 形态识别
            pattern_score = self.evaluate_patterns(data)
            
            # 技术指标信号
            indicator_score = self.evaluate_indicators(data)
            
            # 趋势信号
            trend_score = self.evaluate_trend(data)
            
            # 成交量信号
            volume_score = self.evaluate_volume(data)
            
            # 计算综合得分
            total_score = (
                pattern_score * self.weights['pattern'] +
                indicator_score * self.weights['indicator'] +
                trend_score * self.weights['trend'] +
                volume_score * self.weights['volume']
            )
            
            # 收集信号
            signals = self.collect_signals(data)
            
            latest = data.iloc[-1]
            
            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'score': float(total_score),
                'pattern_score': float(pattern_score),
                'indicator_score': float(indicator_score),
                'trend_score': float(trend_score),
                'volume_score': float(volume_score),
                'current_price': float(latest['close']),
                'rsi': float(latest['rsi']) if not pd.isna(latest['rsi']) else 50.0,
                'macd': float(latest['macd']) if not pd.isna(latest['macd']) else 0.0,
                'volume_ratio': float(latest['volume'] / latest['volume_sma']) if not pd.isna(latest['volume_sma']) else 1.0,
                'signals': signals
            }
            
        except Exception as e:
            print(f"分析 {symbol} {timeframe} 时出错: {e}")
            return None
    
    def evaluate_patterns(self, data: pd.DataFrame) -> float:
        """评估K线形态"""
        try:
            latest = data.iloc[-1]
            prev = data.iloc[-2] if len(data) > 1 else latest
            
            score = 0.0
            
            # 锤子线形态
            body = abs(latest['close'] - latest['open'])
            lower_shadow = min(latest['close'], latest['open']) - latest['low']
            upper_shadow = latest['high'] - max(latest['close'], latest['open'])
            
            if body > 0 and lower_shadow >= 2 * body and upper_shadow <= 0.1 * body:
                score += 2.0
            
            # 十字星形态
            if body <= 0.001 * latest['close'] and (latest['high'] - latest['low']) >= 0.01 * latest['close']:
                score += 1.0
            
            # 看涨吞没
            if (prev['close'] < prev['open'] and latest['close'] > latest['open'] and
                latest['open'] < prev['close'] and latest['close'] > prev['open']):
                score += 3.0
            
            return min(score, 5.0)
            
        except:
            return 0.0
    
    def evaluate_indicators(self, data: pd.DataFrame) -> float:
        """评估技术指标"""
        try:
            latest = data.iloc[-1]
            prev = data.iloc[-2] if len(data) > 1 else latest
            
            score = 0.0
            
            # RSI超卖回升
            if not pd.isna(latest['rsi']) and not pd.isna(prev['rsi']):
                if prev['rsi'] < 30 and latest['rsi'] > 35:
                    score += 2.0
            
            # MACD金叉
            if (not pd.isna(latest['macd']) and not pd.isna(latest['macd_signal']) and
                not pd.isna(prev['macd']) and not pd.isna(prev['macd_signal'])):
                if latest['macd'] > latest['macd_signal'] and prev['macd'] <= prev['macd_signal']:
                    score += 2.0
            
            # 布林带支撑
            if (not pd.isna(latest['bb_lower']) and not pd.isna(prev['bb_lower'])):
                if prev['low'] <= prev['bb_lower'] and latest['close'] > latest['bb_lower']:
                    score += 1.5
            
            return min(score, 5.0)
            
        except:
            return 0.0
    
    def evaluate_trend(self, data: pd.DataFrame) -> float:
        """评估趋势"""
        try:
            latest = data.iloc[-1]
            
            score = 0.0
            
            # 均线多头排列
            if (not pd.isna(latest['sma_5']) and not pd.isna(latest['sma_10']) and 
                not pd.isna(latest['sma_20']) and not pd.isna(latest['sma_50'])):
                if (latest['sma_5'] > latest['sma_10'] > latest['sma_20'] > latest['sma_50']):
                    score += 2.0
            
            # 价格在均线上方
            if not pd.isna(latest['sma_20']) and latest['close'] > latest['sma_20']:
                score += 1.0
            
            return min(score, 3.0)
            
        except:
            return 0.0
    
    def evaluate_volume(self, data: pd.DataFrame) -> float:
        """评估成交量"""
        try:
            latest = data.iloc[-1]
            
            score = 0.0
            
            # 成交量放大
            if not pd.isna(latest['volume_sma']) and latest['volume'] > latest['volume_sma'] * 1.5:
                score += 2.0
            
            # 价量配合
            if latest['close'] > latest['open'] and latest['volume'] > latest['volume_sma']:
                score += 1.0
            
            return min(score, 3.0)
            
        except:
            return 0.0
    
    def collect_signals(self, data: pd.DataFrame) -> list:
        """收集信号"""
        signals = []
        
        try:
            latest = data.iloc[-1]
            prev = data.iloc[-2] if len(data) > 1 else latest
            
            # RSI信号
            if not pd.isna(latest['rsi']):
                if latest['rsi'] < 30:
                    signals.append("RSI超卖")
                elif latest['rsi'] > 70:
                    signals.append("RSI超买")
                elif prev['rsi'] < 30 and latest['rsi'] > 35:
                    signals.append("RSI超卖回升")
            
            # MACD信号
            if (not pd.isna(latest['macd']) and not pd.isna(latest['macd_signal'])):
                if latest['macd'] > latest['macd_signal']:
                    signals.append("MACD金叉")
            
            # 布林带信号
            if not pd.isna(latest['bb_upper']) and not pd.isna(latest['bb_lower']):
                if latest['close'] > latest['bb_upper']:
                    signals.append("突破布林带上轨")
                elif latest['close'] < latest['bb_lower']:
                    signals.append("跌破布林带下轨")
            
            # 成交量信号
            if not pd.isna(latest['volume_sma']) and latest['volume'] > latest['volume_sma'] * 1.5:
                signals.append("成交量放大")
            
            # 趋势信号
            if (not pd.isna(latest['sma_5']) and not pd.isna(latest['sma_20']) and
                latest['sma_5'] > latest['sma_20']):
                signals.append("短期上升趋势")
            
        except:
            pass
        
        return signals

    def multi_timeframe_analysis(self, symbols: list = None) -> dict:
        """多时间周期分析"""
        if symbols is None:
            symbols = self.symbols[:5]  # 分析前5个交易对

        results = {}

        print(f"🔍 开始多时间周期分析，标的数量: {len(symbols)}")
        print("=" * 60)

        for symbol in symbols:
            print(f"\n分析 {symbol}:")
            symbol_results = {}

            # 分析各个时间周期
            for timeframe in self.timeframes:
                tf_result = self.analyze_symbol_timeframe(symbol, timeframe)
                if tf_result:
                    symbol_results[timeframe] = tf_result
                    print(f"  {timeframe:>3}: 得分 {tf_result['score']:.1f}, RSI {tf_result['rsi']:.1f}, 信号 {len(tf_result['signals'])}")

            if symbol_results:
                # 计算多周期综合得分
                total_score = 0
                weight_sum = 0

                for tf, result in symbol_results.items():
                    weight = self.timeframe_weights.get(tf, 0.1)
                    total_score += result['score'] * weight
                    weight_sum += weight

                multi_tf_score = total_score / weight_sum if weight_sum > 0 else 0

                # 多周期确认
                confirmation = self.check_multi_timeframe_confirmation(symbol_results)

                symbol_results['multi_tf_score'] = multi_tf_score
                symbol_results['confirmation'] = confirmation

                print(f"  综合得分: {multi_tf_score:.2f}")
                print(f"  趋势一致: {'✓' if confirmation['trend_alignment'] else '✗'}")
                print(f"  信号一致: {'✓' if confirmation['signal_consistency'] else '✗'}")
                print(f"  风险等级: {confirmation['risk_level']}")

                results[symbol] = symbol_results

        return results

    def check_multi_timeframe_confirmation(self, symbol_results: dict) -> dict:
        """检查多时间周期确认"""
        confirmation = {
            'trend_alignment': False,
            'signal_consistency': False,
            'risk_level': 'medium'
        }

        # 获取各周期得分
        scores = {}
        for tf in ['1d', '4h', '1h', '30m', '15m']:
            if tf in symbol_results:
                scores[tf] = symbol_results[tf]['score']

        # 检查趋势一致性
        daily_score = scores.get('1d', 0)
        h4_score = scores.get('4h', 0)
        h1_score = scores.get('1h', 0)

        if daily_score > 3 and h4_score > 2:
            confirmation['trend_alignment'] = True

        # 检查信号一致性
        high_score_count = sum(1 for score in scores.values() if score > 2.5)
        if high_score_count >= 3:
            confirmation['signal_consistency'] = True

        # 风险评估
        if confirmation['trend_alignment'] and confirmation['signal_consistency']:
            confirmation['risk_level'] = 'low'
        elif confirmation['trend_alignment'] or confirmation['signal_consistency']:
            confirmation['risk_level'] = 'medium'
        else:
            confirmation['risk_level'] = 'high'

        return confirmation

    def send_wechat_notification(self, results: dict):
        """发送企业微信通知"""
        try:
            if not results:
                return

            # 构建消息内容
            message = self.build_notification_message(results)

            # 发送请求
            data = {
                "msgtype": "text",
                "text": {
                    "content": message
                }
            }

            response = requests.post(
                self.wechat_webhook,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    print("✅ 企业微信通知发送成功")
                else:
                    print(f"❌ 企业微信通知发送失败: {result}")
            else:
                print(f"❌ 企业微信通知请求失败: {response.status_code}")

        except Exception as e:
            print(f"❌ 发送企业微信通知失败: {e}")

    def build_notification_message(self, results: dict) -> str:
        """构建通知消息"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 筛选高分标的
            high_score_symbols = []
            for symbol, data in results.items():
                multi_tf_score = data.get('multi_tf_score', 0)
                if multi_tf_score >= 2.5:
                    high_score_symbols.append((symbol, data))

            # 按综合得分排序
            high_score_symbols.sort(key=lambda x: x[1]['multi_tf_score'], reverse=True)

            message = f"🚀 数字货币多周期选币提醒\n"
            message += f"⏰ 时间: {timestamp}\n"
            message += f"📊 分析周期: 1d/4h/1h/30m/15m\n"
            message += f"🎯 发现 {len(high_score_symbols)} 个优质标的\n\n"

            # 添加前5个结果
            for i, (symbol, data) in enumerate(high_score_symbols[:5], 1):
                multi_tf_score = data['multi_tf_score']
                confirmation = data['confirmation']

                # 获取最新价格（使用日线数据）
                daily_data = data.get('1d', {})
                price = daily_data.get('current_price', 0)
                rsi = daily_data.get('rsi', 50)

                # 获取主要信号
                all_signals = []
                for tf_data in data.values():
                    if isinstance(tf_data, dict) and 'signals' in tf_data:
                        all_signals.extend(tf_data['signals'])

                # 去重并取前2个
                unique_signals = list(dict.fromkeys(all_signals))[:2]
                signals_str = ', '.join(unique_signals) if unique_signals else '无明显信号'

                message += f"{i}. {symbol}\n"
                message += f"   💯 综合得分: {multi_tf_score:.2f}\n"
                message += f"   💰 当前价格: {price:.4f}\n"
                message += f"   📈 RSI: {rsi:.1f}\n"
                message += f"   🔔 主要信号: {signals_str}\n"
                message += f"   ⚡ 风险等级: {confirmation['risk_level']}\n\n"

            # 添加各周期得分详情
            if high_score_symbols:
                message += "📋 多周期得分详情:\n"
                for symbol, data in high_score_symbols[:3]:
                    message += f"{symbol}: "
                    scores = []
                    for tf in ['1d', '4h', '1h', '30m', '15m']:
                        if tf in data:
                            score = data[tf]['score']
                            scores.append(f"{tf}({score:.1f})")
                    message += " ".join(scores) + "\n"

            message += "\n⚠️ 风险提示: 仅供参考，请谨慎投资"

            return message

        except Exception as e:
            print(f"构建通知消息失败: {e}")
            return f"数字货币多周期选币系统运行完成\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

    def run_enhanced_analysis(self):
        """运行增强版分析"""
        print("🚀 增强版数字货币多周期选币系统")
        print("=" * 60)
        print("支持时间周期: 1d, 4h, 1h, 30m, 15m")
        print("集成企业微信推送功能")
        print("=" * 60)

        # 运行多时间周期分析
        results = self.multi_timeframe_analysis()

        # 显示汇总结果
        self.display_summary_results(results)

        # 发送企业微信通知
        print("\n📱 发送企业微信通知...")
        self.send_wechat_notification(results)

        return results

    def display_summary_results(self, results: dict):
        """显示汇总结果"""
        print("\n📊 多周期分析汇总")
        print("=" * 80)

        if not results:
            print("未找到符合条件的标的")
            return

        # 按综合得分排序
        sorted_results = sorted(
            results.items(),
            key=lambda x: x[1].get('multi_tf_score', 0),
            reverse=True
        )

        print(f"{'排名':<4} {'代码':<12} {'综合得分':<8} {'1d':<6} {'4h':<6} {'1h':<6} {'30m':<6} {'15m':<6} {'风险等级':<8} {'趋势一致'}")
        print("-" * 80)

        for i, (symbol, data) in enumerate(sorted_results, 1):
            multi_tf_score = data.get('multi_tf_score', 0)
            confirmation = data.get('confirmation', {})

            # 获取各周期得分
            scores = {}
            for tf in ['1d', '4h', '1h', '30m', '15m']:
                if tf in data:
                    scores[tf] = f"{data[tf]['score']:.1f}"
                else:
                    scores[tf] = "-"

            trend_align = "✓" if confirmation.get('trend_alignment') else "✗"
            risk_level = confirmation.get('risk_level', 'unknown')

            print(f"{i:<4} {symbol:<12} {multi_tf_score:<8.2f} "
                  f"{scores['1d']:<6} {scores['4h']:<6} {scores['1h']:<6} "
                  f"{scores['30m']:<6} {scores['15m']:<6} {risk_level:<8} {trend_align}")


def main():
    """主函数"""
    analyzer = EnhancedCryptoAnalyzer()

    # 运行增强版分析
    results = analyzer.run_enhanced_analysis()

    print("\n" + "=" * 60)
    print("✅ 增强版多周期分析完成")
    print("=" * 60)
    print("新增功能:")
    print("• 支持15分钟和30分钟周期分析")
    print("• 多时间周期权重优化配置")
    print("• 企业微信自动推送功能")
    print("• 风险等级智能评估")
    print("• 趋势一致性确认机制")


if __name__ == "__main__":
    main()
